package com.glodon.qydata.service.system;

import com.glodon.qydata.common.AccountTypeService;
import com.glodon.qydata.common.enums.AccountTypeEnum;
import com.glodon.qydata.mapper.system.DataCleanupMapper;
import com.glodon.qydata.service.system.DataCleanupService.CustomerCodeCleanupResult;
import com.glodon.qydata.service.system.DataCleanupService.CustomerCodeGroupStats;
import com.glodon.qydata.service.system.DataCleanupService.CustomerCodeRecordIdInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * DataCleanupService 测试类
 * 主要测试 zb_standards_trade 表的数据清理功能
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-01
 */
@ExtendWith(MockitoExtension.class)
class DataCleanupServiceTest {

    @Mock
    private DataCleanupMapper dataCleanupMapper;

    @Mock
    private AccountTypeService accountTypeService;

    @InjectMocks
    private DataCleanupService dataCleanupService;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }

    @Test
    void testPerformTradeDataCleanup_AnalysisOnly() {
        // 准备测试数据
        when(dataCleanupMapper.countTableRecords("zb_standards_trade")).thenReturn(1000L);
        
        List<CustomerCodeGroupStats> mockStats = new ArrayList<>();
        mockStats.add(new CustomerCodeGroupStats("*********", 50L)); // 个人账号
        mockStats.add(new CustomerCodeGroupStats("ENTERPRISE001", 100L)); // 企业账号
        when(dataCleanupMapper.getCustomerCodeGroupStats("zb_standards_trade")).thenReturn(mockStats);

        List<CustomerCodeRecordIdInfo> mockRecordIds = new ArrayList<>();
        for (int i = 1; i <= 50; i++) {
            mockRecordIds.add(new CustomerCodeRecordIdInfo((long) i, "*********"));
        }
        for (int i = 51; i <= 150; i++) {
            mockRecordIds.add(new CustomerCodeRecordIdInfo((long) i, "ENTERPRISE001"));
        }
        when(dataCleanupMapper.collectAllRecordIdsByCustomerCode("zb_standards_trade")).thenReturn(mockRecordIds);

        // 模拟账号类型判断
        when(accountTypeService.getAccountType("*********")).thenReturn(AccountTypeEnum.PERSONAL_TYPE);
        when(accountTypeService.getAccountType("ENTERPRISE001")).thenReturn(AccountTypeEnum.ENTERPRISE_TYPE);

        // 执行测试（仅分析，不执行清理）
        CustomerCodeCleanupResult result = dataCleanupService.performTradeDataCleanup(false);

        // 验证结果
        assertNotNull(result);
        assertEquals(1000L, result.getTotalRecords());
        assertFalse(result.isCleanupExecuted());
        assertEquals("zb_standards_trade 表数据分析完成，未执行清理操作", result.getMessage());
        
        // 验证个人账号数据收集
        assertNotNull(result.getPersonalAccountDataList());
        assertEquals(1, result.getPersonalAccountDataList().size());
        assertEquals("*********", result.getPersonalAccountDataList().get(0).getCustomerCode());
        assertEquals(50L, result.getPersonalAccountDataList().get(0).getRecordCount());

        // 验证没有执行删除操作
        verify(dataCleanupMapper, never()).deleteRecordsByCustomerCode(anyString(), anyString());
    }

    @Test
    void testPerformTradeDataCleanup_WithExecution() {
        // 准备测试数据
        when(dataCleanupMapper.countTableRecords("zb_standards_trade")).thenReturn(100L);
        
        List<CustomerCodeGroupStats> mockStats = new ArrayList<>();
        mockStats.add(new CustomerCodeGroupStats("*********", 30L)); // 个人账号
        when(dataCleanupMapper.getCustomerCodeGroupStats("zb_standards_trade")).thenReturn(mockStats);

        List<CustomerCodeRecordIdInfo> mockRecordIds = new ArrayList<>();
        for (int i = 1; i <= 30; i++) {
            mockRecordIds.add(new CustomerCodeRecordIdInfo((long) i, "*********"));
        }
        when(dataCleanupMapper.collectAllRecordIdsByCustomerCode("zb_standards_trade")).thenReturn(mockRecordIds);

        // 模拟账号类型判断
        when(accountTypeService.getAccountType("*********")).thenReturn(AccountTypeEnum.PERSONAL_TYPE);
        
        // 模拟删除操作
        when(dataCleanupMapper.deleteRecordsByCustomerCode("zb_standards_trade", "*********")).thenReturn(30);

        // 执行测试（执行清理）
        CustomerCodeCleanupResult result = dataCleanupService.performTradeDataCleanup(true);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isCleanupExecuted());
        assertEquals(30L, result.getDeletedRecords());
        assertTrue(result.getMessage().contains("共删除 30 条个人账号相关记录"));

        // 验证执行了删除操作
        verify(dataCleanupMapper, times(1)).deleteRecordsByCustomerCode("zb_standards_trade", "*********");
    }

    @Test
    void testIsNumeric() {
        // 测试数字判断方法（通过反射调用私有方法或创建测试用的公共方法）
        // 这里假设我们有一个公共的测试方法
        assertTrue(isNumericTest("*********"));
        assertFalse(isNumericTest("ENTERPRISE001"));
        assertFalse(isNumericTest(""));
        assertFalse(isNumericTest(null));
        assertFalse(isNumericTest("123ABC"));
    }

    /**
     * 测试用的数字判断方法（模拟 DataCleanupService 中的私有方法）
     */
    private boolean isNumericTest(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }
        return str.matches("\\d+");
    }

    @Test
    void testFormatBytes() {
        // 测试字节格式化方法
        assertEquals("0 B", formatBytesTest(0));
        assertEquals("1024 B", formatBytesTest(1024));
        assertEquals("1.00 KB", formatBytesTest(1024));
        assertEquals("1.00 MB", formatBytesTest(1024 * 1024));
        assertEquals("1.00 GB", formatBytesTest(1024L * 1024 * 1024));
    }

    /**
     * 测试用的字节格式化方法（模拟 DataCleanupService 中的私有方法）
     */
    private String formatBytesTest(long bytes) {
        if (bytes < 0) return "0 B";
        if (bytes < 1024) return bytes + " B";

        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = bytes;

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return String.format("%.2f %s", size, units[unitIndex]);
    }

    @Test
    void testPerformMainQuantityDataCleanup_AnalysisOnly() {
        // 准备测试数据
        when(dataCleanupMapper.countTableRecords("zb_standards_main_quantity")).thenReturn(2000L);

        List<CustomerCodeGroupStats> mockStats = new ArrayList<>();
        mockStats.add(new CustomerCodeGroupStats("*********", 80L)); // 个人账号
        mockStats.add(new CustomerCodeGroupStats("ENTERPRISE002", 200L)); // 企业账号
        when(dataCleanupMapper.getCustomerCodeGroupStats("zb_standards_main_quantity")).thenReturn(mockStats);

        List<CustomerCodeRecordIdInfo> mockRecordIds = new ArrayList<>();
        for (int i = 1; i <= 80; i++) {
            mockRecordIds.add(new CustomerCodeRecordIdInfo((long) i, "*********"));
        }
        for (int i = 81; i <= 280; i++) {
            mockRecordIds.add(new CustomerCodeRecordIdInfo((long) i, "ENTERPRISE002"));
        }
        when(dataCleanupMapper.collectAllRecordIdsByCustomerCode("zb_standards_main_quantity")).thenReturn(mockRecordIds);

        // 模拟账号类型判断
        when(accountTypeService.getAccountType("*********")).thenReturn(AccountTypeEnum.PERSONAL_TYPE);
        when(accountTypeService.getAccountType("ENTERPRISE002")).thenReturn(AccountTypeEnum.ENTERPRISE_TYPE);

        // 执行测试（仅分析，不执行清理）
        CustomerCodeCleanupResult result = dataCleanupService.performMainQuantityDataCleanup(false);

        // 验证结果
        assertNotNull(result);
        assertEquals(2000L, result.getTotalRecords());
        assertFalse(result.isCleanupExecuted());
        assertEquals("zb_standards_main_quantity 表数据分析完成，未执行清理操作", result.getMessage());

        // 验证个人账号数据收集
        assertNotNull(result.getPersonalAccountDataList());
        assertEquals(1, result.getPersonalAccountDataList().size());
        assertEquals("*********", result.getPersonalAccountDataList().get(0).getCustomerCode());
        assertEquals(80L, result.getPersonalAccountDataList().get(0).getRecordCount());

        // 验证没有执行删除操作
        verify(dataCleanupMapper, never()).deleteRecordsByCustomerCode(anyString(), anyString());
    }

    @Test
    void testPerformMainQuantityDataCleanup_WithExecution() {
        // 准备测试数据
        when(dataCleanupMapper.countTableRecords("zb_standards_main_quantity")).thenReturn(150L);

        List<CustomerCodeGroupStats> mockStats = new ArrayList<>();
        mockStats.add(new CustomerCodeGroupStats("*********", 60L)); // 个人账号
        when(dataCleanupMapper.getCustomerCodeGroupStats("zb_standards_main_quantity")).thenReturn(mockStats);

        List<CustomerCodeRecordIdInfo> mockRecordIds = new ArrayList<>();
        for (int i = 1; i <= 60; i++) {
            mockRecordIds.add(new CustomerCodeRecordIdInfo((long) i, "*********"));
        }
        when(dataCleanupMapper.collectAllRecordIdsByCustomerCode("zb_standards_main_quantity")).thenReturn(mockRecordIds);

        // 模拟账号类型判断
        when(accountTypeService.getAccountType("*********")).thenReturn(AccountTypeEnum.PERSONAL_TYPE);

        // 模拟删除操作
        when(dataCleanupMapper.deleteRecordsByCustomerCode("zb_standards_main_quantity", "*********")).thenReturn(60);

        // 执行测试（执行清理）
        CustomerCodeCleanupResult result = dataCleanupService.performMainQuantityDataCleanup(true);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isCleanupExecuted());
        assertEquals(60L, result.getDeletedRecords());
        assertTrue(result.getMessage().contains("共删除 60 条个人账号相关记录"));

        // 验证执行了删除操作
        verify(dataCleanupMapper, times(1)).deleteRecordsByCustomerCode("zb_standards_main_quantity", "*********");
    }

    @Test
    void testPerformMainQuantityDataCleanup_NoPersonalAccounts() {
        // 准备测试数据 - 只有企业账号
        when(dataCleanupMapper.countTableRecords("zb_standards_main_quantity")).thenReturn(500L);

        List<CustomerCodeGroupStats> mockStats = new ArrayList<>();
        mockStats.add(new CustomerCodeGroupStats("ENTERPRISE003", 300L)); // 企业账号
        mockStats.add(new CustomerCodeGroupStats("COMPANY001", 200L)); // 企业账号
        when(dataCleanupMapper.getCustomerCodeGroupStats("zb_standards_main_quantity")).thenReturn(mockStats);

        List<CustomerCodeRecordIdInfo> mockRecordIds = new ArrayList<>();
        for (int i = 1; i <= 300; i++) {
            mockRecordIds.add(new CustomerCodeRecordIdInfo((long) i, "ENTERPRISE003"));
        }
        for (int i = 301; i <= 500; i++) {
            mockRecordIds.add(new CustomerCodeRecordIdInfo((long) i, "COMPANY001"));
        }
        when(dataCleanupMapper.collectAllRecordIdsByCustomerCode("zb_standards_main_quantity")).thenReturn(mockRecordIds);

        // 执行测试（执行清理）
        CustomerCodeCleanupResult result = dataCleanupService.performMainQuantityDataCleanup(true);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isCleanupExecuted());
        assertEquals(0L, result.getDeletedRecords()); // 没有个人账号，删除记录数为0

        // 验证个人账号数据列表为空
        assertNotNull(result.getPersonalAccountDataList());
        assertEquals(0, result.getPersonalAccountDataList().size());

        // 验证没有执行删除操作（因为没有个人账号）
        verify(dataCleanupMapper, never()).deleteRecordsByCustomerCode(anyString(), anyString());
    }

    @Test
    void testPerformProjectFeatureDataCleanup_AnalysisOnly() {
        // 准备测试数据
        when(dataCleanupMapper.countTableRecords("zb_project_feature_standards")).thenReturn(3000L);

        List<CustomerCodeGroupStats> mockStats = new ArrayList<>();
        mockStats.add(new CustomerCodeGroupStats("*********", 120L)); // 个人账号
        mockStats.add(new CustomerCodeGroupStats("ENTERPRISE004", 400L)); // 企业账号
        when(dataCleanupMapper.getCustomerCodeGroupStats("zb_project_feature_standards")).thenReturn(mockStats);

        List<CustomerCodeRecordIdInfo> mockRecordIds = new ArrayList<>();
        for (int i = 1; i <= 120; i++) {
            mockRecordIds.add(new CustomerCodeRecordIdInfo((long) i, "*********"));
        }
        for (int i = 121; i <= 520; i++) {
            mockRecordIds.add(new CustomerCodeRecordIdInfo((long) i, "ENTERPRISE004"));
        }
        when(dataCleanupMapper.collectAllRecordIdsByCustomerCode("zb_project_feature_standards")).thenReturn(mockRecordIds);

        // 模拟账号类型判断
        when(accountTypeService.getAccountType("*********")).thenReturn(AccountTypeEnum.PERSONAL_TYPE);
        when(accountTypeService.getAccountType("ENTERPRISE004")).thenReturn(AccountTypeEnum.ENTERPRISE_TYPE);

        // 执行测试（仅分析，不执行清理）
        CustomerCodeCleanupResult result = dataCleanupService.performProjectFeatureDataCleanup(false);

        // 验证结果
        assertNotNull(result);
        assertEquals(3000L, result.getTotalRecords());
        assertFalse(result.isCleanupExecuted());
        assertEquals("zb_project_feature_standards 表数据分析完成，未执行清理操作", result.getMessage());

        // 验证个人账号数据收集
        assertNotNull(result.getPersonalAccountDataList());
        assertEquals(1, result.getPersonalAccountDataList().size());
        assertEquals("*********", result.getPersonalAccountDataList().get(0).getCustomerCode());
        assertEquals(120L, result.getPersonalAccountDataList().get(0).getRecordCount());

        // 验证没有执行删除操作
        verify(dataCleanupMapper, never()).deleteRecordsByCustomerCode(anyString(), anyString());
    }

    @Test
    void testPerformProjectFeatureDataCleanup_WithExecution() {
        // 准备测试数据
        when(dataCleanupMapper.countTableRecords("zb_project_feature_standards")).thenReturn(200L);

        List<CustomerCodeGroupStats> mockStats = new ArrayList<>();
        mockStats.add(new CustomerCodeGroupStats("*********", 90L)); // 个人账号
        when(dataCleanupMapper.getCustomerCodeGroupStats("zb_project_feature_standards")).thenReturn(mockStats);

        List<CustomerCodeRecordIdInfo> mockRecordIds = new ArrayList<>();
        for (int i = 1; i <= 90; i++) {
            mockRecordIds.add(new CustomerCodeRecordIdInfo((long) i, "*********"));
        }
        when(dataCleanupMapper.collectAllRecordIdsByCustomerCode("zb_project_feature_standards")).thenReturn(mockRecordIds);

        // 模拟账号类型判断
        when(accountTypeService.getAccountType("*********")).thenReturn(AccountTypeEnum.PERSONAL_TYPE);

        // 模拟删除操作
        when(dataCleanupMapper.deleteRecordsByCustomerCode("zb_project_feature_standards", "*********")).thenReturn(90);

        // 执行测试（执行清理）
        CustomerCodeCleanupResult result = dataCleanupService.performProjectFeatureDataCleanup(true);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isCleanupExecuted());
        assertEquals(90L, result.getDeletedRecords());
        assertTrue(result.getMessage().contains("共删除 90 条个人账号相关记录"));

        // 验证执行了删除操作
        verify(dataCleanupMapper, times(1)).deleteRecordsByCustomerCode("zb_project_feature_standards", "*********");
    }

    @Test
    void testPerformProjectFeatureDataCleanup_MultiplePersonalAccounts() {
        // 准备测试数据 - 多个个人账号
        when(dataCleanupMapper.countTableRecords("zb_project_feature_standards")).thenReturn(800L);

        List<CustomerCodeGroupStats> mockStats = new ArrayList<>();
        mockStats.add(new CustomerCodeGroupStats("*********", 100L)); // 个人账号1
        mockStats.add(new CustomerCodeGroupStats("*********", 80L));  // 个人账号2
        mockStats.add(new CustomerCodeGroupStats("ENTERPRISE005", 620L)); // 企业账号
        when(dataCleanupMapper.getCustomerCodeGroupStats("zb_project_feature_standards")).thenReturn(mockStats);

        List<CustomerCodeRecordIdInfo> mockRecordIds = new ArrayList<>();
        for (int i = 1; i <= 100; i++) {
            mockRecordIds.add(new CustomerCodeRecordIdInfo((long) i, "*********"));
        }
        for (int i = 101; i <= 180; i++) {
            mockRecordIds.add(new CustomerCodeRecordIdInfo((long) i, "*********"));
        }
        for (int i = 181; i <= 800; i++) {
            mockRecordIds.add(new CustomerCodeRecordIdInfo((long) i, "ENTERPRISE005"));
        }
        when(dataCleanupMapper.collectAllRecordIdsByCustomerCode("zb_project_feature_standards")).thenReturn(mockRecordIds);

        // 模拟账号类型判断
        when(accountTypeService.getAccountType("*********")).thenReturn(AccountTypeEnum.PERSONAL_TYPE);
        when(accountTypeService.getAccountType("*********")).thenReturn(AccountTypeEnum.PERSONAL_TYPE);
        when(accountTypeService.getAccountType("ENTERPRISE005")).thenReturn(AccountTypeEnum.ENTERPRISE_TYPE);

        // 模拟删除操作
        when(dataCleanupMapper.deleteRecordsByCustomerCode("zb_project_feature_standards", "*********")).thenReturn(100);
        when(dataCleanupMapper.deleteRecordsByCustomerCode("zb_project_feature_standards", "*********")).thenReturn(80);

        // 执行测试（执行清理）
        CustomerCodeCleanupResult result = dataCleanupService.performProjectFeatureDataCleanup(true);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isCleanupExecuted());
        assertEquals(180L, result.getDeletedRecords()); // 100 + 80
        assertTrue(result.getMessage().contains("共删除 180 条个人账号相关记录"));

        // 验证个人账号数据列表
        assertNotNull(result.getPersonalAccountDataList());
        assertEquals(2, result.getPersonalAccountDataList().size());

        // 验证执行了删除操作
        verify(dataCleanupMapper, times(1)).deleteRecordsByCustomerCode("zb_project_feature_standards", "*********");
        verify(dataCleanupMapper, times(1)).deleteRecordsByCustomerCode("zb_project_feature_standards", "*********");
        verify(dataCleanupMapper, never()).deleteRecordsByCustomerCode("zb_project_feature_standards", "ENTERPRISE005");
    }

    @Test
    void testPerformCategoryViewDataCleanup_AnalysisOnly() {
        // 准备测试数据 - 双表
        when(dataCleanupMapper.countTableRecords("zb_project_feature_category_view_standards")).thenReturn(4000L);
        when(dataCleanupMapper.countTableRecords("zb_project_feature_category_view_standards_0")).thenReturn(3600L);

        List<CustomerCodeGroupStats> mockStats1 = new ArrayList<>();
        mockStats1.add(new CustomerCodeGroupStats("*********", 150L)); // 个人账号
        mockStats1.add(new CustomerCodeGroupStats("ENTERPRISE006", 600L)); // 企业账号
        when(dataCleanupMapper.getCustomerCodeGroupStats("zb_project_feature_category_view_standards")).thenReturn(mockStats1);

        List<CustomerCodeGroupStats> mockStats2 = new ArrayList<>();
        mockStats2.add(new CustomerCodeGroupStats("*********", 120L)); // 个人账号（分表）
        mockStats2.add(new CustomerCodeGroupStats("ENTERPRISE006", 500L)); // 企业账号（分表）
        when(dataCleanupMapper.getCustomerCodeGroupStats("zb_project_feature_category_view_standards_0")).thenReturn(mockStats2);

        List<CustomerCodeRecordIdInfo> mockRecordIds1 = new ArrayList<>();
        for (int i = 1; i <= 150; i++) {
            mockRecordIds1.add(new CustomerCodeRecordIdInfo((long) i, "*********"));
        }
        for (int i = 151; i <= 750; i++) {
            mockRecordIds1.add(new CustomerCodeRecordIdInfo((long) i, "ENTERPRISE006"));
        }
        when(dataCleanupMapper.collectAllRecordIdsByCustomerCode("zb_project_feature_category_view_standards")).thenReturn(mockRecordIds1);

        List<CustomerCodeRecordIdInfo> mockRecordIds2 = new ArrayList<>();
        for (int i = 1001; i <= 1120; i++) {
            mockRecordIds2.add(new CustomerCodeRecordIdInfo((long) i, "*********"));
        }
        for (int i = 1121; i <= 1620; i++) {
            mockRecordIds2.add(new CustomerCodeRecordIdInfo((long) i, "ENTERPRISE006"));
        }
        when(dataCleanupMapper.collectAllRecordIdsByCustomerCode("zb_project_feature_category_view_standards_0")).thenReturn(mockRecordIds2);

        // 模拟账号类型判断
        when(accountTypeService.getAccountType("*********")).thenReturn(AccountTypeEnum.PERSONAL_TYPE);
        when(accountTypeService.getAccountType("ENTERPRISE006")).thenReturn(AccountTypeEnum.ENTERPRISE_TYPE);

        // 执行测试（仅分析，不执行清理）
        CustomerCodeCleanupResult result = dataCleanupService.performCategoryViewDataCleanup(false);

        // 验证结果
        assertNotNull(result);
        assertEquals(7600L, result.getTotalRecords()); // 4000 + 3600
        assertFalse(result.isCleanupExecuted());
        assertEquals("zb_project_feature_category_view_standards 表数据分析完成，未执行清理操作", result.getMessage());

        // 验证个人账号数据收集
        assertNotNull(result.getPersonalAccountDataList());
        assertEquals(1, result.getPersonalAccountDataList().size());
        assertEquals("*********", result.getPersonalAccountDataList().get(0).getCustomerCode());
        assertEquals(270L, result.getPersonalAccountDataList().get(0).getRecordCount()); // 150 + 120

        // 验证分组统计合并
        assertNotNull(result.getCustomerCodeGroupStats());
        assertEquals(270L, result.getCustomerCodeGroupStats().get("*********")); // 150 + 120
        assertEquals(1100L, result.getCustomerCodeGroupStats().get("ENTERPRISE006")); // 600 + 500

        // 验证没有执行删除操作
        verify(dataCleanupMapper, never()).deleteRecordsByCustomerCode(anyString(), anyString());
    }

    @Test
    void testPerformCategoryViewDataCleanup_WithExecution() {
        // 准备测试数据 - 双表
        when(dataCleanupMapper.countTableRecords("zb_project_feature_category_view_standards")).thenReturn(300L);
        when(dataCleanupMapper.countTableRecords("zb_project_feature_category_view_standards_0")).thenReturn(250L);

        List<CustomerCodeGroupStats> mockStats1 = new ArrayList<>();
        mockStats1.add(new CustomerCodeGroupStats("*********", 80L)); // 个人账号
        when(dataCleanupMapper.getCustomerCodeGroupStats("zb_project_feature_category_view_standards")).thenReturn(mockStats1);

        List<CustomerCodeGroupStats> mockStats2 = new ArrayList<>();
        mockStats2.add(new CustomerCodeGroupStats("*********", 70L)); // 个人账号（分表）
        when(dataCleanupMapper.getCustomerCodeGroupStats("zb_project_feature_category_view_standards_0")).thenReturn(mockStats2);

        List<CustomerCodeRecordIdInfo> mockRecordIds1 = new ArrayList<>();
        for (int i = 1; i <= 80; i++) {
            mockRecordIds1.add(new CustomerCodeRecordIdInfo((long) i, "*********"));
        }
        when(dataCleanupMapper.collectAllRecordIdsByCustomerCode("zb_project_feature_category_view_standards")).thenReturn(mockRecordIds1);

        List<CustomerCodeRecordIdInfo> mockRecordIds2 = new ArrayList<>();
        for (int i = 1001; i <= 1070; i++) {
            mockRecordIds2.add(new CustomerCodeRecordIdInfo((long) i, "*********"));
        }
        when(dataCleanupMapper.collectAllRecordIdsByCustomerCode("zb_project_feature_category_view_standards_0")).thenReturn(mockRecordIds2);

        // 模拟账号类型判断
        when(accountTypeService.getAccountType("*********")).thenReturn(AccountTypeEnum.PERSONAL_TYPE);

        // 模拟删除操作
        when(dataCleanupMapper.deleteRecordsByCustomerCode("zb_project_feature_category_view_standards", "*********")).thenReturn(80);
        when(dataCleanupMapper.deleteRecordsByCustomerCode("zb_project_feature_category_view_standards_0", "*********")).thenReturn(70);

        // 执行测试（执行清理）
        CustomerCodeCleanupResult result = dataCleanupService.performCategoryViewDataCleanup(true);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isCleanupExecuted());
        assertEquals(150L, result.getDeletedRecords()); // 80 + 70
        assertTrue(result.getMessage().contains("共删除 150 条个人账号相关记录"));

        // 验证执行了删除操作
        verify(dataCleanupMapper, times(1)).deleteRecordsByCustomerCode("zb_project_feature_category_view_standards", "*********");
        verify(dataCleanupMapper, times(1)).deleteRecordsByCustomerCode("zb_project_feature_category_view_standards_0", "*********");
    }

    @Test
    void testPerformCategoryViewDataCleanup_EmptyTables() {
        // 准备测试数据 - 空表
        when(dataCleanupMapper.countTableRecords("zb_project_feature_category_view_standards")).thenReturn(0L);
        when(dataCleanupMapper.countTableRecords("zb_project_feature_category_view_standards_0")).thenReturn(0L);

        when(dataCleanupMapper.getCustomerCodeGroupStats("zb_project_feature_category_view_standards")).thenReturn(new ArrayList<>());
        when(dataCleanupMapper.getCustomerCodeGroupStats("zb_project_feature_category_view_standards_0")).thenReturn(new ArrayList<>());
        when(dataCleanupMapper.collectAllRecordIdsByCustomerCode("zb_project_feature_category_view_standards")).thenReturn(new ArrayList<>());
        when(dataCleanupMapper.collectAllRecordIdsByCustomerCode("zb_project_feature_category_view_standards_0")).thenReturn(new ArrayList<>());

        // 执行测试（执行清理）
        CustomerCodeCleanupResult result = dataCleanupService.performCategoryViewDataCleanup(true);

        // 验证结果
        assertNotNull(result);
        assertEquals(0L, result.getTotalRecords());
        assertTrue(result.isCleanupExecuted());
        assertEquals(0L, result.getDeletedRecords());

        // 验证个人账号数据列表为空
        assertNotNull(result.getPersonalAccountDataList());
        assertEquals(0, result.getPersonalAccountDataList().size());

        // 验证没有执行删除操作（因为没有数据）
        verify(dataCleanupMapper, never()).deleteRecordsByCustomerCode(anyString(), anyString());
    }
}
