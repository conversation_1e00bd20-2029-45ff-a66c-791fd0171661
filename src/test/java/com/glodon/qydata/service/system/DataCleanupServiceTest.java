package com.glodon.qydata.service.system;

import com.glodon.qydata.common.AccountTypeService;
import com.glodon.qydata.common.enums.AccountTypeEnum;
import com.glodon.qydata.mapper.system.DataCleanupMapper;
import com.glodon.qydata.service.system.DataCleanupService.CustomerCodeCleanupResult;
import com.glodon.qydata.service.system.DataCleanupService.CustomerCodeGroupStats;
import com.glodon.qydata.service.system.DataCleanupService.CustomerCodeRecordIdInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * DataCleanupService 测试类
 * 主要测试 zb_standards_trade 表的数据清理功能
 * 
 * <AUTHOR> Assistant
 * @date 2025-08-01
 */
@ExtendWith(MockitoExtension.class)
class DataCleanupServiceTest {

    @Mock
    private DataCleanupMapper dataCleanupMapper;

    @Mock
    private AccountTypeService accountTypeService;

    @InjectMocks
    private DataCleanupService dataCleanupService;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
    }

    @Test
    void testPerformTradeDataCleanup_AnalysisOnly() {
        // 准备测试数据
        when(dataCleanupMapper.countTableRecords("zb_standards_trade")).thenReturn(1000L);
        
        List<CustomerCodeGroupStats> mockStats = new ArrayList<>();
        mockStats.add(new CustomerCodeGroupStats("*********", 50L)); // 个人账号
        mockStats.add(new CustomerCodeGroupStats("ENTERPRISE001", 100L)); // 企业账号
        when(dataCleanupMapper.getCustomerCodeGroupStats("zb_standards_trade")).thenReturn(mockStats);

        List<CustomerCodeRecordIdInfo> mockRecordIds = new ArrayList<>();
        for (int i = 1; i <= 50; i++) {
            mockRecordIds.add(new CustomerCodeRecordIdInfo((long) i, "*********"));
        }
        for (int i = 51; i <= 150; i++) {
            mockRecordIds.add(new CustomerCodeRecordIdInfo((long) i, "ENTERPRISE001"));
        }
        when(dataCleanupMapper.collectAllRecordIdsByCustomerCode("zb_standards_trade")).thenReturn(mockRecordIds);

        // 模拟账号类型判断
        when(accountTypeService.getAccountType("*********")).thenReturn(AccountTypeEnum.PERSONAL_TYPE);
        when(accountTypeService.getAccountType("ENTERPRISE001")).thenReturn(AccountTypeEnum.ENTERPRISE_TYPE);

        // 执行测试（仅分析，不执行清理）
        CustomerCodeCleanupResult result = dataCleanupService.performTradeDataCleanup(false);

        // 验证结果
        assertNotNull(result);
        assertEquals(1000L, result.getTotalRecords());
        assertFalse(result.isCleanupExecuted());
        assertEquals("zb_standards_trade 表数据分析完成，未执行清理操作", result.getMessage());
        
        // 验证个人账号数据收集
        assertNotNull(result.getPersonalAccountDataList());
        assertEquals(1, result.getPersonalAccountDataList().size());
        assertEquals("*********", result.getPersonalAccountDataList().get(0).getCustomerCode());
        assertEquals(50L, result.getPersonalAccountDataList().get(0).getRecordCount());

        // 验证没有执行删除操作
        verify(dataCleanupMapper, never()).deleteRecordsByCustomerCode(anyString(), anyString());
    }

    @Test
    void testPerformTradeDataCleanup_WithExecution() {
        // 准备测试数据
        when(dataCleanupMapper.countTableRecords("zb_standards_trade")).thenReturn(100L);
        
        List<CustomerCodeGroupStats> mockStats = new ArrayList<>();
        mockStats.add(new CustomerCodeGroupStats("*********", 30L)); // 个人账号
        when(dataCleanupMapper.getCustomerCodeGroupStats("zb_standards_trade")).thenReturn(mockStats);

        List<CustomerCodeRecordIdInfo> mockRecordIds = new ArrayList<>();
        for (int i = 1; i <= 30; i++) {
            mockRecordIds.add(new CustomerCodeRecordIdInfo((long) i, "*********"));
        }
        when(dataCleanupMapper.collectAllRecordIdsByCustomerCode("zb_standards_trade")).thenReturn(mockRecordIds);

        // 模拟账号类型判断
        when(accountTypeService.getAccountType("*********")).thenReturn(AccountTypeEnum.PERSONAL_TYPE);
        
        // 模拟删除操作
        when(dataCleanupMapper.deleteRecordsByCustomerCode("zb_standards_trade", "*********")).thenReturn(30);

        // 执行测试（执行清理）
        CustomerCodeCleanupResult result = dataCleanupService.performTradeDataCleanup(true);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isCleanupExecuted());
        assertEquals(30L, result.getDeletedRecords());
        assertTrue(result.getMessage().contains("共删除 30 条个人账号相关记录"));

        // 验证执行了删除操作
        verify(dataCleanupMapper, times(1)).deleteRecordsByCustomerCode("zb_standards_trade", "*********");
    }

    @Test
    void testIsNumeric() {
        // 测试数字判断方法（通过反射调用私有方法或创建测试用的公共方法）
        // 这里假设我们有一个公共的测试方法
        assertTrue(isNumericTest("*********"));
        assertFalse(isNumericTest("ENTERPRISE001"));
        assertFalse(isNumericTest(""));
        assertFalse(isNumericTest(null));
        assertFalse(isNumericTest("123ABC"));
    }

    /**
     * 测试用的数字判断方法（模拟 DataCleanupService 中的私有方法）
     */
    private boolean isNumericTest(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }
        return str.matches("\\d+");
    }

    @Test
    void testFormatBytes() {
        // 测试字节格式化方法
        assertEquals("0 B", formatBytesTest(0));
        assertEquals("1024 B", formatBytesTest(1024));
        assertEquals("1.00 KB", formatBytesTest(1024));
        assertEquals("1.00 MB", formatBytesTest(1024 * 1024));
        assertEquals("1.00 GB", formatBytesTest(1024L * 1024 * 1024));
    }

    /**
     * 测试用的字节格式化方法（模拟 DataCleanupService 中的私有方法）
     */
    private String formatBytesTest(long bytes) {
        if (bytes < 0) return "0 B";
        if (bytes < 1024) return bytes + " B";

        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = bytes;

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return String.format("%.2f %s", size, units[unitIndex]);
    }
}
