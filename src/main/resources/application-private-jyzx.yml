server:
  port: 8083

log:
  level: DEBUG

spring:
  datasource:
    #主数据源
    master:
      url: ****************************************************************************************************************************************************************************************************************************************************************************************
      username: root
      password: h4D!P4hV^Lo#jHdr
      driverClassName: com.mysql.cj.jdbc.Driver
      type: com.alibaba.druid.pool.DruidDataSource

  #缓存
  redis:
    host: redis-0.redis.public-service
    port: 6379
    password: c&HR@R*vZXAx63n*
    timeout: 10000ms
    maxTotal: 300
    jedis:
      pool:
        max-wait: 18000
        max-active: 800
        max-idle: 400
        min-idle: 1
  #上传文件大小设置
  servlet:
    multipart:
      max-file-size: 80MB
      max-request-size: 100MB


config:
  #依赖服务相关配置
  depend:
    #依赖广联云服务域名
    glodonUrl: https://account.glodon.com
    glodonApiAuthUrl: https://api-auth.glodon.com
    glodonEntUrl: https://ent.glodon.com
    #数字成本平台接口URL
    digitalCostApiUrl: https://dcost.ccbecc.com:9501
    #委托服务域名
    trustUrl: https://dcost.ccbecc.com:9504
#    todo
  gcw_cloud:
    APPId: dcost_qylib_private_jyzx
    secret: 8O'N%o.L
    secretKey: c77766ebcb3c912aa98d967b37ad78cb
glodon:
  getUserinfoByIdentityHost: https://account.glodon.com
#  前端已禁用导入功能
  middlegroundhost: http://server-gcdp-mbcb.gcost-platform-sprint
#  已经开通外网 不需要修改
  gcw_cloud_url: http://cloud-api.gldjc.com

#发送埋点相关 不需要修改
ip2region:
  path: D:\develop\config\ipRegion\ip2region.db
basic_auth:
  service.key: MiOWRrW6hYBmW6xQE8OGRH2f9I5wfxUG
  server.secret: vG23xiwByuRxKdhAehgKaDEK89jyKzsC


#是否埋点，默认埋点，私有化部署不买点
sendIoLog: false