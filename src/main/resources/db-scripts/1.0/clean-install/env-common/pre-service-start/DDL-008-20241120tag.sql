use `db_cost_data_platform_pro`;
create table `db_cost_data_platform_pro`.category_tag
(
    id            bigint                             not null comment '主键ID'  primary key,
    name          varchar(100)                       not null comment '标签名称',
    enterprise_id varchar(30)                        null comment '企业id',
    create_time   datetime                           null comment '创建时间',
    modify_time   datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    ord           int                                null comment '顺序'
)
    comment '工程分类标签发布表';



create table `db_cost_data_platform_pro`.category_tag_self
(
    id            bigint                             not null comment '主键ID'  primary key,
    name          varchar(100)                       not null comment '标签名称',
    enterprise_id varchar(30)                        null comment '企业id',
    create_time   datetime                           null comment '创建时间',
    modify_time   datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    original_id   bigint                             null comment '主表category_tag的id',
    ord           int                                null comment '顺序'
)
    comment '工程分类标签暂存表';



create table `db_cost_data_platform_pro`.category_tag_enum
(
    id            bigint                             not null comment '主键ID'  primary key,
    name          varchar(100)                       not null comment '标签枚举名称',
    enterprise_id varchar(30)                        null comment '企业id',
    create_time   datetime                           null comment '创建时间',
    modify_time   datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    tag_id        bigint                             not null comment 'category_tag的id',
    ord           int                                null comment '顺序'
)
    comment '工程分类标签枚举发布表';



create table `db_cost_data_platform_pro`.category_tag_enum_self
(
    id            bigint                             not null comment '主键ID'  primary key,
    name          varchar(100)                       not null comment '标签枚举名称',
    enterprise_id varchar(30)                        null comment '企业id',
    create_time   datetime                           null comment '创建时间',
    modify_time   datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间',
    tag_id        bigint                             not null comment 'category_tag的id',
    original_id   bigint                             null comment '原始id，对应主表category_tag_enum的id',
    ord           int                                null comment '顺序'
)
    comment '工程分类标签枚举暂存表';



create table `db_cost_data_platform_pro`.category_tag_enum_rel
(
    id            bigint                             not null primary key,
    category_code varchar(50)                        not null comment '一级工程分类编码',
    tag_id        bigint                             not null comment '标签id',
    tag_enum_id   bigint                             not null comment '标签枚举值id',
    enterprise_id varchar(30)                        null comment '企业id',
    create_time   datetime                           null comment '创建时间',
    modify_time   datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '工程分类与标签及枚举值关系发布表';



create table `db_cost_data_platform_pro`.category_tag_enum_rel_self
(
    id            bigint                             not null primary key,
    category_code varchar(50)                        not null comment '一级工程分类编码',
    tag_id        bigint                             not null comment '标签id',
    tag_enum_id   bigint                             not null comment '标签枚举值id',
    enterprise_id varchar(30)                        null comment '企业id',
    original_id   bigint                             null comment '原始id，对应主表category_tag_enum_rel的id',
    create_time   datetime                           null comment '创建时间',
    modify_time   datetime default CURRENT_TIMESTAMP null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '工程分类与标签及枚举值关系暂存表';



CREATE TABLE `db_cost_data_platform_pro`.`trade_tag_enum` (
                                  `id` bigint(20) NOT NULL COMMENT '主键ID',
                                  `name` varchar(100) NOT NULL COMMENT '标签枚举名称',
                                  `enterprise_id` varchar(30) DEFAULT NULL COMMENT '企业id',
                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                  `modify_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  `ord` int(11) DEFAULT NULL COMMENT '顺序',
                                  PRIMARY KEY (`id`),
                                  KEY `enterprise_id_index` (`enterprise_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='专业标签选项字典表';


create table `db_cost_data_platform_pro`.tag_integration
(
    id            bigint      not null comment '主键ID'
        primary key,
    enterprise_id varchar(30) null comment '企业id',
    create_time   datetime    null comment '创建时间',
    tag_type      int         null comment '初始化的数据类型,0:工程分类 1:专业标签'
)
    comment '标签初始化集成标签表';


ALTER TABLE `db_cost_data_platform_pro`.zb_standards_trade ADD trade_tag_id bigint(20) NULL COMMENT '专业标签id';

create index enterprise_id_index
    on `db_cost_data_platform_pro`.category_tag (enterprise_id);
create index enterprise_id_index
    on `db_cost_data_platform_pro`.category_tag_enum_rel_self (enterprise_id);
create index enterprise_id_index
    on `db_cost_data_platform_pro`.category_tag_self (enterprise_id);
create index enterprise_id_index
    on `db_cost_data_platform_pro`.category_tag_enum (enterprise_id);
create index enterprise_id_index
    on `db_cost_data_platform_pro`.category_tag_enum_self (enterprise_id);
create index enterprise_id_index
    on `db_cost_data_platform_pro`.category_tag_enum_rel (enterprise_id);

create index enterprise_id_index
    on `db_cost_data_platform_pro`.tag_integration (enterprise_id);
