use `db_cost_data_platform_pro`;
insert into `db_cost_data_platform_pro`.category_tag (id, name, enterprise_id, ord) values (1, '所属行业', '-100', 1);
insert into `db_cost_data_platform_pro`.category_tag (id, name, enterprise_id, ord) values (2, '使用性质', '-100', 2);


insert into `db_cost_data_platform_pro`.category_tag_enum (id, name, enterprise_id, tag_id, ord) values (1, '房屋建筑工程', '-100', 1, 1);
insert into `db_cost_data_platform_pro`.category_tag_enum (id, name, enterprise_id, tag_id, ord) values (2, '房屋修缮工程', '-100', 1, 2);
insert into `db_cost_data_platform_pro`.category_tag_enum (id, name, enterprise_id, tag_id, ord) values (3, '市政工程', '-100', 1, 3);
insert into `db_cost_data_platform_pro`.category_tag_enum (id, name, enterprise_id, tag_id, ord) values (4, '轨道交通工程', '-100', 1, 4);
insert into `db_cost_data_platform_pro`.category_tag_enum (id, name, enterprise_id, tag_id, ord) values (5, '公路工程', '-100', 1, 5);
insert into `db_cost_data_platform_pro`.category_tag_enum (id, name, enterprise_id, tag_id, ord) values (6, '民用建筑', '-100', 2, 1);
insert into `db_cost_data_platform_pro`.category_tag_enum (id, name, enterprise_id, tag_id, ord) values (7, '工业建筑', '-100', 2, 2);
insert into `db_cost_data_platform_pro`.category_tag_enum (id, name, enterprise_id, tag_id, ord) values (8, '农业建筑', '-100', 2, 3);
insert into `db_cost_data_platform_pro`.category_tag_enum (id, name, enterprise_id, tag_id, ord) values (9, '园林建筑', '-100', 2, 4);
insert into `db_cost_data_platform_pro`.category_tag_enum (id, name, enterprise_id, tag_id, ord) values (10, '构筑物', '-100', 2, 5);


INSERT INTO `db_cost_data_platform_pro`.trade_tag_enum (id, name, enterprise_id,  ord) VALUES (1, '建筑工程', '-100', 1);
INSERT INTO `db_cost_data_platform_pro`.trade_tag_enum (id, name, enterprise_id,  ord) VALUES (2, '装配式工程', '-100', 2);
INSERT INTO `db_cost_data_platform_pro`.trade_tag_enum (id, name, enterprise_id,  ord) VALUES (3, '装饰工程', '-100', 3);
INSERT INTO `db_cost_data_platform_pro`.trade_tag_enum (id, name, enterprise_id,  ord) VALUES (4, '安装工程', '-100', 4);
INSERT INTO `db_cost_data_platform_pro`.trade_tag_enum (id, name, enterprise_id,  ord) VALUES (5, '园林工程', '-100', 5);
INSERT INTO `db_cost_data_platform_pro`.trade_tag_enum (id, name, enterprise_id,  ord) VALUES (6, '市政工程', '-100', 6);
