use `db_cost_data_platform_pro`;
INSERT INTO db_cost_data_platform_pro.sharding_route (customer_code, table_type, current_table_name, create_time)
SELECT customer_code, 0, 'zb_project_feature_category_view_standards', current_timestamp()
FROM zb_project_feature_category_view_standards
WHERE customer_code IS NOT NULL AND customer_code != ''
GROUP BY customer_code;

INSERT INTO db_cost_data_platform_pro.sharding_route (customer_code, table_type, current_table_name, create_time)
SELECT qy_code, 1, 'tb_commonprojcategory_standards', current_timestamp()
FROM tb_commonprojcategory_standards
WHERE qy_code IS NOT NULL AND qy_code != ''
GROUP BY qy_code;
