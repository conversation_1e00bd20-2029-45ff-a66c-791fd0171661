CREATE DATABASE IF NOT EXISTS `db_cost_data_platform_pro`;

USE `db_cost_data_platform_pro`;

-- db_cost_data_platform_pro.tb_area definition

CREATE TABLE `db_cost_data_platform_pro`.`tb_area` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `areaid` varchar(40) DEFAULT NULL,
  `pid` varchar(40) DEFAULT NULL,
  `ord` int(11) DEFAULT NULL,
  `area_code` varchar(6) DEFAULT NULL COMMENT '地区编码',
  `name` varchar(50) DEFAULT NULL COMMENT '地区名称',
  `short_name` varchar(50) DEFAULT NULL COMMENT '地区简称',
  `parent_id` int(11) DEFAULT NULL COMMENT '上级地区id',
  `parent_area_code` varchar(6) DEFAULT NULL COMMENT '上级地区编码',
  `abbr_spell` varchar(16) DEFAULT NULL COMMENT '名称简拼',
  `full_spell` varchar(60) DEFAULT NULL COMMENT '名称全拼',
  `level` tinyint(1) DEFAULT NULL COMMENT '级别',
  `sort` smallint(255) DEFAULT NULL COMMENT '排序依据',
  `area` varchar(60) DEFAULT NULL COMMENT '所属大区',
  `longitude` double DEFAULT NULL COMMENT '经度',
  `latitude` double DEFAULT NULL COMMENT '纬度',
  `displayable` tinyint(4) DEFAULT NULL COMMENT '是否显示（1：显示，0：不显示）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_area_code` (`area_code`),
  UNIQUE KEY `tb_area_areaid_uindex` (`areaid`),
  KEY `tb_area_updated_at_IDX` (`updated_at`)
) ENGINE=InnoDB AUTO_INCREMENT=8796 DEFAULT CHARSET=utf8;


-- db_cost_data_platform_pro.tb_commonprojcategory_standards definition

CREATE TABLE `db_cost_data_platform_pro`.`tb_commonprojcategory_standards` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `commonprojcategoryid` varchar(40) NOT NULL COMMENT '业态编码',
  `categoryname` varchar(200) DEFAULT NULL COMMENT '分类名称',
  `type` int(1) DEFAULT '1',
  `categorycode1` varchar(50) DEFAULT NULL COMMENT '一级编码',
  `categorycode2` varchar(50) DEFAULT NULL COMMENT '二级编码',
  `categorycode3` varchar(50) DEFAULT NULL COMMENT '三级编码',
  `categorycode4` varchar(50) DEFAULT NULL COMMENT '四级编码',
  `category_type_code` varchar(50) DEFAULT NULL COMMENT '业态类型编码',
  `category_type_name` varchar(50) DEFAULT NULL COMMENT '业态类型名称',
  `level` bigint(11) DEFAULT NULL COMMENT '等级',
  `projcount` int(11) DEFAULT NULL COMMENT '（历史遗留）',
  `mainparamname` varchar(50) DEFAULT NULL COMMENT '（历史遗留）',
  `unit` varchar(20) DEFAULT NULL COMMENT '（历史遗留）单位',
  `ord` int(11) DEFAULT NULL COMMENT '排序',
  `province_process` varchar(64) DEFAULT NULL COMMENT '（历史遗留）项目地区标识：采用地区拼音进行标识，如：shanghai',
  `qy_code` varchar(100) DEFAULT NULL COMMENT '企业编码',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除(1已删除,0未删除)',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_using` tinyint(1) DEFAULT '1' COMMENT '是否启用（1，启用；0，禁用）',
  `update_global_id` varchar(32) DEFAULT NULL COMMENT '更新人',
  `remark` varchar(400) DEFAULT NULL COMMENT '备注',
  `is_usable` tinyint(1) DEFAULT '0' COMMENT '（历史遗留）是否被使用（1，使用；0，未使用）',
  `global_id` varchar(32) DEFAULT NULL COMMENT '创建人',
  `accountname` varchar(50) DEFAULT NULL COMMENT '（历史遗留）创建账号',
  `enterprise_id` bigint(30) DEFAULT NULL COMMENT 'gccs企业id',
  PRIMARY KEY (`id`),
  KEY `categoryname_idx` (`categoryname`),
  KEY `categorycode1_idx` (`categorycode1`),
  KEY `CUSTOMERCODE` (`qy_code`),
  KEY `tb_commonprojcategory_standards_update_time_IDX` (`update_time`),
  KEY `categorycode3_idx` (`categorycode3`),
  KEY `categorycode2_idx` (`categorycode2`),
  KEY `categorycode4_idx` (`categorycode4`),
  KEY `commonprojcategoryid` (`commonprojcategoryid`)
) ENGINE=InnoDB AUTO_INCREMENT=******** DEFAULT CHARSET=utf8;


-- db_cost_data_platform_pro.tb_commonprojcategory_standards_ord_temp definition

CREATE TABLE `db_cost_data_platform_pro`.`tb_commonprojcategory_standards_ord_temp` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `commonprojcategoryid` varchar(40) NOT NULL COMMENT '分类编码',
  `categoryname` varchar(200) DEFAULT NULL COMMENT '分类名称',
  `categorycode1` varchar(50) DEFAULT NULL COMMENT '一级编码',
  `categorycode2` varchar(50) DEFAULT NULL COMMENT '二级编码',
  `categorycode3` varchar(50) DEFAULT NULL COMMENT '三级编码',
  `category_type_code` varchar(50) DEFAULT NULL COMMENT '分类类型编码',
  `category_type_name` varchar(50) DEFAULT NULL COMMENT '分类类型名称',
  `level` bigint(11) DEFAULT NULL COMMENT '等级',
  `projcount` int(11) DEFAULT NULL COMMENT '（历史遗留）',
  `mainparamname` varchar(50) DEFAULT NULL COMMENT '（历史遗留）',
  `unit` varchar(20) DEFAULT NULL COMMENT '（历史遗留）单位',
  `ord` int(11) DEFAULT NULL COMMENT '排序',
  `province_process` varchar(64) DEFAULT NULL COMMENT '（历史遗留）项目地区标识：采用地区拼音进行标识，如：shanghai',
  `global_id` varchar(32) DEFAULT NULL COMMENT '创建人',
  `accountname` varchar(50) DEFAULT NULL COMMENT '创建账号',
  `is_usable` tinyint(1) DEFAULT '0' COMMENT '（历史遗留）是否被使用（1，使用；0，未使用）',
  `qy_code` varchar(100) DEFAULT NULL,
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除(1已删除,0未删除)',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_using` tinyint(1) DEFAULT '1' COMMENT '是否启用（1，启用；0，禁用）',
  `update_global_id` varchar(32) DEFAULT NULL COMMENT '更新人',
  `remark` varchar(400) DEFAULT NULL COMMENT '备注',
  `enterprise_id` bigint(30) DEFAULT NULL COMMENT 'gccs企业id',
  PRIMARY KEY (`id`),
  KEY `categoryname_idx` (`categoryname`),
  KEY `categorycode1_idx` (`categorycode1`),
  KEY `categorycode3_idx` (`categorycode3`),
  KEY `categorycode2_idx` (`categorycode2`),
  KEY `commonprojcategoryid` (`commonprojcategoryid`)
) ENGINE=InnoDB AUTO_INCREMENT=193 DEFAULT CHARSET=utf8 COMMENT='标准统一-工程分类-排序临时表，用于初始化内置分类的ord（勿改勿删）';


-- db_cost_data_platform_pro.tb_commonprojcategory_standards_self definition

CREATE TABLE `db_cost_data_platform_pro`.`tb_commonprojcategory_standards_self` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `commonprojcategoryid` varchar(40) NOT NULL COMMENT '分类编码',
  `categoryname` varchar(200) DEFAULT NULL COMMENT '分类名称',
  `type` int(1) DEFAULT '1',
  `categorycode1` varchar(50) DEFAULT NULL COMMENT '一级编码',
  `categorycode2` varchar(50) DEFAULT NULL COMMENT '二级编码',
  `categorycode3` varchar(50) DEFAULT NULL COMMENT '三级编码',
  `categorycode4` varchar(50) DEFAULT NULL COMMENT '四级编码',
  `category_type_code` varchar(50) DEFAULT NULL COMMENT '分类类型编码',
  `category_type_name` varchar(50) DEFAULT NULL COMMENT '分类类型名称',
  `level` bigint(11) DEFAULT NULL COMMENT '等级',
  `projcount` int(11) DEFAULT NULL COMMENT '（历史遗留）',
  `mainparamname` varchar(50) DEFAULT NULL COMMENT '（历史遗留）',
  `unit` varchar(20) DEFAULT NULL COMMENT '（历史遗留）单位',
  `ord` int(11) DEFAULT NULL COMMENT '排序',
  `province_process` varchar(64) DEFAULT NULL COMMENT '（历史遗留）项目地区标识：采用地区拼音进行标识，如：shanghai',
  `global_id` varchar(32) DEFAULT NULL COMMENT '创建人',
  `accountname` varchar(50) DEFAULT NULL COMMENT '创建账号',
  `is_usable` tinyint(1) DEFAULT '0' COMMENT '（历史遗留）是否被使用（1，使用；0，未使用）',
  `qy_code` varchar(100) DEFAULT NULL,
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除(1已删除,0未删除)',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_using` tinyint(1) DEFAULT '1' COMMENT '是否启用（1，启用；0，禁用）',
  `update_global_id` varchar(32) DEFAULT NULL COMMENT '更新人',
  `remark` varchar(400) DEFAULT NULL COMMENT '备注',
  `origin_id` int(11) DEFAULT NULL COMMENT '关联tb_commonprojcategory_standards表id',
  `self_global_id` varchar(32) DEFAULT NULL COMMENT '当前个人标准归属于哪个用户',
  `enterprise_id` bigint(30) DEFAULT NULL COMMENT 'gccs企业id',
  PRIMARY KEY (`id`),
  KEY `categoryname_idx` (`categoryname`),
  KEY `categorycode1_idx` (`categorycode1`),
  KEY `CUSTOMERCODE` (`qy_code`),
  KEY `categorycode3_idx` (`categorycode3`),
  KEY `categorycode2_idx` (`categorycode2`),
  KEY `categorycode4_idx` (`categorycode4`),
  KEY `commonprojcategoryid` (`commonprojcategoryid`)
) ENGINE=InnoDB AUTO_INCREMENT=1412036 DEFAULT CHARSET=utf8 COMMENT='标准统一-工程分类';


-- db_cost_data_platform_pro.tb_commonprojcategory_standards_used definition

CREATE TABLE `db_cost_data_platform_pro`.`tb_commonprojcategory_standards_used` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `qy_code` varchar(100) DEFAULT NULL,
  `tenantId` varchar(20) DEFAULT NULL,
  `type` tinyint(1) DEFAULT NULL,
  `tenantglobalId` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `CUSTOMERCODE` (`qy_code`)
) ENGINE=InnoDB AUTO_INCREMENT=478 DEFAULT CHARSET=utf8;


-- db_cost_data_platform_pro.zb_lib_user definition

CREATE TABLE `db_cost_data_platform_pro`.`zb_lib_user` (
  `id` bigint(20) NOT NULL COMMENT '用户ID',
  `global_id` varchar(40) NOT NULL COMMENT '广联云globalId',
  `utype` int(11) DEFAULT NULL COMMENT '用户类型，vip体系，云账号体系：',
  `status` int(11) DEFAULT NULL COMMENT '用户状态：1-有效，2-过期',
  `accoun_tname` varchar(50) DEFAULT NULL COMMENT '账号',
  `user_name` varchar(100) DEFAULT NULL COMMENT '用户名',
  `is_main` int(11) DEFAULT NULL COMMENT '是否为主账号',
  `is_manager` int(11) DEFAULT NULL COMMENT '是否为管理员账号',
  `join_main_global_id` varchar(40) DEFAULT NULL COMMENT '关联主账号id',
  `join_manager_global_id` varchar(40) DEFAULT NULL COMMENT '关联管理员账号id',
  `company_id` int(11) DEFAULT NULL COMMENT '企业id',
  `customer_code` varchar(100) DEFAULT NULL,
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `vip_account_name` varchar(50) DEFAULT NULL COMMENT 'vip账号原账号名称',
  `full_name` varchar(255) DEFAULT NULL COMMENT '真实姓名',
  `guide_num` int(11) NOT NULL DEFAULT '0' COMMENT '已进行新手指引次数-最多3次',
  `guide_flag` int(11) NOT NULL DEFAULT '0' COMMENT '是否完成新手指引：1-完成，0-未完成',
  `auth_enterprise_id` bigint(30) DEFAULT NULL COMMENT '广联云企业ID',
  `single_db_id` varchar(32) DEFAULT '0' COMMENT '独立库id,string类型',
  PRIMARY KEY (`id`),
  KEY `global_indx` (`global_id`),
  KEY `zb_lib_user_accoun_tname_IDX` (`accoun_tname`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='指标库用户表';


-- db_cost_data_platform_pro.zb_project_feature_category_view_standards definition

CREATE TABLE `db_cost_data_platform_pro`.`zb_project_feature_category_view_standards` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `trade_id` bigint(20) NOT NULL COMMENT '工程专业id',
  `category_code` varchar(50) NOT NULL COMMENT '工程分类 一级分类编码',
  `type` int(1) DEFAULT '1' COMMENT '工程分类type',
  `feature_id` bigint(20) NOT NULL COMMENT 'zb_project_feature_standards特征id',
  `ord_category` int(11) NOT NULL COMMENT '分类视图排序',
  `customer_code` varchar(100) DEFAULT NULL,
  `trade_name` varchar(100) DEFAULT NULL COMMENT '专业名称',
  `origin_id` bigint(30) DEFAULT NULL,
  `enterprise_id` bigint(30) DEFAULT NULL COMMENT 'gccs企业id',
  `modify_time_sjzt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间-造价中台用',
  PRIMARY KEY (`id`),
  KEY `CUSTOMERCODE` (`customer_code`),
  KEY `zb_project_feature_category_view_standards_modify_time_sjzt_IDX` (`modify_time_sjzt`),
  KEY `zb_project_feature_category_view_standards_trade_id_IDX` (`trade_id`)
) ENGINE=InnoDB AUTO_INCREMENT=174869117 DEFAULT CHARSET=utf8 COMMENT='工程特征表-分类视图暂存表';


-- db_cost_data_platform_pro.zb_project_feature_category_view_standards_self definition

CREATE TABLE `db_cost_data_platform_pro`.`zb_project_feature_category_view_standards_self` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `trade_id` bigint(20) NOT NULL COMMENT '工程专业id',
  `category_code` varchar(50) NOT NULL COMMENT '工程分类 一级分类编码',
  `type` int(1) DEFAULT '1' COMMENT '工程分类type',
  `feature_id` bigint(20) NOT NULL COMMENT 'zb_project_feature_standards特征id',
  `ord_category` int(11) NOT NULL COMMENT '分类视图排序',
  `customer_code` varchar(100) DEFAULT NULL,
  `trade_name` varchar(100) DEFAULT NULL COMMENT '专业名称',
  `enterprise_id` bigint(30) DEFAULT NULL COMMENT 'gccs企业id',
  `origin_id` bigint(20) DEFAULT NULL,
  `self_global_id` bigint(20) DEFAULT NULL COMMENT '当前个人标准归属于哪个用户',
  PRIMARY KEY (`id`),
  KEY `zb_project_feature_category_view_standards_self_trade_id_IDX` (`trade_id`),
  KEY `CUSTOMERCODE` (`customer_code`),
  KEY `SELF_GLOBAL_ID_IDX` (`self_global_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4775726161180520735 DEFAULT CHARSET=utf8;


-- db_cost_data_platform_pro.zb_project_feature_standards definition

CREATE TABLE `db_cost_data_platform_pro`.`zb_project_feature_standards` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `expression_id` bigint(20) DEFAULT NULL COMMENT '工程特征名称类型表主键id zb_standards_expression',
  `feature_level` varchar(50) DEFAULT NULL COMMENT '（历史遗留）特征层级与类别对应关系：项目信息【1】、单项信息【2】、项目信息，单项信息【1,2】',
  `is_sync` tinyint(1) DEFAULT '0' COMMENT '（历史遗留）是否同步：1同步；0不同步；特征层级为 项目信息，单项信息时，是否同步列存在按钮；其他状态，该列为‘-’',
  `type` tinyint(1) DEFAULT '1' COMMENT '类型  1 数字新成本工程分类标准；2《建设工程分类标准》GB/T 50841-2013；3 工程造价指标分类及编制指南(中价协2021)',
  `project_type` text COMMENT '工程分类:json串:{"projectType":"[["001","001001","001001002"],["001","001001","001001003"],["001","001001","001001004"],["001","001001","001001005"],["001","001002","001002001"],["001","001002","001002002"],["001","001002","001002003"],["001","001003","001003002"],["001","001003","001003003"],["001","001003","001003004"],["001","001004","001004001"],["001","001004","001004002"],["001","001004","001004003"],["001","001004","001004004"],["001","001005"],["001","001006","001006001"],["001","001006","001006002"],["001","001006","001006003"],["001","001006","001006004"],["001","001006","001006005"],["001","001006","001006006"],["001","001006","001006007"],["001","001007"],["002","002001","002001001"],["002","002001","002001002"],["002","002001","002001003"],["002","002002"],["002","002003"],["002","002004"],["002","002005"],["002","002006","002006001"],["002","002006","002006002"],["002","002006","002006003"],["002","002007"]]"}',
  `is_using` tinyint(1) DEFAULT '0' COMMENT '是否启用：1启用；0未启用；（系统：默认启用；用户新建：新建工程特征【是否启用】状态，启用后的工程特征，才可在【新增工程特征】时搜索）',
  `is_default` tinyint(1) DEFAULT '0' COMMENT '（历史遗留）是否默认：：开启【1】、关闭【0】',
  `is_required` tinyint(1) DEFAULT '0' COMMENT '是否必填：1 必填；0 不必填',
  `remark` varchar(300) DEFAULT NULL COMMENT '注释，最大300个字符',
  `is_from_system` tinyint(1) DEFAULT '0' COMMENT '（历史遗留）是否由系统工程特征复制而来：1 是； 0 否',
  `zb_project_feature_id` bigint(20) DEFAULT NULL COMMENT '（历史遗留）源工程特征ID：对应zb_project_feature的主键id',
  `customer_code` varchar(100) DEFAULT NULL,
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：1已删除；0未删除',
  `create_global_id` bigint(20) DEFAULT NULL COMMENT '创建人ID（广联云用户体系）：-100为系统内置',
  `update_global_id` bigint(20) DEFAULT NULL COMMENT '更新人ID（广联达用户体系）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `product_pro_type` int(11) DEFAULT NULL COMMENT '（历史遗留）对应dictionary表id，专业工程包含“-、建筑装饰工程、安装工程、园林绿化工程、仿古建筑工程、市政工程、矿山工程、构筑物工程、城市轨道交通工程、爆破工程、其他”',
  `is_expression` tinyint(1) DEFAULT '0' COMMENT '是否计算口径：1是；0否',
  `ord_trade` int(11) DEFAULT NULL COMMENT '专业视图排序',
  `trade_id` bigint(20) NOT NULL COMMENT '工程专业id',
  `is_search_condition` tinyint(1) DEFAULT '0' COMMENT '是否为筛选项，0：不是，1：是',
  `trade_name` varchar(100) DEFAULT NULL COMMENT '专业名称',
  `enterprise_id` bigint(30) DEFAULT NULL COMMENT 'gccs企业id',
  `modify_time_sjzt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间-造价中台用',
  `name` varchar(200) DEFAULT NULL COMMENT '工程特征名称',
  `type_code` varchar(50) DEFAULT NULL COMMENT '数据类型:值为dictionary表中type_code字段等于data_type所对应的code字段的值',
  `option` varchar(1000) DEFAULT NULL COMMENT '工程特征对应的选项，与数据类型连用,数据格式为json字符串：{id:xxx，name：xxx,sort: xxx}',
  `unit` varchar(10) DEFAULT NULL COMMENT '单位',
  `expression_code` varchar(50) DEFAULT NULL COMMENT '口径编码(可作为口径key,同时可固化编码进行数据处理)',
  `is_expression_default` tinyint(1) DEFAULT '0' COMMENT '是否计算口径内置',
  PRIMARY KEY (`id`),
  KEY `zb_project_feature_standards_expression_id_IDX` (`expression_id`),
  KEY `CUSTOMERCODE` (`customer_code`),
  KEY `TRADE_ID_INDEX` (`trade_id`),
  KEY `zb_project_feature_standards_modify_time_sjzt_IDX` (`modify_time_sjzt`),
  KEY `idx_multi_cti` (`customer_code`,`type`,`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='工程特征表';


-- db_cost_data_platform_pro.zb_project_feature_standards_self definition

CREATE TABLE `db_cost_data_platform_pro`.`zb_project_feature_standards_self` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `expression_id` bigint(20) DEFAULT NULL COMMENT '工程特征名称类型表主键id zb_standards_expression',
  `feature_level` varchar(50) DEFAULT NULL COMMENT '（历史遗留）特征层级与类别对应关系：项目信息【1】、单项信息【2】、项目信息，单项信息【1,2】',
  `is_sync` tinyint(1) DEFAULT '0' COMMENT '（历史遗留）是否同步：1同步；0不同步；特征层级为 项目信息，单项信息时，是否同步列存在按钮；其他状态，该列为‘-’',
  `type` int(1) DEFAULT '1' COMMENT '工程分类type',
  `project_type` text COMMENT '工程分类:json串:{"projectType":"[["001","001001","001001002"],["001","001001","001001003"],["001","001001","001001004"],["001","001001","001001005"],["001","001002","001002001"],["001","001002","001002002"],["001","001002","001002003"],["001","001003","001003002"],["001","001003","001003003"],["001","001003","001003004"],["001","001004","001004001"],["001","001004","001004002"],["001","001004","001004003"],["001","001004","001004004"],["001","001005"],["001","001006","001006001"],["001","001006","001006002"],["001","001006","001006003"],["001","001006","001006004"],["001","001006","001006005"],["001","001006","001006006"],["001","001006","001006007"],["001","001007"],["002","002001","002001001"],["002","002001","002001002"],["002","002001","002001003"],["002","002002"],["002","002003"],["002","002004"],["002","002005"],["002","002006","002006001"],["002","002006","002006002"],["002","002006","002006003"],["002","002007"]]"}',
  `is_using` tinyint(1) DEFAULT '0' COMMENT '是否启用：1启用；0未启用；（系统：默认启用；用户新建：新建工程特征【是否启用】状态，启用后的工程特征，才可在【新增工程特征】时搜索）',
  `is_default` tinyint(1) DEFAULT '0' COMMENT '（历史遗留）是否默认：：开启【1】、关闭【0】',
  `is_required` tinyint(1) DEFAULT '0' COMMENT '是否必填：1 必填；0 不必填',
  `remark` varchar(300) DEFAULT NULL COMMENT '注释，最大300个字符',
  `is_from_system` tinyint(1) DEFAULT '0' COMMENT '（历史遗留）是否由系统工程特征复制而来：1 是； 0 否',
  `zb_project_feature_id` bigint(20) DEFAULT NULL COMMENT '（历史遗留）源工程特征ID：对应zb_project_feature的主键id',
  `customer_code` varchar(100) DEFAULT NULL,
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：1已删除；0未删除',
  `create_global_id` bigint(20) DEFAULT NULL COMMENT '创建人ID（广联云用户体系）：-100为系统内置',
  `update_global_id` bigint(20) DEFAULT NULL COMMENT '更新人ID（广联达用户体系）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `product_pro_type` int(11) DEFAULT NULL COMMENT '（历史遗留）对应dictionary表id，专业工程包含“-、建筑装饰工程、安装工程、园林绿化工程、仿古建筑工程、市政工程、矿山工程、构筑物工程、城市轨道交通工程、爆破工程、其他”',
  `is_expression` tinyint(1) DEFAULT '0' COMMENT '是否计算口径：1是；0否',
  `ord_trade` int(11) DEFAULT NULL COMMENT '专业视图排序',
  `trade_id` bigint(20) NOT NULL COMMENT '工程专业id',
  `is_search_condition` tinyint(1) DEFAULT '0' COMMENT '是否为筛选项，0：不是，1：是',
  `trade_name` varchar(100) DEFAULT NULL COMMENT '专业名称',
  `origin_id` bigint(30) DEFAULT NULL,
  `self_global_id` bigint(20) DEFAULT NULL COMMENT '当前个人标准归属于哪个用户',
  `enterprise_id` bigint(30) DEFAULT NULL COMMENT 'gccs企业id',
  `name` varchar(200) DEFAULT NULL COMMENT '工程特征名称',
  `type_code` varchar(50) DEFAULT NULL COMMENT '数据类型:值为dictionary表中type_code字段等于data_type所对应的code字段的值',
  `option` varchar(1000) DEFAULT NULL COMMENT '工程特征对应的选项，与数据类型连用,数据格式为json字符串：{id:xxx，name：xxx,sort: xxx}',
  `unit` varchar(10) DEFAULT NULL COMMENT '单位',
  `expression_code` varchar(50) DEFAULT NULL COMMENT '口径编码(可作为口径key,同时可固化编码进行数据处理)',
  `is_expression_default` tinyint(1) DEFAULT '0' COMMENT '是否计算口径内置',
  PRIMARY KEY (`id`),
  KEY `zb_project_feature_standards_self_trade_id_IDX` (`trade_id`),
  KEY `CUSTOMERCODE` (`customer_code`),
  KEY `SELF_GLOBAL_ID_IDX` (`self_global_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='工程特征表';


-- db_cost_data_platform_pro.zb_standards_build_category definition

CREATE TABLE `db_cost_data_platform_pro`.`zb_standards_build_category` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `standard_id` bigint(20) DEFAULT NULL COMMENT '标准id',
  `category_code` varchar(12) DEFAULT NULL COMMENT '工程分类编码',
  `enterprise_id` bigint(20) DEFAULT NULL COMMENT '企业id',
  `ord` int(11) DEFAULT NULL COMMENT '排序',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `zb_standards_build_category_standard_id_IDX` (`standard_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- db_cost_data_platform_pro.zb_standards_build_category_self definition

CREATE TABLE `db_cost_data_platform_pro`.`zb_standards_build_category_self` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `standard_id` bigint(20) DEFAULT NULL COMMENT '标准id',
  `category_code` varchar(12) DEFAULT NULL COMMENT '工程分类编码',
  `global_id` bigint(20) DEFAULT NULL COMMENT '企业id',
  `ord` int(11) DEFAULT NULL COMMENT '排序',
  `origin_id` bigint(30) DEFAULT NULL COMMENT '原id',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `zb_standards_build_category_standard_id_IDX` (`standard_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- db_cost_data_platform_pro.zb_standards_build_position definition

CREATE TABLE `db_cost_data_platform_pro`.`zb_standards_build_position` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `build_category_id` bigint(20) NOT NULL COMMENT ' 建造标准业态表id',
  `standard_id` bigint(20) NOT NULL COMMENT '标准id',
  `name` varchar(100) DEFAULT NULL COMMENT ' 名称',
  `ord` int(11) DEFAULT NULL COMMENT '排序',
  `type` tinyint(4) DEFAULT NULL COMMENT ' 0:定位，1工程分类',
  `category_code` varchar(12) DEFAULT NULL COMMENT '工程分类编码',
  `enterprise_id` bigint(20) DEFAULT NULL COMMENT '企业id',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `zb_standards_build_position_build_category_id_IDX` (`build_category_id`),
  KEY `zb_standards_build_position_standard_id_IDX` (`standard_id`,`name`,`category_code`)
) ENGINE=InnoDB AUTO_INCREMENT=4775375187527860329 DEFAULT CHARSET=utf8 COMMENT='企业标准数据-建造标准定位';


-- db_cost_data_platform_pro.zb_standards_build_position_detail definition

CREATE TABLE `db_cost_data_platform_pro`.`zb_standards_build_position_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `position_id` bigint(20) NOT NULL COMMENT '定位id',
  `standard_detail_desc_id` bigint(20) DEFAULT NULL COMMENT ' 标准细则-标准说明id ',
  `standard_detail_id` bigint(20) NOT NULL COMMENT '标准细则id',
  `standard_id` bigint(20) DEFAULT NULL COMMENT '标准id',
  `value` varchar(500) DEFAULT NULL COMMENT '标准说明的值',
  `enterprise_id` bigint(20) DEFAULT NULL COMMENT '企业id',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `zb_standards_build_position_detail_position_id_IDX` (`position_id`,`standard_detail_desc_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4774925158784699266 DEFAULT CHARSET=utf8 COMMENT='企业标准数据-建造标准定位-细则';


-- db_cost_data_platform_pro.zb_standards_build_position_detail_self definition

CREATE TABLE `db_cost_data_platform_pro`.`zb_standards_build_position_detail_self` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `position_id` bigint(20) NOT NULL COMMENT '定位id',
  `standard_detail_desc_id` bigint(20) DEFAULT NULL COMMENT ' 标准细则-标准说明id ',
  `standard_detail_id` bigint(20) NOT NULL COMMENT '标准细则id',
  `standard_id` bigint(20) DEFAULT NULL COMMENT '标准id',
  `value` varchar(500) DEFAULT NULL COMMENT '标准说明的值',
  `global_id` bigint(20) DEFAULT NULL COMMENT '企业id',
  `origin_id` bigint(30) DEFAULT NULL COMMENT '原id',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `zb_standards_build_position_detail_position_id_IDX` (`position_id`,`standard_detail_desc_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4774925158784699266 DEFAULT CHARSET=utf8 COMMENT='企业标准数据-建造标准定位-细则';


-- db_cost_data_platform_pro.zb_standards_build_position_self definition

CREATE TABLE `db_cost_data_platform_pro`.`zb_standards_build_position_self` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `build_category_id` bigint(20) NOT NULL COMMENT ' 建造标准业态表id',
  `standard_id` bigint(20) NOT NULL COMMENT '标准id',
  `name` varchar(100) DEFAULT NULL COMMENT ' 名称',
  `ord` int(11) DEFAULT NULL COMMENT '排序',
  `type` tinyint(4) DEFAULT NULL COMMENT ' 0:定位，1工程分类',
  `category_code` varchar(12) DEFAULT NULL COMMENT '工程分类编码',
  `global_id` bigint(20) DEFAULT NULL COMMENT '企业id',
  `origin_id` bigint(30) DEFAULT NULL COMMENT '原id',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `zb_standards_build_position_build_category_id_IDX` (`build_category_id`),
  KEY `zb_standards_build_position_standard_id_IDX` (`standard_id`,`name`,`category_code`)
) ENGINE=InnoDB AUTO_INCREMENT=4775375187527860329 DEFAULT CHARSET=utf8 COMMENT='企业标准数据-建造标准定位-暂存';


-- db_cost_data_platform_pro.zb_standards_build_standard definition

CREATE TABLE `db_cost_data_platform_pro`.`zb_standards_build_standard` (
  `id` bigint(30) NOT NULL COMMENT '主键',
  `name` varchar(60) DEFAULT NULL COMMENT '标准名称',
  `category_code` varchar(60) DEFAULT NULL COMMENT '工程分类编码',
  `category_name` varchar(200) DEFAULT NULL COMMENT '工程分类名称',
  `customer_code` varchar(100) DEFAULT NULL,
  `global_id` bigint(30) DEFAULT NULL COMMENT '创建人',
  `is_example` tinyint(1) NOT NULL DEFAULT '1' COMMENT '标准类型   0 用户自建 1 内置标准',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除  0 未删除、1 删除',
  `category_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '业态是否被删除  0 未删除 1 删除',
  `is_used` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否被使用  0 没被使用  1 使用',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_enabled` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否启用，0：未启用，1：已启用',
  `version` int(11) DEFAULT '0' COMMENT '版本号，每更新一次标准或标准细则加一',
  `enterprise_id` bigint(30) DEFAULT NULL COMMENT 'gccs企业id',
  `src_standard_id` bigint(20) DEFAULT NULL COMMENT '原建造标准id',
  PRIMARY KEY (`id`),
  KEY `CUSTOMERCODE_INDEX` (`customer_code`),
  KEY `CATEGORYCODE_INDEX` (`category_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='企业标准数据-建造标准表';


-- db_cost_data_platform_pro.zb_standards_build_standard_detail definition

CREATE TABLE `db_cost_data_platform_pro`.`zb_standards_build_standard_detail` (
  `id` bigint(30) NOT NULL COMMENT '主键',
  `standard_id` bigint(30) DEFAULT NULL COMMENT '标准id',
  `level_code` varchar(60) DEFAULT NULL COMMENT '层级编码',
  `name` varchar(500) DEFAULT NULL COMMENT '建造标准细则名称',
  `desc` text COMMENT '描述实例',
  `level` int(3) DEFAULT NULL COMMENT '层级',
  `remark` varchar(400) DEFAULT NULL COMMENT '备注',
  `ord` int(3) NOT NULL DEFAULT '1' COMMENT '顺序',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `trade_id` bigint(20) DEFAULT NULL COMMENT '专业工程id，对应zb_standards_trade.id',
  `trade_name` varchar(100) DEFAULT NULL COMMENT '专业名称',
  `enterprise_id` bigint(30) DEFAULT NULL COMMENT 'gccs企业id',
  PRIMARY KEY (`id`),
  KEY `STANDARDID_INDEX` (`standard_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='企业数据标准-建造标准细则表';


-- db_cost_data_platform_pro.zb_standards_build_standard_detail_desc definition

CREATE TABLE `db_cost_data_platform_pro`.`zb_standards_build_standard_detail_desc` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `name` varchar(100) DEFAULT NULL COMMENT '标准名称',
  `detail_desc` varchar(2000) DEFAULT NULL COMMENT '标准说明',
  `standard_id` bigint(20) NOT NULL COMMENT '建造标准id',
  `standard_detail_id` bigint(20) NOT NULL COMMENT '建造标准细则id',
  `code` varchar(50) DEFAULT NULL COMMENT '编码',
  `type_code` varchar(10) NOT NULL COMMENT '数据类型： text文本类，number数值类，date日期类，select单选类，selects多选类',
  `select_list` varchar(500) DEFAULT NULL COMMENT '枚举值',
  `trade_id` bigint(20) DEFAULT NULL COMMENT '专业id',
  `trade_name` varchar(100) DEFAULT NULL COMMENT '专业名称',
  `ord` int(11) DEFAULT NULL COMMENT '排序',
  `enterprise_id` bigint(20) DEFAULT NULL COMMENT '用户id',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `zb_standards_build_standard_detail_desc_standard_id_IDX` (`standard_id`,`standard_detail_id`),
  KEY `zb_standards_build_standard_detail_desc_standard_detail_id_IDX` (`standard_detail_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='(企业标准数据-建造标准-标准说明表)';


-- db_cost_data_platform_pro.zb_standards_build_standard_detail_desc_self definition

CREATE TABLE `db_cost_data_platform_pro`.`zb_standards_build_standard_detail_desc_self` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `name` varchar(100) DEFAULT NULL COMMENT '标准名称',
  `detail_desc` varchar(500) DEFAULT NULL COMMENT '标准说明',
  `standard_id` bigint(20) NOT NULL COMMENT '建造标准id',
  `standard_detail_id` bigint(20) NOT NULL COMMENT '建造标准细则id',
  `code` varchar(50) DEFAULT NULL COMMENT '编码',
  `type_code` varchar(10) DEFAULT NULL COMMENT '数据类型： text文本类，number数值类，date日期类，select单选类，selects多选类',
  `select_list` varchar(500) DEFAULT NULL COMMENT '枚举值',
  `trade_id` bigint(20) DEFAULT NULL COMMENT '专业id',
  `trade_name` varchar(100) DEFAULT NULL COMMENT '专业名称',
  `ord` int(11) DEFAULT NULL COMMENT '排序',
  `global_id` bigint(20) DEFAULT NULL COMMENT '用户id',
  `origin_id` bigint(30) DEFAULT NULL COMMENT '原id',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `detail_desc_self_standard_id_IDX` (`standard_id`,`standard_detail_id`),
  KEY `detail_desc_self_standard_detail_id_IDX` (`standard_detail_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='(企业标准数据-建造标准-标准说明表-暂存表)';


-- db_cost_data_platform_pro.zb_standards_build_standard_detail_self definition

CREATE TABLE `db_cost_data_platform_pro`.`zb_standards_build_standard_detail_self` (
  `id` bigint(30) NOT NULL COMMENT '主键',
  `standard_id` bigint(30) DEFAULT NULL COMMENT '标准id',
  `level_code` varchar(60) DEFAULT NULL COMMENT '层级编码',
  `name` varchar(500) DEFAULT NULL COMMENT '建造标准细则名称',
  `desc` text COMMENT '描述实例',
  `level` int(3) DEFAULT NULL COMMENT '层级',
  `remark` varchar(400) DEFAULT NULL COMMENT '备注',
  `ord` int(3) NOT NULL DEFAULT '1' COMMENT '顺序',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `trade_id` bigint(20) DEFAULT NULL COMMENT '专业工程id，对应zb_standards_trade.id',
  `trade_name` varchar(100) DEFAULT NULL COMMENT '专业名称',
  `global_id` varchar(32) DEFAULT NULL COMMENT '数据所属用户',
  `origin_id` bigint(30) DEFAULT NULL,
  `enterprise_id` bigint(30) DEFAULT NULL COMMENT 'gccs企业id',
  PRIMARY KEY (`id`),
  KEY `STANDARD_ID_IDX` (`standard_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='企业数据标准-建造标准细则暂存表';


-- db_cost_data_platform_pro.zb_standards_build_standard_self definition

CREATE TABLE `db_cost_data_platform_pro`.`zb_standards_build_standard_self` (
  `id` bigint(30) NOT NULL COMMENT '主键',
  `name` varchar(60) DEFAULT NULL COMMENT '标准名称',
  `category_code` varchar(60) DEFAULT NULL COMMENT '工程分类编码',
  `category_name` varchar(200) DEFAULT NULL COMMENT '工程分类名称',
  `customer_code` varchar(100) DEFAULT NULL,
  `global_id` bigint(30) DEFAULT NULL COMMENT '创建人',
  `is_example` tinyint(1) NOT NULL DEFAULT '1' COMMENT '标准类型   0 用户自建 1 内置标准',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除  0 未删除、1 删除',
  `category_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '业态是否被删除  0 未删除 1 删除',
  `is_used` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否被使用  0 没被使用  1 使用',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_enabled` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否启用，0：未启用，1：已启用',
  `origin_id` bigint(30) DEFAULT NULL COMMENT '对应已发布标准的id',
  `temp_global_id` varchar(20) NOT NULL COMMENT '暂存人globalId',
  `is_updated` tinyint(4) DEFAULT NULL COMMENT '是否被编辑过（包括标准本身的编辑以及对应细则的增删改',
  `enterprise_id` bigint(30) DEFAULT NULL COMMENT 'gccs企业id',
  `src_standard_id` bigint(20) DEFAULT NULL COMMENT '原建造标准id',
  PRIMARY KEY (`id`),
  KEY `TEMP_GLOBAL_ID_IDX` (`temp_global_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='企业标准数据-建造标准暂存表';


-- db_cost_data_platform_pro.zb_standards_expression definition

CREATE TABLE `db_cost_data_platform_pro`.`zb_standards_expression` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `expression_code` varchar(50) DEFAULT NULL COMMENT '（历史遗留）口径编码(可作为口径key,同时可固化编码进行数据处理),采用分布式生成器生成',
  `data_type` tinyint(4) DEFAULT NULL COMMENT '（历史遗留）计算口径值得类型（0:浮点，1:整数）',
  `rule` varchar(200) DEFAULT NULL COMMENT '（历史遗留）规则设计，预留字段',
  `status` int(1) DEFAULT NULL COMMENT '（历史遗留）',
  `scope` int(2) DEFAULT NULL COMMENT '（历史遗留）口径范围(1,个人，2，企业，0.系统)',
  `qy_code` varchar(255) DEFAULT NULL COMMENT '企业编号',
  `is_usable` tinyint(1) DEFAULT '0' COMMENT '（历史遗留）是否使用',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：1已删除；0未删除',
  `name` varchar(200) DEFAULT NULL COMMENT '工程特征名称',
  `type_code` varchar(50) DEFAULT NULL COMMENT '数据类型:值为dictionary表中type_code字段等于data_type所对应的code字段的值',
  `type` int(1) DEFAULT '1' COMMENT '工程分类type',
  `option` varchar(1000) DEFAULT NULL COMMENT '工程特征对应的选项，与数据类型连用,数据格式为json字符串：{id:xxx，name：xxx,sort: xxx}',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `create_global_id` bigint(20) DEFAULT NULL COMMENT '创建用户ID',
  `update_global_id` bigint(20) DEFAULT NULL COMMENT '更新用户ID',
  `is_expression` tinyint(1) DEFAULT '0' COMMENT '是否计算口径：1是；0否（数值型，支持设置是否计算口径）',
  `unit` varchar(10) DEFAULT NULL COMMENT '单位',
  `expression_is_from_system` tinyint(1) DEFAULT '0' COMMENT '计算口径是否系统内置：1 是； 0 否',
  `expression_is_using` tinyint(1) DEFAULT '0' COMMENT '计算口径是否启用:1启用；0未启用；',
  `expression_ord` int(11) DEFAULT NULL COMMENT '计算口径排序',
  `expression_create_global_id` bigint(20) DEFAULT NULL COMMENT '计算口径创建用户ID',
  `expression_create_time` timestamp NULL DEFAULT NULL COMMENT '计算口径创建时间',
  `expression_remark` varchar(300) DEFAULT NULL COMMENT '计算口径备注',
  `enterprise_id` bigint(30) DEFAULT NULL COMMENT 'gccs企业id',
  `expression_code_bak` varchar(50) DEFAULT NULL,
  `old_name` varchar(200) DEFAULT NULL COMMENT '旧的工程特征名称',
  PRIMARY KEY (`id`),
  KEY `zb_standards_expression_name_IDX` (`name`),
  KEY `zb_standards_expression_create_time_IDX` (`create_time`),
  KEY `QYCODE` (`qy_code`,`expression_ord`),
  KEY `zb_standards_expression_expression_code_IDX` (`expression_code`)
) ENGINE=InnoDB AUTO_INCREMENT=4775727799861299999 DEFAULT CHARSET=utf8 COMMENT='工程特征名称类型及计算口径表';


-- db_cost_data_platform_pro.zb_standards_expression_self definition

CREATE TABLE `db_cost_data_platform_pro`.`zb_standards_expression_self` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `expression_code` varchar(50) DEFAULT NULL COMMENT '（历史遗留）口径编码(可作为口径key,同时可固化编码进行数据处理),采用分布式生成器生成',
  `data_type` tinyint(4) DEFAULT NULL COMMENT '（历史遗留）计算口径值得类型（0:浮点，1:整数）',
  `rule` varchar(200) DEFAULT NULL COMMENT '（历史遗留）规则设计，预留字段',
  `status` int(1) DEFAULT NULL COMMENT '（历史遗留）',
  `scope` int(2) DEFAULT NULL COMMENT '（历史遗留）口径范围(1,个人，2，企业，0.系统)',
  `qy_code` varchar(255) DEFAULT NULL COMMENT '企业编号',
  `is_usable` tinyint(1) DEFAULT '0' COMMENT '（历史遗留）是否使用',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：1已删除；0未删除',
  `name` varchar(200) DEFAULT NULL COMMENT '工程特征名称',
  `type_code` varchar(50) DEFAULT NULL COMMENT '数据类型:值为dictionary表中type_code字段等于data_type所对应的code字段的值',
  `type` int(1) DEFAULT '1' COMMENT '工程分类type',
  `option` varchar(1000) DEFAULT NULL COMMENT '工程特征对应的选项，与数据类型连用,数据格式为json字符串：{id:xxx，name：xxx,sort: xxx}',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `create_global_id` bigint(20) DEFAULT NULL COMMENT '创建用户ID',
  `update_global_id` bigint(20) DEFAULT NULL COMMENT '更新用户ID',
  `is_expression` tinyint(1) DEFAULT '0' COMMENT '是否计算口径：1是；0否（数值型，支持设置是否计算口径）',
  `unit` varchar(10) DEFAULT NULL COMMENT '单位',
  `expression_is_from_system` tinyint(1) DEFAULT '0' COMMENT '计算口径是否系统内置：1 是； 0 否',
  `expression_is_using` tinyint(1) DEFAULT '0' COMMENT '计算口径是否启用:1启用；0未启用；',
  `expression_ord` int(11) DEFAULT NULL COMMENT '计算口径排序',
  `expression_create_global_id` bigint(20) DEFAULT NULL COMMENT '计算口径创建用户ID',
  `expression_create_time` timestamp NULL DEFAULT NULL COMMENT '计算口径创建时间',
  `expression_remark` varchar(300) DEFAULT NULL COMMENT '计算口径备注',
  `origin_id` bigint(30) DEFAULT NULL,
  `self_global_id` bigint(20) DEFAULT NULL COMMENT '当前个人标准归属于哪个用户',
  `enterprise_id` bigint(30) DEFAULT NULL COMMENT 'gccs企业id',
  `expression_code_bak` varchar(50) DEFAULT NULL,
  `old_name` varchar(200) DEFAULT NULL COMMENT '旧的工程特征名称',
  PRIMARY KEY (`id`),
  KEY `EXPRESSION_CODE` (`expression_code`),
  KEY `EXPRESSION_CODE_BAK_IDX` (`expression_code_bak`),
  KEY `SELF_GLOBAL_ID_IDX` (`self_global_id`),
  KEY `QYCODE` (`qy_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='工程特征名称类型及计算口径暂存表';


-- db_cost_data_platform_pro.zb_standards_main_quantity definition

CREATE TABLE `db_cost_data_platform_pro`.`zb_standards_main_quantity` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `trade_id` bigint(20) NOT NULL COMMENT '专业id',
  `description` varchar(100) NOT NULL COMMENT '科目名称',
  `unit` varchar(100) DEFAULT NULL COMMENT '科目单位',
  `remark` varchar(500) DEFAULT NULL COMMENT '科目备注',
  `type` tinyint(4) DEFAULT NULL COMMENT '科目类型  0 内置科目  1 新增科目',
  `customer_code` varchar(100) NOT NULL COMMENT '企业编码',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否已被删除，0：未删除，1：已删除',
  `ord` int(11) NOT NULL COMMENT '排序字段',
  `create_global_id` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_global_id` bigint(20) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `trade_code` varchar(10) DEFAULT NULL COMMENT '专业编码',
  `trade_name` varchar(100) NOT NULL COMMENT '专业名称',
  `enterprise_id` bigint(30) DEFAULT NULL COMMENT 'gccs企业id',
  PRIMARY KEY (`id`),
  KEY `zb_standards_main_quantity_trade_code_IDX` (`trade_code`),
  KEY `CUSTOMERCODE` (`customer_code`),
  KEY `zb_standards_main_quantity_trade_id_IDX` (`trade_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='企业数据标准-主要量指标';


-- db_cost_data_platform_pro.zb_standards_main_quantity_self definition

CREATE TABLE `db_cost_data_platform_pro`.`zb_standards_main_quantity_self` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `trade_id` bigint(20) NOT NULL COMMENT '专业id',
  `description` varchar(100) NOT NULL COMMENT '科目名称',
  `unit` varchar(100) DEFAULT NULL COMMENT '科目单位',
  `remark` varchar(500) DEFAULT NULL COMMENT '科目备注',
  `type` tinyint(4) DEFAULT NULL COMMENT '科目类型  0 内置科目  1 新增科目',
  `customer_code` varchar(100) NOT NULL COMMENT '企业编码',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否已被删除，0：未删除，1：已删除',
  `ord` int(11) NOT NULL COMMENT '排序字段',
  `create_global_id` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_global_id` bigint(20) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `trade_code` varchar(10) DEFAULT NULL COMMENT '专业编码',
  `trade_name` varchar(100) NOT NULL COMMENT '专业名称',
  `global_id` varchar(32) DEFAULT NULL,
  `origin_id` varchar(20) DEFAULT NULL,
  `enterprise_id` bigint(30) DEFAULT NULL COMMENT 'gccs企业id',
  PRIMARY KEY (`id`),
  KEY `zb_standards_main_quantity_trade_code_IDX` (`trade_code`),
  KEY `CUSTOMERCODE` (`customer_code`),
  KEY `zb_standards_main_quantity_trade_id_IDX` (`trade_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='企业数据标准-主要量指标';


-- db_cost_data_platform_pro.zb_standards_project_info definition

CREATE TABLE `db_cost_data_platform_pro`.`zb_standards_project_info` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `name` varchar(100) DEFAULT NULL COMMENT '项目信息名',
  `type_code` varchar(100) DEFAULT NULL COMMENT '数据类型，对应dictionary表中type_name为数据类型的记录，text文本类，number数值类，date日期类，select单选类，selects多选类',
  `unit` varchar(10) DEFAULT NULL COMMENT '单位',
  `select_list` varchar(1000) DEFAULT NULL COMMENT '枚举值',
  `is_using` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否已启用，0：未启用，1：已启用',
  `is_required` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否必填，0：非必填，1：必填',
  `is_expression` tinyint(1) DEFAULT '0' COMMENT '是否计算口径',
  `creator_id` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updater_id` bigint(20) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(300) DEFAULT NULL COMMENT '备注',
  `customer_code` varchar(100) NOT NULL COMMENT '企业编码',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否已被删除，0：未删除，1：已删除',
  `ord` int(11) NOT NULL COMMENT '排序字段',
  `standard_data_type` tinyint(4) NOT NULL COMMENT '标准数据的类型，1：项目信息，2：合同信息',
  `enterprise_id` bigint(30) DEFAULT NULL COMMENT 'gccs企业id',
  `modify_time_sjzt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间-造价中台用',
  PRIMARY KEY (`id`),
  KEY `idx_customercode_standarddatatype` (`customer_code`,`standard_data_type`),
  KEY `zb_standards_project_info_modify_time_sjzt_IDX` (`modify_time_sjzt`),
  KEY `idx_customercode_isdeleted_standarddatatype` (`customer_code`,`is_deleted`,`standard_data_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='企业数据标准-项目信息、合同信息表';


-- db_cost_data_platform_pro.zb_standards_project_info_self definition

CREATE TABLE `db_cost_data_platform_pro`.`zb_standards_project_info_self` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `name` varchar(100) DEFAULT NULL COMMENT '项目信息名',
  `type_code` varchar(100) DEFAULT NULL COMMENT '数据类型，对应dictionary表中type_name为数据类型的记录，text文本类，number数值类，date日期类，select单选类，selects多选类',
  `unit` varchar(10) DEFAULT NULL COMMENT '单位',
  `select_list` varchar(1000) DEFAULT NULL COMMENT '枚举值',
  `is_using` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否已启用，0：未启用，1：已启用',
  `is_required` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否必填，0：非必填，1：必填',
  `is_expression` tinyint(1) DEFAULT '0' COMMENT '是否计算口径',
  `creator_id` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `updater_id` bigint(20) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(300) DEFAULT NULL COMMENT '备注',
  `customer_code` varchar(100) NOT NULL COMMENT '企业编码',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否已被删除，0：未删除，1：已删除',
  `ord` int(11) NOT NULL COMMENT '排序字段',
  `standard_data_type` tinyint(4) NOT NULL COMMENT '标准数据的类型，1：项目信息，2：合同信息',
  `global_id` varchar(32) NOT NULL COMMENT '数据所属用户id',
  `origin_id` bigint(20) DEFAULT NULL COMMENT '发布表对应id',
  `enterprise_id` bigint(30) DEFAULT NULL COMMENT 'gccs企业id',
  PRIMARY KEY (`id`),
  KEY `GLOBAL_ID_IDX` (`global_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='企业数据标准-项目信息、合同信息表';


-- db_cost_data_platform_pro.zb_standards_trade definition

CREATE TABLE `db_cost_data_platform_pro`.`zb_standards_trade` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `description` varchar(100) DEFAULT NULL COMMENT '专业名称',
  `trade_code` varchar(10) DEFAULT NULL COMMENT '专业编码',
  `type` tinyint(4) DEFAULT NULL COMMENT '专业类型 0 内置专业 1 新增专业',
  `creator_id` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `customer_code` varchar(100) NOT NULL COMMENT '企业编码',
  `enterprise_id` varchar(32) DEFAULT NULL COMMENT '企业ID',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否已被删除，0：未删除，1：已删除',
  `ord` int(11) NOT NULL COMMENT '排序字段',
  `refer_trade` varchar(10) DEFAULT NULL COMMENT '引用专业编码',
  `updater_id` bigint(20) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `modify_time_sjzt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间-造价中台用',
  `enabled` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否启用 1启用 0禁用',
  PRIMARY KEY (`id`),
  KEY `idx_customercode_ord` (`customer_code`, `ord`),
  KEY `idx_customercode_type` (`customer_code`, `type`),
  KEY `zb_standards_trade_modify_time_sjzt_IDX` (`modify_time_sjzt`)
) ENGINE = InnoDB DEFAULT CHARSET = utf8 COMMENT = '企业数据标准-专业表';

-- db_cost_data_platform_pro.zb_standards_unit definition

CREATE TABLE `db_cost_data_platform_pro`.`zb_standards_unit` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `name` varchar(100) NOT NULL COMMENT '单位名称',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否已删除，0：未删除，1：已删除',
  `customer_code` varchar(100) DEFAULT NULL COMMENT '企业编码',
  `zb_standards_project_info_id` bigint(20) DEFAULT NULL COMMENT '对应zb_standards_project_info表的主键',
  `unit_source_type` tinyint(4) DEFAULT NULL COMMENT '单位来源类型，1：项目信息，2：合同信息',
  `creator_id` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `is_checked` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否选中',
  `enterprise_id` bigint(30) DEFAULT NULL COMMENT 'gccs企业id',
  PRIMARY KEY (`id`),
  KEY `idx_cc` (`customer_code`),
  KEY `idx_zspii` (`zb_standards_project_info_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='企业数据标准-单位表';


-- db_cost_data_platform_pro.zb_unit definition

CREATE TABLE `db_cost_data_platform_pro`.`zb_unit` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `name` varchar(100) NOT NULL COMMENT '单位名称',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否已删除，0：未删除，1：已删除',
  `customer_code` varchar(100) DEFAULT NULL COMMENT '企业编码',
  `is_editable` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否允许编辑（0：否，1：是）',
  `creator_id` varchar(100) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_id` varchar(100) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `sort` int(5) DEFAULT '1' COMMENT '排序',
  `enterprise_id` bigint(30) DEFAULT NULL COMMENT 'gccs企业id',
  `modify_time_sjzt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间-造价中台用',
  PRIMARY KEY (`id`),
  KEY `zb_unit_modify_time_sjzt_IDX` (`modify_time_sjzt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='单位表';

-- db_cost_data_platform_pro.repair_log definition

CREATE TABLE `db_cost_data_platform_pro`.`repair_log` (
  `id` bigint(20) NOT NULL COMMENT '用户ID',
  `customer_code` varchar(100) DEFAULT NULL COMMENT '企业编码',
  `is_edited` tinyint(1) DEFAULT NULL COMMENT '是否数据被用户修改过',
  `is_repeat` tinyint(1) DEFAULT NULL COMMENT '是否数据重复',
  `erro_type` varchar(100) DEFAULT NULL COMMENT '数据错误类型',
  `repair_status` int(11) DEFAULT NULL COMMENT '修复状态1-已修复，2，修复失败',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` int(11) DEFAULT NULL COMMENT '修复版本号',
  PRIMARY KEY (`id`),
  KEY `zb_lib_user_repair_customer_code_IDX` (`customer_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='数据修复记录（修复数据用）';

ALTER TABLE db_cost_data_platform_pro.zb_standards_expression ADD repair_name varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '修复前计算口径名,称对应字段name';
ALTER TABLE db_cost_data_platform_pro.zb_standards_expression ADD invalid int(11) DEFAULT 0 NOT NULL COMMENT '数据是否有效，1为无效，0为有效';
ALTER TABLE db_cost_data_platform_pro.zb_standards_expression_self ADD invalid int(11) DEFAULT 0 NOT NULL COMMENT '数据是否有效，1为无效，0为有效';
ALTER TABLE db_cost_data_platform_pro.zb_standards_expression_self COMMENT='工程特征名称类型及计算口径暂存表(2023年5月29日确认业务上此表已经没有用了)';
ALTER TABLE db_cost_data_platform_pro.zb_project_feature_standards ADD repair_expression_id bigint(20) NULL COMMENT '修复数据时expression_id的备份';

ALTER TABLE db_cost_data_platform_pro.zb_project_feature_standards ADD invalid int(11) DEFAULT 0 NOT NULL COMMENT '数据是否有效，1为无效，0为有效, 2根据专业id找不到有效专业';
ALTER TABLE db_cost_data_platform_pro.zb_project_feature_standards_self ADD invalid int(11) DEFAULT 0 NOT NULL COMMENT '数据是否有效，1为无效，0为有效, 2根据专业id找不到有效专业';
ALTER TABLE db_cost_data_platform_pro.zb_project_feature_category_view_standards ADD invalid int(11) DEFAULT 0 NOT NULL COMMENT '数据是否有效，1为无效，0为有效';
ALTER TABLE db_cost_data_platform_pro.zb_project_feature_category_view_standards_self ADD invalid int(11) DEFAULT 0 NOT NULL COMMENT '数据是否有效，1为无效，0为有效';

ALTER TABLE db_cost_data_platform_pro.tb_commonprojcategory_standards ADD invalid int(11) DEFAULT 0 NOT NULL COMMENT '数据是否有效，1为无效，0为有效';
ALTER TABLE db_cost_data_platform_pro.tb_commonprojcategory_standards_self ADD invalid int(11) DEFAULT 0 NOT NULL COMMENT '数据是否有效，1为无效，0为有效';
ALTER TABLE db_cost_data_platform_pro.tb_commonprojcategory_standards ADD repair_categorycode1 varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '修复前一级编码';
ALTER TABLE db_cost_data_platform_pro.tb_commonprojcategory_standards ADD repair_categorycode2 varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '修复前二级编码';
ALTER TABLE db_cost_data_platform_pro.tb_commonprojcategory_standards ADD repair_categorycode3 varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '修复前三级编码';
ALTER TABLE db_cost_data_platform_pro.tb_commonprojcategory_standards ADD repair_categorycode4 varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '修复前四级编码';
ALTER TABLE db_cost_data_platform_pro.tb_commonprojcategory_standards_self ADD repair_categorycode1 varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '修复前一级编码';
ALTER TABLE db_cost_data_platform_pro.tb_commonprojcategory_standards_self ADD repair_categorycode2 varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '修复前二级编码';
ALTER TABLE db_cost_data_platform_pro.tb_commonprojcategory_standards_self ADD repair_categorycode3 varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '修复前三级编码';
ALTER TABLE db_cost_data_platform_pro.tb_commonprojcategory_standards_self ADD repair_categorycode4 varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '修复前四级编码';

ALTER TABLE db_cost_data_platform_pro.zb_standards_project_info ADD invalid int(11) DEFAULT 0 NOT NULL COMMENT '数据是否有效，1为无效，0为有效';
ALTER TABLE db_cost_data_platform_pro.zb_standards_project_info_self ADD invalid int(11) DEFAULT 0 NOT NULL COMMENT '数据是否有效，1为无效，0为有效';
ALTER TABLE db_cost_data_platform_pro.zb_standards_trade ADD invalid int(11) DEFAULT 0 NOT NULL COMMENT '数据是否有效，1为无效，0为有效';