use `db_cost_data_platform_pro`;

CREATE TABLE `db_cost_data_platform_pro`.`publish_info` (
                                `id` bigint(20) NOT NULL COMMENT '主键ID',
                                `customer_code` varchar(100) NOT NULL COMMENT '企业编码',
                                `global_id` varchar(32) DEFAULT NULL,
                                `type` varchar(100) NOT NULL COMMENT '本次发布的模块',
                                `version` int(11) NOT NULL COMMENT '版本号',
                                `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
                                PRIMARY KEY (`id`),
                                KEY `customer_code_index` (`customer_code`,`type`,`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='基础信息库发布状态';

CREATE TABLE `db_cost_data_platform_pro`.`option_upgrade_log` (
                                      `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
                                      `customer_code` varchar(100) NOT NULL COMMENT '企业编码',
                                      `type` varchar(100) NOT NULL COMMENT '类型',
                                      `link_id` bigint(20) NOT NULL COMMENT '原表id',
                                      `old_value` varchar(5000) NOT NULL COMMENT '旧值',
                                      `new_value` varchar(5000) NOT NULL COMMENT '新值',
                                      `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态',
                                      `msg` text COMMENT 'msg',
                                      `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                      PRIMARY KEY (`id`),
                                      KEY `idx_ct` (`customer_code`,`type`)
) ENGINE=InnoDB AUTO_INCREMENT=269049 DEFAULT CHARSET=utf8 COMMENT='数据修复记录（修复数据用）';

ALTER TABLE `db_cost_data_platform_pro`.`zb_standards_build_position_detail` CHANGE `value` `value` VARCHAR(2000) CHARSET utf8 COLLATE utf8_general_ci NULL COMMENT '标准说明的值';
ALTER TABLE `db_cost_data_platform_pro`.`zb_standards_build_position_detail_self` CHANGE `value` `value` VARCHAR(2000) CHARSET utf8 COLLATE utf8_general_ci NULL COMMENT '标准说明的值';