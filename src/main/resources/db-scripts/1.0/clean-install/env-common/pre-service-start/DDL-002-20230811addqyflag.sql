use `db_cost_data_platform_pro`;
ALTER TABLE db_cost_data_platform_pro.tb_commonprojcategory_standards ADD qy_code_old varchar(100) NULL COMMENT '企业编码（旧）';
ALTER TABLE db_cost_data_platform_pro.tb_commonprojcategory_standards ADD qy_flag tinyint(1) NULL COMMENT '企业标识：1-企业id；0-企业编码';
ALTER TABLE db_cost_data_platform_pro.tb_commonprojcategory_standards_self ADD qy_code_old varchar(100) NULL COMMENT '企业编码（旧）';
ALTER TABLE db_cost_data_platform_pro.tb_commonprojcategory_standards_self ADD qy_flag tinyint(1) NULL COMMENT '企业标识：1-企业id；0-企业编码';
ALTER TABLE db_cost_data_platform_pro.tb_commonprojcategory_standards_used ADD qy_code_old varchar(100) NULL COMMENT '企业编码（旧）';
ALTER TABLE db_cost_data_platform_pro.tb_commonprojcategory_standards_used ADD qy_flag tinyint(1) NULL COMMENT '企业标识：1-企业id；0-企业编码';
ALTER TABLE db_cost_data_platform_pro.zb_project_feature_category_view_standards ADD qy_code_old varchar(100) NULL COMMENT '企业编码（旧）';
ALTER TABLE db_cost_data_platform_pro.zb_project_feature_category_view_standards ADD qy_flag tinyint(1) NULL COMMENT '企业标识：1-企业id；0-企业编码';
ALTER TABLE db_cost_data_platform_pro.zb_project_feature_category_view_standards_self ADD qy_code_old varchar(100) NULL COMMENT '企业编码（旧）';
ALTER TABLE db_cost_data_platform_pro.zb_project_feature_category_view_standards_self ADD qy_flag tinyint(1) NULL COMMENT '企业标识：1-企业id；0-企业编码';
ALTER TABLE db_cost_data_platform_pro.zb_project_feature_standards ADD qy_code_old varchar(100) NULL COMMENT '企业编码（旧）';
ALTER TABLE db_cost_data_platform_pro.zb_project_feature_standards ADD qy_flag tinyint(1) NULL COMMENT '企业标识：1-企业id；0-企业编码';
ALTER TABLE db_cost_data_platform_pro.zb_project_feature_standards_self ADD qy_code_old varchar(100) NULL COMMENT '企业编码（旧）';
ALTER TABLE db_cost_data_platform_pro.zb_project_feature_standards_self ADD qy_flag tinyint(1) NULL COMMENT '企业标识：1-企业id；0-企业编码';
ALTER TABLE db_cost_data_platform_pro.zb_standards_build_standard ADD qy_code_old varchar(100) NULL COMMENT '企业编码（旧）';
ALTER TABLE db_cost_data_platform_pro.zb_standards_build_standard ADD qy_flag tinyint(1) NULL COMMENT '企业标识：1-企业id；0-企业编码';
ALTER TABLE db_cost_data_platform_pro.zb_standards_build_standard_self ADD qy_code_old varchar(100) NULL COMMENT '企业编码（旧）';
ALTER TABLE db_cost_data_platform_pro.zb_standards_build_standard_self ADD qy_flag tinyint(1) NULL COMMENT '企业标识：1-企业id；0-企业编码';
ALTER TABLE db_cost_data_platform_pro.zb_standards_expression ADD qy_code_old varchar(100) NULL COMMENT '企业编码（旧）';
ALTER TABLE db_cost_data_platform_pro.zb_standards_expression ADD qy_flag tinyint(1) NULL COMMENT '企业标识：1-企业id；0-企业编码';
ALTER TABLE db_cost_data_platform_pro.zb_standards_expression_self ADD qy_code_old varchar(100) NULL COMMENT '企业编码（旧）';
ALTER TABLE db_cost_data_platform_pro.zb_standards_expression_self ADD qy_flag tinyint(1) NULL COMMENT '企业标识：1-企业id；0-企业编码';
ALTER TABLE db_cost_data_platform_pro.zb_standards_main_quantity ADD qy_code_old varchar(100) NULL COMMENT '企业编码（旧）';
ALTER TABLE db_cost_data_platform_pro.zb_standards_main_quantity ADD qy_flag tinyint(1) NULL COMMENT '企业标识：1-企业id；0-企业编码';
ALTER TABLE db_cost_data_platform_pro.zb_standards_main_quantity_self ADD qy_code_old varchar(100) NULL COMMENT '企业编码（旧）';
ALTER TABLE db_cost_data_platform_pro.zb_standards_main_quantity_self ADD qy_flag tinyint(1) NULL COMMENT '企业标识：1-企业id；0-企业编码';
ALTER TABLE db_cost_data_platform_pro.zb_standards_project_info ADD qy_code_old varchar(100) NULL COMMENT '企业编码（旧）';
ALTER TABLE db_cost_data_platform_pro.zb_standards_project_info ADD qy_flag tinyint(1) NULL COMMENT '企业标识：1-企业id；0-企业编码';
ALTER TABLE db_cost_data_platform_pro.zb_standards_project_info_self ADD qy_code_old varchar(100) NULL COMMENT '企业编码（旧）';
ALTER TABLE db_cost_data_platform_pro.zb_standards_project_info_self ADD qy_flag tinyint(1) NULL COMMENT '企业标识：1-企业id；0-企业编码';
ALTER TABLE db_cost_data_platform_pro.zb_standards_trade ADD qy_code_old varchar(100) NULL COMMENT '企业编码（旧）';
ALTER TABLE db_cost_data_platform_pro.zb_standards_trade ADD qy_flag tinyint(1) NULL COMMENT '企业标识：1-企业id；0-企业编码';
ALTER TABLE db_cost_data_platform_pro.zb_standards_unit ADD qy_code_old varchar(100) NULL COMMENT '企业编码（旧）';
ALTER TABLE db_cost_data_platform_pro.zb_standards_unit ADD qy_flag tinyint(1) NULL COMMENT '企业标识：1-企业id；0-企业编码';
ALTER TABLE db_cost_data_platform_pro.zb_unit ADD qy_code_old varchar(100) NULL COMMENT '企业编码（旧）';
ALTER TABLE db_cost_data_platform_pro.zb_unit ADD qy_flag tinyint(1) NULL COMMENT '企业标识：1-企业id；0-企业编码';
