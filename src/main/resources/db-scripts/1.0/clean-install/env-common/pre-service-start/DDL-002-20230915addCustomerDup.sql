use `db_cost_data_platform_pro`;

CREATE TABLE `db_cost_data_platform_pro`.`sys_zb_customer_dup` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `enterprise_id` varchar(40) DEFAULT NULL COMMENT '企业id',
  `customer_code` varchar(100) DEFAULT NULL COMMENT '企业编码',
  PRIMARY KEY (`id`),
  <PERSON>EY `sys_all_enterprise_customer_code_IDX` (`customer_code`) USING BTREE,
  KEY `sys_all_enterprise_enterprise_id_IDX` (`enterprise_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户企业编码对应表（1对多）';