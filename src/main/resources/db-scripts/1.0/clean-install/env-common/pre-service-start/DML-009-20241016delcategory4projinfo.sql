use `db_cost_data_platform_pro`;

update `db_cost_data_platform_pro`.zb_standards_project_info set is_common = 0, category_code = '001' where standard_data_type = 1 and customer_code = '-1' and name = '产品定位';
update `db_cost_data_platform_pro`.zb_standards_project_info set is_common = 0, category_code = '001,002,003,004,005,006,009,014,007,008' where standard_data_type = 1 and customer_code = '-1' and name = '建设用地面积';
update `db_cost_data_platform_pro`.zb_standards_project_info set is_common = 0, category_code = '001,002,003,004,005,006,009,014,007,008' where standard_data_type = 1 and customer_code = '-1' and name = '建筑占地面积';
update `db_cost_data_platform_pro`.zb_standards_project_info set is_common = 0, category_code = '001,002,003,004,005,006,009,014,007,008' where standard_data_type = 1 and customer_code = '-1' and name = '总建筑面积';
update `db_cost_data_platform_pro`.zb_standards_project_info set is_common = 0, category_code = '001,002,003,004,005,006,007,008' where standard_data_type = 1 and customer_code = '-1' and name = '精装修面积';
update `db_cost_data_platform_pro`.zb_standards_project_info set is_common = 0, category_code = '001,002,004' where standard_data_type = 1 and customer_code = '-1' and name = '可售比';
update `db_cost_data_platform_pro`.zb_standards_project_info set is_common = 0, category_code = '001,004' where standard_data_type = 1 and customer_code = '-1' and name = '总计容面积';
update `db_cost_data_platform_pro`.zb_standards_project_info set is_common = 0, category_code = '001,002,003,004,009' where standard_data_type = 1 and customer_code = '-1' and name = '规划容积率';
update `db_cost_data_platform_pro`.zb_standards_project_info set is_common = 0, category_code = '001,002,003,004,005,006,009' where standard_data_type = 1 and customer_code = '-1' and name = '建筑密度';
update `db_cost_data_platform_pro`.zb_standards_project_info set is_common = 0, category_code = '001,002,003,004,005,006,009,007,008' where standard_data_type = 1 and customer_code = '-1' and name = '建筑限高';
update `db_cost_data_platform_pro`.zb_standards_project_info set is_common = 0, category_code = '001' where standard_data_type = 1 and customer_code = '-1' and name = '总户数';
update `db_cost_data_platform_pro`.zb_standards_project_info set is_common = 0, category_code = '009,012,014,013,011,007,008' where standard_data_type = 1 and customer_code = '-1' and name = '抗震设防度数';
update `db_cost_data_platform_pro`.zb_standards_project_info set is_common = 0, category_code = '009' where standard_data_type = 1 and customer_code = '-1' and name = '装配率';
update `db_cost_data_platform_pro`.zb_standards_project_info set is_common = 0, category_code = '001,002,004' where standard_data_type = 1 and customer_code = '-1' and name = 'PC单体建筑预制率';
update `db_cost_data_platform_pro`.zb_standards_project_info set is_common = 0, category_code = '001,002,003,004,005,006,009,008' where standard_data_type = 1 and customer_code = '-1' and name = '绿色建筑等级';
update `db_cost_data_platform_pro`.zb_standards_project_info set is_common = 0, category_code = '012,013' where standard_data_type = 1 and customer_code = '-1' and name = '市政道路面积';
update `db_cost_data_platform_pro`.zb_standards_project_info set is_common = 0, category_code = '001,002,003,004,005,006,009,012,014,011,007,008' where standard_data_type = 1 and customer_code = '-1' and name = '景观园林面积';
update `db_cost_data_platform_pro`.zb_standards_project_info set is_common = 0, category_code = '001,002,004,005,009,008' where standard_data_type = 1 and customer_code = '-1' and name = '园区出入口数量';
update `db_cost_data_platform_pro`.zb_standards_project_info set is_common = 0, category_code = '001,002,003,004,005,006,009,014,008' where standard_data_type = 1 and customer_code = '-1' and name = '绿地率';
update `db_cost_data_platform_pro`.zb_standards_project_info set is_common = 0, category_code = '001,002,003,004,005,006,009,007,008' where standard_data_type = 1 and customer_code = '-1' and name = '车位数';
update `db_cost_data_platform_pro`.zb_standards_project_info set is_common = 0, category_code = '001,002,003,004,005,006,009' where standard_data_type = 1 and customer_code = '-1' and name = '车位配比';
update `db_cost_data_platform_pro`.zb_standards_project_info set is_common = 0, category_code = '001,002,003,004,005,006,009,007,008' where standard_data_type = 1 and customer_code = '-1' and name = '充电桩数量';
update `db_cost_data_platform_pro`.zb_standards_project_info set is_common = 0, category_code = '009,007,008' where standard_data_type = 1 and customer_code = '-1' and name = '耐火等级';


update `db_cost_data_platform_pro`.zb_standards_project_info set is_deleted = 1 where name = '工程分类' and standard_data_type = 1;
update `db_cost_data_platform_pro`.zb_standards_project_info_self set is_deleted = 1 where name = '工程分类' and standard_data_type = 1;