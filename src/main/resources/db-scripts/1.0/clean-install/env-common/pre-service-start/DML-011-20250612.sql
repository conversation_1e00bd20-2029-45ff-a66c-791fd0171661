-- 工程分类初始化完成后,重置sys_sequence表的最大id,防止私有化脚本部署时执行多次造成运行时分布式id小于初始id的情况(异常情况兼容,服务运行中,重新初始化了脚本)
REPLACE INTO `db_cost_data_platform_pro`.sys_sequence (SEQUENCE_NAME, CURRENT_VALUE, INCREMENT_BY, MAX_VALUE, MIN_VALUE, CYCLE_FLAG, CREATE_TIME,
                                                       LAST_UPDATE_TIME)
values ('tb_commonprojcategory_standards',
        COALESCE(CEIL((SELECT MAX(id) FROM `db_cost_data_platform_pro`.tb_commonprojcategory_standards) / 1000.0) * 1000, 1), 1,
        9223372036854775807, 1, 0, CURRENT_TIME(),
        CURRENT_TIME());