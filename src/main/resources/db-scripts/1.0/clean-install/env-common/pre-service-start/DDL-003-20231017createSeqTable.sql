use `db_cost_data_platform_pro`;
CREATE TABLE `db_cost_data_platform_pro`.`sys_sequence` (
                                `SEQUENCE_NAME` varchar(64) NOT NULL COMMENT '序列名称',
                                `CURRENT_VALUE` decimal(19, 0) DEFAULT '1' COMMENT '当前值',
                                `INCREMENT_BY` decimal(5, 0) DEFAULT '1' COMMENT '步长，递增的序列值是n 如果n是正数就递增,如果是负数就递减 默认是1',
                                `MAX_VALUE` decimal(19, 0) DEFAULT NULL COMMENT '最大值',
                                `MIN_VALUE` decimal(19, 0) DEFAULT NULL COMMENT '最小值',
                                `CYCLE_FLAG` tinyint(1) DEFAULT '0' COMMENT '循环标志，循环true/不循环false',
                                `CREATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                `LAST_UPDATE_TIME` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '最后更新时间',
                                `DESCRIPTION` varchar(256) DEFAULT NULL COMMENT '描述',
                                PRIMARY KEY (`SEQUENCE_NAME`) USING BTREE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COMMENT = '序列';

CREATE TABLE `db_cost_data_platform_pro`.`sys_distributed_lock_register` (
                                                 `REGISTER_ID` int NOT NULL AUTO_INCREMENT COMMENT '锁定义标识',
                                                 `LOCK_NAME` varchar(128) DEFAULT NULL COMMENT '锁名称',
                                                 `LOCK_DESC` varchar(128) DEFAULT NULL COMMENT '锁描述',
                                                 `CREATE_TIME` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                 `UPDATE_TIME` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                                 PRIMARY KEY (`REGISTER_ID`) USING BTREE,
                                                 KEY `xxx` (`LOCK_NAME`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 DEFAULT CHARSET = utf8mb4 COMMENT = '分布式锁注册';


