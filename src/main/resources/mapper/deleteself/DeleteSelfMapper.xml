<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.qydata.mapper.deleteself.DeleteSelfMapper">

    <select id="selectCategoryAll" resultType="com.glodon.qydata.entity.deleteself.SelfEntity">
        select self_global_id as selfGlobalId, create_time as createTime, update_time as updateTime, qy_code as customerCode
        from tb_commonprojcategory_standards_self
        where
            is_deleted = 0
          and invalid = 0
        <if test="customerCode != null and customerCode != ''" >
            and qy_code = #{customerCode}
        </if>
    </select>

    <delete id="deleteCategory">
        delete from tb_commonprojcategory_standards_self where self_global_id in
        <foreach item="item" index="index" collection="globalIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="selectProjectOrContractAll" resultType="com.glodon.qydata.entity.deleteself.SelfEntity">
        select global_id as selfGlobalId, create_time as createTime, update_time as updateTime, customer_code as customerCode
        from zb_standards_project_info_self
        where
            is_deleted = 0
          and invalid = 0
          and standard_data_type = #{type}
        <if test="customerCode != null and customerCode != ''" >
            and customer_code = #{customerCode}
        </if>
    </select>

    <delete id="deleteProjectOrContract">
        delete from zb_standards_project_info_self where global_id in
        <foreach item="item" index="index" collection="globalIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        and standard_data_type = #{type}
    </delete>


    <select id="selectFeatureAll" resultType="com.glodon.qydata.entity.deleteself.SelfEntity">
        select self_global_id as selfGlobalId, create_time as createTime, update_time as updateTime, customer_code as customerCode
        from zb_project_feature_standards_self
        where
            is_deleted = 0
          and invalid = 0
        <if test="customerCode != null and customerCode != ''" >
            and customer_code = #{customerCode}
        </if>
    </select>

    <delete id="deleteFeature">
        delete from zb_project_feature_standards_self where self_global_id in
        <foreach item="item" index="index" collection="globalIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteFeatureView">
        delete from zb_project_feature_category_view_standards_self where self_global_id in
        <foreach item="item" index="index" collection="globalIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>


    <select id="selectBuildStandardAll" resultType="com.glodon.qydata.entity.deleteself.SelfEntity">
        select temp_global_id as selfGlobalId, create_time as createTime, update_time as updateTime, customer_code as customerCode
        from zb_standards_build_standard_self
        where
            is_deleted = 0
        <if test="customerCode != null and customerCode != ''" >
            and customer_code = #{customerCode}
        </if>
    </select>

    <select id="selectBuildStandardId" resultType="java.lang.Long">
        select id as id
        from zb_standards_build_standard_self
        where
            temp_global_id in
        <foreach item="item" index="index" collection="tempGlobalIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <delete id="deleteBuildStandardDetailDesc">
        delete from zb_standards_build_standard_detail_desc_self where standard_id in
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteBuildStandardDetail">
        delete from zb_standards_build_standard_detail_self where standard_id in
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteBuildStandardCategory">
        delete from zb_standards_build_category_self where standard_id in
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteBuildStandardPositionDetail">
        delete from zb_standards_build_position_detail_self where standard_id in
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteBuildStandardPosition">
        delete from zb_standards_build_position_self where standard_id in
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteBuildStandard">
        delete from zb_standards_build_standard_self where temp_global_id in
        <foreach item="item" index="index" collection="globalIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>


    <select id="selectMainQuantityAll" resultType="com.glodon.qydata.entity.deleteself.SelfEntity">
        select global_id as selfGlobalId, create_time as createTime, update_time as updateTime, customer_code as customerCode
        from zb_standards_main_quantity_self
        where
            is_deleted = 0
        <if test="customerCode != null and customerCode != ''" >
            and customer_code = #{customerCode}
        </if>
    </select>

    <delete id="deleteMainQuantity">
        delete from zb_standards_main_quantity_self where global_id in
        <foreach item="item" index="index" collection="globalIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>


    <select id="selectExpressionAll" resultType="com.glodon.qydata.entity.deleteself.SelfEntity">
        select self_global_id as selfGlobalId, create_time as createTime, update_time as updateTime, qy_code as customerCode
        from zb_standards_expression_self
        where
            is_deleted = 0
          and invalid = 0
        <if test="customerCode != null and customerCode != ''" >
            and qy_code = #{customerCode}
        </if>
    </select>

    <delete id="deleteExpression">
        delete from zb_standards_expression_self where self_global_id not in
        <foreach item="item" index="index" collection="globalIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>


</mapper>