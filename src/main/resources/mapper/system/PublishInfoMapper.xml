<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.glodon.qydata.mapper.system.PublishInfoMapper">

    <insert id="insert" parameterType="com.glodon.qydata.entity.system.PublishInfo">
        INSERT INTO publish_info (id, customer_code, global_id, type, version)
        VALUES (#{id}, #{customerCode}, #{globalId}, #{type}, #{version})
    </insert>

    <select id="findMaxVersionInfo" parameterType="String" resultType="com.glodon.qydata.entity.system.PublishInfo">
        SELECT *
        FROM publish_info
        WHERE customer_code = #{customerCode}
        ORDER BY `version` DESC
        LIMIT 1
    </select>

    <select id="findMaxVersion" parameterType="String" resultType="Integer">
        SELECT IFNULL(MAX(VERSION), 0)
        FROM publish_info
        WHERE customer_code = #{customerCode}
    </select>
</mapper>
