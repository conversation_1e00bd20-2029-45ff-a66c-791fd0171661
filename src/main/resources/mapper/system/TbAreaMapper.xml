<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.glodon.qydata.mapper.system.TbAreaMapper" >
  <resultMap id="BaseResultMap" type="com.glodon.qydata.entity.system.TbArea" >
    <id column="id" property="id" jdbcType="INTEGER" />
    <result column="areaid" property="areaid" jdbcType="VARCHAR" />
    <result column="pid" property="pid" jdbcType="VARCHAR" />
    <result column="ord" property="ord" jdbcType="INTEGER" />
    <result column="area_code" property="areaCode" jdbcType="VARCHAR" />
    <result column="related_area_code" property="relatedAreaCode" jdbcType="VARCHAR" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="short_name" property="shortName" jdbcType="VARCHAR" />
    <result column="parent_id" property="parentId" jdbcType="INTEGER" />
    <result column="parent_area_code" property="parentAreaCode" jdbcType="VARCHAR" />
    <result column="abbr_spell" property="abbrSpell" jdbcType="VARCHAR" />
    <result column="full_spell" property="fullSpell" jdbcType="VARCHAR" />
    <result column="level" property="level" jdbcType="BIT" />
    <result column="sort" property="sort" jdbcType="SMALLINT" />
    <result column="area" property="area" jdbcType="VARCHAR" />
    <result column="longitude" property="longitude" jdbcType="DOUBLE" />
    <result column="latitude" property="latitude" jdbcType="DOUBLE" />
    <result column="displayable" property="displayable" jdbcType="TINYINT" />
    <result column="created_at" property="createdAt" jdbcType="TIMESTAMP" />
    <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, areaid, pid, ord, area_code, related_area_code, `name`, short_name, parent_id, parent_area_code, abbr_spell,
    full_spell, `level`, sort, area, longitude, latitude, displayable
  </sql>
  <select id="getAreaList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_area
    <if test="scope != null and scope == 'ONLY_VALID'">
      where displayable = 1
    </if>
    ORDER BY ord DESC
  </select>

  <select id="getAreaByAreaIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_area
    where
    <foreach collection="areaIds" index="index" item="item" separator="," open="areaid IN (" close=")">
      #{item}
    </foreach>
  </select>

  <update id="hideByAreaIds">
    <foreach collection="list" item="item" index="index" open="" separator=";" close="">
      update tb_area
      set
      <if test="item.relatedAreaCode != null and item.relatedAreaCode != '' ">
        related_area_code = #{item.relatedAreaCode},
      </if>
      displayable = 0
      where areaid = #{item.areaid}
    </foreach>
  </update>

  <insert id="batchInsert">
    insert into tb_area
    (id, areaid, pid, ord, area_code, related_area_code,
    `name`, short_name, parent_id, parent_area_code, abbr_spell,
    full_spell, `level`, sort, area, longitude,
    latitude, displayable)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (
      #{item.id},#{item.areaid},#{item.pid},#{item.ord},#{item.areaCode},#{item.relatedAreaCode},
      #{item.name},#{item.shortName},#{item.parentId},#{item.parentAreaCode},#{item.abbrSpell},
      #{item.fullSpell},#{item.level},#{item.sort},#{item.area},#{item.longitude},
      #{item.latitude},#{item.displayable}
      )
    </foreach>
  </insert>

  <update id="batchUpdateAreaCode" parameterType="list">
    <foreach collection="list" index="index" item="item" separator=";">
      update tb_area set parent_area_code = #{item.parentAreaCode, jdbcType=VARCHAR}, area_code = #{item.areaCode, jdbcType=VARCHAR}
      where id = #{item.id, jdbcType=INTEGER}
    </foreach>
  </update>

  <update id="batchUpdate">
    <foreach collection="list" item="item" index="index" open="" separator=";" close="">
      update tb_area
      set areaid = #{item.areaid},
      pid = #{item.pid},
      ord = #{item.ord},
      area_code = #{item.areaCode},
      related_area_code = #{item.relatedAreaCode},
      `name` = #{item.name},
      short_name = #{item.shortName},
      parent_id = #{item.parentId},
      parent_area_code = #{item.parentAreaCode},
      abbr_spell = #{item.abbrSpell},
      full_spell = #{item.fullSpell},
      `level` = #{item.level},
      sort = #{item.sort},
      area = #{item.area},
      longitude = #{item.longitude},
      latitude = #{item.latitude},
      displayable = #{item.displayable}
      where id = #{item.id}
    </foreach>
  </update>

</mapper>
