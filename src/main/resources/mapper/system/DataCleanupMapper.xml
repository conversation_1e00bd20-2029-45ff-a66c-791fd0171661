<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.qydata.mapper.system.DataCleanupMapper">



    <!-- qy_code分组统计结果映射 -->
    <resultMap id="QyCodeGroupStatsResultMap" type="com.glodon.qydata.service.system.DataCleanupService$QyCodeGroupStats">
        <result column="qy_code" property="qyCode" jdbcType="VARCHAR"/>
        <result column="record_count" property="recordCount" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 记录ID信息结果映射 -->
    <resultMap id="RecordIdInfoResultMap" type="com.glodon.qydata.service.system.DataCleanupService$RecordIdInfo">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="qy_code" property="qyCode" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 统计表记录数量 -->
    <select id="countTableRecords" resultType="java.lang.Long">
        SELECT COUNT(*) FROM ${tableName}
    </select>



    <!-- 按qy_code分组统计记录数 -->
    <select id="getQyCodeGroupStats" resultMap="QyCodeGroupStatsResultMap">
        SELECT 
            qy_code,
            COUNT(*) as record_count
        FROM ${tableName}
        WHERE qy_code IS NOT NULL 
        AND qy_code != ''
        GROUP BY qy_code
        ORDER BY record_count DESC
    </select>

    <!-- 收集指定qy_code的所有记录ID -->
    <select id="collectRecordIdsByQyCode" resultMap="RecordIdInfoResultMap">
        SELECT
            id,
            qy_code
        FROM ${tableName}
        WHERE qy_code = #{qyCode}
        ORDER BY id
    </select>

    <!-- 批量收集所有qy_code的记录ID（优化版） -->
    <select id="collectAllRecordIds" resultMap="RecordIdInfoResultMap">
        SELECT
            id,
            qy_code
        FROM ${tableName}
        WHERE qy_code IS NOT NULL
        AND qy_code != ''
        ORDER BY qy_code, id
    </select>

    <!-- 删除指定qy_code的记录（物理删除） -->
    <delete id="deleteRecordsByQyCode">
        DELETE FROM ${tableName} 
        WHERE qy_code = #{qyCode}
    </delete>

    <!-- 软删除指定qy_code的记录 -->
    <update id="softDeleteRecordsByQyCode">
        UPDATE ${tableName} 
        SET is_deleted = 1, update_time = NOW() 
        WHERE qy_code = #{qyCode} 
        AND is_deleted = 0
    </update>

    <!-- 批量删除指定qy_code的记录（物理删除） -->
    <delete id="batchDeleteRecordsByQyCodes">
        DELETE FROM ${tableName} 
        WHERE qy_code IN
        <foreach collection="qyCodes" item="qyCode" open="(" close=")" separator=",">
            #{qyCode}
        </foreach>
    </delete>

    <!-- 批量软删除指定qy_code的记录 -->
    <update id="batchSoftDeleteRecordsByQyCodes">
        UPDATE ${tableName} 
        SET is_deleted = 1, update_time = NOW() 
        WHERE qy_code IN
        <foreach collection="qyCodes" item="qyCode" open="(" close=")" separator=",">
            #{qyCode}
        </foreach>
        AND is_deleted = 0
    </update>

    <!-- 获取数据库基本信息 -->
    <select id="getDatabaseInfo" resultType="java.util.Map">
        SELECT 
            DATABASE() as db_name,
            USER() as user_name,
            VERSION() as version,
            @@character_set_database as charset,
            @@collation_database as collation
    </select>

    <!-- 测试数据库连接 -->
    <select id="testConnection" resultType="java.lang.Integer">
        SELECT 1
    </select>

    <!-- 获取指定表的记录数统计（按条件） -->
    <select id="countTableRecordsWithCondition" resultType="java.lang.Long">
        SELECT COUNT(*) FROM ${tableName}
        <if test="whereCondition != null and whereCondition != ''">
            WHERE ${whereCondition}
        </if>
    </select>

    <!-- 获取所有不同的qy_code -->
    <select id="getDistinctQyCodes" resultType="java.lang.String">
        SELECT DISTINCT qy_code
        FROM ${tableName}
        WHERE qy_code IS NOT NULL 
        AND qy_code != ''
        ORDER BY qy_code
    </select>

    <!-- 获取指定qy_code的记录详情 -->
    <select id="getRecordDetailsByQyCode" resultType="java.util.Map">
        SELECT *
        FROM ${tableName}
        WHERE qy_code = #{qyCode}
        ORDER BY id
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 验证表是否存在 -->
    <select id="checkTableExists" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM information_schema.tables
        WHERE table_schema = DATABASE()
        AND table_name = #{tableName}
    </select>

    <!-- ==================== 新增：支持 customer_code 字段的 SQL 映射 ==================== -->

    <!-- customer_code分组统计结果映射 -->
    <resultMap id="CustomerCodeGroupStatsResultMap" type="com.glodon.qydata.service.system.DataCleanupService$CustomerCodeGroupStats">
        <result column="customer_code" property="customerCode" jdbcType="VARCHAR"/>
        <result column="record_count" property="recordCount" jdbcType="BIGINT"/>
    </resultMap>

    <!-- customer_code记录ID信息结果映射 -->
    <resultMap id="CustomerCodeRecordIdInfoResultMap" type="com.glodon.qydata.service.system.DataCleanupService$CustomerCodeRecordIdInfo">
        <result column="id" property="id" jdbcType="BIGINT"/>
        <result column="customer_code" property="customerCode" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 按customer_code分组统计记录数 -->
    <select id="getCustomerCodeGroupStats" resultMap="CustomerCodeGroupStatsResultMap">
        SELECT
            customer_code,
            COUNT(*) as record_count
        FROM ${tableName}
        WHERE customer_code IS NOT NULL
        AND customer_code != ''
        GROUP BY customer_code
        ORDER BY record_count DESC
    </select>

    <!-- 收集指定customer_code的所有记录ID -->
    <select id="collectRecordIdsByCustomerCode" resultMap="CustomerCodeRecordIdInfoResultMap">
        SELECT
            id,
            customer_code
        FROM ${tableName}
        WHERE customer_code = #{customerCode}
        ORDER BY id
    </select>

    <!-- 批量收集所有customer_code的记录ID（优化版） -->
    <select id="collectAllRecordIdsByCustomerCode" resultMap="CustomerCodeRecordIdInfoResultMap">
        SELECT
            id,
            customer_code
        FROM ${tableName}
        WHERE customer_code IS NOT NULL
        AND customer_code != ''
        ORDER BY customer_code, id
    </select>

    <!-- 删除指定customer_code的记录（物理删除） -->
    <delete id="deleteRecordsByCustomerCode">
        DELETE FROM ${tableName}
        WHERE customer_code = #{customerCode}
    </delete>

    <!-- 软删除指定customer_code的记录 -->
    <update id="softDeleteRecordsByCustomerCode">
        UPDATE ${tableName}
        SET is_deleted = 1, update_time = NOW()
        WHERE customer_code = #{customerCode}
        AND is_deleted = 0
    </update>

    <!-- 批量删除指定customer_code的记录（物理删除） -->
    <delete id="batchDeleteRecordsByCustomerCodes">
        DELETE FROM ${tableName}
        WHERE customer_code IN
        <foreach collection="customerCodes" item="customerCode" open="(" close=")" separator=",">
            #{customerCode}
        </foreach>
    </delete>

    <!-- 批量软删除指定customer_code的记录 -->
    <update id="batchSoftDeleteRecordsByCustomerCodes">
        UPDATE ${tableName}
        SET is_deleted = 1, update_time = NOW()
        WHERE customer_code IN
        <foreach collection="customerCodes" item="customerCode" open="(" close=")" separator=",">
            #{customerCode}
        </foreach>
        AND is_deleted = 0
    </update>

    <!-- 获取所有不同的customer_code -->
    <select id="getDistinctCustomerCodes" resultType="java.lang.String">
        SELECT DISTINCT customer_code
        FROM ${tableName}
        WHERE customer_code IS NOT NULL
        AND customer_code != ''
        ORDER BY customer_code
    </select>

    <!-- 获取指定customer_code的记录详情 -->
    <select id="getRecordDetailsByCustomerCode" resultType="java.util.Map">
        SELECT *
        FROM ${tableName}
        WHERE customer_code = #{customerCode}
        ORDER BY id
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

</mapper>
