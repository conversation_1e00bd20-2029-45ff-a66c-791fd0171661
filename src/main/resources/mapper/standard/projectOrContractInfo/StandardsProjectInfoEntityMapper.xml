<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.qydata.mapper.standard.projectOrContractInfo.StandardsProjectInfoMapper">
    <insert id="insertBatch">
       INSERT INTO zb_standards_project_info
    (id, name, type_code, unit, select_list, is_using, is_required, is_expression, creator_id, create_time, updater_id,
    update_time, remark, customer_code, is_deleted, ord, standard_data_type, qy_code_old, qy_flag, pid, type, is_common, category_code)
    values
    <foreach collection="list" index="index" item="item"  separator=",">
      (
        #{item.id}, #{item.name}, #{item.typeCode}, #{item.unit}, #{item.selectList}, #{item.isUsing},
         #{item.isRequired}, #{item.isExpression}, #{item.creatorId}, #{item.createTime}, #{item.updaterId}, #{item.updateTime},
          #{item.remark}, #{item.customerCode}, #{item.isDeleted}, #{item.ord}, #{item.standardDataType},
        #{item.qyCodeOld}, #{item.qyFlag}, #{item.pid}, #{item.type}, #{item.isCommon}, #{item.categoryCode}
        )
    </foreach>
    </insert>
    <delete id="deleteByCustomerCode">
        delete from zb_standards_project_info where customer_code = #{customerCode}
    </delete>

    <select id="selectAllByCustomerCode"
            resultType="com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity">
        select * from zb_standards_project_info where customer_code =#{customerCode} and invalid = 0
    </select>
    <select id="selectAllPublishData"
            resultType="com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity"
            useCache="false" flushCache="true">
        select * from zb_standards_project_info where customer_code =#{customerCode}
        <if test="isShowDelete == null or isShowDelete == 0" >
            AND is_deleted = 0
        </if>
        AND standard_data_type=#{type}
        AND invalid = 0
        order by ord
    </select>
    <update id="batchUpdatePublish" parameterType="list">
        <foreach collection="list" index="index" item="item" separator=";">
            update zb_standards_project_info
            <set >
                name = #{item.name,jdbcType=VARCHAR},
                type_code = #{item.typeCode,jdbcType=VARCHAR},
                unit = #{item.unit,jdbcType=VARCHAR},
                select_list = #{item.selectList,jdbcType=VARCHAR},
                is_using = #{item.isUsing,jdbcType=TINYINT},
                is_required = #{item.isRequired,jdbcType=TINYINT},
                is_expression = #{item.isExpression,jdbcType=TINYINT},
                updater_id = #{item.updaterId,jdbcType=BIGINT},
                update_time = #{item.updateTime,jdbcType=TIMESTAMP},
                remark = #{item.remark,jdbcType=VARCHAR},
                customer_code = #{item.customerCode,jdbcType=VARCHAR},
                is_deleted = #{item.isDeleted,jdbcType=TINYINT},
                ord = #{item.ord,jdbcType=INTEGER},
                standard_data_type = #{item.standardDataType,jdbcType=TINYINT},
                pid = #{item.pid,jdbcType=BIGINT},
                type = #{item.type,jdbcType=TINYINT},
                is_common = #{item.isCommon},
                category_code = #{item.categoryCode}
            </set>
            where id = #{item.originId,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="batchUpdateOrd">
        <foreach collection="selfList" item="item" separator=";">
            update zb_standards_project_info_self
            set ord = #{item.ord}
            where id = #{item.id}
        </foreach>
    </update>

    <delete id="deleteSelfByCustomerCode" parameterType="java.lang.String">
        delete from zb_standards_project_info_self where customer_code = #{customerCode}
        <if test="standardDataType != null" >
            and standard_data_type = #{standardDataType}
        </if>
    </delete>

    <insert id="insertBatchSelf">
        INSERT INTO zb_standards_project_info_self
        (id, name, type_code, unit, select_list, is_using, is_required, is_expression, creator_id, create_time, updater_id,
        update_time, remark, customer_code, is_deleted, ord, standard_data_type, origin_id, qy_code_old, qy_flag, type, pid, is_common, category_code)
        values
        <foreach collection="list" index="index" item="item"  separator=",">
            (
            #{item.id}, #{item.name}, #{item.typeCode}, #{item.unit},#{item.selectList}, #{item.isUsing},
            #{item.isRequired}, #{item.isExpression},#{item.creatorId}, #{item.createTime}, #{item.updaterId}, #{item.updateTime},
            #{item.remark}, #{item.customerCode}, #{item.isDeleted}, #{item.ord}, #{item.standardDataType}, #{item.originId},
            #{item.qyCodeOld}, #{item.qyFlag}, #{item.type}, #{item.pid}, #{item.isCommon}, #{item.categoryCode}
            )
        </foreach>
    </insert>

    <select id="selectProductPositioning" resultType="com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity">
        SELECT id,`name`,type_code,select_list, is_using FROM zb_standards_project_info
        WHERE customer_code = #{customerCode} AND `standard_data_type` = 1 AND `name` = "产品定位" AND type_code = "select" AND is_deleted = 0 AND is_using != 0
          LIMIT 1
    </select>

    <select id="selectExpression" resultType="com.glodon.qydata.entity.standard.expression.ZbStandardsExpression">
        select
            `name`, `unit`, update_time
        from zb_standards_project_info
        where customer_code = #{customerCode}
          and `standard_data_type` = 1
          and type_code = "number"
          and is_expression = 1
          and is_deleted = 0
          and invalid = 0
    </select>

    <select id="selectSelfList" resultType="com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity">
        select * from zb_standards_project_info_self
        where customer_code = #{customerCode}
        <if test="isShowDelete == null or isShowDelete == 0" >
            AND is_deleted = 0
        </if>
        AND standard_data_type = #{type}
        AND invalid = 0
        order by ord
    </select>

    <update id="batchUpdateOrdAndPid" parameterType="list">
        <foreach collection="list" index="index" item="item" separator=";">
            update zb_standards_project_info_self
            <set >
                ord = #{item.ord,jdbcType=INTEGER},
                pid = #{item.pid,jdbcType=BIGINT},
                <if test="item.isDeleted != null" >
                    is_deleted = #{item.isDeleted,jdbcType=TINYINT}
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="batchDelete" parameterType="list">
        update zb_standards_project_info_self
        set is_deleted = 1
        where id in (
        <foreach collection="list" item="item" separator=",">
            #{item,jdbcType=BIGINT}
        </foreach>
        )
    </update>

    <update id="batchUpdateSelective" parameterType="list">
        <foreach collection="list" index="index" item="item" separator=";">
            UPDATE zb_standards_project_info
            <set>
                <if test="item.name != null">
                    name = #{item.name,jdbcType=VARCHAR},
                </if>
                <if test="item.typeCode != null">
                    type_code = #{item.typeCode,jdbcType=VARCHAR},
                </if>
                <if test="item.unit != null">
                    unit = #{item.unit,jdbcType=VARCHAR},
                </if>
                <if test="item.selectList != null">
                    select_list = #{item.selectList,jdbcType=VARCHAR},
                </if>
                <if test="item.isUsing != null">
                    is_using = #{item.isUsing,jdbcType=TINYINT},
                </if>
                <if test="item.isRequired != null">
                    is_required = #{item.isRequired,jdbcType=TINYINT},
                </if>
                <if test="item.isExpression != null">
                    is_expression = #{item.isExpression,jdbcType=TINYINT},
                </if>
                <if test="item.updaterId != null">
                    updater_id = #{item.updaterId,jdbcType=BIGINT},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.customerCode != null">
                    customer_code = #{item.customerCode,jdbcType=VARCHAR},
                </if>
                <if test="item.isDeleted != null">
                    is_deleted = #{item.isDeleted,jdbcType=TINYINT},
                </if>
                <if test="item.ord != null">
                    ord = #{item.ord,jdbcType=INTEGER},
                </if>
                <if test="item.standardDataType != null">
                    standard_data_type = #{item.standardDataType,jdbcType=TINYINT},
                </if>
                <if test="item.pid != null">
                    pid = #{item.pid,jdbcType=BIGINT},
                </if>
                <if test="item.type != null">
                    type = #{item.type,jdbcType=TINYINT},
                </if>
                <if test="item.isCommon != null">
                    is_common = #{item.isCommon},
                </if>
                <if test="item.categoryCode != null">
                    category_code = #{item.categoryCode},
                </if>
            </set>
            WHERE id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="batchUpdateSelfSelective" parameterType="list">
        <foreach collection="list" index="index" item="item" separator=";">
            UPDATE zb_standards_project_info_self
            <set>
                <if test="item.name != null">
                    name = #{item.name,jdbcType=VARCHAR},
                </if>
                <if test="item.typeCode != null">
                    type_code = #{item.typeCode,jdbcType=VARCHAR},
                </if>
                <if test="item.unit != null">
                    unit = #{item.unit,jdbcType=VARCHAR},
                </if>
                <if test="item.selectList != null">
                    select_list = #{item.selectList,jdbcType=VARCHAR},
                </if>
                <if test="item.isUsing != null">
                    is_using = #{item.isUsing,jdbcType=TINYINT},
                </if>
                <if test="item.isRequired != null">
                    is_required = #{item.isRequired,jdbcType=TINYINT},
                </if>
                <if test="item.isExpression != null">
                    is_expression = #{item.isExpression,jdbcType=TINYINT},
                </if>
                <if test="item.updaterId != null">
                    updater_id = #{item.updaterId,jdbcType=BIGINT},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.customerCode != null">
                    customer_code = #{item.customerCode,jdbcType=VARCHAR},
                </if>
                <if test="item.isDeleted != null">
                    is_deleted = #{item.isDeleted,jdbcType=TINYINT},
                </if>
                <if test="item.ord != null">
                    ord = #{item.ord,jdbcType=INTEGER},
                </if>
                <if test="item.standardDataType != null">
                    standard_data_type = #{item.standardDataType,jdbcType=TINYINT},
                </if>
                <if test="item.pid != null">
                    pid = #{item.pid,jdbcType=BIGINT},
                </if>
                <if test="item.type != null">
                    type = #{item.type,jdbcType=TINYINT},
                </if>
                <if test="item.isCommon != null">
                    is_common = #{item.isCommon},
                </if>
                <if test="item.categoryCode != null">
                    category_code = #{item.categoryCode},
                </if>
                <if test="item.originId != null">
                    origin_id = #{item.originId},
                </if>
            </set>
            WHERE id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="selectAllCustomerCodes" resultType="java.lang.String">
        select distinct customer_code from zb_standards_project_info
        where customer_code not in ('-100', 'null', 'test', '-100(old)', '-1', 'standardList_TC_0.data.gldUserId', '测试基础库初始化重复-可删除-企业1', '测试基础库初始化重复-可删除-企业2', 'defaultCustomerCode')
        AND standard_data_type=#{type};
    </select>

    <select id="selectSelfAllCustomerCodes" resultType="java.lang.String">
        select distinct customer_code from zb_standards_project_info_self
        where customer_code not in ('-100', 'null', 'test', '-100(old)', '-1', 'standardList_TC_0.data.gldUserId', '测试基础库初始化重复-可删除-企业1', '测试基础库初始化重复-可删除-企业2', 'defaultCustomerCode')
        AND standard_data_type=#{type};
</select>
</mapper>
