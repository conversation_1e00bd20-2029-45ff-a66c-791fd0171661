<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.glodon.qydata.mapper.standard.feature.ProjectFeatureCategoryViewSelfMapper" >
  <resultMap id="BaseResultMap" type="com.glodon.qydata.entity.standard.feature.ProjectFeatureCategoryView" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="trade_id" property="tradeId" jdbcType="BIGINT"/>
    <result column="category_code" property="categoryCode" jdbcType="VARCHAR"/>
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="feature_id" property="featureId" jdbcType="BIGINT"/>
    <result column="ord_category" property="ordCategory" jdbcType="INTEGER"/>
    <result column="customer_code" property="customerCode" jdbcType="VARCHAR"/>
    <result column="trade_name" property="tradeName" jdbcType="VARCHAR"/>
    <result column="origin_id" property="originId" jdbcType="BIGINT" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, `trade_id`, `category_code`, `type`, `feature_id`, `ord_category`, `customer_code`, trade_name, origin_id
  </sql>

  <insert id="saveBatch" parameterType="com.glodon.qydata.entity.standard.feature.ProjectFeatureCategoryView" >
    insert into zb_project_feature_category_view_standards_self (trade_id,category_code,type,feature_id,ord_category,customer_code,trade_name,origin_id,qy_code_old,qy_flag) values
    <foreach collection="list" item="item" index="index" separator="," >
      (
      #{item.tradeId},
      #{item.categoryCode},
      #{item.type},
      #{item.featureId},
      #{item.ordCategory},
      #{item.customerCode},
      #{item.tradeName},
      #{item.originId},
      #{item.qyCodeOld},
      #{item.qyFlag}
      )
    </foreach>
  </insert>

  <!--根据工程特征ID集合删除工程特征-->
  <delete id="deleteByFeatureId" parameterType="java.lang.Long" >
    delete from zb_project_feature_category_view_standards_self
    where feature_id = #{featureId,jdbcType=BIGINT}
  </delete>
  <!--根据工程特征ID集合删除工程特征-->
  <delete id="deleteByFeatureIds" parameterType="java.lang.Long" >
    delete from zb_project_feature_category_view_standards_self
    where feature_id in
    <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </delete>

  <!--根据分类和专业查询-->
  <select id="selectByCategoryAndTrade" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from zb_project_feature_category_view_standards_self
    where customer_code = #{customerCode,jdbcType=VARCHAR} and trade_id = #{tradeId,jdbcType=BIGINT}
    and invalid = 0
    <if test="categoryCode != null" >
      and category_code = #{categoryCode,jdbcType=VARCHAR}
    </if>
    <if test="type != null" >
      and `type` = #{type,jdbcType=INTEGER}
    </if>
    ORDER BY ord_category
  </select>

  <update id="batchUpdateOrd" parameterType="list">
    <foreach collection="list" index="index" item="item" separator=";">
      update zb_project_feature_category_view_standards_self set ord_category=#{item.ordCategory, jdbcType=INTEGER}
      where id = #{item.id, jdbcType=INTEGER}
    </foreach>
  </update>

  <delete id="batchDeleteFeatureIds">
    delete from zb_project_feature_category_view_standards_self
    where category_code = #{categoryCode,jdbcType=BIGINT} and feature_id in
    <foreach collection="featureIdList" separator="," open="(" close=")" item="item">
      #{item}
    </foreach>
  </delete>
  <delete id="batchDeleteFeatureCategory">
    delete from zb_project_feature_category_view_standards_self
    where feature_id = #{featureId,jdbcType=BIGINT} and category_code in
    <foreach collection="categoryCodeList" separator="," open="(" close=")" item="item">
      #{item}
    </foreach>
  </delete>

  <select id="selectBySelfCategoryAndCustomerCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from zb_project_feature_category_view_standards_self
    where customer_code = #{customerCode,jdbcType=VARCHAR}
    and invalid = 0
    <if test="type != null" >
      AND `type` = #{type,jdbcType=INTEGER}
    </if>
    <if test="categoryCode != null" >
      and category_code = #{categoryCode,jdbcType=VARCHAR}
    </if>
    ORDER BY ord_category
  </select>

  <select id="selectBySelfCustomerCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from zb_project_feature_category_view_standards_self
    where customer_code = #{customerCode,jdbcType=VARCHAR} and invalid = 0
    <if test="type != null" >
      AND `type` = #{type,jdbcType=INTEGER}
    </if>
  </select>

  <delete id="deleteBySelfCustomerCode">
    delete from zb_project_feature_category_view_standards_self where customer_code = #{customerCode}
    <if test="type != null" >
      AND `type` = #{type,jdbcType=INTEGER}
    </if>
  </delete>

  <insert id="insertSelfBatch" parameterType="list">
    insert into zb_project_feature_category_view_standards_self (id,trade_id,category_code,type,feature_id,ord_category,customer_code,trade_name,origin_id,qy_code_old,qy_flag) values
    <foreach collection="list" item="item" index="index" separator="," >
      (
      #{item.id},
      #{item.tradeId},
      #{item.categoryCode},
      #{item.type},
      #{item.featureId},
      #{item.ordCategory},
      #{item.customerCode},
      #{item.tradeName},
      #{item.originId},
      #{item.qyCodeOld},
      #{item.qyFlag}
      )
    </foreach>
    ON DUPLICATE KEY UPDATE
    trade_id = VALUES(trade_id),
    category_code = VALUES(category_code),
    type = VALUES(type),
    feature_id = VALUES(feature_id),
    ord_category = VALUES(ord_category),
    customer_code = VALUES(customer_code),
    trade_name = VALUES(trade_name),
    origin_id = VALUES(origin_id),
    qy_code_old = VALUES(qy_code_old),
    qy_flag = VALUES(qy_flag)
  </insert>


  <!--根据工程专业ID集合删除工程特征-->
  <delete id="deleteByTradeId" parameterType="java.lang.Long" >
    delete from zb_project_feature_category_view_standards_self
    where trade_id = #{tradeId,jdbcType=BIGINT}
  </delete>

  <select id="countByCategoryCodeExpressionId" resultType="java.lang.Integer">
    SELECT count(-1) FROM zb_project_feature_category_view_standards_self categoryView
    LEFT JOIN zb_project_feature_standards_self tradeView ON categoryView.feature_id = tradeView.id
    WHERE categoryView.customer_code = #{customerCode,jdbcType=VARCHAR}
    AND categoryView.trade_id = #{tradeId,jdbcType=BIGINT}
    AND categoryView.`type` = #{type,jdbcType=INTEGER}
    AND tradeView.name = #{name,jdbcType=VARCHAR}
    AND categoryView.category_code = #{categoryCode,jdbcType=VARCHAR}
    AND tradeView.invalid = 0 and categoryView.invalid = 0
    <if test="id != null" >
      AND tradeView.id !=  #{id,jdbcType=BIGINT}
    </if>
    AND tradeView.is_deleted = 0
  </select>

  <select id="selectAllGlobalIds" parameterType="java.lang.String" resultType="java.lang.Long">
    select distinct self_global_id from zb_project_feature_category_view_standards_self where customer_code = #{customerCode,jdbcType=VARCHAR} and invalid = 0
  </select>

  <select id="selectByCategoryAndCustomerCodeAndTradeId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from zb_project_feature_category_view_standards_self
    where customer_code = #{customerCode,jdbcType=VARCHAR}
    and category_code = #{categoryCode,jdbcType=VARCHAR}
    and trade_id = #{tradeId,jdbcType=BIGINT} and invalid = 0
    <if test="type != null" >
      AND `type` = #{type,jdbcType=INTEGER}
    </if>
    ORDER BY ord_category
  </select>

  <delete id="batchDeleteByIds">
    delete from zb_project_feature_category_view_standards_self
    where id in
    <foreach collection="ids" separator="," open="(" close=")" item="item">
      #{item}
    </foreach>
  </delete>

  <delete id="batchDeleteByTradeId" parameterType="java.util.List" >
    delete from zb_project_feature_category_view_standards_self
    where
    trade_id in
    <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </delete>

  <select id="selectRecordCount" resultType="java.lang.Integer">
    select count(*)
    from zb_project_feature_category_view_standards_self
    where customer_code = #{customerCode,jdbcType=VARCHAR}
    and invalid = 0
    <if test="type != null" >
      and `type` = #{type,jdbcType=INTEGER}
    </if>
    ORDER BY ord_category
  </select>
</mapper>