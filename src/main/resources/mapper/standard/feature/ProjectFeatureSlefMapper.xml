<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.glodon.qydata.mapper.standard.feature.ProjectFeatureSelfMapper" >
  <resultMap id="BaseResultMap" type="com.glodon.qydata.entity.standard.feature.ProjectFeature" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="expression_id" property="expressionId" jdbcType="BIGINT" />
    <result column="feature_level" property="featureLevel" jdbcType="VARCHAR" />
    <result column="is_sync" property="isSync" jdbcType="BIT" />
    <result column="type" property="type" jdbcType="INTEGER"/>
    <result column="project_type" property="projectType" jdbcType="VARCHAR" />
    <result column="is_using" property="isUsing" jdbcType="BIT" />
    <result column="is_default" property="isDefault" jdbcType="BIT" />
    <result column="is_required" property="isRequired" jdbcType="BIT" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="zb_project_feature_id" property="zbProjectFeatureId" jdbcType="BIGINT" />
    <result column="is_from_system" property="isFromSystem" jdbcType="BIT" />
    <result column="customer_code" property="customerCode" jdbcType="VARCHAR" />
    <result column="trade_name" property="tradeName" jdbcType="VARCHAR" />
    <result column="is_deleted" property="isDeleted" jdbcType="BIT" />
    <result column="create_global_id" property="createGlobalId" jdbcType="BIGINT" />
    <result column="update_global_id" property="updateGlobalId" jdbcType="BIGINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="product_pro_type" property="productProType" jdbcType="INTEGER"/>
    <result column="is_expression" property="isExpression" jdbcType="INTEGER"/>
    <result column="ord_trade" property="ordTrade" jdbcType="INTEGER"/>
    <result column="trade_id" property="tradeId" jdbcType="BIGINT"/>
    <result column="is_search_condition" property="isSearchCondition" jdbcType="INTEGER"/>
    <result column="origin_id" property="originId" jdbcType="BIGINT"/>
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type_code" jdbcType="VARCHAR" property="typeCode" />
    <result column="option" jdbcType="VARCHAR" property="option" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="expression_code" jdbcType="VARCHAR" property="expressionCode" />
    <result column="is_expression_default" jdbcType="INTEGER" property="isExpressionDefault" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, expression_id, feature_level, is_sync, type, project_type, is_using, is_default, is_deleted,
    is_required, remark, zb_project_feature_id, is_from_system, customer_code, create_global_id, update_global_id,
    create_time, update_time, product_pro_type, is_expression, ord_trade, trade_id, is_search_condition, trade_name, origin_id,
    `name`, type_code, `option`, unit, expression_code, is_expression_default
  </sql>
  <sql id="No_Blob_Column_List" >
    id, expression_id, feature_level, is_sync, type, is_using, is_default, is_deleted,
    is_required, remark, zb_project_feature_id, is_from_system, customer_code, create_global_id, update_global_id,
    create_time, update_time, product_pro_type, is_expression, ord_trade, trade_id, is_search_condition, trade_name, origin_id,
    `name`, type_code, `option`, unit, expression_code, is_expression_default
  </sql>

  <!--新增工程特征-->
  <insert id="insertSelective" parameterType="com.glodon.qydata.entity.standard.feature.ProjectFeature" >
    insert into zb_project_feature_standards_self
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="expressionId != null" >
        `expression_id`,
      </if>
      <if test="featureLevel != null" >
        feature_level,
      </if>
      <if test="isSync != null" >
        is_sync,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="projectType != null" >
        project_type,
      </if>
      <if test="isUsing != null" >
        is_using,
      </if>
      <if test="isDefault != null" >
        is_default,
      </if>
      <if test="isRequired != null" >
        is_required,
      </if>
      <if test="remark != null" >
        remark,
      </if>
      <if test="zbProjectFeatureId != null" >
        zb_project_feature_id,
      </if>
      <if test="isFromSystem != null" >
        is_from_system,
      </if>
      <if test="customerCode != null" >
        customer_code,
      </if>
      <if test="tradeName != null" >
        trade_name,
      </if>
      <if test="isDeleted != null" >
        is_deleted,
      </if>
      <if test="createGlobalId != null" >
        create_global_id,
      </if>
      <if test="updateGlobalId != null" >
        update_global_id,
      </if>
      <if test="createTime != null" >
        create_time,
      </if>
      <if test="updateTime != null" >
        update_time,
      </if>
      <if test="productProType != null">
        product_pro_type,
      </if>
      <if test="isExpression != null">
        is_expression,
      </if>
      <if test="ordTrade != null">
        ord_trade,
      </if>
      <if test="tradeId != null">
        trade_id,
      </if>
      <if test="isSearchCondition != null">
        is_search_condition,
      </if>
      <if test="originId != null">
        origin_id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="typeCode != null">
        type_code,
      </if>
      <if test="option != null">
        `option`,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="expressionCode != null">
        expression_code,
      </if>
      <if test="isExpressionDefault != null">
        is_expression_default,
      </if>
      <if test="qyCodeOld != null" >
        qy_code_old,
      </if>
      <if test="qyFlag != null" >
        qy_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="expressionId != null" >
        #{expressionId,jdbcType=BIGINT},
      </if>
      <if test="featureLevel != null" >
        #{featureLevel,jdbcType=VARCHAR},
      </if>
      <if test="isSync != null" >
        #{isSync,jdbcType=BIT},
      </if>
      <if test="type != null" >
        #{type,jdbcType=INTEGER},
      </if>
      <if test="projectType != null" >
        #{projectType,jdbcType=VARCHAR},
      </if>
      <if test="isUsing != null" >
        #{isUsing,jdbcType=BIT},
      </if>
      <if test="isDefault != null" >
        #{isDefault,jdbcType=BIT},
      </if>
      <if test="isRequired != null" >
        #{isRequired,jdbcType=BIT},
      </if>
      <if test="remark != null" >
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="zbProjectFeatureId != null" >
        #{zbProjectFeatureId,jdbcType=BIGINT},
      </if>
      <if test="isFromSystem != null" >
        #{isFromSystem,jdbcType=BIT},
      </if>
      <if test="customerCode != null" >
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="tradeName != null" >
        #{tradeName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null" >
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="createGlobalId != null" >
        #{createGlobalId,jdbcType=BIGINT},
      </if>
      <if test="updateGlobalId != null" >
        #{updateGlobalId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null" >
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="productProType != null">
        #{productProType,jdbcType=INTEGER},
      </if>
      <if test="isExpression != null">
      #{isExpression,jdbcType=INTEGER},
      </if>
      <if test="ordTrade != null">
      #{ordTrade,jdbcType=INTEGER},
      </if>
      <if test="tradeId != null">
      #{tradeId,jdbcType=BIGINT},
      </if>
      <if test="isSearchCondition != null">
        #{isSearchCondition,jdbcType=BIGINT},
      </if>
      <if test="originId != null">
        #{originId,jdbcType=BIGINT},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="typeCode != null" >
        #{typeCode,jdbcType=VARCHAR},
      </if>
      <if test="option != null" >
        #{option,jdbcType=VARCHAR},
      </if>
      <if test="unit != null" >
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="expressionCode != null" >
        #{expressionCode,jdbcType=VARCHAR},
      </if>
      <if test="isExpressionDefault != null" >
        #{expressionCode,jdbcType=INTEGER},
      </if>
      <if test="qyCodeOld != null" >
        #{qyCodeOld,jdbcType=VARCHAR},
      </if>
      <if test="qyFlag != null" >
        #{qyFlag,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <insert id="insertSelectiveBatch" parameterType="com.glodon.qydata.entity.standard.feature.ProjectFeature" >
    insert into zb_project_feature_standards_self
        (id,
    `expression_id`,
    feature_level,
    is_sync,
    project_type,
    is_using,
    is_default,
    is_required,
    remark,
    zb_project_feature_id,
    is_from_system,
    customer_code,
    trade_name,
    is_deleted,
    create_global_id,
    update_global_id,
    update_time,
    product_pro_type,
    is_expression,
    ord_trade,
    trade_id,
    is_search_condition,
    origin_id,
    type,
    `name`,
    type_code,
    `option`,
    unit,
    expression_code,
    is_expression_default,
    qy_code_old,
    qy_flag)values
    <foreach collection="list" index="index" separator="," item="item" >
      (
        #{item.id,jdbcType=BIGINT},
        #{item.expressionId,jdbcType=BIGINT},
        #{item.featureLevel,jdbcType=VARCHAR},
        #{item.isSync,jdbcType=BIT},
        #{item.projectType,jdbcType=VARCHAR},
        #{item.isUsing,jdbcType=BIT},
        #{item.isDefault,jdbcType=BIT},
        #{item.isRequired,jdbcType=BIT},
        #{item.remark,jdbcType=VARCHAR},
        #{item.zbProjectFeatureId,jdbcType=BIGINT},
        #{item.isFromSystem,jdbcType=BIT},
        #{item.customerCode,jdbcType=VARCHAR},
        #{item.tradeName,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=BIT},
        #{item.createGlobalId,jdbcType=BIGINT},
        #{item.updateGlobalId,jdbcType=BIGINT},
        #{item.updateTime,jdbcType=TIMESTAMP},
        #{item.productProType,jdbcType=INTEGER},
        #{item.isExpression,jdbcType=INTEGER},
        #{item.ordTrade,jdbcType=INTEGER},
        #{item.tradeId,jdbcType=BIGINT},
        #{item.isSearchCondition,jdbcType=BIGINT},
        #{item.originId,jdbcType=BIGINT},
        #{item.type,jdbcType=BIGINT},
        #{item.name,jdbcType=VARCHAR},
        #{item.typeCode,jdbcType=VARCHAR},
        #{item.option,jdbcType=VARCHAR},
        #{item.unit,jdbcType=VARCHAR},
        #{item.expressionCode,jdbcType=VARCHAR},
        #{item.isExpressionDefault,jdbcType=INTEGER},
        #{item.qyCodeOld,jdbcType=VARCHAR},
        #{item.qyFlag,jdbcType=INTEGER}
      )
    </foreach>
  </insert>

  <!--根据工程特征主键ID查询工程特征信息-->
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from zb_project_feature_standards_self
    where is_deleted = 0
    and id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByIds" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from zb_project_feature_standards_self
    where is_deleted = 0
    and id in
    <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <!--更新工程特征信息-->
  <update id="updateByPrimaryKeySelective" parameterType="com.glodon.qydata.entity.standard.feature.ProjectFeature" >
    update zb_project_feature_standards_self
    <set >
      <if test="expressionId != null" >
        `expression_id` = #{expressionId,jdbcType=BIGINT},
      </if>
      <if test="featureLevel != null" >
        feature_level = #{featureLevel,jdbcType=VARCHAR},
      </if>
      <if test="isSync != null" >
        is_sync = #{isSync,jdbcType=BIT},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="projectType != null" >
        project_type = #{projectType,jdbcType=VARCHAR},
      </if>
      <if test="isUsing != null" >
        is_using = #{isUsing,jdbcType=BIT},
      </if>
      <if test="isDefault != null" >
        is_default = #{isDefault,jdbcType=BIT},
      </if>
      <if test="isRequired != null" >
        is_required = #{isRequired,jdbcType=BIT},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="zbProjectFeatureId != null" >
        zb_project_feature_id = #{zbProjectFeatureId,jdbcType=BIGINT},
      </if>
      <if test="isFromSystem != null" >
        is_from_system = #{isFromSystem,jdbcType=BIT},
      </if>
      <if test="customerCode != null" >
        customer_code = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null" >
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="createGlobalId != null" >
        create_global_id = #{createGlobalId,jdbcType=BIGINT},
      </if>
      <if test="updateGlobalId != null" >
        update_global_id = #{updateGlobalId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="productProType != null">
        product_pro_type = #{productProType,jdbcType=INTEGER},
      </if>
      <if test="isExpression != null">
        is_expression = #{isExpression,jdbcType=INTEGER},
      </if>
      <if test="ordTrade != null">
        ord_trade = #{ordTrade,jdbcType=INTEGER},
      </if>
      <if test="tradeId != null">
        trade_id = #{tradeId,jdbcType=INTEGER},
      </if>
      <if test="tradeName != null">
        trade_name = #{tradeName,jdbcType=VARCHAR},
      </if>
      <if test="isSearchCondition != null">
        is_search_condition = #{isSearchCondition,jdbcType=INTEGER},
      </if>
      <if test="originId != null">
        origin_id = #{originId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="typeCode != null">
        type_code = #{typeCode,jdbcType=VARCHAR},
      </if>
      <if test="option != null">
        `option` = #{option,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="expressionCode != null">
        expression_code = #{expressionCode,jdbcType=VARCHAR},
      </if>
      <if test="isExpressionDefault != null">
        is_expression_default = #{isExpressionDefault,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByIdsSelective" >
    update zb_project_feature_standards_self set
        is_deleted = 1
    where id in
    <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </update>
  <select id="countByTradeIdExpressionId" resultType="java.lang.Integer">
    SELECT  count(-1) FROM zb_project_feature_standards_self
    WHERE is_deleted = 0
    AND customer_code = #{customerCode,jdbcType=VARCHAR}
    AND trade_id = #{tradeId,jdbcType=BIGINT}
    AND `name` = #{name,jdbcType=VARCHAR}
    AND `type` = #{type,jdbcType=INTEGER}
    AND invalid = 0
    <if test="id != null" >
      AND id !=  #{id,jdbcType=BIGINT}
    </if>
  </select>
  <select id="countByTradeId" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/> FROM zb_project_feature_standards_self
    WHERE is_deleted = 0
    AND customer_code = #{customerCode,jdbcType=VARCHAR}
    AND trade_id = #{tradeId,jdbcType=BIGINT}
    AND `type` = #{type,jdbcType=INTEGER}
    AND invalid = 0
  </select>

  <select id="findByTradeIdExpressionId" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/> FROM zb_project_feature_standards_self
    WHERE
      customer_code = #{customerCode,jdbcType=VARCHAR}
      AND `type` = #{type,jdbcType=INTEGER}
      AND trade_id = #{tradeId,jdbcType=BIGINT}
      AND `name` = #{name,jdbcType=VARCHAR}
      AND `type_code` = #{typeCode,jdbcType=VARCHAR}
      AND invalid = 0
  </select>
  <select id="findByTradeId" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/> FROM zb_project_feature_standards_self
    WHERE
    customer_code = #{customerCode,jdbcType=VARCHAR}
    AND `type` = #{type,jdbcType=INTEGER}
    AND trade_id = #{tradeId,jdbcType=BIGINT}
    AND invalid = 0
  </select>

 <update id="deleteByTradeId" parameterType="java.lang.Long">
      update zb_project_feature_standards_self  set is_deleted = 1 where trade_id = #{tradeId,jdbcType=BIGINT}
 </update>

  <update id="batchUpdateOrd" parameterType="list">
    <foreach collection="list" index="index" item="item" separator=";">
      update zb_project_feature_standards_self set ord_trade=#{item.ordTrade, jdbcType=INTEGER}
      where id = #{item.id, jdbcType=INTEGER}
    </foreach>
  </update>

  <update id="batchUpdateFeatureInfo" parameterType="list">
    <foreach collection="list" index="index" item="item" separator=";">
      update zb_project_feature_standards_self
      <set >
      <if test="item.projectType != null" >
        `project_type` = #{item.projectType,jdbcType=BIGINT},
      </if>
      <if test="item.updateGlobalId != null" >
        `update_global_id` = #{item.updateGlobalId,jdbcType=BIGINT},
      </if>
      <if test="item.updateTime != null" >
        `update_time` = #{item.updateTime,jdbcType=BIGINT},
      </if>
      </set>
      where id = #{item.id, jdbcType=INTEGER}
    </foreach>
  </update>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from zb_project_feature_standards_self
    where id = #{featureId,jdbcType=BIGINT}
  </delete>

  <select id="selectAll" resultMap="BaseResultMap" useCache="false" flushCache="true">
    select
    <include refid="Base_Column_List" />
    from zb_project_feature_standards_self
    where customer_code = #{customerCode} and `type` = #{type,jdbcType=INTEGER} AND invalid = 0
  </select>

  <delete id="deleteByCustomerCode" parameterType="java.lang.String" >
    delete from zb_project_feature_standards_self
    where customer_code = #{customerCode,jdbcType=VARCHAR}
  </delete>

  <select id="selectBySelfTradeId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from zb_project_feature_standards_self
    where customer_code = #{customerCode,jdbcType=VARCHAR} AND is_deleted = 0 AND invalid = 0
    <if test="tradeId != null" >
      and trade_id = #{tradeId,jdbcType=BIGINT}
    </if>
    <if test="type != null" >
      and type = #{type,jdbcType=INTEGER}
    </if>
    order by ord_trade
  </select>

  <select id="selectBySelfFeatureIdList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from zb_project_feature_standards_self where customer_code = #{customerCode,jdbcType=VARCHAR}
    AND invalid = 0
    <if test="type != null" >
      AND `type` = #{type,jdbcType=INTEGER}
    </if>
    <if test="isShowNotUsing != 1" >
      AND is_using = 1
    </if>
    and id in
    <foreach collection="featureIds" item="item" index="index" separator="," open="(" close=")">
      #{item}
    </foreach>
  </select>

  <select id="selectByCustomCodeAndTradeIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from zb_project_feature_standards_self where customer_code = #{customerCode,jdbcType=VARCHAR}
    AND is_deleted = 0
    AND invalid = 0
    and trade_id in
    <foreach collection="tradeIds" item="tradeId" index="index" separator="," open="(" close=")">
      #{tradeId}
    </foreach>
  </select>

  <select id="selectSelfAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from zb_project_feature_standards_self
    where
    customer_code = #{customerCode,jdbcType=VARCHAR}
    AND invalid = 0
    <if test="type != null" >
      AND `type` = #{type,jdbcType=INTEGER}
    </if>
  </select>

  <delete id="deleteBySelfCustomerCode">
    delete from zb_project_feature_standards_self where customer_code = #{customerCode} and `type` = #{type}
  </delete>

  <insert id="insertSelfBatch" parameterType="list">
    insert into zb_project_feature_standards_self (id,expression_id,feature_level,is_sync,type,project_type,is_using,is_default,is_required,remark,is_from_system,zb_project_feature_id,
    customer_code,is_deleted,create_global_id,update_global_id,create_time,update_time,product_pro_type,is_expression,ord_trade,trade_id,trade_name,is_search_condition,origin_id,
    `name`, type_code, `option`, unit, expression_code, is_expression_default, qy_code_old, qy_flag) values
    <foreach collection="list" item="item" index="index" separator="," >
      (
      #{item.id}, #{item.expressionId}, #{item.featureLevel}, #{item.isSync}, #{item.type}, #{item.projectType},
      #{item.isUsing}, #{item.isDefault}, #{item.isRequired}, #{item.remark}, #{item.isFromSystem},
      #{item.zbProjectFeatureId}, #{item.customerCode}, #{item.isDeleted}, #{item.createGlobalId}, #{item.updateGlobalId},
      now(), #{item.updateTime}, #{item.productProType}, #{item.isExpression}, #{item.ordTrade},
      #{item.tradeId}, #{item.tradeName}, #{item.isSearchCondition}, #{item.originId},
      #{item.name,jdbcType=VARCHAR}, #{item.typeCode,jdbcType=VARCHAR}, #{item.option,jdbcType=VARCHAR},
      #{item.unit,jdbcType=VARCHAR}, #{item.expressionCode,jdbcType=VARCHAR}, #{item.isExpressionDefault,jdbcType=INTEGER},
      #{item.qyCodeOld,jdbcType=VARCHAR}, #{item.qyFlag,jdbcType=INTEGER}
      )
    </foreach>
    ON DUPLICATE KEY UPDATE
    expression_id = VALUES(expression_id),
    feature_level = VALUES(feature_level),
    is_sync = VALUES(is_sync),
    type = VALUES(type),
    project_type = VALUES(project_type),
    is_using = VALUES(is_using),
    is_default = VALUES(is_default),
    is_required = VALUES(is_required),
    remark = VALUES(remark),
    is_from_system = VALUES(is_from_system),
    zb_project_feature_id = VALUES(zb_project_feature_id),
    customer_code = VALUES(customer_code),
    is_deleted = VALUES(is_deleted),
    create_global_id = VALUES(create_global_id),
    update_global_id = VALUES(update_global_id),
    create_time = VALUES(create_time),
    update_time = VALUES(update_time),
    product_pro_type = VALUES(product_pro_type),
    is_expression = VALUES(is_expression),
    ord_trade = VALUES(ord_trade),
    trade_id = VALUES(trade_id),
    trade_name = VALUES(trade_name),
    is_search_condition = VALUES(is_search_condition),
    origin_id = VALUES(origin_id),
    `name` = VALUES(`name`),
    type_code = VALUES(type_code),
    `option` = VALUES(`option`),
    unit = VALUES(unit),
    expression_code = VALUES(expression_code),
    is_expression_default = VALUES(is_expression_default),
    qy_code_old = VALUES(qy_code_old),
    qy_flag = VALUES(qy_flag)
  </insert>

  <select id="selectAllGlobalIds" resultType="java.lang.Long">
    select distinct self_global_id from zb_project_feature_standards_self where  customer_code = #{customerCode} and is_deleted = 0 AND invalid = 0;
  </select>

  <select id="selectAllNoBlobByCustomCode" resultMap="BaseResultMap">
    select
    <include refid="No_Blob_Column_List" />
    from zb_project_feature_standards_self
    where customer_code = #{customerCode} AND invalid = 0
    <if test="type != null" >
      and `type` = #{type,jdbcType=INTEGER}
    </if>
  </select>

  <update id="batchUpdateSelective" parameterType="list">
    <foreach collection="list" index="index" item="item" separator=";">
      update zb_project_feature_standards_self
      <set >
        <if test="item.expressionId != null" >
          expression_id = #{item.expressionId,jdbcType=BIGINT},
        </if>
        <if test="item.featureLevel != null" >
          feature_level = #{item.featureLevel,jdbcType=VARCHAR},
        </if>
        <if test="item.isSync != null" >
          is_sync = #{item.isSync,jdbcType=BIT},
        </if>
        <if test="item.type != null" >
          `type` = #{item.type,jdbcType=INTEGER},
        </if>
        <if test="item.projectType != null" >
          project_type = #{item.projectType,jdbcType=VARCHAR},
        </if>
        <if test="item.isUsing != null" >
          is_using = #{item.isUsing,jdbcType=BIT},
        </if>
        <if test="item.isDefault != null" >
          is_default = #{item.isDefault,jdbcType=BIT},
        </if>
        <if test="item.isRequired != null" >
          is_required = #{item.isRequired,jdbcType=BIT},
        </if>
        <if test="item.remark != null" >
          remark = #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.isFromSystem != null" >
          is_from_system = #{item.isFromSystem,jdbcType=BIT},
        </if>
        <if test="item.zbProjectFeatureId != null" >
          zb_project_feature_id = #{item.zbProjectFeatureId,jdbcType=BIGINT},
        </if>
        <if test="item.customerCode != null" >
          customer_code = #{item.customerCode,jdbcType=VARCHAR},
        </if>
        <if test="item.isDeleted != null" >
          is_deleted = #{item.isDeleted,jdbcType=BIT},
        </if>
        <if test="item.createGlobalId != null" >
          create_global_id = #{item.createGlobalId,jdbcType=BIGINT},
        </if>
        <if test="item.updateGlobalId != null" >
          update_global_id = #{item.updateGlobalId,jdbcType=BIGINT},
        </if>
        <if test="item.createTime != null" >
          create_time = #{item.createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.updateTime != null" >
          update_time = #{item.updateTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.productProType != null" >
          product_pro_type = #{item.productProType,jdbcType=VARCHAR},
        </if>
        <if test="item.isExpression != null" >
          is_expression = #{item.isExpression,jdbcType=INTEGER},
        </if>
        <if test="item.ordTrade != null" >
          ord_trade = #{item.ordTrade,jdbcType=INTEGER},
        </if>
        <if test="item.tradeId != null" >
          trade_id = #{item.tradeId,jdbcType=BIGINT},
        </if>
        <if test="item.isSearchCondition != null" >
          is_search_condition = #{item.isSearchCondition,jdbcType=INTEGER},
        </if>
        <if test="item.tradeName != null" >
          trade_name = #{item.tradeName,jdbcType=VARCHAR},
        </if>
        <if test="item.originId != null" >
          origin_id = #{item.originId,jdbcType=BIGINT},
        </if>
        <if test="item.name != null" >
          `name` = #{item.name,jdbcType=VARCHAR},
        </if>
        <if test="item.typeCode != null" >
          type_code = #{item.typeCode,jdbcType=VARCHAR},
        </if>
        <if test="item.option != null" >
          `option` = #{item.option,jdbcType=VARCHAR},
        </if>
        <if test="item.unit != null" >
          unit = #{item.unit,jdbcType=VARCHAR},
        </if>
        <if test="item.expressionCode != null" >
          expression_code = #{item.expressionCode,jdbcType=VARCHAR},
        </if>
        <if test="item.isExpressionDefault != null" >
          is_expression_default = #{item.isExpressionDefault,jdbcType=INTEGER},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="selectAllCustomerCodes" resultType="java.lang.String">
    select distinct customer_code from zb_project_feature_standards_self
    where customer_code not in ('-100', 'null', 'test', '-100(old)', '-1', 'standardList_TC_0.data.gldUserId', '测试基础库初始化重复-可删除-企业1', '测试基础库初始化重复-可删除-企业2', 'defaultCustomerCode');
  </select>

  <select id="selectByTradeIds" resultType="com.glodon.qydata.entity.standard.feature.ProjectFeature">
    select *from zb_project_feature_standards_self
    where
    trade_id in
    <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="selectByName" resultMap="BaseResultMap">
    SELECT * FROM zb_project_feature_standards_self zpfs
    WHERE zpfs.customer_code = #{customerCode,jdbcType=VARCHAR}
    AND zpfs.trade_id = #{tradeId,jdbcType=BIGINT}
    AND zpfs.`type` = #{type,jdbcType=INTEGER}
    AND zpfs.name = #{name,jdbcType=VARCHAR}
    AND zpfs.invalid = 0
    and zpfs.is_deleted = 0
  </select>

  <select id="selectRecordCount" resultType="java.lang.Integer">
    select
    count(*)
    from zb_project_feature_standards_self
    where
    customer_code = #{customerCode,jdbcType=VARCHAR}
    AND invalid = 0
    <if test="type != null" >
      AND `type` = #{type,jdbcType=INTEGER}
    </if>
  </select>
</mapper>