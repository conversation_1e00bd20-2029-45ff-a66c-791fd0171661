<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.qydata.mapper.standard.trade.ZbStandardsTradeMapper">
  <resultMap id="BaseResultMap" type="com.glodon.qydata.entity.standard.trade.ZbStandardsTrade">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="trade_code" jdbcType="VARCHAR" property="tradeCode" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="creator_id" jdbcType="BIGINT" property="creatorId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="customer_code" jdbcType="VARCHAR" property="customerCode" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ord" jdbcType="INTEGER" property="ord" />
    <result column="refer_trade" jdbcType="VARCHAR" property="referTrade" />
    <result column="updater_id" jdbcType="BIGINT" property="updaterId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="enabled" jdbcType="TINYINT" property="enabled" />
    <result column="trade_tag_id" jdbcType="BIGINT" property="tradeTagId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, description, trade_code, `type`, creator_id, create_time, customer_code, is_deleted, ord, refer_trade,updater_id,update_time,enabled, trade_tag_id
  </sql>
  <select id="selectById" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/> from zb_standards_trade where id = #{id}
  </select>
  <delete id="deleteById" parameterType="java.lang.Long" >
    delete from zb_standards_trade where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.glodon.qydata.entity.standard.trade.ZbStandardsTrade">
    insert into zb_standards_trade (id, description, trade_code, 
      `type`, creator_id, create_time,
      customer_code, is_deleted, ord, 
      refer_trade,updater_id,update_time,enabled,qy_code_old,qy_flag)
    values (#{id,jdbcType=BIGINT}, #{description,jdbcType=VARCHAR}, #{tradeCode,jdbcType=VARCHAR}, 
      #{type,jdbcType=TINYINT}, #{creatorId,jdbcType=BIGINT}, now(),
      #{customerCode,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, #{ord,jdbcType=INTEGER}, 
      #{referTrade,jdbcType=VARCHAR}, #{updaterId,jdbcType=BIGINT}, now(),#{enabled},
      #{qyCodeOld,jdbcType=VARCHAR}, #{qyFlag,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.glodon.qydata.entity.standard.trade.ZbStandardsTrade">
    insert into zb_standards_trade
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="tradeCode != null">
        trade_code,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="customerCode != null">
        customer_code,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ord != null">
        ord,
      </if>
      <if test="referTrade != null">
        refer_trade,
      </if>
      <if test="updaterId != null">
        updater_id,
      </if>
      <if test="qyCodeOld != null">
        qy_code_old,
      </if>
      <if test="qyFlag != null">
        qy_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="tradeCode != null">
        #{tradeCode,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ord != null">
        #{ord,jdbcType=INTEGER},
      </if>
      <if test="referTrade != null">
        #{referTrade,jdbcType=VARCHAR},
      </if>
      <if test="updaterId != null">
        #{updaterId,jdbcType=BIGINT},
      </if>
      <if test="qyCodeOld != null">
        #{qyCodeOld,jdbcType=VARCHAR},
      </if>
      <if test="qyFlag != null">
        #{qyFlag,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <insert id="batchInsert">
    insert into zb_standards_trade (id, description, trade_code,
    `type`, creator_id, create_time,
    customer_code, is_deleted, ord,
    refer_trade,updater_id, qy_code_old, qy_flag)
    VALUES
    <foreach collection="list" index="index" separator="," item="item" >
      (
      #{item.id,jdbcType=BIGINT}, #{item.description,jdbcType=VARCHAR}, #{item.tradeCode,jdbcType=VARCHAR},
      #{item.type,jdbcType=TINYINT}, #{item.creatorId,jdbcType=BIGINT}, now(),
      #{item.customerCode,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=TINYINT}, #{item.ord,jdbcType=INTEGER},
      #{item.referTrade,jdbcType=VARCHAR},#{item.updaterId,jdbcType=BIGINT},
      #{item.qyCodeOld,jdbcType=VARCHAR}, #{item.qyFlag,jdbcType=INTEGER}
      )
    </foreach>
  </insert>
    <delete id="deleteByCustomerCode" parameterType="java.lang.String">
      delete from zb_standards_trade where customer_code = #{customerCode}
    </delete>
    <select id="selectListByCustomerCodeAndType" resultMap="BaseResultMap">
    select  <include refid="Base_Column_List"/> from zb_standards_trade where customer_code = #{customerCode} and `type` = #{type} and is_deleted = 0 and invalid = 0
  </select>
  <select id="selectListByCusAndReferTradeCode" resultMap="BaseResultMap">
    select  <include refid="Base_Column_List"/> from zb_standards_trade where customer_code = #{customerCode} and refer_trade = #{referTradeCode} and invalid = 0 order by trade_code ASC
  </select>
  <select id="selectListByCusAndTradeCode" resultMap="BaseResultMap">
    select  <include refid="Base_Column_List"/> from zb_standards_trade where customer_code = #{customerCode} and trade_code = #{tradeCode} and invalid = 0 order by trade_code ASC
  </select>
  <update id="updateTrade" parameterType="com.glodon.qydata.entity.standard.trade.ZbStandardsTrade">
    update zb_standards_trade
    <set >
      <if test="description != null" >
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="tradeCode != null" >
        trade_code = #{tradeCode,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="creatorId != null" >
        creator_id = #{creatorId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null" >
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerCode != null" >
        customer_code = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null" >
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ord != null" >
        ord = #{ord,jdbcType=INTEGER},
      </if>
      <if test="referTrade != null" >
        refer_trade = #{referTrade,jdbcType=VARCHAR},
      </if>
      <if test="updaterId != null" >
        updater_id = #{updaterId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateTradeIsDeleted">
    update zb_standards_trade set is_deleted = #{isDeleted,jdbcType=TINYINT},updater_id = #{updaterId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="searchRecordsByCusCodeAndName" resultMap="BaseResultMap" parameterType="java.lang.String">
    select <include refid="Base_Column_List"/>
    FROM zb_standards_trade
    where is_deleted  = 0 and description = BINARY #{description} and customer_code = #{customerCode} and invalid = 0 limit 1
  </select>
  <select id="selectListByCustomerCode" resultMap="BaseResultMap" parameterType="java.lang.String">
    select <include refid="Base_Column_List"/>
    FROM zb_standards_trade
    where is_deleted  = 0 and  customer_code = #{customerCode} and invalid = 0 order by ord ASC
  </select>

  <select id="selectAllListByCustomerCode" resultMap="BaseResultMap" parameterType="java.lang.String" useCache="false" flushCache="true">
    select <include refid="Base_Column_List"/>
    FROM zb_standards_trade
    where customer_code = #{customerCode} and invalid = 0 order by ord ASC
  </select>

  <select id="selectDescriptionListByIds" resultMap="BaseResultMap">
    select id, description
    FROM zb_standards_trade
    where
    <foreach collection="tradeIds" index="index" item="item" separator="," open="id IN (" close=")">
      #{item}
    </foreach>
    and customer_code = #{customerCode} and invalid = 0
  </select>

  <update id="batchUpdateOrd" parameterType="list">
    <foreach collection="list" index="index" item="item" separator=";">
      update zb_standards_trade set `ord` = #{item.ord, jdbcType=INTEGER}
      where id = #{item.id, jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="batchUpdateEnabled" parameterType="list">
    <foreach collection="list" index="index" item="item" separator=";">
      update zb_standards_trade set enabled = #{item.enabled,jdbcType=TINYINT}, trade_tag_id = #{item.tradeTagId,jdbcType=BIGINT}
      where id = #{item.id, jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>