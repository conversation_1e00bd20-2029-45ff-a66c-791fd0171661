<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.qydata.mapper.standard.buildStandard.ZbStandardsBuildCategoryMapper">

    <resultMap id="BaseResultMap" type="com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildCategory">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="standardId" column="standard_id" jdbcType="BIGINT"/>
            <result property="categoryCode" column="category_code" jdbcType="VARCHAR"/>
            <result property="enterpriseId" column="enterprise_id" jdbcType="BIGINT"/>
            <result property="ord" column="ord" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="originId" column="origin_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,standard_id,category_code,enterprise_id,
        ord,remark,
        create_time,update_time
    </sql>
    <sql id="Temp_Column_List">
        id,standard_id,category_code,
        ord,remark,origin_id,
        create_time,update_time
    </sql>
    <insert id="batchSaveSelf">
        insert into zb_standards_build_category_self
        (id,standard_id,category_code,origin_id,ord,remark)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id},#{item.standardId},#{item.categoryCode},#{item.originId},#{item.ord},#{item.remark}
            )
        </foreach>
    </insert>
    <insert id="batchSave">
        insert into zb_standards_build_category
        (id,standard_id,category_code,enterprise_id,ord,remark)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id},#{item.standardId},#{item.categoryCode},
            #{item.enterpriseId},#{item.ord},#{item.remark}
            )
        </foreach>
    </insert>
    <select id="selectByStandardId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from zb_standards_build_category where standard_id = #{standardId}
    </select>

    <select id="selectSelfByStandardId" resultMap="BaseResultMap">
        select <include refid="Temp_Column_List"/> from zb_standards_build_category_self where standard_id = #{standardId}
    </select>
    <delete id="delSelfByStandardId">
        delete from zb_standards_build_category_self where standard_id = #{standardId}
    </delete>

    <delete id="delByStandardIds">
        delete from zb_standards_build_category where standard_id in
        <foreach collection="standardIds" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </delete>

    <delete id="delSelfByStandardIds">
        delete from zb_standards_build_category_self where standard_id in
        <foreach collection="standardIds" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </delete>
    <select id="selectSelfByStandardIds" resultMap="BaseResultMap">
        select <include refid="Temp_Column_List"/> from zb_standards_build_category_self where standard_id in
        <foreach collection="list" separator="," open="(" close=")" item="item" >
            #{item}
        </foreach>
    </select>
    <select id="selectByStandardIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from zb_standards_build_category
        where standard_id in
        <foreach collection="standardIds" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        order by `ord`
    </select>
</mapper>
