<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.qydata.mapper.standard.buildStandard.ZbStandardsBuildStandardDetailDescMapper">

    <resultMap id="BaseResultMap" type="com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildStandardDetailDesc">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="detailDesc" column="detail_desc" jdbcType="VARCHAR"/>
            <result property="standardId" column="standard_id" jdbcType="BIGINT"/>
            <result property="standardDetailId" column="standard_detail_id" jdbcType="BIGINT"/>
            <result property="code" column="code" jdbcType="VARCHAR"/>
            <result property="typeCode" column="type_code" jdbcType="VARCHAR"/>
            <result property="selectList" column="select_list" jdbcType="VARCHAR"/>
            <result property="tradeId" column="trade_id" jdbcType="BIGINT"/>
            <result property="tradeName" column="trade_name" jdbcType="VARCHAR"/>
            <result property="ord" column="ord" jdbcType="INTEGER"/>
            <result property="enterpriseId" column="enterprise_id" jdbcType="BIGINT"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,detail_desc,
        standard_id,standard_detail_id,code,
        type_code,select_list,trade_id,
        trade_name,ord,
        remark,create_time,update_time
    </sql>
    <sql id="Temp_Column_List">
        id,name,detail_desc,
        standard_id,standard_detail_id,code,
        type_code,select_list,trade_id,
        trade_name,ord,
        remark,origin_id,create_time,update_time
    </sql>
    <select id="selectByStandardId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from zb_standards_build_standard_detail_desc where standard_id = #{standardId} order by ord asc
    </select>
    <select id="selectSelfByStandardId" resultType="com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildStandardDetailDesc">
        select <include refid="Temp_Column_List"/> from zb_standards_build_standard_detail_desc_self where standard_id = #{standardId} order by ord asc
    </select>

    <select id="selectSelfByStandardIds" resultType="com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildStandardDetailDesc">
        select <include refid="Temp_Column_List"/> from zb_standards_build_standard_detail_desc_self where standard_id in
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectByStandardIds" resultType="com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildStandardDetailDesc">
        select <include refid="Base_Column_List"/> from zb_standards_build_standard_detail_desc where standard_id in
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <insert id="batchSaveSelf">
        insert into zb_standards_build_standard_detail_desc_self
        (id,name,detail_desc,
        standard_id,standard_detail_id,code,
        type_code,select_list,trade_id,
        trade_name,ord,
        remark,origin_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id},#{item.name},#{item.detailDesc},#{item.standardId},
            #{item.standardDetailId},#{item.code},#{item.typeCode},#{item.selectList},
            #{item.tradeId},#{item.tradeName},#{item.ord},#{item.remark},#{item.originId}
            )
        </foreach>
    </insert>
    <insert id="batchSave">
        insert into zb_standards_build_standard_detail_desc
        (id,name,detail_desc,
        standard_id,standard_detail_id,code,
        type_code,select_list,trade_id,
        trade_name,ord,
        remark)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id},#{item.name},#{item.detailDesc},#{item.standardId},
            #{item.standardDetailId},#{item.code},#{item.typeCode},#{item.selectList},
            #{item.tradeId},#{item.tradeName},#{item.ord},#{item.remark}
            )
        </foreach>
    </insert>
    <delete id="batchDelSelf">
        delete from zb_standards_build_standard_detail_desc_self where id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </delete>
    <delete id="batchDelSelfById">
        delete from zb_standards_build_standard_detail_desc_self where id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="batchDelByStandardIds">
        delete from zb_standards_build_standard_detail_desc where standard_id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="batchDelSelfByStandardIds">
        delete from zb_standards_build_standard_detail_desc_self where standard_id in
        <foreach collection="standardIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>
    <select id="selectSelfByDetailIdIAsc" resultMap="BaseResultMap">
        select
        <include refid="Temp_Column_List"/>
        from zb_standards_build_standard_detail_desc_self where standard_detail_id = #{detailId,jdbcType=BIGINT}
        order by ord asc
    </select>

    <update id="batchUpdateOrd" parameterType="list">
        <foreach collection="list" index="index" item="item" separator=";">
            update zb_standards_build_standard_detail_desc_self set ord=#{item.ord, jdbcType=INTEGER}
            <if test="item.updateTime == null">
                ,update_time = null
            </if>
            where id = #{item.id, jdbcType=INTEGER}
        </foreach>
    </update>

    <select id="selectSelfById" resultMap="BaseResultMap">
        select
        <include refid="Temp_Column_List"/>
        from zb_standards_build_standard_detail_desc_self where id = #{id,jdbcType=BIGINT}
    </select>

    <insert id="insertSelf">
        insert into zb_standards_build_standard_detail_desc_self
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="detailDesc != null">
                detail_desc,
            </if>
            <if test="standardId != null">
                standard_id,
            </if>
            <if test="standardDetailId != null">
                standard_detail_id,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="typeCode != null">
                type_code,
            </if>
            <if test="selectList != null">
                select_list,
            </if>
            <if test="tradeId != null">
                trade_id,
            </if>
            <if test="tradeName != null">
                trade_name,
            </if>
            <if test="ord != null">
                ord,
            </if>
            <if test="originId != null">
                origin_id,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="detailDesc != null">
                #{detailDesc,jdbcType=VARCHAR},
            </if>
            <if test="standardId != null">
                #{standardId,jdbcType=BIGINT},
            </if>
            <if test="standardDetailId != null">
                #{standardDetailId,jdbcType=BIGINT},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="typeCode != null">
                #{typeCode,jdbcType=VARCHAR},
            </if>
            <if test="selectList != null">
                #{selectList,jdbcType=VARCHAR},
            </if>
            <if test="tradeId != null">
                #{tradeId,jdbcType=BIGINT},
            </if>
            <if test="tradeName != null">
                #{tradeName,jdbcType=VARCHAR},
            </if>
            <if test="ord != null">
                #{ord,jdbcType=INTEGER},
            </if>
            <if test="originId != null">
                #{originId,jdbcType=BIGINT},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildStandardDetailDesc">
        update zb_standards_build_standard_detail_desc_self
        <set >
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="detailDesc != null">
                detail_desc = #{detailDesc,jdbcType=VARCHAR},
            </if>
            <if test="standardId != null">
                standard_id = #{standardId,jdbcType=BIGINT},
            </if>
            <if test="standardDetailId != null">
                standard_detail_id = #{standardDetailId,jdbcType=BIGINT},
            </if>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="typeCode != null">
                type_code = #{typeCode,jdbcType=VARCHAR},
            </if>
            <if test="selectList != null">
                select_list = #{selectList,jdbcType=VARCHAR},
            </if>
            <if test="tradeId != null">
                trade_id = #{tradeId,jdbcType=BIGINT},
            </if>
            <if test="tradeName != null">
                trade_name = #{tradeName,jdbcType=VARCHAR},
            </if>
            <if test="ord != null">
                ord = #{ord,jdbcType=INTEGER},
            </if>
            <if test="originId != null">
                origin_id = #{originId,jdbcType=BIGINT},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="batchUpdateSelective" parameterType="list">
        <foreach collection="list" index="index" item="item" separator=";">
            UPDATE zb_standards_build_standard_detail_desc
            <set>
                <if test="item.name != null">
                    name = #{item.name,jdbcType=VARCHAR},
                </if>
                <if test="item.detailDesc != null">
                    detail_desc = #{item.detailDesc,jdbcType=VARCHAR},
                </if>
                <if test="item.standardId != null">
                    standard_id = #{item.standardId,jdbcType=BIGINT},
                </if>
                <if test="item.standardDetailId != null">
                    standard_detail_id = #{item.standardDetailId,jdbcType=BIGINT},
                </if>
                <if test="item.code != null">
                    code = #{item.code,jdbcType=VARCHAR},
                </if>
                <if test="item.typeCode != null">
                    type_code = #{item.typeCode,jdbcType=VARCHAR},
                </if>
                <if test="item.selectList != null">
                    select_list = #{item.selectList,jdbcType=VARCHAR},
                </if>
                <if test="item.tradeId != null">
                    trade_id = #{item.tradeId,jdbcType=BIGINT},
                </if>
                <if test="item.tradeName != null">
                    trade_name = #{item.tradeName,jdbcType=VARCHAR},
                </if>
                <if test="item.ord != null">
                    ord = #{item.ord,jdbcType=INTEGER},
                </if>
                <if test="item.enterpriseId != null">
                    enterprise_id = #{item.enterpriseId,jdbcType=BIGINT},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            WHERE id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="batchUpdateSelfSelective" parameterType="list">
        <foreach collection="list" index="index" item="item" separator=";">
            UPDATE zb_standards_build_standard_detail_desc_self
            <set>
                <if test="item.name != null">
                    name = #{item.name,jdbcType=VARCHAR},
                </if>
                <if test="item.detailDesc != null">
                    detail_desc = #{item.detailDesc,jdbcType=VARCHAR},
                </if>
                <if test="item.standardId != null">
                    standard_id = #{item.standardId,jdbcType=BIGINT},
                </if>
                <if test="item.standardDetailId != null">
                    standard_detail_id = #{item.standardDetailId,jdbcType=BIGINT},
                </if>
                <if test="item.code != null">
                    code = #{item.code,jdbcType=VARCHAR},
                </if>
                <if test="item.typeCode != null">
                    type_code = #{item.typeCode,jdbcType=VARCHAR},
                </if>
                <if test="item.selectList != null">
                    select_list = #{item.selectList,jdbcType=VARCHAR},
                </if>
                <if test="item.tradeId != null">
                    trade_id = #{item.tradeId,jdbcType=BIGINT},
                </if>
                <if test="item.tradeName != null">
                    trade_name = #{item.tradeName,jdbcType=VARCHAR},
                </if>
                <if test="item.ord != null">
                    ord = #{item.ord,jdbcType=INTEGER},
                </if>
                <if test="item.originId != null">
                    origin_id = #{item.originId,jdbcType=BIGINT},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            WHERE id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>
