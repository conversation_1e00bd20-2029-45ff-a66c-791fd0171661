<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.qydata.mapper.standard.unit.ZbUnitMapper">
  <resultMap id="BaseResultMap" type="com.glodon.qydata.entity.standard.unit.ZbUnit">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="customer_code" jdbcType="VARCHAR" property="customerCode" />
    <result column="is_editable" jdbcType="TINYINT" property="isEditable"/>
    <result column="creator_id" jdbcType="VARCHAR" property="creatorId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_id" jdbcType="VARCHAR" property="updateId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
  </resultMap>
  <sql id="Base_Column_List">
    id,name,is_deleted,customer_code,is_editable,creator_id,create_time,update_id,update_time,sort
  </sql>
  <insert id="insertSelective" parameterType="com.glodon.qydata.entity.standard.unit.ZbUnit">
    insert into zb_unit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="customerCode != null">
        customer_code,
      </if>
      <if test="isEditable != null">
        is_editable,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="qyCodeOld != null">
        qy_code_old,
      </if>
      <if test="qyFlag != null">
        qy_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="isEditable != null">
        #{isEditable,jdbcType=TINYINT},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="qyCodeOld != null">
        #{qyCodeOld,jdbcType=VARCHAR},
      </if>
      <if test="qyFlag != null">
        #{qyFlag,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="selectByPrimaryKey" parameterType="long" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/>
    FROM zb_unit WHERE id = #{id,jdbcType=BIGINT} AND is_deleted = 0
  </select>

  <select id="selectByCond" parameterType="map" resultMap="BaseResultMap">
    SELECT <include refid="Base_Column_List"/>
    FROM zb_unit
    <where>
      is_deleted = 0
      <if test="id!=null">
        and id = #{id,jdbcType=BIGINT}
      </if>
      <if test="customerCodeList!=null">
        and customer_code in
        <foreach collection="customerCodeList" open="(" item="customerCode" separator="," close=")">
          #{customerCode,jdbcType=VARCHAR}
        </foreach>
      </if>
      <if test="searchText!=null and searchText!=''">
        and name like '%${searchText}%'
      </if>
      <if test="name!=null and name!=''">
        and name = #{name,jdbcType=VARCHAR}
      </if>
    </where>
    order by customer_code,sort
    <if test="offSet==null and limit!=null">
      limit ${limit}
    </if>
    <if test="offSet!=null and limit!=null">
      limit ${offSet},${limit}
    </if>
  </select>
  <select id="selectMaxSort" parameterType="string" resultType="int">
    SELECT IFNULL(max(sort),0) FROM zb_unit where customer_code = #{customerCode,jdbcType=VARCHAR}
  </select>
  <update id="updateByPrimaryKey" parameterType="com.glodon.qydata.entity.standard.unit.ZbUnit">
    UPDATE zb_unit
    <set>
      <if test="name!=null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="updateId!=null">
        update_id = #{updateId,jdbcType=VARCHAR},
      </if>
      update_time = SYSDATE()
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="java.util.List">
    INSERT INTO zb_unit (id, name, is_deleted, customer_code, is_editable, creator_id, create_time, update_id, update_time, sort)
    VALUES
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.name}, #{item.isDeleted}, #{item.customerCode},
      #{item.isEditable}, #{item.creatorId}, #{item.createTime},
      #{item.updateId}, #{item.updateTime}, #{item.sort})
    </foreach>
  </insert>
</mapper>