<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.glodon.qydata.mapper.standard.category.CommonProjCategoryUsedMapper" >
  <resultMap id="BaseResultMap" type="com.glodon.qydata.entity.standard.category.CommonProjectCategoryUsed" >
    <result column="id" property="id" jdbcType="INTEGER" />
    <result column="qy_code" property="qyCode" jdbcType="VARCHAR" />
    <result column="tenantId" property="tenantId" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="INTEGER" />
    <result column="tenantglobalId" property="tenantglobalId" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Base_Column_List" >
    id,qy_code,tenantId,`type`,tenantglobalId
  </sql>

  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_commonprojcategory_standards_used
  </select>

  <insert id="batchInsert" parameterType="java.util.List" >
    insert into tb_commonprojcategory_standards_used (qy_code, tenantId, type, tenantglobalId, qy_code_old, qy_flag) values
    <foreach collection="list" item="item" index="index" separator="," >
      (
      #{item.qyCode},
      #{item.tenantId},
      #{item.type},
      #{item.tenantglobalId},
      #{item.qyCodeOld},
      #{item.qyFlag}
      )
    </foreach>
  </insert>

</mapper>