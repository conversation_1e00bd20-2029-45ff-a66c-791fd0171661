<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.glodon.qydata.mapper.standard.category.CategoryTagMapper">

    <select id="selectByEnterpriseId" resultType="com.glodon.qydata.entity.standard.category.CategoryTag">
        select * from category_tag
        where enterprise_id = #{enterpriseId}
    </select>

    <delete id="deleteByEnterpriseId">
        delete from category_tag
        where enterprise_id = #{enterpriseId}
    </delete>

    <insert id="saveBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into category_tag (id,name,enterprise_id,create_time,ord) values
        <foreach collection="list" item="item" index="index" separator="," >
            (
            #{item.id},
            #{item.name},
            #{item.enterpriseId},
            #{item.createTime},
            #{item.ord}
            )
        </foreach>
    </insert>


</mapper>
