<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.glodon.qydata.mapper.standard.category.CategoryTagEnumRelSelfMapper">

    <select id="selectByEnterpriseId" resultType="com.glodon.qydata.entity.standard.category.CategoryTagEnumRel">--
    SELECT cter.*, ct.name AS tagName, cte.name AS tagEnumName
    FROM category_tag_enum_rel_self cter
             JOIN category_tag_self ct ON cter.tag_id = ct.id
             JOIN category_tag_enum_self cte ON cter.tag_enum_id = cte.id
    WHERE cter.enterprise_id = #{enterpriseId}
    ORDER BY ct.ord ASC, cte.ord ASC
    </select>

    <insert id="saveBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into category_tag_enum_rel_self (id,category_code,tag_id,tag_enum_id,enterprise_id,original_id,create_time,modify_time) values
        <foreach collection="list" item="item" index="index" separator="," >
            (
            #{item.id},
            #{item.categoryCode},
            #{item.tagId},
            #{item.tagEnumId},
            #{item.enterpriseId},
            #{item.originalId},
            #{item.createTime},
            #{item.modifyTime}
            )
        </foreach>
    </insert>

    <delete id="deleteByEnterpriseId">
        delete from category_tag_enum_rel_self where enterprise_id = #{enterpriseId};
    </delete>

    <delete id="deleteByCategoryCode">
        delete from category_tag_enum_rel_self where enterprise_id = #{enterpriseId}
        and category_code in
        <foreach collection="categoryCodeList" item="categoryCode" open="(" separator="," close=")">
            #{categoryCode}
        </foreach>
    </delete>

    <update id="updateById">
        update category_tag_enum_rel_self
        set category_code = #{categoryCode}
        where id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>
