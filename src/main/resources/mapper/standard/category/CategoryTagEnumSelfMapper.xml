<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.glodon.qydata.mapper.standard.category.CategoryTagEnumSelfMapper">

    <select id="selectByTagId" resultType="com.glodon.qydata.entity.standard.category.CategoryTagEnum">
        select * from category_tag_enum_self
        where enterprise_id = #{enterpriseId}
        <if test="tagId != null">
            and tag_id = #{tagId}
        </if>
        order by ord asc
    </select>

    <select id="selectByTagIds" resultType="com.glodon.qydata.entity.standard.category.CategoryTagEnum">
        select * from category_tag_enum_self
        where enterprise_id = #{enterpriseId}
        and tag_id in
        <foreach collection="tagIds" separator="," index="index" item="item" open="(" close=")">
            #{item}
        </foreach>
        order by ord asc
    </select>

    <select id="deleteByTagId">
        delete from category_tag_enum_self
        where enterprise_id = #{enterpriseId}
        <if test="tagId != null">
            and tag_id = #{tagId}
        </if>
    </select>

    <select id="deleteByTagIds">
        delete from category_tag_enum_self
        where enterprise_id = #{enterpriseId}
        and tag_id in
        <foreach collection="tagIds" separator="," index="index" item="item" open="(" close=")">
            #{item}
        </foreach>
    </select>

    <insert id="saveBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into category_tag_enum_self (id,name,enterprise_id,create_time,modify_time,tag_id,original_id, ord) values
        <foreach collection="list" item="item" index="index" separator="," >
            (
            #{item.id},
            #{item.name},
            #{item.enterpriseId},
            #{item.createTime},
            #{item.modifyTime},
            #{item.tagId},
            #{item.originalId},
            #{item.ord}
            )
        </foreach>
    </insert>

</mapper>
