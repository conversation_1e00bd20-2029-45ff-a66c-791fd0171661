<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.glodon.qydata.mapper.sharding.ShardingRouteMapper" >
  <select id="getTableNameByCustomerCode" resultType="com.glodon.qydata.entity.sharding.ShardingRoute">
    SELECT
    id, customer_code, enterprise_id, table_type, current_table_name, create_time, update_time
    FROM sharding_route
    WHERE customer_code = #{customerCode}
    AND table_type = #{tableType} limit 1
  </select>


  <insert id="insertBatch">
    insert into sharding_route
    (customer_code, enterprise_id, table_type, current_table_name, create_time, update_time)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (
        #{item.customerCode},#{item.enterpriseId},
        #{item.tableType},#{item.currentTableName},
        #{item.createTime},#{item.updateTime}
      )
    </foreach>
  </insert>

  <insert id="insert">
    insert into sharding_route
    (customer_code, enterprise_id, table_type, current_table_name, create_time, update_time)
    values (#{customerCode}, #{enterpriseId},
            #{tableType}, #{currentTableName},
            #{createTime}, #{updateTime})
  </insert>


</mapper>
