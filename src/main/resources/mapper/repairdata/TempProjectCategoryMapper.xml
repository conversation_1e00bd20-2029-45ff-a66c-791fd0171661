<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.glodon.qydata.mapper.repairdata.TempProjectCategoryMapper" >
    <select id="selectRepeatRecord" resultType="java.lang.Integer">
        SELECT count(1) as repeatCount, qy_code FROM tb_commonprojcategory_standards
        where qy_code = #{customerCode,jdbcType=VARCHAR} and invalid = 0
        group by qy_code,type,commonprojcategoryid, categoryname having repeatCount>1 limit 1
     </select>
    <select id="selectErroCategoryCode" resultType="java.lang.String">
        select qy_code from tb_commonprojcategory_standards
        where qy_code = #{customerCode,jdbcType=VARCHAR} and commonprojcategoryid != categorycode3 and `level` = 3  and invalid = 0 and
        <foreach collection="types" index="index" item="item" separator="," open="type IN (" close=")">
            #{item}
        </foreach> limit 1
     </select>
    <select id="selectInvalidCategory" resultType="java.lang.String">
        select qy_code from tb_commonprojcategory_standards
        where qy_code = #{customerCode,jdbcType=VARCHAR} and invalid = 0 and
        <foreach collection="types" index="index" item="item" separator="," open="type not IN (" close=")">
            #{item}
        </foreach>
        limit 1
     </select>
    <select id="selectAllCustomerCode" resultType="java.lang.String">
        select distinct qy_code from tb_commonprojcategory_standards where qy_code !='-100' and qy_code is not null
     </select>
    <select id="selectNeedTrimCategory" resultType="java.lang.String">
        select qy_code from tb_commonprojcategory_standards
        where qy_code = #{customerCode,jdbcType=VARCHAR} and invalid = 0
        and commonprojcategoryid ='004001002'
        and categoryname = binary '中型/小型百货 ' and type = 1
        limit 1
    </select>
    <update id="updateCategory" parameterType="com.glodon.qydata.entity.standard.category.CommonProjCategory">
        update tb_commonprojcategory_standards
        <set >
            <if test="categoryname != null" >
                categoryname = #{categoryname,jdbcType=VARCHAR},
            </if>
            <if test="categorycode1 != null" >
                categorycode1 = #{categorycode1,jdbcType=VARCHAR},
            </if>
            <if test="categorycode2 != null" >
                categorycode2 = #{categorycode2,jdbcType=VARCHAR},
            </if>
            <if test="categorycode3 != null" >
                categorycode3 = #{categorycode3,jdbcType=VARCHAR},
            </if>
            <if test="categorycode4 != null" >
                categorycode4 = #{categorycode4,jdbcType=VARCHAR},
            </if>
            <if test="repairCategorycode1 != null" >
                repair_categorycode1 = #{repairCategorycode1,jdbcType=VARCHAR},
            </if>
            <if test="repairCategorycode2 != null" >
                repair_categorycode2 = #{repairCategorycode2,jdbcType=VARCHAR},
            </if>
            <if test="repairCategorycode3 != null" >
                repair_categorycode3 = #{repairCategorycode3,jdbcType=VARCHAR},
            </if>
            <if test="repairCategorycode4 != null" >
                repair_categorycode4 = #{repairCategorycode4,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER} and qy_code = #{qyCode,jdbcType=VARCHAR}
    </update>
    <update id="syncSelfCategory" parameterType="com.glodon.qydata.entity.standard.category.CommonProjCategory">
        update tb_commonprojcategory_standards_self
        <set >
            <if test="categoryname != null" >
                categoryname = #{categoryname,jdbcType=VARCHAR},
            </if>
            <if test="categorycode1 != null" >
                categorycode1 = #{categorycode1,jdbcType=VARCHAR},
            </if>
            <if test="categorycode2 != null" >
                categorycode2 = #{categorycode2,jdbcType=VARCHAR},
            </if>
            <if test="categorycode3 != null" >
                categorycode3 = #{categorycode3,jdbcType=VARCHAR},
            </if>
            <if test="categorycode4 != null" >
                categorycode4 = #{categorycode4,jdbcType=VARCHAR},
            </if>
            <if test="repairCategorycode1 != null" >
                repair_categorycode1 = #{repairCategorycode1,jdbcType=VARCHAR},
            </if>
            <if test="repairCategorycode2 != null" >
                repair_categorycode2 = #{repairCategorycode2,jdbcType=VARCHAR},
            </if>
            <if test="repairCategorycode3 != null" >
                repair_categorycode3 = #{repairCategorycode3,jdbcType=VARCHAR},
            </if>
            <if test="repairCategorycode4 != null" >
                repair_categorycode4 = #{repairCategorycode4,jdbcType=VARCHAR},
            </if>
        </set>
        where origin_id = #{id,jdbcType=INTEGER} and qy_code = #{qyCode,jdbcType=VARCHAR}
    </update>
    <update id="setCategoryInvalid" parameterType="java.lang.Long">
        update tb_commonprojcategory_standards  set invalid = 1 where qy_code = #{customerCode,jdbcType=VARCHAR}  and invalid = 0 and
        <foreach collection="ids" index="index" item="item" separator="," open="id IN (" close=")">
            #{item}
        </foreach>
    </update>
    <update id="setSelfCategoryInvalid" parameterType="java.lang.Long">
        update tb_commonprojcategory_standards_self  set invalid = 1 where qy_code = #{customerCode,jdbcType=VARCHAR}  and invalid = 0 and
        <foreach collection="originIds" index="index" item="item" separator="," open="origin_id IN (" close=")">
            #{item}
        </foreach>
    </update>
</mapper>