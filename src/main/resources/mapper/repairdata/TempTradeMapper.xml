<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.qydata.mapper.repairdata.TempTradeMapper">
    <select id="selectRepeatRecord" resultType="java.lang.Integer">
        select count(1) as repeatCount from zb_standards_trade
        where customer_code = #{customerCode,jdbcType=VARCHAR} and invalid = 0
        group by customer_code,`type`,trade_code, description having repeatCount>1 limit 1
     </select>

    <select id="selectValidTradeId" resultType="java.lang.Long">
      select trade_id, count(1) featureCount from zb_project_feature_standards
      where customer_code = #{customerCode,jdbcType=VARCHAR} and invalid = 0 and is_deleted = 0 and
        <foreach collection="tradeIds" index="index" item="item" separator="," open="trade_id IN (" close=")">
            #{item}
        </foreach>
      group by trade_id order by featureCount desc limit 1
    </select>

    <update id="setTradeInvalId" parameterType="java.lang.Long">
        update zb_standards_trade set invalid = 1 where customer_code = #{customerCode,jdbcType=VARCHAR}  and invalid = 0 and
        <foreach collection="ids" index="index" item="item" separator="," open="id IN (" close=")">
            #{item}
        </foreach>
    </update>

    <select id="selectValidFeature" resultType="java.lang.String">
        select customer_code from zb_project_feature_standards
        where customer_code = #{customerCode,jdbcType=VARCHAR} and invalid =0
        and trade_id not in (select id from zb_standards_trade where customer_code = #{customerCode,jdbcType=VARCHAR}
        and invalid = 0 and is_deleted=0) limit 1
    </select>
    <select id="selectValidFeatureCategoryView" resultType="java.lang.String">
        select customer_code from zb_project_feature_category_view_standards
        where customer_code = #{customerCode,jdbcType=VARCHAR} and invalid =0
        and trade_id not in (select id from zb_standards_trade where customer_code = #{customerCode,jdbcType=VARCHAR}
        and invalid = 0 and is_deleted=0) limit 1
    </select>

    <update id="setFeatureInvalid" parameterType="java.lang.Long">
        update zb_project_feature_standards  set invalid = 2 where customer_code = #{customerCode,jdbcType=VARCHAR} and invalid = 0 and
        <foreach collection="tradeIds" index="index" item="item" separator="," open="trade_id not IN (" close=")">
            #{item}
        </foreach>
    </update>
    <update id="setSelfFeatureInvalid" parameterType="java.lang.Long">
        update zb_project_feature_standards_self  set invalid = 2 where customer_code = #{customerCode,jdbcType=VARCHAR} and invalid = 0 and
        <foreach collection="tradeIds" index="index" item="item" separator="," open="trade_id not IN (" close=")">
            #{item}
        </foreach>
    </update>

    <update id="setCategoryViewInvalid" parameterType="java.lang.Long">
        update zb_project_feature_category_view_standards  set invalid = 1 where customer_code = #{customerCode,jdbcType=VARCHAR} and invalid = 0 and
        <foreach collection="tradeIds" index="index" item="item" separator="," open="trade_id not IN (" close=")">
            #{item}
        </foreach>
    </update>
    <update id="setSelfCategoryViewInvalid" parameterType="java.lang.Long">
        update zb_project_feature_category_view_standards_self  set invalid = 1 where customer_code = #{customerCode,jdbcType=VARCHAR} and invalid = 0 and
        <foreach collection="tradeIds" index="index" item="item" separator="," open="trade_id not IN (" close=")">
            #{item}
        </foreach>
    </update>

    <select id="selectGlobalIds" resultType="java.lang.Long">
        select distinct self_global_id from  zb_project_feature_standards_self where customer_code = #{customerCode,jdbcType=VARCHAR}
    </select>

</mapper>
