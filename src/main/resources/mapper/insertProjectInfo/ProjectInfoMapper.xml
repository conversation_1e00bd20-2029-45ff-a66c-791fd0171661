<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.qydata.insertprojectinfo.mapper.ProjectInfoMapper">
    <update id="updateInfo">
        <foreach collection="updateList" item="item" separator=";">
            update ${tableName}
            set unit = #{item.unit}, is_expression = #{item.isExpression}, remark = #{item.remark}
            where id = #{item.id}
        </foreach> 
    </update>
    <update id="updateOriginIdOfSelf">
        <foreach collection="updateList" item="item" separator=";">
            update zb_standards_project_info_self
            set origin_id = #{item.originId}
            where id = #{item.id}
        </foreach>
    </update>

    <select id="searchUsersWithInvalidProjectInfo" resultType="com.glodon.qydata.insertprojectinfo.entity.Global">
        select customer_code as customerCode, global_id as globalId
        from zb_standards_project_info_self
        where standard_data_type = 1
        group by customer_code,global_id
        having count(id) &lt; 24
    </select>

    <select id="searchCustomerCodeWithInvalidProjectInfo" resultType="java.lang.String">
        select customer_code from zb_standards_project_info where standard_data_type = 1 group by customer_code having count(id) = 22
    </select>

    <select id="searchCustomerCodeFromProjectInfo" resultType="java.lang.String">
        select distinct customer_code
        from zb_standards_project_info
    </select>

    <select id="queryProjectNameCount" resultType="java.lang.Integer">
        select count(*) from zb_standards_project_info where customer_code = #{customerCode} and name = '项目名称'
    </select>

    <select id="querySelfProjectNameCount" resultType="java.lang.Integer">
        select count(*) from zb_standards_project_info_self
        where customer_code = #{customerCode} and global_id = #{globalId} and name = '项目名称'
    </select>

    <delete id="deleteProjectInfoRecords">
        delete from zb_standards_project_info where customer_code in
        <foreach collection="customerCodes" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and standard_data_type = 1
    </delete>

    <delete id="deleteSelfProjectInfoRecords">
        <foreach collection="users " separator=";" item="item">
            delete from zb_standards_project_info_self
            where customer_code = #{item.customerCode} and global_id = #{item.globalId} and standard_data_type = 1
        </foreach>
    </delete>

    <select id="searchCustomerCodeAndGlobalIdFromProjectInfoSelf" resultType="com.glodon.qydata.insertprojectinfo.entity.Global">
        select customer_code as customerCode, global_id as globalId
        from zb_standards_project_info_self
        group by customer_code, global_id
    </select>

    <select id="searchConflictCustomerCode" resultType="java.lang.String">
        select distinct customer_code
        from zb_standards_project_info
        where standard_data_type = 1
          and is_deleted = 0
          and name in ('建设用地面积','建筑占地面积','总建筑面积','精装修面积','可售比','总计容面积','规划容积率','建筑密度',
                       '建筑限高','总户数','抗震设防度数','装配率','PC单体建筑预制率','绿色建筑等级','市政道路面积','景观园林面积','园区出入口数量',
                       '绿地率','车位数','车位配比','充电桩数量','耐火等级')
    </select>

    <select id="searchConflictCustomerCodeAndGlobalId" resultType="com.glodon.qydata.insertprojectinfo.entity.Global">
        select distinct customer_code as customerCode, global_id as globalId
        from zb_standards_project_info_self
        where standard_data_type = 1
          and is_deleted = 0
          and name in ('建设用地面积','建筑占地面积','总建筑面积','精装修面积','可售比','总计容面积','规划容积率','建筑密度',
                       '建筑限高','总户数','抗震设防度数','装配率','PC单体建筑预制率','绿色建筑等级','市政道路面积','景观园林面积','园区出入口数量',
                       '绿地率','车位数','车位配比','充电桩数量','耐火等级')
    </select>

    <select id="searchMaxOrdFromProjectInfo" resultType="java.lang.Integer">
        select max(ord) from zb_standards_project_info
    </select>

    <select id="searchMaxOrdFromProjectInfoSelf" resultType="java.lang.Integer">
        select max(ord) from zb_standards_project_info_self
    </select>

    <select id="searchConflictInfo" resultType="com.glodon.qydata.insertprojectinfo.entity.StandardsProjectInfoEntity">
        select *
        from ${tableName}
        where standard_data_type = 1
          and is_deleted = 0
          and name in ('建设用地面积','建筑占地面积','总建筑面积','精装修面积','可售比','总计容面积','规划容积率','建筑密度',
                       '建筑限高','总户数','抗震设防度数','装配率','PC单体建筑预制率','绿色建筑等级','市政道路面积','景观园林面积','园区出入口数量',
                       '绿地率','车位数','车位配比','充电桩数量','耐火等级')
    </select>

    <select id="selectGlobalIdsByCustomerCode" resultType="java.lang.String">
        select distinct global_id as globalId from zb_standards_project_info_self where customer_code = #{customerCode}
    </select>
    <select id="selectCustomerByCondition" resultType="com.glodon.qydata.controller.temp.projectinfo.entity.Prams">
        select id, customer_code, qy_code_old, qy_flag, max(ord) as ord from zb_standards_project_info
        where is_deleted = 0 and standard_data_type = #{standardDataType}
        <if test="customerCodeList != null and customerCodeList.size > 0">
            and customer_code in
            <foreach collection="customerCodeList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="infoNameList != null and infoNameList.size > 0">
            and name in
            <foreach collection="infoNameList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        group by customer_code
    </select>
    <select id="selectSelfCustomerByCondition" resultType="com.glodon.qydata.controller.temp.projectinfo.entity.Prams">
        select id, customer_code, qy_code_old, qy_flag, origin_id, max(ord) as ord from zb_standards_project_info_self
        where is_deleted = 0 and standard_data_type = #{standardDataType}
        <if test="customerCodeList != null and customerCodeList.size > 0">
            and customer_code in
            <foreach collection="customerCodeList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="infoNameList != null and infoNameList.size > 0">
            and name in
            <foreach collection="infoNameList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        group by customer_code
    </select>
    <select id="selectByCondition" resultType="com.glodon.qydata.insertprojectinfo.entity.StandardsProjectInfoEntity">
        select * from zb_standards_project_info_self
        where is_deleted = 0 and standard_data_type = #{standardDataType}
        <if test="customerCodeList != null and customerCodeList.size > 0">
            and customer_code in
            <foreach collection="customerCodeList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <if test="infoNameList != null and infoNameList.size > 0">
            and name in
            <foreach collection="infoNameList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <insert id="projectInfoInsert">
        INSERT INTO zb_standards_project_info
        (id, name, type_code, unit, select_list, is_using, is_required, is_expression, remark, customer_code, is_deleted, ord, standard_data_type, qy_code_old, qy_flag)
        VALUES
        <foreach collection="projectInfoEntities" item="item" separator=",">
            (#{item.id}, #{item.name}, #{item.typeCode}, #{item.unit}, #{item.selectList}, #{item.isUsing}, #{item.isRequired}, #{item.isExpression}, #{item.remark}, #{item.customerCode}, #{item.isDeleted}, #{item.ord}, #{item.standardDataType}, #{item.qyCodeOld}, #{item.qyFlag})
        </foreach>
    </insert>

    <insert id="projectInfoSelfInsert" >
        INSERT INTO zb_standards_project_info_self
        (id, name, type_code, unit, select_list, is_using, is_required, is_expression, remark, customer_code, is_deleted, ord, standard_data_type, global_id, origin_id, qy_code_old, qy_flag)
        VALUES
        <foreach collection="projectInfoEntities" item="item" separator=",">
            (#{item.id}, #{item.name}, #{item.typeCode}, #{item.unit}, #{item.selectList}, #{item.isUsing}, #{item.isRequired}, #{item.isExpression}, #{item.remark}, #{item.customerCode}, #{item.isDeleted}, #{item.ord}, #{item.standardDataType}, #{item.globalId}, #{item.originId}, #{item.qyCodeOld}, #{item.qyFlag})
        </foreach>
    </insert>
</mapper>