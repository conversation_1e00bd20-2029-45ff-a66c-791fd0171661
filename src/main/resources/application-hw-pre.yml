server:
  port: 8083

log:
  level: DEBUG

spring:
  datasource:
    #主数据源
    master:
      url: ******************************************************************************************************************************************************************************************************************************************************************************************
      username: basic_info
      password: lt4EcWgGmta$^zH
#      url: *********************************************************************************************************************************************************************************************************************************************************************
#      username: gldzb_admin
#      password: heLC0Mq%#tDj
      driverClassName: com.mysql.cj.jdbc.Driver
      type: com.alibaba.druid.pool.DruidDataSource
  main:
    allow-bean-definition-overriding: true
  shardingsphere:
    props:
      sql:
        show: false
    datasource:
      names: ds1
      ds1:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: ******************************************************************************************************************************************************************************************************************************************************************************************
        username: basic_info
        password: lt4EcWgGmta$^zH
    sharding:
      tables:
        zb_project_feature_category_view_standards:
          key-generator:
            column: id
            type: SNOWFLAKE
            props:
              worker-id: 123
          actual-data-nodes: ds1.zb_project_feature_category_view_standards,ds1.zb_project_feature_category_view_standards_0
          table-strategy:
            standard:
              precise-algorithm-class-name: com.glodon.qydata.config.CustomShardingAlgorithm
              sharding-column: customer_code
        tb_commonprojcategory_standards:
          actual-data-nodes: ds1.tb_commonprojcategory_standards,ds1.tb_commonprojcategory_standards_0
          table-strategy:
            standard:
              precise-algorithm-class-name: com.glodon.qydata.config.CustomShardingAlgorithm
              sharding-column: qy_code

  #缓存
  redis:
    host: redis-f259ca8b-c4c1-423f-aa25-bcf162d176c4.cn-north-4.dcs.myhuaweicloud.com
    port: 6379
    password: aKrFqUxM9H6VN#vG
#    host: r-m5e47r1mt8wxz24s69pd.redis.rds.aliyuncs.com
#    port: 6379
#    password: JNZP4UsWl49fW00
    timeout: 10000ms
    maxTotal: 300
    jedis:
      pool:
        max-wait: 18000
        max-active: 800
        max-idle: 400
        min-idle: 1
  #上传文件大小设置
  servlet:
    multipart:
      max-file-size: 80MB
      max-request-size: 100MB
config:
  #依赖服务相关配置
  depend:
    #依赖广联云服务域名
    glodonUrl: https://account.glodon.com
    glodonApiAuthUrl: https://api-auth.glodon.com
    glodonEntUrl: https://ent.glodon.com
    #数字成本平台接口URL
    digitalCostApiUrl: http://digital-cost-pre.glodon.com
    #委托服务域名
    trustUrl: https://dcost-sub-pre.glodon.com
    # 指标神器项目划分
    zbsqItemUrl: https://dcost-sub-pre.glodon.com/item
  gcw_cloud:
    APPId: dcost_zblib
    secret: 0L4M99Jl
    secretKey: 1deb69b1411600c5df6f3832b7b96a6b
glodon:
  getUserinfoByIdentityHost: https://account.glodon.com
  middlegroundhost: http://server-gcdp-mbcb.gcost-platform-pre/
  gcw_cloud_url: http://cloud-api.gldjc.com
#IP地址查询，db文件下载地址：https://gitee.com/lionsoul/ip2region/tree/master/data
ip2region:
  path: /opt/project/data/ip2region.db
basic_auth:
  service.key: MiOWRrW6hYBmW6xQE8OGRH2f9I5wfxUG
  server.secret: vG23xiwByuRxKdhAehgKaDEK89jyKzsC

#SpringBoot 内部服务监控 Actuator 暴露端点prometheus 接入MyOps对JVM进行监控
management:
  endpoints:
    web:
      exposure:
        include: health,info,prometheus,metrics
      base-path: /ebq-actuator
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http:
          server:
            requests: true
    tags:
      application: ${artifactId}
# 授权中心服务
apiAuth:
  url: https://api-auth.glodon.com
  appKey: kqIUvqfcH7obwsZRx4U3L3yG5M3sLuRL
  appSecret: EVmzN0G5ebSGh44q9NNhVlwDywvBxSXd
  g-signature: F864378123E9BA92E14E6C7862257FCC

# 当前最新分表名称(新注册企业入当前最新分表)
sharding_table:
  current_table_name: "{0:'zb_project_feature_category_view_standards_0', 1:'tb_commonprojcategory_standards_0'}"

sequence:
  defaultCacheSize: 1000
  defaultIntegerIdStartValue: 100000000