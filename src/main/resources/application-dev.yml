server:
  port: 8083

log:
  level: DEBUG

spring:
  datasource:
    # ShardingSphere 5.3.2 Java API配置 (当privateDeployment=false时使用)
    # 不再使用外部YAML配置，改为Java API手动创建分片数据源
    # 普通数据源配置 (当privateDeployment=true时使用)
    master:
      url: *******************************************************************************************************************************************************************************************************************************************************************************
      username: basic_info
      password: 4jRirpksDJQfb0v
      driverClassName: com.mysql.cj.jdbc.Driver
      type: com.alibaba.druid.pool.DruidDataSource
  main:
    allow-bean-definition-overriding: true
  shardingsphere:
    props:
      sql:
        show: true
    datasource:
      names: ds1
      ds1:
        type: com.alibaba.druid.pool.DruidDataSource
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: *******************************************************************************************************************************************************************************************************************************************************************************
        username: basic_info
        password: 4jRirpksDJQfb0v
  data:
    #缓存
    redis:
      host: ***********
      port: 6379
      password:
      timeout: 10000ms
      maxTotal: 300
      jedis:
        pool:
          max-wait: 18000
          max-active: 800
          max-idle: 400
          min-idle: 1
  #上传文件大小设置
  servlet:
    multipart:
      max-file-size: 80MB
      max-request-size: 100MB

config:
  #依赖服务相关配置
  depend:
    #依赖广联云服务域名
    glodonUrl: https://account-test.glodon.com
    glodonApiAuthUrl: https://api-auth-aetest.glodon.com
    glodonEntUrl: https://ent.glodon.com
    #数字成本平台接口URL
    digitalCostApiUrl: http://digital-cost-pre.glodon.com
    #委托服务域名
    trustUrl: https://dcost-sub-test-sprint.glodon.com
    # 指标神器项目划分
    zbsqItemUrl: https://dcost-sub-test-sprint.glodon.com/item
  gcw_cloud:
    APPId: gcw_qy_data_test
    secret: mV1+6Kze
    secretKey: 1d533c999a94c539c4e470615a06c819
glodon:
  getUserinfoByIdentityHost: https://account.glodon.com
  middlegroundhost: http://server-gcdp-mbcb.gcost-platform-sprint
  gcw_cloud_url: http://cloud-api.gldjc.com
#IP地址查询，db文件下载地址：https://gitee.com/lionsoul/ip2region/tree/master/data
ip2region:
  path: D:\develop\config\ipRegion\ip2region.db
basic_auth:
  service.key: E6xtqZdaM3D0no2jG9Q6p5yisfh4WmJJ
  server.secret: LgseozdKrctNuttrEFh8mSIiTGjHXTth
# 授权中心服务
apiAuth:
  url: https://api-auth-aetest.glodon.com
  appKey: kqIUvqfcH7obwsZRx4U3L3yG5M3sLuRL
  appSecret: EVmzN0G5ebSGh44q9NNhVlwDywvBxSXd
  g-signature: F864378123E9BA92E14E6C7862257FCC


# 当前最新分表名称(新注册企业入当前最新分表)
sharding_table:
  current_table_name: "{0:'zb_project_feature_category_view_standards_0', 1:'tb_commonprojcategory_standards_0'}"


sequence:
  defaultCacheSize: 1000
  defaultIntegerIdStartValue: 1