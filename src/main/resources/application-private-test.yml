apiAuth:
  appKey: kqIUvqfcH7obwsZRx4U3L3yG5M3sLuRL
  g-signature: F864378123E9BA92E14E6C7862257FCC
  url: https://api-auth.glodon.com
basic_auth:
  server:
    secret: vG23xiwByuRxKdhAehgKaDEK89jyKzsC
  service:
    key: MiOWRrW6hYBmW6xQE8OGRH2f9I5wfxUG
config:
  depend:
    digitalCostApiUrl: ${ditital.cost.url}
    glodonApiAuthUrl: https://api-auth.glodon.com
    glodonEntUrl: https://ent.glodon.com
    glodonUrl: https://account.glodon.com
    trustUrl: ${dcost.sub.url}
  gcw_cloud:
    APPId: dcost_zblib
    secret: 0L4M99Jl
    secretKey: 1deb69b1411600c5df6f3832b7b96a6b
dcost.sub.url: https://dcost-private.glodon.com:9504
dcost.sub.zbsq.url: https://dcost-private.glodon.com:9505
dcost.url: https://dcost-private.glodon.com
ditital.cost.url: https://dcost-private.glodon.com:9501
gccs.url: https://gccs.glodon.com
glodon:
  gcw_cloud_url: http://cloud-api.gldjc.com
  getUserinfoByIdentityHost: https://account.glodon.com
  middlegroundhost: http://server-gcdp-mbcb.gcost-platform-pd
ip2region:
  path: /opt/project/data/ip2region.db
log:
  level: INFO
privateDeployment: true
sendIoLog: false
sendLogDebug: false
server:
  port: 8083
spring:
  application:
    name: qydata-basic-info
  banner:
    location: banner.txt
  datasource:
    master:
      driverClassName: com.mysql.cj.jdbc.Driver
      initial-size: 5
      max-active: 500
      min-idle: 10
      password: h4D!P4hV^Lo#jHdr
      type: com.alibaba.druid.pool.DruidDataSource
      url: ****************************************************************************************************************************************************************************************************************************************************************************************************************
      username: root
  redis:
    host: redis-0.redis.glodon-dcost-private-public
    jedis:
      pool:
        max-active: 800
        max-idle: 400
        max-wait: 18000
        min-idle: 1
    maxTotal: 300
    password: c&HR@R*vZXAx63n*
    port: 6379
    timeout: 10000ms
  servlet:
    multipart:
      max-file-size: 80MB
      max-request-size: 100MB
xmgl.api.url: https://pm-private-api.glodon.com
xmgl.url: https://pm-private.glodon.com
zjy.url: https://dcost-private.glodon.com:9502
