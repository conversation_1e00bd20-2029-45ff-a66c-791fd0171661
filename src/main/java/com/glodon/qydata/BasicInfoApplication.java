package com.glodon.qydata;

import com.glodon.qydata.config.CustomImportSelector;
import com.glodon.qydata.service.system.DataCleanupService;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;

@Slf4j
@SpringBootApplication
@Import(CustomImportSelector.class)
//@EnableDiscoveryClient
@EnableFeignClients
@MapperScan({"com.glodon.qydata.insertprojectinfo.mapper", "com.glodon.qydata.mapper"})
@EnableAspectJAutoProxy(exposeProxy = true)
@EnableScheduling
public class BasicInfoApplication extends SpringBootServletInitializer {

    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder builder) {
        return builder.sources(BasicInfoApplication.class);
    }

    public static void main(String[] args) {
        ConfigurableApplicationContext run = SpringApplication.run(BasicInfoApplication.class, args);
        log.info("-----------Glodon Qydata BasicInfo Project Start Success.-------------");
        //run.getBean(DataCleanupService.class).performDataCleanup(Boolean.FALSE);
        //run.getBean(DataCleanupService.class).performTradeDataCleanup(Boolean.FALSE);
        //run.getBean(DataCleanupService.class).performMainQuantityDataCleanup(Boolean.FALSE);
        //run.getBean(DataCleanupService.class).performProjectFeatureDataCleanup(Boolean.FALSE);
        //run.getBean(DataCleanupService.class).performCategoryViewDataCleanup(Boolean.FALSE);
        //run.getBean(DataCleanupService.class).performCategoryViewDataCleanup(Boolean.FALSE);
        //run.getBean(DataCleanupService.class).performExpressionDataCleanup(Boolean.FALSE);
        run.getBean(DataCleanupService.class).performExpressionDataCleanup(Boolean.FALSE);
    }


    @Bean
//	@LoadBalanced
    RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        return restTemplate;
    }
}