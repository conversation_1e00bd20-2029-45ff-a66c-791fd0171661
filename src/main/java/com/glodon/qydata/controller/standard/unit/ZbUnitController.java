package com.glodon.qydata.controller.standard.unit;

import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.common.annotation.BusinessCache;
import com.glodon.qydata.entity.standard.unit.ZbUnit;
import com.glodon.qydata.service.standard.unit.IZbUnitService;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.standard.unit.SearchUnitVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @description: 单位控制器
 * @author: wangq
 * @create: 2022-01-17 14:46
 **/
@Slf4j
@RestController
@RequestMapping("/basicInfo/unit")
@Tag(name = "单位相关接口类", description = "单位相关接口类")
public class ZbUnitController extends BaseController {

    @Autowired
    private IZbUnitService zbUnitService;

    /**
     * @Description 获取单位列表
     * <AUTHOR>
     * @Date 2022/1/17 15:02
     * @param searchUnitVo
     * @return com.glodon.qydata.vo.common.ResponseVo
     **/
    @Operation(summary = "获取单位列表")
    @GetMapping("/getUnitList")
    public ResponseVo getUnitList(SearchUnitVo searchUnitVo){
        try {
            String customerCode = getCustomerCode();
            searchUnitVo.setCustomerCode(customerCode);
            List<ZbUnit> unitList = zbUnitService.getUnitList(searchUnitVo);

            return ResponseVo.success(unitList);
        }catch (Exception e){
            return ResponseVo.error(e.getMessage());
        }
    }

    /**
     * @Description 根据单位id获取单位详细信息
     * <AUTHOR>
     * @Date 2022/1/17 15:12
     * @param unitId
     * @return com.glodon.qydata.vo.common.ResponseVo
     **/
    @Operation(summary = "根据单位id获取单位详细信息")
    @GetMapping("/getDetail")
    public ResponseVo getUnitList(@Validated @NotBlank(message = "单位id不能为空") String unitId){
        try {
            String customerCode = getCustomerCode();
            ZbUnit detail = zbUnitService.getDetail(customerCode, unitId);

            return ResponseVo.success(detail);
        }catch (Exception e){
            return ResponseVo.error(e.getMessage());
        }
    }

    /**
     * @Description 新增单位
     * <AUTHOR>
     * @Date 2022/1/17 15:18
     * @param unitName
     * @return com.glodon.qydata.vo.common.ResponseVo
     **/
    @Operation(summary = "新增单位")
    @PostMapping("/addUnit")
    @BusinessCache(customerCode = "${defaultCustomerCode}", isInvalidateCache = true)
    public ResponseVo addUnit(String unitName){
        try {
            String globalId = getGlobalId();
            return ResponseVo.success(zbUnitService.addUnit(unitName,globalId));
        }catch (Exception e){
            return ResponseVo.error(e.getMessage());
        }
    }

    /**
     * @Description 编辑单位
     * <AUTHOR>
     * @Date 2022/1/27 14:43
     * @param id
     * @param name
     * @return com.glodon.qydata.vo.common.ResponseVo
     **/
    @Operation(summary = "编辑单位")
    @PutMapping("/updateUnit")
    @BusinessCache(customerCode = "${defaultCustomerCode}", isInvalidateCache = true)
    public ResponseVo updateUnit(String id,String name){
        try {
            String globalId = getGlobalId();
            return zbUnitService.updateUnit(id,name,globalId);
        }catch (Exception e){
            return ResponseVo.error(e.getMessage());
        }
    }
}
