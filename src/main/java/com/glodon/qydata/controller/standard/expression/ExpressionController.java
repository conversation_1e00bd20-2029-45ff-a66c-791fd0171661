package com.glodon.qydata.controller.standard.expression;

import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.common.annotation.Permission;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.service.standard.category.impl.CommonProjectCategoryUsedService;
import com.glodon.qydata.service.standard.expression.IExpressionSelfService;
import com.glodon.qydata.service.standard.expression.IExpressionService;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.standard.expression.ExpressionResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.glodon.qydata.common.constant.Constants.WHETHER_FALSE;

/**
 * @description: 计算口径
 * <AUTHOR>
 * @date 2021/11/5 11:24
 */
@RestController
@RequestMapping("/basicInfo/standards/expression")
@Tag(name = "计算口径相关接口类", description = "计算口径相关接口类")
public class ExpressionController extends BaseController {

    @Autowired
    private IExpressionSelfService expressionSelfService;

    @Autowired
    private IExpressionService expressionService;

    @Autowired
    private CommonProjectCategoryUsedService projectCategoryUsedService;
    /**
     * @description: 获取计算口径列表
     * @return com.gcj.common.ResponseVo
     * <AUTHOR>
     * @date 2021/11/5 11:27
     */
    @Operation(summary = "获取计算口径列表")
    @GetMapping
    public ResponseVo<List<ExpressionResultVo>> expressionList() {
        String customerCode = getCustomerCode();
        Integer type = projectCategoryUsedService.getUsedCategoryType(customerCode);
        return ResponseVo.success(expressionService.selectExpression(customerCode, type, WHETHER_FALSE));
    }

    /**
     * @description: 特征页面勾选是否计算口径，加入计算口径字典表
     * @param
     * @return com.gcj.common.ResponseVo<java.lang.String>
     * <AUTHOR>
     * @date 2021/11/8 10:46
     */
    @Operation(summary = "特征页面勾选是否计算口径，加入计算口径字典表")
    @PostMapping
    public ResponseVo<String> add(Long expressionId) {
        long globalId = getGlobalIdLong();
        expressionSelfService.addExpressionDict(expressionId, globalId);
        return ResponseVo.success();
    }

    /**
     * @description: 删除计算口径
     * @return com.gcj.common.ResponseVo
     * <AUTHOR>
     * @date 2021/11/5 16:27
     */
    @Operation(summary = "删除计算口径")
    @Permission
    @DeleteMapping
    public ResponseVo<String> delete(Long expressionId) throws BusinessException {
        expressionSelfService.deleteByPrimaryKey(expressionId);
        return ResponseVo.success();
    }

    /**
     * @description: 修改单位、是否启用
     * @param
     * @return com.gcj.common.ResponseVo<java.lang.String>
     * <AUTHOR>
     * @date 2021/11/8 14:34
     */
    @Operation(summary = "修改单位、是否启用")
    @Permission
    @PutMapping
    public ResponseVo<String> enable(Long expressionId, Integer operate, String unit) throws BusinessException{
        expressionSelfService.enable(expressionId, operate, unit);
        return ResponseVo.success();
    }
}
