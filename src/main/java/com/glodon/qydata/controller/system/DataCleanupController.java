package com.glodon.qydata.controller.system;

import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.service.system.DataCleanupService;
import com.glodon.qydata.vo.common.ResponseVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 数据清理控制器
 * 用于清理个人账号相关的数据
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-31
 */
@Slf4j
@RestController
@RequestMapping("/basicInfo/system/dataCleanup")
@Tag(name = "数据清理管理", description = "个人账号数据清理相关接口")
public class DataCleanupController extends BaseController {

    @Autowired
    private DataCleanupService dataCleanupService;

    /**
     * 执行数据清理操作
     * 
     * @param executeCleanup 是否执行清理操作，true-执行删除，false-仅分析展示
     * @return 清理结果
     */
    @Operation(summary = "执行数据清理操作", description = "清理tb_commonprojcategory_standards和tb_commonprojcategory_standards_0表中的个人账号数据")
    @PostMapping("/performCleanup")
    public ResponseVo<DataCleanupService.CleanupResult> performDataCleanup(
            @Parameter(description = "是否执行清理操作，true-执行删除，false-仅分析展示", required = true)
            @RequestParam(value = "executeCleanup", defaultValue = "false") Boolean executeCleanup) {
        try {
            log.info("接收到数据清理请求，executeCleanup: {}, 操作用户: {}", executeCleanup, getGlobalId());
            // 执行数据清理
            DataCleanupService.CleanupResult result = dataCleanupService.performDataCleanup(executeCleanup);
            log.info("数据清理请求处理完成，executeCleanup: {}, 结果: {}", executeCleanup, result.getMessage());
            return ResponseVo.success(result);
        } catch (Exception e) {
            log.error("数据清理操作失败", e);
            return ResponseVo.error("数据清理操作失败: " + e.getMessage());
        }
    }

    /**
     * 获取数据统计信息（仅分析，不执行清理）
     * 
     * @return 数据统计结果
     */
    @Operation(summary = "获取数据统计信息", description = "获取两张表的数据统计信息，包括总量、分组统计、账号类型分析等")
    @GetMapping("/getStatistics")
    public ResponseVo<DataCleanupService.CleanupResult> getDataStatistics() {
        try {
            log.info("接收到数据统计请求，操作用户: {}", getGlobalId());
            // 仅分析，不执行清理
            DataCleanupService.CleanupResult result = dataCleanupService.performDataCleanup(false);
            log.info("数据统计请求处理完成");
            return ResponseVo.success(result);
        } catch (Exception e) {
            log.error("获取数据统计信息失败", e);
            return ResponseVo.error("获取数据统计信息失败: " + e.getMessage());
        }
    }
}
