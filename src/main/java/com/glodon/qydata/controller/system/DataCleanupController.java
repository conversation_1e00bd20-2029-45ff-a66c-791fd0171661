package com.glodon.qydata.controller.system;

import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.service.system.DataCleanupService;
import com.glodon.qydata.vo.common.ResponseVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 数据清理控制器
 * 用于清理个人账号相关的数据
 * 
 * <AUTHOR> Assistant
 * @date 2025-07-31
 */
@Slf4j
@RestController
@RequestMapping("/basicInfo/system/dataCleanup")
@Tag(name = "数据清理管理", description = "个人账号数据清理相关接口")
public class DataCleanupController extends BaseController {

    @Autowired
    private DataCleanupService dataCleanupService;

    /**
     * 执行数据清理操作
     * 
     * @param executeCleanup 是否执行清理操作，true-执行删除，false-仅分析展示
     * @return 清理结果
     */
    @Operation(summary = "执行数据清理操作", description = "清理tb_commonprojcategory_standards和tb_commonprojcategory_standards_0表中的个人账号数据")
    @PostMapping("/performCleanup")
    public ResponseVo<DataCleanupService.CleanupResult> performDataCleanup(
            @Parameter(description = "是否执行清理操作，true-执行删除，false-仅分析展示", required = true)
            @RequestParam(value = "executeCleanup", defaultValue = "false") Boolean executeCleanup) {
        try {
            log.info("接收到数据清理请求，executeCleanup: {}, 操作用户: {}", executeCleanup, getGlobalId());
            // 执行数据清理
            DataCleanupService.CleanupResult result = dataCleanupService.performCommonProjectCategoryDataCleanup(executeCleanup);
            log.info("数据清理请求处理完成，executeCleanup: {}, 结果: {}", executeCleanup, result.getMessage());
            return ResponseVo.success(result);
        } catch (Exception e) {
            log.error("数据清理操作失败", e);
            return ResponseVo.error("数据清理操作失败: " + e.getMessage());
        }
    }

    /**
     * 获取数据统计信息（仅分析，不执行清理）
     * 
     * @return 数据统计结果
     */
    @Operation(summary = "获取数据统计信息", description = "获取两张表的数据统计信息，包括总量、分组统计、账号类型分析等")
    @GetMapping("/getStatistics")
    public ResponseVo<DataCleanupService.CleanupResult> getDataStatistics() {
        try {
            log.info("接收到数据统计请求，操作用户: {}", getGlobalId());
            // 仅分析，不执行清理
            DataCleanupService.CleanupResult result = dataCleanupService.performCommonProjectCategoryDataCleanup(false);
            log.info("数据统计请求处理完成");
            return ResponseVo.success(result);
        } catch (Exception e) {
            log.error("获取数据统计信息失败", e);
            return ResponseVo.error("获取数据统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 执行 zb_standards_trade 表数据清理操作
     *
     * @param executeCleanup 是否执行清理操作，true-执行删除，false-仅分析展示
     * @return 清理结果
     */
    @Operation(summary = "执行zb_standards_trade表数据清理操作", description = "清理zb_standards_trade表中的个人账号数据")
    @PostMapping("/performTradeCleanup")
    public ResponseVo<DataCleanupService.CustomerCodeCleanupResult> performTradeDataCleanup(
            @Parameter(description = "是否执行清理操作，true-执行删除，false-仅分析展示", required = true)
            @RequestParam(value = "executeCleanup", defaultValue = "false") Boolean executeCleanup) {
        try {
            log.info("接收到zb_standards_trade表数据清理请求，executeCleanup: {}, 操作用户: {}", executeCleanup, getGlobalId());
            // 执行数据清理
            DataCleanupService.CustomerCodeCleanupResult result = dataCleanupService.performTradeDataCleanup(executeCleanup);
            log.info("zb_standards_trade表数据清理请求处理完成，executeCleanup: {}, 结果: {}", executeCleanup, result.getMessage());
            return ResponseVo.success(result);
        } catch (Exception e) {
            log.error("zb_standards_trade表数据清理操作失败，操作用户: {}", getGlobalId(), e);
            return ResponseVo.error("zb_standards_trade表数据清理操作失败: " + e.getMessage());
        }
    }

    /**
     * 获取 zb_standards_trade 表数据统计信息（仅分析，不执行清理）
     *
     * @return 数据统计结果
     */
    @Operation(summary = "获取zb_standards_trade表数据统计信息", description = "获取zb_standards_trade表的数据统计信息，包括总量、分组统计、账号类型分析等")
    @GetMapping("/getTradeStatistics")
    public ResponseVo<DataCleanupService.CustomerCodeCleanupResult> getTradeDataStatistics() {
        try {
            log.info("接收到zb_standards_trade表数据统计请求，操作用户: {}", getGlobalId());
            // 仅分析，不执行清理
            DataCleanupService.CustomerCodeCleanupResult result = dataCleanupService.performTradeDataCleanup(false);
            log.info("zb_standards_trade表数据统计请求处理完成");
            return ResponseVo.success(result);
        } catch (Exception e) {
            log.error("获取zb_standards_trade表数据统计信息失败，操作用户: {}", getGlobalId(), e);
            return ResponseVo.error("获取zb_standards_trade表数据统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 执行 zb_standards_main_quantity 表数据清理操作
     *
     * @param executeCleanup 是否执行清理操作，true-执行删除，false-仅分析展示
     * @return 清理结果
     */
    @Operation(summary = "执行zb_standards_main_quantity表数据清理操作", description = "清理zb_standards_main_quantity表中的个人账号数据")
    @PostMapping("/performMainQuantityCleanup")
    public ResponseVo<DataCleanupService.CustomerCodeCleanupResult> performMainQuantityDataCleanup(
            @Parameter(description = "是否执行清理操作，true-执行删除，false-仅分析展示", required = true)
            @RequestParam(value = "executeCleanup", defaultValue = "false") Boolean executeCleanup) {
        try {
            log.info("接收到zb_standards_main_quantity表数据清理请求，executeCleanup: {}, 操作用户: {}", executeCleanup, getGlobalId());
            // 执行数据清理
            DataCleanupService.CustomerCodeCleanupResult result = dataCleanupService.performMainQuantityDataCleanup(executeCleanup);
            log.info("zb_standards_main_quantity表数据清理请求处理完成，executeCleanup: {}, 结果: {}", executeCleanup, result.getMessage());
            return ResponseVo.success(result);
        } catch (Exception e) {
            log.error("zb_standards_main_quantity表数据清理操作失败，操作用户: {}", getGlobalId(), e);
            return ResponseVo.error("zb_standards_main_quantity表数据清理操作失败: " + e.getMessage());
        }
    }

    /**
     * 获取 zb_standards_main_quantity 表数据统计信息（仅分析，不执行清理）
     *
     * @return 数据统计结果
     */
    @Operation(summary = "获取zb_standards_main_quantity表数据统计信息", description = "获取zb_standards_main_quantity表的数据统计信息，包括总量、分组统计、账号类型分析等")
    @GetMapping("/getMainQuantityStatistics")
    public ResponseVo<DataCleanupService.CustomerCodeCleanupResult> getMainQuantityDataStatistics() {
        try {
            log.info("接收到zb_standards_main_quantity表数据统计请求，操作用户: {}", getGlobalId());
            // 仅分析，不执行清理
            DataCleanupService.CustomerCodeCleanupResult result = dataCleanupService.performMainQuantityDataCleanup(false);
            log.info("zb_standards_main_quantity表数据统计请求处理完成");
            return ResponseVo.success(result);
        } catch (Exception e) {
            log.error("获取zb_standards_main_quantity表数据统计信息失败，操作用户: {}", getGlobalId(), e);
            return ResponseVo.error("获取zb_standards_main_quantity表数据统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 执行 zb_project_feature_standards 表数据清理操作
     *
     * @param executeCleanup 是否执行清理操作，true-执行删除，false-仅分析展示
     * @return 清理结果
     */
    @Operation(summary = "执行zb_project_feature_standards表数据清理操作", description = "清理zb_project_feature_standards表中的个人账号数据")
    @PostMapping("/performProjectFeatureCleanup")
    public ResponseVo<DataCleanupService.CustomerCodeCleanupResult> performProjectFeatureDataCleanup(
            @Parameter(description = "是否执行清理操作，true-执行删除，false-仅分析展示", required = true)
            @RequestParam(value = "executeCleanup", defaultValue = "false") Boolean executeCleanup) {
        try {
            log.info("接收到zb_project_feature_standards表数据清理请求，executeCleanup: {}, 操作用户: {}", executeCleanup, getGlobalId());
            // 执行数据清理
            DataCleanupService.CustomerCodeCleanupResult result = dataCleanupService.performProjectFeatureDataCleanup(executeCleanup);
            log.info("zb_project_feature_standards表数据清理请求处理完成，executeCleanup: {}, 结果: {}", executeCleanup, result.getMessage());
            return ResponseVo.success(result);
        } catch (Exception e) {
            log.error("zb_project_feature_standards表数据清理操作失败，操作用户: {}", getGlobalId(), e);
            return ResponseVo.error("zb_project_feature_standards表数据清理操作失败: " + e.getMessage());
        }
    }

    /**
     * 获取 zb_project_feature_standards 表数据统计信息（仅分析，不执行清理）
     *
     * @return 数据统计结果
     */
    @Operation(summary = "获取zb_project_feature_standards表数据统计信息", description = "获取zb_project_feature_standards表的数据统计信息，包括总量、分组统计、账号类型分析等")
    @GetMapping("/getProjectFeatureStatistics")
    public ResponseVo<DataCleanupService.CustomerCodeCleanupResult> getProjectFeatureDataStatistics() {
        try {
            log.info("接收到zb_project_feature_standards表数据统计请求，操作用户: {}", getGlobalId());
            // 仅分析，不执行清理
            DataCleanupService.CustomerCodeCleanupResult result = dataCleanupService.performProjectFeatureDataCleanup(false);
            log.info("zb_project_feature_standards表数据统计请求处理完成");
            return ResponseVo.success(result);
        } catch (Exception e) {
            log.error("获取zb_project_feature_standards表数据统计信息失败，操作用户: {}", getGlobalId(), e);
            return ResponseVo.error("获取zb_project_feature_standards表数据统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 执行 zb_project_feature_category_view_standards 双表数据清理操作
     *
     * @param executeCleanup 是否执行清理操作，true-执行删除，false-仅分析展示
     * @return 清理结果
     */
    @Operation(summary = "执行zb_project_feature_category_view_standards双表数据清理操作", description = "清理zb_project_feature_category_view_standards双表中的个人账号数据")
    @PostMapping("/performCategoryViewCleanup")
    public ResponseVo<DataCleanupService.CustomerCodeCleanupResult> performCategoryViewDataCleanup(
            @Parameter(description = "是否执行清理操作，true-执行删除，false-仅分析展示", required = true)
            @RequestParam(value = "executeCleanup", defaultValue = "false") Boolean executeCleanup) {
        try {
            log.info("接收到zb_project_feature_category_view_standards双表数据清理请求，executeCleanup: {}, 操作用户: {}", executeCleanup, getGlobalId());
            // 执行数据清理
            DataCleanupService.CustomerCodeCleanupResult result = dataCleanupService.performCategoryViewDataCleanup(executeCleanup);
            log.info("zb_project_feature_category_view_standards双表数据清理请求处理完成，executeCleanup: {}, 结果: {}", executeCleanup, result.getMessage());
            return ResponseVo.success(result);
        } catch (Exception e) {
            log.error("zb_project_feature_category_view_standards双表数据清理操作失败，操作用户: {}", getGlobalId(), e);
            return ResponseVo.error("zb_project_feature_category_view_standards双表数据清理操作失败: " + e.getMessage());
        }
    }

    /**
     * 获取 zb_project_feature_category_view_standards 双表数据统计信息（仅分析，不执行清理）
     *
     * @return 数据统计结果
     */
    @Operation(summary = "获取zb_project_feature_category_view_standards双表数据统计信息", description = "获取zb_project_feature_category_view_standards双表的数据统计信息，包括总量、分组统计、账号类型分析等")
    @GetMapping("/getCategoryViewStatistics")
    public ResponseVo<DataCleanupService.CustomerCodeCleanupResult> getCategoryViewDataStatistics() {
        try {
            log.info("接收到zb_project_feature_category_view_standards双表数据统计请求，操作用户: {}", getGlobalId());
            // 仅分析，不执行清理
            DataCleanupService.CustomerCodeCleanupResult result = dataCleanupService.performCategoryViewDataCleanup(false);
            log.info("zb_project_feature_category_view_standards双表数据统计请求处理完成");
            return ResponseVo.success(result);
        } catch (Exception e) {
            log.error("获取zb_project_feature_category_view_standards双表数据统计信息失败，操作用户: {}", getGlobalId(), e);
            return ResponseVo.error("获取zb_project_feature_category_view_standards双表数据统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 执行 zb_standards_expression 表数据清理操作
     *
     * @param executeCleanup 是否执行清理操作，true-执行删除，false-仅分析展示
     * @return 清理结果
     */
    @Operation(summary = "执行zb_standards_expression表数据清理操作", description = "清理zb_standards_expression表中的个人账号数据")
    @PostMapping("/performExpressionCleanup")
    public ResponseVo<DataCleanupService.CleanupResult> performExpressionDataCleanup(
            @Parameter(description = "是否执行清理操作，true-执行删除，false-仅分析展示", required = true)
            @RequestParam(value = "executeCleanup", defaultValue = "false") Boolean executeCleanup) {
        try {
            log.info("接收到zb_standards_expression表数据清理请求，executeCleanup: {}, 操作用户: {}", executeCleanup, getGlobalId());
            // 执行数据清理
            DataCleanupService.CleanupResult result = dataCleanupService.performExpressionDataCleanup(executeCleanup);
            log.info("zb_standards_expression表数据清理请求处理完成，executeCleanup: {}, 结果: {}", executeCleanup, result.getMessage());
            return ResponseVo.success(result);
        } catch (Exception e) {
            log.error("zb_standards_expression表数据清理操作失败，操作用户: {}", getGlobalId(), e);
            return ResponseVo.error("zb_standards_expression表数据清理操作失败: " + e.getMessage());
        }
    }

    /**
     * 获取 zb_standards_expression 表数据统计信息（仅分析，不执行清理）
     *
     * @return 数据统计结果
     */
    @Operation(summary = "获取zb_standards_expression表数据统计信息", description = "获取zb_standards_expression表的数据统计信息，包括总量、分组统计、账号类型分析等")
    @GetMapping("/getExpressionStatistics")
    public ResponseVo<DataCleanupService.CleanupResult> getExpressionDataStatistics() {
        try {
            log.info("接收到zb_standards_expression表数据统计请求，操作用户: {}", getGlobalId());
            // 仅分析，不执行清理
            DataCleanupService.CleanupResult result = dataCleanupService.performExpressionDataCleanup(false);
            log.info("zb_standards_expression表数据统计请求处理完成");
            return ResponseVo.success(result);
        } catch (Exception e) {
            log.error("获取zb_standards_expression表数据统计信息失败，操作用户: {}", getGlobalId(), e);
            return ResponseVo.error("获取zb_standards_expression表数据统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 执行 zb_standards_build_standard 表组数据清理操作
     *
     * @param executeCleanup 是否执行清理操作，true-执行删除，false-仅分析展示
     * @return 清理结果
     */
    @Operation(summary = "执行zb_standards_build_standard表组数据清理操作", description = "清理zb_standards_build_standard表组中的个人账号数据")
    @PostMapping("/performBuildStandardCleanup")
    public ResponseVo<DataCleanupService.CleanupResult> performBuildStandardDataCleanup(
            @Parameter(description = "是否执行清理操作，true-执行删除，false-仅分析展示", required = true)
            @RequestParam(value = "executeCleanup", defaultValue = "false") Boolean executeCleanup) {
        try {
            log.info("接收到zb_standards_build_standard表组数据清理请求，executeCleanup: {}, 操作用户: {}", executeCleanup, getGlobalId());
            // 执行数据清理
            DataCleanupService.CleanupResult result = dataCleanupService.performBuildStandardDataCleanup(executeCleanup);
            log.info("zb_standards_build_standard表组数据清理请求处理完成，executeCleanup: {}, 结果: {}", executeCleanup, result.getMessage());
            return ResponseVo.success(result);
        } catch (Exception e) {
            log.error("zb_standards_build_standard表组数据清理操作失败，操作用户: {}", getGlobalId(), e);
            return ResponseVo.error("zb_standards_build_standard表组数据清理操作失败: " + e.getMessage());
        }
    }

    /**
     * 获取 zb_standards_build_standard 表组数据统计信息（仅分析，不执行清理）
     *
     * @return 数据统计结果
     */
    @Operation(summary = "获取zb_standards_build_standard表组数据统计信息", description = "获取zb_standards_build_standard表组的数据统计信息，包括总量、分组统计、账号类型分析等")
    @GetMapping("/getBuildStandardStatistics")
    public ResponseVo<DataCleanupService.CleanupResult> getBuildStandardDataStatistics() {
        try {
            log.info("接收到zb_standards_build_standard表组数据统计请求，操作用户: {}", getGlobalId());
            // 仅分析，不执行清理
            DataCleanupService.CleanupResult result = dataCleanupService.performBuildStandardDataCleanup(false);
            log.info("zb_standards_build_standard表组数据统计请求处理完成");
            return ResponseVo.success(result);
        } catch (Exception e) {
            log.error("获取zb_standards_build_standard表组数据统计信息失败，操作用户: {}", getGlobalId(), e);
            return ResponseVo.error("获取zb_standards_build_standard表组数据统计信息失败: " + e.getMessage());
        }
    }
}
