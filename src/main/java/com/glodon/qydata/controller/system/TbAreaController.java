package com.glodon.qydata.controller.system;

import com.glodon.qydata.common.annotation.PassAuth;
import com.glodon.qydata.controller.temp.areaRepair.AreaDiffResult;
import com.glodon.qydata.entity.system.TbArea;
import com.glodon.qydata.service.system.IAreaService;
import com.glodon.qydata.util.SendMessageUtil;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.system.UpdateAreaVO;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/12/22 8:44
 */
@RestController
@RequestMapping("/basicInfo/area")
@Slf4j
@Tag(name = "地区相关接口类", description = "地区相关接口类")
public class TbAreaController {

    @Autowired
    private IAreaService areaService;

    /**
     * 获取地区列表
     * @param scope 地区范围：ALL-所有；ONLY_VALID-仅有效；默认仅有效
     * <AUTHOR>
     * @date 2021/10/15 17:19
     */
    @Operation(summary = "获取所有工程类别信息")
    @GetMapping("/getAllArea")
    public List<TbArea> getAllArea(@RequestParam(required = false, defaultValue = "ONLY_VALID") String scope){
        return areaService.getAllArea(scope);
    }

    /**
     * 根据地区areaId获取下级地区信息,要获取全国信息pid传1
     * @param pid areaId
     * @param scope 地区范围：ALL-所有；ONLY_VALID-仅有效；默认仅有效
     */
    //@ApiOperation(value = "获取地区信息", notes = "根据地区id获取下级地区信息;要获取全国信息pid传1;", response = ResponseVo.class, consumes="application/json;charset=UTF-8")
    @GetMapping(value="/getAreaTree")
    public ResponseVo getAreaTree(String pid, @RequestParam(required = false, defaultValue = "ONLY_VALID") String scope) {
        try{
            List<TbArea> list = areaService.getAreaTree(pid, scope);
            return ResponseVo.success(list);
        }catch(Exception e){
            log.error("获取地区列表错误!", e);
        }
        return ResponseVo.error();
    }

    /**
     * 根据name和level获取地区信息，支持一级与二级
     * @param name level
     * @return
     */
    @Operation(summary = "根据name和level获取地区信息，支持一级与二级")
    @GetMapping("/getAreaByName")
    public TbArea getAreaByName(@RequestParam("name") String name, @RequestParam("level") String level){
        return areaService.getAreaByName(name,null, level);
    }

    /**
     * 根据name和level获取地区信息后续三级按照名称获取地区，使用此接口
     * @param city level
     * @return
     */
    @Operation(summary = "根据name和level获取地区信息后续三级按照名称获取地区，使用此接口")
    @GetMapping("/getAreaByNameThreeLevel")
    public TbArea getAreaByNameThreeLevel(@RequestParam("city") String city, String area, @RequestParam("level")String level){
        return areaService.getAreaByName(city, area,level);
    }


    /**
     * 地区更新机制
     * @param updateAreaVO
     * @return
     */
    @Operation(summary = "地区更新机制")
    @PassAuth
    @PostMapping("/dealArea/{user}")
    public ResponseVo dealArea(@RequestBody UpdateAreaVO updateAreaVO, @PathVariable("user") String user){
        areaService.dealArea(updateAreaVO, user);
        return ResponseVo.success();
    }

    @Operation(summary = "对比地区差异")
    @PassAuth
    @GetMapping("/compareArea")
    public ResponseVo compareArea(){
        // 对比差异
        AreaDiffResult areaDiffResult = areaService.compareHandler();
        // 发送消息
        SendMessageUtil.sendMarkdownMessage(areaDiffResult.toMarkdownMsg());
        return ResponseVo.success(areaDiffResult);
    }
}
