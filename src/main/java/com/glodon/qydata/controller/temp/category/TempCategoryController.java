package com.glodon.qydata.controller.temp.category;

import com.glodon.qydata.common.annotation.PassAuth;
import com.glodon.qydata.mapper.standard.category.CommonProjCategoryMapper;
import com.glodon.qydata.vo.common.ResponseVo;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/basicInfo/dataDeal/category")
public class TempCategoryController {
    @Resource
    private CommonProjCategoryMapper commonProjCategoryMapper;
    @Resource
    private TempCategoryService tempCategoryService;


    @PassAuth
    @GetMapping("/preCheck")
    public ResponseVo preCheck(String customerCodes) {
        Runnable r = () -> {
            List<String> allCustomerCodes;
            if (StringUtils.isNotEmpty(customerCodes)) {
                allCustomerCodes = new ArrayList<>(Arrays.asList(customerCodes.split(",")));
            } else {
                allCustomerCodes = commonProjCategoryMapper.selectAllCustomerCode();
            }
            tempCategoryService.preCheck(allCustomerCodes);
        };
        new Thread(r).start();
        return ResponseVo.success();
    }

    @PassAuth
    @GetMapping("/repairDelStatus")
    public ResponseVo repairDelStatus(@RequestParam String customerCodes) {
        Runnable r = () -> {
            List<String> allCustomerCodes  = new ArrayList<>(Arrays.asList(customerCodes.split(",")));
            tempCategoryService.repairDelStatus(allCustomerCodes);
        };
        new Thread(r).start();
        return ResponseVo.success();
    }
}
