package com.glodon.qydata.controller.temp.feature;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.glodon.qydata.common.annotation.BusinessCache;
import com.glodon.qydata.entity.repairdata.RepairLog;
import com.glodon.qydata.entity.standard.feature.ProjectFeature;
import com.glodon.qydata.mapper.repairdata.TempCommonRepairMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureSelfMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.glodon.qydata.controller.repairdata.common.RepairVersionConst.repair_version_5;


@Service
@Slf4j
public class TempFeatureSaveService {
    @Autowired
    private TempCommonRepairMapper tempCommonRepairMapper;
    @Autowired
    private ProjectFeatureMapper projectFeatureMapper;
    @Autowired
    private ProjectFeatureSelfMapper projectFeatureSelfMapper;

    @Transactional(rollbackFor = Exception.class)
    @BusinessCache(customerCode = "${customerCode}", isInvalidateCache = true)
    public void saveToDb(String customerCode, List<ProjectFeature> featureUpdateList, List<ProjectFeature> selfFeatureUpdateList) {
        if (CollUtil.isNotEmpty(selfFeatureUpdateList)) {
            projectFeatureSelfMapper.batchUpdateSelective(selfFeatureUpdateList);
        }
        if (CollUtil.isNotEmpty(featureUpdateList)) {
            projectFeatureMapper.batchUpdateSelective(featureUpdateList);
        }
        successLog(customerCode);
    }

    public void successLog(String customerCode) {
        RepairLog repairLog = new RepairLog();
        repairLog.setRepairStatus(1);

        tempCommonRepairMapper.update(repairLog, new LambdaQueryWrapper<RepairLog>()
                .eq(RepairLog::getCustomerCode, customerCode)
                .eq(RepairLog::getVersion, repair_version_5));
    }
}
