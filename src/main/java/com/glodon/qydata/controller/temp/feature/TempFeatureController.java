package com.glodon.qydata.controller.temp.feature;

import com.glodon.qydata.common.annotation.PassAuth;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureMapper;
import com.glodon.qydata.vo.common.ResponseVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/basicInfo/dataDeal")
public class TempFeatureController {

    @Autowired
    private TempFeatureService tempFeatureService;

    @Autowired
    private ProjectFeatureMapper projectFeatureMapper;


    @PassAuth
    @GetMapping("/preCheck")
    public ResponseVo preCheck(String customerCodes) {
        Runnable r = () -> {
            Set<String> allCustomerCodes;
            if (StringUtils.isNotEmpty(customerCodes)){
                allCustomerCodes = new HashSet<>(Arrays.asList(customerCodes.split(",")));
            }else{
                allCustomerCodes = projectFeatureMapper.selectAllCustomerCodes();
            }
            tempFeatureService.preCheck(allCustomerCodes);
        };
        new Thread(r).start();
        return ResponseVo.success();
    }

    @PassAuth
    @GetMapping("/projectTypeJsonUpgrade")
    public ResponseVo projectTypeJsonUpgrade(String customerCode) {
        Runnable r = () -> {
            Set<String> allCustomerCodes;
            if (StringUtils.isNotEmpty(customerCode)){
                allCustomerCodes = new HashSet<>(Arrays.asList(customerCode.split(",")));
            }else{
                allCustomerCodes = projectFeatureMapper.selectAllCustomerCodes();
            }
            tempFeatureService.projectTypeJsonUpgrade(allCustomerCodes);
        };
        new Thread(r).start();
        return ResponseVo.success();
    }

    @PassAuth
    @GetMapping("/systemDataProjectTypeJsonUpgrade")
    public ResponseVo systemDataProjectTypeJsonUpgrade() {
        tempFeatureService.systemDataProjectTypeJsonUpgrade();
        return ResponseVo.success();
    }
}
