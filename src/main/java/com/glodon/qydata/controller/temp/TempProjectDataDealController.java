package com.glodon.qydata.controller.temp;

import com.glodon.qydata.controller.temp.expression.TempExpressionDataDealHandler;
import com.glodon.qydata.controller.temp.project.TempProjectDataDealHandler;
import com.glodon.qydata.service.init.ImportBuiltInDataService;
import com.glodon.qydata.util.ExcelUtil;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.init.ExpressionExcelVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 给已经初始化过项目信息的企业，在【工程分类】下方增加一行【产品定位】
 * @date 2022/7/18 14:14
 */
@RestController
@RequestMapping("/basicInfo/dataDeal")
@Slf4j
public class TempProjectDataDealController {

    @Autowired
    private TempProjectDataDealHandler tempProjectDataDealHandler;
    @Autowired
    private TempExpressionDataDealHandler tempExpressionDataDealHandler;
    @Autowired
    private ImportBuiltInDataService importBuiltInDataService;


    @GetMapping("/projectData")
    public ResponseVo<String> projectDataDeal(){
        return ResponseVo.success(tempProjectDataDealHandler.projectDataDeal());
    }

    @GetMapping("/projectDataSelf")
    public ResponseVo<String> projectDataDealSelf(){
        return ResponseVo.success(tempProjectDataDealHandler.projectDataDealSelf());
    }

    /**
     * 已把Excel数据导入到库中，处理内置数据，无需处理
     * @return
     */
    @GetMapping("/systemExpressionDataDeal")
    public ResponseVo<String> systemExpressionDataDeal(){
        return ResponseVo.success(tempExpressionDataDealHandler.systemExpressionDataDeal());
    }

    /**
     * 处理企业发布的数据
     * @return
     */
    @GetMapping("/expressionDataDealALL")
    public ResponseVo<String> expressionDataDealALL(){
        Runnable r = () -> {
            tempExpressionDataDealHandler.expressionDataDeal();
            log.info("---------------处理用户的计算口径数据成功-------------");
            tempExpressionDataDealHandler.expressionDataDealSelf();
            log.info("---------------处理用户暂存的计算口径数据成功----------");
        };
        new Thread(r).start();
        return ResponseVo.success();
    }

    /**
     * 处理企业发布的数据
     * @return
     */
    @GetMapping("/expressionDataDeal")
    public ResponseVo<String> expressionDataDeal(){
        Runnable r = () -> {
            tempExpressionDataDealHandler.expressionDataDeal();
            log.info("---------------处理用户的计算口径数据成功-------------");
        };
        new Thread(r).start();
        return ResponseVo.success();
    }

    /**
     * 处理暂存数据
     * @return
     */
    @GetMapping("/expressionDataDealSelf")
    public ResponseVo<String> expressionDataDealSelf(){
        Runnable r = () -> {
            tempExpressionDataDealHandler.expressionDataDealSelf();
            log.info("---------------处理用户暂存的计算口径数据成功-------------");
        };
        new Thread(r).start();
        return ResponseVo.success();
    }

    /**
     * 修复：对暂存的计算口径排序：内置的在最前面
     * @return
     * <AUTHOR>
     */
    @GetMapping("/reOrderExpressionSelf")
    public ResponseVo reOrderExpressionSelf(){
        Runnable r = () -> {
            tempExpressionDataDealHandler.reOrderExpressionSelf();
            log.info("---------------对暂存的计算口径排序完成-------------");
        };
        new Thread(r).start();
        return ResponseVo.success();
    }

    /**
     * 修复：删除分类视图中多余的计算口径数据
     * （包括暂存，只删除该分类下只有这几个计算口径的数据）
     * @return
     * <AUTHOR>
     */
    @PostMapping("/deleteFeatureCategoryViewUnuseData")
    public ResponseVo deleteFeatureCategoryViewUnuseData(MultipartFile file,String sheetName,String standardTradeName){
        //1、解析文件，获得待导入的数据
        List<ExpressionExcelVo> excelDataList = ExcelUtil.readExcel(file, sheetName, ExpressionExcelVo.class);
        if(CollectionUtils.isEmpty(excelDataList)) return ResponseVo.error("文件 不能为空");

        Runnable r = () -> {
            importBuiltInDataService.deleteFeatureCategoryViewUnuseData(excelDataList,standardTradeName);
            log.info("---------------删除分类视图中多余的计算口径数据完成-------------");
        };
        new Thread(r).start();
        return ResponseVo.success();
    }

}
