package com.glodon.qydata.controller.temp.refreshEnterpriseId;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @className: SysZbUser
 * @description:
 * @date: 20203/7/24
 **/
@Data
@Schema(name = "用户关系表")
public class TempSysZbUser {

    @Schema(name = "ID")
    private Integer id;

    @Schema(name = "广联云globalId")
    private String globalId;

    @Schema(name = "企业编码")
    private String customerCode;

    @Schema(name = "企业编码（新）")
    private String qyCodeNew;

    @Schema(name = "企业标识")
    private Integer qyFlag;
}
