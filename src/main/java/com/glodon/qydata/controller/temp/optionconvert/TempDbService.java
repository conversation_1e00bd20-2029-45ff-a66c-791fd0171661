package com.glodon.qydata.controller.temp.optionconvert;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard;
import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildStandardDetailDesc;
import com.glodon.qydata.entity.standard.feature.ProjectFeature;
import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity;
import com.glodon.qydata.mapper.standard.buildStandard.ZbProjectStandardMapper;
import com.glodon.qydata.mapper.standard.buildStandard.ZbStandardsBuildStandardDetailDescMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureSelfMapper;
import com.glodon.qydata.mapper.standard.projectOrContractInfo.StandardsProjectInfoMapper;
import com.glodon.qydata.util.FormatConvertUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.glodon.qydata.common.constant.BusinessConstants.SELECT_LIST_TO_STR;
import static com.glodon.qydata.common.constant.Constants.StandardsProjectOrContractInfoConstants.CONTRACT_INFO;
import static com.glodon.qydata.common.constant.Constants.StandardsProjectOrContractInfoConstants.PROJECT_INFO;
import static com.glodon.qydata.common.constant.Constants.ZbFeatureConstants.WHETHER_TRUE;
import static com.glodon.qydata.controller.temp.optionconvert.TempOptionConvertService.*;


@Service
@Slf4j
public class TempDbService {
    @Resource
    private StandardsProjectInfoMapper standardsProjectInfoMapper;
    @Resource
    private ProjectFeatureMapper projectFeatureMapper;
    @Resource
    private ProjectFeatureSelfMapper projectFeatureSelfMapper;
    @Resource
    private ZbProjectStandardMapper zbProjectStandardMapper;
    @Resource
    private ZbStandardsBuildStandardDetailDescMapper zbStandardsBuildStandardDetailDescMapper;
    @Resource
    private OptionUpgradeLogService optionUpgradeLogService;

    public Set<String> getCustomerCodeByTable(String type) {
        switch (type) {
            case PROJECT:
                return standardsProjectInfoMapper.selectAllCustomerCodes(PROJECT_INFO);
            case PROJECT_SELF:
                return standardsProjectInfoMapper.selectSelfAllCustomerCodes(PROJECT_INFO);
            case CONTRACT:
                return standardsProjectInfoMapper.selectAllCustomerCodes(CONTRACT_INFO);
            case CONTRACT_SELF:
                return standardsProjectInfoMapper.selectSelfAllCustomerCodes(CONTRACT_INFO);
            case FEATURE:
                return projectFeatureMapper.selectAllCustomerCodes();
            case FEATURE_SELF:
                return projectFeatureSelfMapper.selectAllCustomerCodes();
            case DESC:
                return zbProjectStandardMapper.selectAllCustomerCodes();
            case DESC_SELF:
                return zbProjectStandardMapper.selectSelfAllCustomerCodes();
            default:
                return new HashSet<>();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateByType(String customerCode, String type) {
        List<OptionUpgradeLog> optionUpgradeLogList = buildOptionUpgradeLog(customerCode, type);
        if (CollUtil.isEmpty(optionUpgradeLogList)) {
            log.debug("customerCode:{}, type:{} 无需更新", customerCode, type);
            return;
        }
//        updateToDb(optionUpgradeLogList, type);
        optionUpgradeLogService.saveBatch(optionUpgradeLogList);
    }

    private List<OptionUpgradeLog> buildOptionUpgradeLog(String customerCode, String type) {
        switch (type) {
            case PROJECT:
            case PROJECT_SELF:
            case CONTRACT:
            case CONTRACT_SELF:
                return buildOptionUpgradeLog1(customerCode, type);
            case FEATURE:
            case FEATURE_SELF:
                return buildOptionUpgradeLog2(customerCode, type);
            case DESC:
            case DESC_SELF:
                return buildOptionUpgradeLog3(customerCode, type);
            default:
                return Collections.emptyList();
        }
    }

    private void updateToDb(List<OptionUpgradeLog> optionUpgradeLogList, String type) {
        switch (type) {
            case PROJECT:
            case PROJECT_SELF:
            case CONTRACT:
            case CONTRACT_SELF:
                updateToDb1(optionUpgradeLogList, type);
            case FEATURE:
            case FEATURE_SELF:
                updateToDb2(optionUpgradeLogList, type);
            case DESC:
            case DESC_SELF:
                updateToDb3(optionUpgradeLogList, type);
        }
    }

    private List<OptionUpgradeLog> buildOptionUpgradeLog1(String customerCode, String type) {
        List<StandardsProjectInfoEntity> data = fetchDataByType(customerCode, type);

        if (CollUtil.isEmpty(data)) {
            return Collections.emptyList();
        }

        return data.stream()
                .filter(x -> StringUtils.isNotBlank(x.getSelectList()) && FormatConvertUtil.isSpecifyJson(x.getSelectList()))
                .map(x -> createOptionUpgradeLog(customerCode, type, x.getId(), x.getSelectList()))
                .collect(Collectors.toList());
    }

    private List<StandardsProjectInfoEntity> fetchDataByType(String customerCode, String type) {
        switch (type) {
            case PROJECT:
                return standardsProjectInfoMapper.selectAllPublishData(customerCode, WHETHER_TRUE, PROJECT_INFO);
            case PROJECT_SELF:
                return standardsProjectInfoMapper.selectSelfList(customerCode, WHETHER_TRUE, PROJECT_INFO);
            case CONTRACT:
                return standardsProjectInfoMapper.selectAllPublishData(customerCode, WHETHER_TRUE, CONTRACT_INFO);
            case CONTRACT_SELF:
                return standardsProjectInfoMapper.selectSelfList(customerCode, WHETHER_TRUE, CONTRACT_INFO);
            default:
                return Collections.emptyList();
        }
    }

    private List<OptionUpgradeLog> buildOptionUpgradeLog2(String customerCode, String type) {
        boolean isSelf = FEATURE_SELF.equals(type);
        List<ProjectFeature> data = isSelf
                ? projectFeatureSelfMapper.selectAllNoBlobByCustomCode(customerCode, null)
                : projectFeatureMapper.selectByCustomeCode(customerCode, null, WHETHER_TRUE);

        if (CollUtil.isEmpty(data)) {
            return Collections.emptyList();
        }

        return data.stream()
                .filter(x -> StringUtils.isNotBlank(x.getOption()) && FormatConvertUtil.isSpecifyJson(x.getOption()))
                .map(x -> createOptionUpgradeLog(customerCode, type, x.getId(), x.getOption()))
                .collect(Collectors.toList());
    }

    private List<OptionUpgradeLog> buildOptionUpgradeLog3(String customerCode, String type) {
        boolean isSelf = DESC_SELF.equals(type);
        List<ZbProjectStandard> standards = isSelf
                ? zbProjectStandardMapper.selectSelfByCustomerCode(customerCode, WHETHER_TRUE)
                : zbProjectStandardMapper.selectByCustomerCode(customerCode, WHETHER_TRUE);

        if (CollUtil.isEmpty(standards)) {
            return Collections.emptyList();
        }

        List<Long> standardIds = standards.stream().map(ZbProjectStandard::getId).collect(Collectors.toList());
        List<ZbStandardsBuildStandardDetailDesc> data = isSelf
                ? zbStandardsBuildStandardDetailDescMapper.selectSelfByStandardIds(standardIds)
                : zbStandardsBuildStandardDetailDescMapper.selectByStandardIds(standardIds);

        if (CollUtil.isEmpty(data)) {
            return Collections.emptyList();
        }

        return data.stream()
                .filter(x -> StringUtils.isNotBlank(x.getSelectList()) && FormatConvertUtil.isSpecifyJson(x.getSelectList()))
                .map(x -> createOptionUpgradeLog(customerCode, type, x.getId(), x.getSelectList()))
                .collect(Collectors.toList());
    }

    private OptionUpgradeLog createOptionUpgradeLog(String customerCode, String type, Long linkId, String oldValue) {
        OptionUpgradeLog optionUpgradeLog = new OptionUpgradeLog();

        optionUpgradeLog.setCustomerCode(customerCode);
        optionUpgradeLog.setType(type);
        optionUpgradeLog.setLinkId(linkId);
        optionUpgradeLog.setOldValue(oldValue);
        optionUpgradeLog.setNewValue(convertOldValue(oldValue, linkId));

        return optionUpgradeLog;
    }

    private String convertOldValue(String oldValue, Long linkId) {
        try {
            return FormatConvertUtil.convertSelectList(oldValue, SELECT_LIST_TO_STR);
        } catch (Exception e) {
            throw new RuntimeException("linkId:" + linkId + ", oldValue:" + oldValue);
        }
    }

    private void updateToDb1(List<OptionUpgradeLog> optionUpgradeLogList, String type) {
        List<StandardsProjectInfoEntity> updatedEntityList = optionUpgradeLogList.stream()
                .map(x -> {
                    StandardsProjectInfoEntity updatedEntity = new StandardsProjectInfoEntity();
                    updatedEntity.setId(x.getLinkId());
                    updatedEntity.setSelectList(x.getNewValue());
                    return updatedEntity;
                }).collect(Collectors.toList());

        if (PROJECT_SELF.equals(type) || CONTRACT_SELF.equals(type)) {
            standardsProjectInfoMapper.batchUpdateSelfSelective(updatedEntityList);
        } else {
            standardsProjectInfoMapper.batchUpdateSelective(updatedEntityList);
        }
    }

    private void updateToDb2(List<OptionUpgradeLog> optionUpgradeLogList, String type) {
        List<ProjectFeature> updatedEntityList = optionUpgradeLogList.stream()
                .map(x -> {
                    ProjectFeature updatedEntity = new ProjectFeature();
                    updatedEntity.setId(x.getLinkId());
                    updatedEntity.setOption(x.getNewValue());
                    return updatedEntity;
                }).collect(Collectors.toList());

        if (FEATURE_SELF.equals(type)) {
            projectFeatureSelfMapper.batchUpdateSelective(updatedEntityList);
        } else {
            projectFeatureMapper.batchUpdateSelective(updatedEntityList);
        }
    }

    private void updateToDb3(List<OptionUpgradeLog> optionUpgradeLogList, String type) {
        List<ZbStandardsBuildStandardDetailDesc> updatedEntityList = optionUpgradeLogList.stream()
                .map(x -> {
                    ZbStandardsBuildStandardDetailDesc updatedEntity = new ZbStandardsBuildStandardDetailDesc();
                    updatedEntity.setId(x.getLinkId());
                    updatedEntity.setSelectList(x.getNewValue());
                    return updatedEntity;
                }).collect(Collectors.toList());

        if (DESC_SELF.equals(type)) {
            zbStandardsBuildStandardDetailDescMapper.batchUpdateSelfSelective(updatedEntityList);
        } else {
            zbStandardsBuildStandardDetailDescMapper.batchUpdateSelective(updatedEntityList);
        }
    }

    // 查询已处理的客户代码
    public List<String> getProcessedCustomerCodes(String type) {
        QueryWrapper<OptionUpgradeLog> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("DISTINCT customer_code")
                .eq("type", type);
        return optionUpgradeLogService.listObjs(queryWrapper, Object::toString);
    }
}
