package com.glodon.qydata.controller.temp.project;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * zb_standards_project_info - 主键   todo 线上执行后，可直接删除掉
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("zb_standards_project_info")
@Schema(name="StandardsProjectInfoEntity对象", description="zb_standards_project_info")
public class TempStandardsProjectInfoEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(name = "主键")
    private Long id;

    @Schema(name = "项目信息名")
    private String name;

    @Schema(name = "数据类型，对应dictionary表中type_name为数据类型的记录，text文本类，number数值类，date日期类，select单选类，selects多选类")
    private String typeCode;

    @Schema(name = "单位")
    private String unit;

    @Schema(name = "枚举值")
    private String selectList;

    @Schema(name = "是否已启用，0：未启用，1：已启用")
    private Integer isUsing;

    @Schema(name = "是否必填，0：非必填，1：必填")
    private Integer isRequired;

    @Schema(name = "是否计算口径, 0：否，1：是")
    private Integer isExpression;

    @Schema(name = "创建人")
    private Long creatorId;

    @Schema(name = "创建时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createTime;

    @Schema(name = "更新人")
    private Long updaterId;

    @Schema(name = "更新时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime updateTime;

    @Schema(name = "备注")
    private String remark;

    @Schema(name = "企业编码")
    private String customerCode;

    @Schema(name = "是否已被删除，0：未删除，1：已删除")
    private Integer isDeleted;

    @Schema(name = "排序字段")
    private Integer ord;

    @Schema(name = "标准数据的类型，1：项目信息，2：合同信息")
    private Integer standardDataType;

    @Schema(name = "发布表对应id")
    private Long originId;

    @Schema(name = "数据所属用户id")
    private String globalId;
}
