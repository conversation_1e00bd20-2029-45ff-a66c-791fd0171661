package com.glodon.qydata.controller.temp.project;

import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.controller.temp.TempConstant;
import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity;
import com.glodon.qydata.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description: 在【工程分类】下方增加一行【产品定位】，默认内置枚举值：A档、B档、C档、D档，默认启用和必填        todo 线上执行后，可直接删除掉
 * @date 2022/7/21 13:41
 */
@Service
@Slf4j
public class TempProjectDataDealHandler {

    @Autowired
    private TempProjectDataDealService tempProjectDataDealService;

    @Autowired
    private TempProjectDataDealSelfService tempProjectDataDealSelfService;

    @Autowired
    private RedisUtil redisUtil;

    final ThreadPoolExecutor dealExecutor = new ThreadPoolExecutor(5, 10, 3000, TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>());

    /**
     * @description: 在【工程分类】下方增加一行【产品定位】，默认内置枚举值：A档、B档、C档、D档，默认启用和必填
     * @param
     * @return void
     * <AUTHOR>
     * @date 2022/7/18 18:06
     */
    public String projectDataDeal() {
        String lockValueString = redisUtil.generateLockValue();

        if (!redisUtil.tryLock(lockValueString, RedisKeyEnum.TEMP_PROJECT_DATA_DEAL, TempConstant.TYPE_ENTERPRISE)) {
            return "不要重复调用...";
        }

        try {
            // 获取需要处理的企业
            List<String> allCustomerCodeList = tempProjectDataDealService.getAllCustomerCodeList();

            if (CollectionUtils.isEmpty(allCustomerCodeList)){
                return "没有需要处理的企业...";
            }

            log.info("需要处理的企业数：{}", allCustomerCodeList.size());

            this.threadExecutor(allCustomerCodeList);
        } finally {
            redisUtil.unlock(lockValueString, RedisKeyEnum.TEMP_PROJECT_DATA_DEAL, TempConstant.TYPE_ENTERPRISE);
        }

        log.info("全部处理完成...");
        return "全部处理完成...";
    }

    private void threadExecutor(List<String> allCustomerCodeList){
        //线程监控
        final CountDownLatch latch = new CountDownLatch(allCustomerCodeList.size());

        // 分企业执行
        for (String customerCode : allCustomerCodeList) {
            dealExecutor.execute(() ->{
                try {
                    tempProjectDataDealService.dealData(customerCode);
                }catch (Exception e){
                    log.error("企业：{}-出错了", customerCode, e);
                }finally {
                    latch.countDown();
                }
            });
        }

        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("latch.await出错了", e);
        }
    }

    /**
     * @description: 用户暂存 self---在【工程分类】下方增加一行【产品定位】，默认内置枚举值：A档、B档、C档、D档，默认启用和必填
     * @param
     * @return void
     * <AUTHOR>
     * @date 2022/7/18 18:06
     */
    public String projectDataDealSelf() {
        String lockValueString = redisUtil.generateLockValue();

        if (!redisUtil.tryLock(lockValueString, RedisKeyEnum.TEMP_PROJECT_DATA_DEAL, TempConstant.TYPE_SELF)) {
            return "不要重复调用...";
        }

        try {
            // 获取需要处理的企业
            List<String> allCustomerCodeList = tempProjectDataDealService.getAllCustomerCodeList();

            if (CollectionUtils.isNotEmpty(allCustomerCodeList)){
                return "企业级的数据还没处理完...";
            }

            // 获取需要处理的用户
            List<StandardsProjectInfoEntity> allGlobalIdList = tempProjectDataDealSelfService.getAllGlobalIdList();

            if (CollectionUtils.isEmpty(allGlobalIdList)){
                return "没有需要处理的用户...";
            }

            log.info("需要处理的用户数：{}", allGlobalIdList.size());

            this.threadExecutorSelf(allGlobalIdList);
        } finally {
            redisUtil.unlock(lockValueString, RedisKeyEnum.TEMP_PROJECT_DATA_DEAL, TempConstant.TYPE_SELF);
        }

        log.info("self全部处理完成...");
        return "self全部处理完成...";
    }

    private void threadExecutorSelf(List<StandardsProjectInfoEntity> allGlobalIdList){
//        //线程监控
//        final CountDownLatch latch = new CountDownLatch(allGlobalIdList.size());
//
//        // 获取企业级【产品定位】对应的id
//        Map<String, Long> originIdMap = tempProjectDataDealSelfService.getOriginIdMap();
//
//        // 分用户执行
//        for (StandardsProjectInfoEntity projectInfoEntity : allGlobalIdList) {
//            dealExecutor.execute(() ->{
//                try {
//                    String customerCode = projectInfoEntity.getCustomerCode();
//                    tempProjectDataDealSelfService.dealData(customerCode, projectInfoEntity.getGlobalId(), originIdMap.get(customerCode));
//                }catch (Exception e){
//                    log.error("用户：{}-出错了", projectInfoEntity.getGlobalId(), e);
//                }finally {
//                    latch.countDown();
//                }
//            });
//        }
//
//        try {
//            latch.await();
//        } catch (InterruptedException e) {
//            log.error("latch.await出错了", e);
//        }
    }
}
