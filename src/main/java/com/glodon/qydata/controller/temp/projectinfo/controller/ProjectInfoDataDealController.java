package com.glodon.qydata.controller.temp.projectinfo.controller;

import com.glodon.qydata.controller.temp.projectinfo.service.IProjectInfoDataDealService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 项目信息/合同信息数据处理
 */
@RestController
@RequestMapping("/basicInfo/projectInfoDataDeal")
@Slf4j
public class ProjectInfoDataDealController {

    @Autowired
    private IProjectInfoDataDealService projectInfoDataDealService;

    @GetMapping("/projectDataHandler")
    public void projectDataDeal(@RequestParam List<String> customerCode){
        projectInfoDataDealService.dealProjectInfoDataByCustomerCode(customerCode, 2);
    }
}
