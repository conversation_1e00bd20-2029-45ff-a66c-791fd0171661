package com.glodon.qydata.controller.temp.optionconvert;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName(value = "option_upgrade_log")
public class OptionUpgradeLog implements Serializable {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private String customerCode;
    private String type;
    private Long linkId;
    private String oldValue;
    private String newValue;
    private Integer status;
    private String msg;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
