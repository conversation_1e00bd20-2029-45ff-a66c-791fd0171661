package com.glodon.qydata.controller.temp.expression;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.enums.ExpressionTypeEnum;
import com.glodon.qydata.mapper.temp.TempExpressionMapper;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 刷计算口径数据  todo 线上执行后，可直接删除掉
 * @date 2022/7/22 16:44
 */
@Service
@Slf4j
public class TempExpressionDealService extends ServiceImpl<TempExpressionMapper, TempExpression> {

    @Autowired
    private TempExpressionMapper tempExpressionMapper;

    /**
     * @description: 产品提供的内置数据
     * @param
     * @return void
     * <AUTHOR>
     * @date 2022/7/22 17:03
     */
    public Map<String, TempExpression> getExcelData(){
        QueryWrapper<TempExpression> queryWrapper = new QueryWrapper<>();

        queryWrapper.select("`name`,`unit`,`expression_remark`,expression_is_from_system,expression_ord").lambda()
                        .eq(TempExpression::getQyCode, "systemData_0722");

        List<TempExpression> excelData = tempExpressionMapper.selectList(queryWrapper);

        if (CollectionUtils.isNotEmpty(excelData)){
            return excelData.parallelStream().collect(
                    Collectors.toMap(TempExpression::getName, Function.identity(), (v1, v2) -> v2));
        }

        return null;
    }


    /**
     * @description: 获取数字类型的数据-3份
     * @param
     * @return java.util.Map<java.lang.Integer, java.util.List < com.glodon.qydata.controller.temp.expression.TempExpression>>
     * <AUTHOR>
     * @date 2022/7/22 17:56
     */
    public List<TempExpression> getNumberExpression(String customerCode){
        LambdaQueryWrapper<TempExpression> queryWrapper = new LambdaQueryWrapper<TempExpression>()
                .eq(TempExpression::getQyCode, customerCode)
                .eq(TempExpression::getTypeCode, ExpressionTypeEnum.TYPE_NUMBER.getCode())
                .orderByAsc(TempExpression::getExpressionOrd);

        return tempExpressionMapper.selectList(queryWrapper);
    }

    public Map<Integer, List<TempExpression>> getTypeGroup(String customerCode){
        LambdaQueryWrapper<TempExpression> queryWrapper = new LambdaQueryWrapper<TempExpression>()
                .eq(TempExpression::getQyCode, customerCode)
                .eq(TempExpression::getExpressionIsFromSystem, Constants.ZbFeatureConstants.WHETHER_TRUE)
                .orderByAsc(TempExpression::getExpressionOrd);

        List<TempExpression> tempExpression = tempExpressionMapper.selectList(queryWrapper);

        if (CollectionUtils.isNotEmpty(tempExpression)){
            return tempExpression.stream().collect(Collectors.groupingBy(TempExpression::getType));
        }

        return null;
    }

    /**
     * @description: 获取需要处理的企业
     * @param
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2022/7/24 17:53
     */
    public List<String> getAllCustomerCodeList(Integer systemExpressionSize){
        // 查询已经初始化，需要处理的企业；正式线大约4万家企业
        QueryWrapper<TempExpression> allCustomerCodeQuery = new QueryWrapper<>();
        allCustomerCodeQuery.select("distinct qy_code");
        List<TempExpression> allCustomerCodeList = tempExpressionMapper.selectList(allCustomerCodeQuery);

        QueryWrapper<TempExpression> processedCustomerCodeQuery = new QueryWrapper<>();
        processedCustomerCodeQuery.select("qy_code,COUNT(qy_code) countNum").lambda()
                .eq(TempExpression::getExpressionIsFromSystem, Constants.ZbFeatureConstants.WHETHER_TRUE)
                .groupBy(TempExpression::getQyCode).having("countNum % "+ systemExpressionSize + " = 0");

        List<TempExpression> processedCustomerCode = tempExpressionMapper.selectList(processedCustomerCodeQuery);

        log.info("总：{}，已处理：{}，需要处理：{}", allCustomerCodeList.size(), processedCustomerCode.size(), allCustomerCodeList.size() - processedCustomerCode.size());
        allCustomerCodeList.removeAll(processedCustomerCode);

        if (CollectionUtils.isNotEmpty(allCustomerCodeList)){
            return allCustomerCodeList.parallelStream().map(TempExpression::getQyCode).collect(Collectors.toList());
        }

        return null;
    }

    /**
     * @description: 获取内置的计算口径
     * @param
     * @return java.util.List<com.glodon.qydata.controller.temp.expression.TempExpression>
     * <AUTHOR>
     * @date 2022/7/24 18:06
     */
    public Map<String, TempExpression> getSystemExpression(){
        LambdaQueryWrapper<TempExpression> queryWrapper = new LambdaQueryWrapper<TempExpression>()
                .eq(TempExpression::getQyCode, Constants.ZbFeatureConstants.SYSTEM_CUSTOMER_CODE)
                .eq(TempExpression::getExpressionIsFromSystem, Constants.ZbFeatureConstants.WHETHER_TRUE)
                .eq(TempExpression::getType, 1)
                .orderByAsc(TempExpression::getExpressionOrd);

        List<TempExpression> systemExpression = tempExpressionMapper.selectList(queryWrapper);

        if (CollectionUtils.isNotEmpty(systemExpression)){
            return systemExpression.parallelStream().collect(
                    Collectors.toMap(TempExpression::getName, Function.identity(), (v1, v2) -> v2));
        }

        return null;
    }

    /**
     * @param type
     * @param targetData 系统内置数据
     * @param oldExpressionList 企业的数据
     * @param updateList
     * @param insertList
     */
    public void dataDeal(Integer type, Map<String, TempExpression> targetData, List<TempExpression> oldExpressionList,
                          List<TempExpression> updateList, List<TempExpression> insertList){
        HashMap<String, TempExpression> targetDataCopy = new HashMap<>(targetData.size());
        targetDataCopy.putAll(targetData);
        targetDataCopy = this.clone(targetDataCopy);

        // 用户自定义的计算口径放在内置的后面
        int size = targetDataCopy.size();
        for (String key : targetDataCopy.keySet()) {
            TempExpression e = targetDataCopy.get(key);
            Integer ord = e.getExpressionOrd();
            if(ord!=null && ord.intValue()>size){
                size = ord.intValue();
            }
        }

        for (TempExpression oldExpression : oldExpressionList) {
            String name = oldExpression.getName();
            if (targetDataCopy.containsKey(name)) {
                // name、unit、remark、ord、expression_is_from_system
                TempExpression excelExpression = targetDataCopy.get(name);

                if(Constants.ZbFeatureConstants.WHETHER_TRUE !=oldExpression.getExpressionIsFromSystem().intValue()) {
                    TempExpression updateExpression = new TempExpression();
                    updateExpression.setId(oldExpression.getId());
                    updateExpression.setUnit(excelExpression.getUnit());
                    updateExpression.setExpressionRemark(excelExpression.getExpressionRemark());
                    updateExpression.setExpressionOrd(excelExpression.getExpressionOrd());
                    updateExpression.setIsExpression(Constants.ZbFeatureConstants.WHETHER_TRUE);
                    updateExpression.setExpressionIsFromSystem(excelExpression.getExpressionIsFromSystem());
                    updateExpression.setExpressionIsUsing(Constants.ZbFeatureConstants.WHETHER_TRUE);
                    updateExpression.setExpressionCreateGlobalId(-100L);

                    updateList.add(updateExpression);
                }
                targetDataCopy.remove(name);
            }else{
                Integer isExpression = oldExpression.getIsExpression();
                if (isExpression != null && Constants.ZbExpressionConstants.WHETHER_TRUE == isExpression){

                    TempExpression updateExpression = new TempExpression();
                    updateExpression.setId(oldExpression.getId());
                    updateExpression.setExpressionIsFromSystem(Constants.ZbFeatureConstants.WHETHER_FALSE);
                    updateExpression.setExpressionOrd(++size);

                    updateList.add(updateExpression);
                }
            }
        }

        targetDataCopy.values().forEach(item -> item.setType(type));
        insertList.addAll(targetDataCopy.values());
    }

    @Transactional(rollbackFor = Exception.class)
    public void executeSql(List<TempExpression> updateList, List<TempExpression> insertList, String customerCode){
        if (CollectionUtils.isNotEmpty(updateList)){
            this.updateBatchById(updateList);
        }

        this.batchSaveExpression(insertList, customerCode);
    }

    public void batchSaveExpression(List<TempExpression> insertList, String customerCode){
        if (CollectionUtils.isEmpty(insertList)){
            return;
        }

        int size = insertList.size();
        List<Long> nextId = SnowflakeIdUtils.getNextId(size * 2);

        for (int i = 0; i < insertList.size(); i++) {
            TempExpression insertExpression = insertList.get(i);
            insertExpression.setId(nextId.get(i));
            insertExpression.setQyCode(customerCode);
            insertExpression.setCreateGlobalId(-100L);
            insertExpression.setIsExpression(Constants.ZbFeatureConstants.WHETHER_TRUE);
            insertExpression.setTypeCode(ExpressionTypeEnum.TYPE_NUMBER.getCode());
            insertExpression.setExpressionIsUsing(Constants.ZbFeatureConstants.WHETHER_TRUE);
            insertExpression.setExpressionCreateGlobalId(-100L);
        }

        this.saveBatch(insertList);
    }

    /**
     * 使用对象的序列化进而实现深拷贝
     * @param obj
     * @param <T>
     * @return
     */
    private <T extends Serializable> T clone(T obj) {
        T cloneObj = null;
        try {
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ObjectOutputStream oos = new ObjectOutputStream(bos);
            oos.writeObject(obj);
            oos.close();
            ByteArrayInputStream bis = new ByteArrayInputStream(bos.toByteArray());
            ObjectInputStream ois = new ObjectInputStream(bis);
            cloneObj = (T) ois.readObject();
            ois.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return cloneObj;
    }

}
