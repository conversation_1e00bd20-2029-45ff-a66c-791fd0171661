package com.glodon.qydata.controller.temp.expressionMove;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("feature_record_self")
public class FeatureRecordSelf implements Serializable {

    private static final long serialVersionUID = -2719957245485285827L;

    private Long id;

    /**
     * globalId
     */
    private Long globalId;

    /**
     * 企业编码
     */
    private String customerCode;

    /**
     * expressionToFeature状态
     */
    private Integer toFeatureStatus;

    /**
     * 增加内置状态
     */
    private Integer addStatus;

    public FeatureRecordSelf() {}

    public FeatureRecordSelf(Integer toFeatureStatus, Integer addStatus) {
        this.toFeatureStatus = toFeatureStatus;
        this.addStatus = addStatus;
    }
}