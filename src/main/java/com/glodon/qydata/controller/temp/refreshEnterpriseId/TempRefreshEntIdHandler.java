package com.glodon.qydata.controller.temp.refreshEnterpriseId;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.controller.temp.TempConstant;
import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.mapper.standard.category.CommonProjCategoryMapper;
import com.glodon.qydata.mapper.temp.TempRefreshEntIdMapper;
import com.glodon.qydata.service.system.IGlodonUserService;
import com.glodon.qydata.util.RedisUtil;
import com.glodon.qydata.vo.system.EntInfoVo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description: 在【工程分类】下方增加一行【产品定位】，默认内置枚举值：A档、B档、C档、D档，默认启用和必填        todo 线上执行后，可直接删除掉
 * @date 2022/7/21 13:41
 */
@Service
@Slf4j
@SuppressWarnings("squid:S1192")
public class TempRefreshEntIdHandler {

    @Autowired
    private IGlodonUserService glodonUserService;

    @Autowired
    private TempRefreshEntIdMapper tempRefreshEntIdMapper;

    @Autowired
    private CommonProjCategoryMapper commonProjCategoryMapper;

    @Autowired
    private RedisUtil redisUtil;

    protected static Map<String, String> bigTableMap = new HashMap<>(5);

    static {
        bigTableMap.put("tb_commonprojcategory_standards", "qy_code");
        bigTableMap.put("zb_project_feature_category_view_standards", "customer_code");
        bigTableMap.put("zb_project_feature_standards", "customer_code");
        bigTableMap.put("zb_standards_expression", "qy_code");
        bigTableMap.put("zb_standards_main_quantity", "customer_code");
    }

    final ThreadPoolExecutor dealExecutor = new ThreadPoolExecutor(5, 10, 3000, TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>());

    /**
     * @description: 刷新数据
     * @param
     * <AUTHOR>
     * @date 2023/7/25 18:06
     */
    public void refreshSysZbUser(boolean all) {
        String lockValueString = redisUtil.generateLockValue();
        if (!redisUtil.tryLock(lockValueString, RedisKeyEnum.TEMP_ENT_ID_DEAL_LOCK, TempConstant.TYPE_ENTERPRISE)) {
            return;
        }

        try {
            // 获取需要处理的企业
            List<TempSysZbUser> zbRelations = all ? tempRefreshEntIdMapper.selectAll() : tempRefreshEntIdMapper.selectByEntIdIsNull();
            if (CollectionUtils.isEmpty(zbRelations)) {
                return;
            }

            redisUtil.setString(RedisKeyEnum.TEMP_ENT_ID_DEAL_STATUS, "0", "sysZbUser"); // 开始
            fillEnterpriseId(zbRelations);
            insertEnterpriseRelation(zbRelations);
        } catch (Exception e) {
            redisUtil.setString(RedisKeyEnum.TEMP_ENT_ID_DEAL_STATUS, "-1", "sysZbUser"); // 异常
            e.printStackTrace();
        } finally {
            redisUtil.unlock(lockValueString, RedisKeyEnum.TEMP_ENT_ID_DEAL_LOCK, TempConstant.TYPE_ENTERPRISE);
        }

        redisUtil.setString(RedisKeyEnum.TEMP_ENT_ID_DEAL_STATUS, "1", "sysZbUser"); // 完成
        log.info("全部处理完成...");
    }

    private void fillEnterpriseId(List<TempSysZbUser> zbRelations) {
        log.info("需要处理的数量：{}", zbRelations.size());

        for (TempSysZbUser zbRelation : zbRelations) {
            String globalId = zbRelation.getGlobalId();
            String customerCode = zbRelation.getCustomerCode();

            if (StringUtils.isBlank(globalId) ||
                    "-100".equals(globalId) ||
                    StringUtils.isBlank(customerCode) ||
                    "-1".equals(customerCode) ||
                    "-100".equals(customerCode)) {
                continue;
            }

            String enterpriseId = zbRelation.getQyCodeNew();
            if (StringUtils.isBlank(enterpriseId)) {
                enterpriseId = glodonUserService.getEnterpriseId(globalId, null);
                zbRelation.setQyCodeNew(enterpriseId);
                zbRelation.setQyFlag(StringUtils.isNotBlank(enterpriseId) ? 1 : 0);
            }
        }
    }

    private void insertEnterpriseRelation(List<TempSysZbUser> zbRelations) {
        log.info("需要插入的数量：{}", zbRelations.size());
        if (CollectionUtils.isEmpty(zbRelations)){
            return;
        }

        List<List<TempSysZbUser>> dataLists = Lists.partition(zbRelations,500);
        for (List<TempSysZbUser> relations : dataLists) {
            try {
                // id置空
                relations.forEach(x -> x.setId(null));
                tempRefreshEntIdMapper.batchInsertOther(relations);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public JSONArray getEntInfo(String globalIds) {
        if (StringUtils.isEmpty(globalIds)) {
            return new JSONArray();
        }

        JSONArray result = new JSONArray();

        String[] globalIdList = globalIds.split(",");
        for (String globalId : globalIdList) {
            String enterpriseId = glodonUserService.getEnterpriseId(globalId, null);
            String dbCustomerCode = glodonUserService.getCustomerCodeInner(globalId);
            String qyCodeNew = glodonUserService.getCustomerCode(globalId);
            EntInfoVo entInfo = glodonUserService.getEntInfo(globalId);
            String gcCustomerCode = entInfo == null ? "" : entInfo.getCustomerCode();

            JSONObject object = new JSONObject(true);
            object.put("globalId", globalId);
            object.put("qyCodeNew", qyCodeNew);
            object.put("enterpriseId", enterpriseId);
            object.put("dbCustomerCode", dbCustomerCode);
            object.put("gcCustomerCode", gcCustomerCode);
            result.add(object);
        }

        return result;
    }

    /**
     * @description: 刷新大表
     * @param
     * <AUTHOR>
     * @date 2023/8/15 18:06
     */
    public void refreshBigTable(String tables) {
        String lockValueString = redisUtil.generateLockValue();
        if (!redisUtil.tryLock(lockValueString, RedisKeyEnum.TEMP_ENT_ID_DEAL_LOCK, TempConstant.TYPE_ENTERPRISE)) {
            return;
        }

        List<String> tableList = new ArrayList<>();
        try {
            if ("all".equals(tables)) {
                tableList.addAll(bigTableMap.keySet());
            } else if (StringUtils.isNotBlank(tables)) {
                tableList.addAll(Arrays.asList(tables.split(",")));
            }

            log.info("开始刷新大表：{}", tableList);
            redisUtil.setString(RedisKeyEnum.TEMP_ENT_ID_DEAL_STATUS, "0", "all"); // 开始

            tableListHandle(tableList);
        } catch (Exception e) {
            redisUtil.setString(RedisKeyEnum.TEMP_ENT_ID_DEAL_STATUS, "-1", "all"); // 异常
            e.printStackTrace();
        } finally {
            redisUtil.unlock(lockValueString, RedisKeyEnum.TEMP_ENT_ID_DEAL_LOCK, TempConstant.TYPE_ENTERPRISE);
        }

        redisUtil.setString(RedisKeyEnum.TEMP_ENT_ID_DEAL_STATUS, "1", "all"); // 完成
        log.info("刷新大表完成...");
    }

    /**
     * @Description 方法抽提
     * @param tableList
     **/
    private void tableListHandle(List<String> tableList) {
        for (String table : tableList) {
            try {
                log.info("开始处理表：{}", table);
                redisUtil.setString(RedisKeyEnum.TEMP_ENT_ID_DEAL_STATUS, "0", table); // 开始
                LocalDateTime start = LocalDateTime.now();

                doOneBigTable(table);

                LocalDateTime end = LocalDateTime.now();
                long cost = Duration.between(start, end).toMinutes();
                redisUtil.setString(RedisKeyEnum.TEMP_ENT_ID_DEAL_STATUS, "1:" + cost, table); // 完成
                log.info("处理表完成：{}", table);
            } catch (Exception e) {
                redisUtil.setString(RedisKeyEnum.TEMP_ENT_ID_DEAL_STATUS, "-1", table); // 异常
                e.printStackTrace();
            }
        }
    }
    public void doOneBigTable(String table) {
        if (!bigTableMap.containsKey(table)) {
            return;
        }

        String qyCodeName = bigTableMap.get(table);
        List<String> qyCodeList = tempRefreshEntIdMapper.selectQYCodeFromTable(table, qyCodeName);
        if (CollectionUtils.isEmpty(qyCodeList)) {
            return;
        }

        for (String qyCode : qyCodeList) {
            if (StringUtils.isBlank(qyCode)) {
                continue;
            }

            tempRefreshEntIdMapper.updateTableNewQYCode(table, qyCodeName, qyCode);
        }
    }

    /**
     * @description: 验证企业编码和企业id对应关系
     * @param
     * <AUTHOR>
     * @date 2023/8/15 18:06
     */
    public void sysQyCodeIdVerify(int flag) {
        String lockValueString = redisUtil.generateLockValue();
        if (!redisUtil.tryLock(lockValueString, RedisKeyEnum.TEMP_ENT_ID_DEAL_LOCK, TempConstant.TYPE_ENTERPRISE)) {
            return;
        }

        try {
            // 获取需要处理的企业
            List<TempSysQyCodeIdVerify> verifyList = tempRefreshEntIdMapper.selectVerifyEntId();
            if (CollectionUtils.isEmpty(verifyList)) {
                return;
            }

            redisUtil.setString(RedisKeyEnum.TEMP_ENT_ID_DEAL_STATUS, "0", "sysQyCodeIdVerify"); // 开始
            fillCustomerCode(verifyList, flag);
        } catch (Exception e) {
            redisUtil.setString(RedisKeyEnum.TEMP_ENT_ID_DEAL_STATUS, "-1", "sysQyCodeIdVerify"); // 异常
            e.printStackTrace();
        } finally {
            redisUtil.unlock(lockValueString, RedisKeyEnum.TEMP_ENT_ID_DEAL_LOCK, TempConstant.TYPE_ENTERPRISE);
        }

        redisUtil.setString(RedisKeyEnum.TEMP_ENT_ID_DEAL_STATUS, "1", "sysQyCodeIdVerify"); // 完成
        log.info("全部处理完成...");
    }

    private void fillCustomerCode(List<TempSysQyCodeIdVerify> verifyList, int flag) {
        log.info("需要处理的数量：{}", verifyList.size());

        for (TempSysQyCodeIdVerify verify : verifyList) {
            String enterpriseId = verify.getQyCodeNew();

            if (StringUtils.isBlank(enterpriseId)) {
                continue;
            }
            
            String qyVerify = flag == 1 ? glodonUserService.getCustomerCodeInner(enterpriseId) :
                    glodonUserService.getEnterpriseId(enterpriseId, null);
            verify.setQyVerify(qyVerify);
        }

        log.info("需要插入的数量：{}", verifyList.size());
        if (CollectionUtils.isEmpty(verifyList)){
            return;
        }

        List<List<TempSysQyCodeIdVerify>> dataLists = Lists.partition(verifyList,500);
        for (List<TempSysQyCodeIdVerify> relations : dataLists) {
            try {
                // id置空
                relations.forEach(x -> x.setId(null));
                tempRefreshEntIdMapper.batchInsertOtherVerify(relations);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * @description: 修复更新时间
     * @param
     * <AUTHOR>
     * @date 2023/8/25 18:06
     */
    public void repairUpdateTime(Integer size) {
        String lockValueString = redisUtil.generateLockValue();
        if (!redisUtil.tryLock(lockValueString, RedisKeyEnum.TEMP_ENT_ID_DEAL_LOCK, TempConstant.TYPE_ENTERPRISE)) {
            return;
        }

        try {
            // 获取需要处理的企业
            List<CommonProjCategory> backupList = commonProjCategoryMapper.selectAllCategoryBackup(size);
            if (CollectionUtils.isEmpty(backupList)) {
                return;
            }

            redisUtil.setString(RedisKeyEnum.TEMP_ENT_ID_DEAL_STATUS, "0", "repairUpdateTime"); // 开始
            doRepairUpdateTime(backupList);
        } catch (Exception e) {
            redisUtil.setString(RedisKeyEnum.TEMP_ENT_ID_DEAL_STATUS, "-1", "repairUpdateTime"); // 异常
            e.printStackTrace();
        } finally {
            redisUtil.unlock(lockValueString, RedisKeyEnum.TEMP_ENT_ID_DEAL_LOCK, TempConstant.TYPE_ENTERPRISE);
        }

        redisUtil.setString(RedisKeyEnum.TEMP_ENT_ID_DEAL_STATUS, "1", "repairUpdateTime"); // 完成
        log.info("全部处理完成...");
    }

    private void doRepairUpdateTime(List<CommonProjCategory> backupList) {
        log.info("需要处理的数量：{}", backupList.size());
        List<CommonProjCategory> updateList = new ArrayList<>();

        for (CommonProjCategory backupCategory : backupList) {
            Integer id = backupCategory.getId();
            String updateGlobalId = backupCategory.getUpdateGlobalId();
            Date updateTime = backupCategory.getUpdateTime();

            if (id == null || updateGlobalId == null || updateTime == null) {
                continue;
            }

            CommonProjCategory category = commonProjCategoryMapper.getCategoryByPrimaryKey(id);
            // 是否需要修复
            if (isNeedRepair(backupCategory, category)) {
                updateList.add(backupCategory);
            }
        }

        log.info("需要更新的数量：{}", updateList.size());
        if (CollectionUtils.isEmpty(updateList)){
            return;
        }

        List<List<CommonProjCategory>> dataLists = Lists.partition(updateList,500);
        for (List<CommonProjCategory> partitions : dataLists) {
            try {
                commonProjCategoryMapper.batchUpdateUpdateTime(partitions);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private boolean isNeedRepair(CommonProjCategory backupCategory, CommonProjCategory category) {
        if (backupCategory == null || category == null) {
            return false;
        }

        return Objects.equals(backupCategory.getId(), category.getId()) &&
                Objects.equals(backupCategory.getCommonprojcategoryid(), category.getCommonprojcategoryid()) &&
                Objects.equals(backupCategory.getCategoryname(), category.getCategoryname()) &&
                Objects.equals(backupCategory.getType(), category.getType()) &&
                Objects.equals(backupCategory.getCategorycode1(), category.getCategorycode1()) &&
                Objects.equals(backupCategory.getCategorycode2(), category.getCategorycode2()) &&
                Objects.equals(backupCategory.getCategorycode3(), category.getCategorycode3()) &&
                Objects.equals(backupCategory.getCategorycode4(), category.getCategorycode4()) &&
                Objects.equals(backupCategory.getLevel(), category.getLevel()) &&
                Objects.equals(backupCategory.getCategoryTypeCode(), category.getCategoryTypeCode()) &&
                Objects.equals(backupCategory.getCategoryTypeName(), category.getCategoryTypeName()) &&
                Objects.equals(backupCategory.getGlobalId(), category.getGlobalId()) &&
                Objects.equals(backupCategory.getQyCode(), category.getQyCode()) &&
                Objects.equals(backupCategory.getIsDeleted(), category.getIsDeleted()) &&
                Objects.equals(backupCategory.getIsUsing(), category.getIsUsing()) &&
                Objects.equals(backupCategory.getUpdateGlobalId(), category.getUpdateGlobalId()) &&
                Objects.equals(backupCategory.getRemark(), category.getRemark());
    }

}
