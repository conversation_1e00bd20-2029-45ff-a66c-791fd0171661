package com.glodon.qydata.controller.temp.autoteam;

import com.glodon.qydata.mapper.temp.TempMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.Arrays;

@Service
public class AutoTeamDeleteErrorDataService {

    @Autowired
    TempMapper tempMapper;

    private static final String CUSTOMER_CODE = "'1-4EWC2Q1'";

    private final String[] SQL_ARRAY ={
            "DELETE FROM `zb_standards_expression` WHERE qy_code = {0}",
            "DELETE FROM `zb_project_feature_standards` WHERE customer_code = {0}",
            "DELETE FROM `zb_project_feature_category_view_standards` WHERE customer_code = {0}",
            "DELETE FROM `zb_standards_trade` WHERE customer_code = {0}",
            "DELETE FROM `zb_standards_main_quantity` WHERE customer_code = {0}",
            "DELETE FROM `zb_standards_expression_self` WHERE qy_code = {0}",
            "DELETE FROM `zb_project_feature_standards_self` WHERE customer_code = {0}",
            "DELETE FROM `zb_project_feature_category_view_standards_self` WHERE customer_code = {0}",
            "DELETE FROM `zb_standards_main_quantity_self` WHERE customer_code = {0}"};

    public void deleteFeatureErrorRelationData(){
        Arrays.stream(SQL_ARRAY).forEach(item ->{
            tempMapper.deleteData(MessageFormat.format(item, CUSTOMER_CODE));
        });
    }
}
