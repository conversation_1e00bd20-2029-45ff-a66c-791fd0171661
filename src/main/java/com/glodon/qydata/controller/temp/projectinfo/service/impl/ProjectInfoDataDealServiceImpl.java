package com.glodon.qydata.controller.temp.projectinfo.service.impl;

import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.controller.temp.projectinfo.entity.Prams;
import com.glodon.qydata.controller.temp.projectinfo.service.IProjectInfoDataDealService;
import com.glodon.qydata.insertprojectinfo.entity.StandardsProjectInfoEntity;
import com.glodon.qydata.insertprojectinfo.mapper.ProjectInfoMapper;
import com.glodon.qydata.insertprojectinfo.service.ProjectInfoInsertService;
import com.glodon.qydata.util.EmptyUtil;
import com.glodon.qydata.util.RedisUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * glodon
 */
@Service
@Slf4j
public class ProjectInfoDataDealServiceImpl implements IProjectInfoDataDealService {

    @Autowired
    private ProjectInfoMapper projectInfoMapper;

    @Autowired
    private ProjectInfoInsertService projectInfoInsertService;

    @Autowired
    private RedisUtil redisUtil;

    private static final List<String> infoNameList = Lists.newArrayList("编制时间");

    private static List<StandardsProjectInfoEntity> initProjectInfoDataColm() {
        List<StandardsProjectInfoEntity> infoEntityList = new ArrayList<>();
        StandardsProjectInfoEntity infoEntity = new StandardsProjectInfoEntity();
        infoEntity.setName("编制时间");
        infoEntity.setTypeCode("date");
        infoEntity.setIsUsing(1);
        infoEntity.setIsRequired(0);
        infoEntity.setIsExpression(0);
        infoEntity.setIsDeleted(0);
        infoEntity.setStandardDataType(2);
        infoEntityList.add(infoEntity);
        return infoEntityList;
    }

    @Override
    public void dealProjectInfoDataByCustomerCode(List<String> customerCode, Integer standardDataType) {
        String projectAndContractInfoDataRefreshLock = redisUtil.getString(RedisKeyEnum.PROJECT_AND_CONTRACT_INFO_DATA_REFRESH_LOCK);
        if (StringUtils.isNotBlank(projectAndContractInfoDataRefreshLock)) {
            log.info("项目信息/合同信息正在刷新中，请稍后重试！");
            return;
        }
        try {
            redisUtil.setString(RedisKeyEnum.PROJECT_AND_CONTRACT_INFO_DATA_REFRESH_LOCK, "Locking......");
            log.info("开始更新");
            // 全部的企业
            List<Prams> allCustomer = projectInfoMapper.selectCustomerByCondition(standardDataType, Collections.emptyList(), customerCode);
            Map<String, Prams> allCustomerMap = allCustomer.stream().collect(Collectors.toMap(Prams::getCustomerCode, Function.identity(), (v1, v2) -> v2));


            // 主表中有特定项目信息/合同信息名称的企业
            List<Prams> customerWithInfoName = projectInfoMapper.selectCustomerByCondition(standardDataType, infoNameList, customerCode);
            Map<String, Prams> customerWithInfoNameMap = customerWithInfoName.stream().collect(Collectors.toMap(Prams::getCustomerCode, Function.identity(), (v1, v2) -> v2));

            // 暂存表中全部企业
            List<Prams> selfAllSelfCustomer = projectInfoMapper.selectSelfCustomerByCondition(standardDataType, Collections.emptyList(), customerCode);
            Map<String, Prams> allSelfCustomerMap = selfAllSelfCustomer.stream().collect(Collectors.toMap(Prams::getCustomerCode, Function.identity(), (v1, v2) -> v2));

            // 暂存表中有特定项目信息/合同信息名称的企业
            List<Prams> selfCustomerWithInfoName = projectInfoMapper.selectSelfCustomerByCondition(standardDataType, infoNameList, customerCode);
            Map<String, Prams> selfCustomerWithInfoNameMap = selfCustomerWithInfoName.stream().collect(Collectors.toMap(Prams::getCustomerCode, Function.identity(), (v1, v2) -> v2));

            log.info("已完成所有企业查询，共有{}家企业，其中有暂存数据的企业{}家", allCustomer.size(), selfAllSelfCustomer.size());

            // 主表中待插入的数据
            List<StandardsProjectInfoEntity> dataToInsertInMain  = new ArrayList<>();
            // 暂存表中待插入的数据
            List<StandardsProjectInfoEntity> dataToInsertInSelf = new ArrayList<>();
            // 暂存表中待插入的数据
            List<StandardsProjectInfoEntity> dataToUpdateInSelf = new ArrayList<>();

            /**
             * 1. 主表中无特定项目/合同信息 and 无暂存表 的企业 --> 只更新主表
             * (allCustomerCode - customerCodeWithInfoName) ∩ (allCustomerCode - selfAllSelfCustomerCode) = allCustomerCode - customerCodeWithInfoName - selfAllSelfCustomerCode
             */
            List<String> customerCodeWithOutSpecificInfoInMainTable = findCustomerCodeWithOutSpecificInfoInMainTable(allCustomerMap.keySet(), customerWithInfoNameMap.keySet(), allSelfCustomerMap.keySet());
            customerCodeWithOutSpecificInfoInMainTable.forEach(code -> {
                projectInfoInsertService.insertIntoProjectInfoV2(initProjectInfoDataColm(), allCustomerMap, dataToInsertInMain, code);
            });
            if (CollectionUtils.isNotEmpty(dataToInsertInMain)) {
                projectInfoMapper.projectInfoInsert(dataToInsertInMain);
                dataToInsertInMain.clear();
            }


            /**
             * 2.主表中无特定项目/合同信息，and 有暂存表，且暂存表无特定项目/合同信息 的企业 --> 更新主表和暂存表
             *  (allCustomerCode - customerCodeWithInfoName) ∩ (selfAllSelfCustomerCode - selfCustomerCodeWithInfoName)
             */
            List<String> customerCodeWithOutSpecificInfoInMainAndTempTable = findCustomerCodeWithOutSpecificInfoInMainAndTemp(allCustomerMap.keySet(), customerWithInfoNameMap.keySet(), allSelfCustomerMap.keySet(), selfCustomerWithInfoNameMap.keySet());
            customerCodeWithOutSpecificInfoInMainAndTempTable.forEach(code -> {
                List<StandardsProjectInfoEntity> insertList = projectInfoInsertService.insertIntoProjectInfoV2(initProjectInfoDataColm(), allCustomerMap, dataToInsertInMain, code);
                List<Long> ids = insertList.stream().map(StandardsProjectInfoEntity::getId).collect(Collectors.toList());
                projectInfoInsertService.insertIntoProjectInfoSelfV2(initProjectInfoDataColm(), allSelfCustomerMap, dataToInsertInSelf, code, ids);
            });
            if (CollectionUtils.isNotEmpty(dataToInsertInMain)) {
                projectInfoMapper.projectInfoInsert(dataToInsertInMain);
                dataToInsertInMain.clear();
            }
            if (CollectionUtils.isNotEmpty(dataToInsertInSelf)) {
                projectInfoMapper.projectInfoSelfInsert(dataToInsertInSelf);
                dataToInsertInSelf.clear();
            }

            /**
             * 3. 主表中无特定项目/合同信息，有暂存表，且暂存表有特定项目/合同信息 的企业
             *  (allCustomerCode - customerCodeWithInfoName) ∩ selfCustomerCodeWithInfoName
             *  3.1 zb_standards_project_info_self  origin_id 为空(新增的暂未发布) (******)
             * 	    将暂存表的复制一份放到主表中，在主表中新增数据时需要给暂存表关联origin_id
             * 	3.2 zb_standards_project_info_self  origin_id 不为空(将主表的某条数据改名)
             *      跳过该企业；
             */
            List<String> customerCodeWithOutSpecificInfoInMainButInTemp = findCustomerCodeWithOutSpecificInfoInMainButInTemp(allCustomerMap.keySet(), customerWithInfoNameMap.keySet(), selfCustomerWithInfoNameMap.keySet());
            customerCodeWithOutSpecificInfoInMainButInTemp.forEach(code -> {
                Prams prams = selfCustomerWithInfoNameMap.get(code);
                Long originId = prams.getOriginId();
                if (EmptyUtil.isEmpty(originId)) {
                    List<StandardsProjectInfoEntity> insertList = projectInfoInsertService.insertIntoProjectInfoV2(initProjectInfoDataColm(), allCustomerMap, dataToInsertInMain, code);
                    Map<String, StandardsProjectInfoEntity> infoNameMap = insertList.stream().collect(Collectors.toMap(StandardsProjectInfoEntity::getName, Function.identity(), (v1, v2) -> v2));
                    List<StandardsProjectInfoEntity> selfEntityList = projectInfoMapper.selectByCondition(standardDataType, Lists.newArrayList(code), infoNameList);
                    selfEntityList.forEach(self -> {
                        StandardsProjectInfoEntity insertEntity = infoNameMap.get(self.getName());
                        self.setOriginId(insertEntity.getId());
                    });
                    dataToUpdateInSelf.addAll(selfEntityList);
                }
            });
            if (CollectionUtils.isNotEmpty(dataToInsertInMain)) {
                projectInfoMapper.projectInfoInsert(dataToInsertInMain);
                dataToInsertInMain.clear();
            }
            if (CollectionUtils.isNotEmpty(dataToUpdateInSelf)) {
                projectInfoMapper.updateOriginIdOfSelf(dataToUpdateInSelf);
                dataToInsertInSelf.clear();
            }
            log.info("更新完成！");
        } catch (Exception e) {
            log.error("项目信息/合同信息异常!", e);
        } finally {
            redisUtil.delete(RedisKeyEnum.PROJECT_AND_CONTRACT_INFO_DATA_REFRESH_LOCK);
        }
    }

    /**
     * 查找主表中无特定项目/合同信息 and 无暂存表 的企业
     */
    private List<String> findCustomerCodeWithOutSpecificInfoInMainTable(Set<String> allCustomerCode,
                                                                        Set<String> customerCodeWithInfoName,
                                                                        Set<String> selfAllSelfCustomerCode) {
        List<String> allCustomerCodeCopy = new ArrayList<>(allCustomerCode);
        if (CollectionUtils.isNotEmpty(customerCodeWithInfoName)) {
            allCustomerCodeCopy.removeAll(customerCodeWithInfoName);
        }
        if (CollectionUtils.isNotEmpty(selfAllSelfCustomerCode)) {
            allCustomerCodeCopy.removeAll(selfAllSelfCustomerCode);
        }
        return allCustomerCodeCopy;
    }

    private List<String> findCustomerCodeWithOutSpecificInfoInMainAndTemp(Set<String> allCustomerCode,
                                                                          Set<String> customerCodeWithInfoName,
                                                                          Set<String> selfAllSelfCustomerCode,
                                                                          Set<String> selfCustomerCodeWithInfoName) {
        List<String> allCustomerCodeCopy = new ArrayList<>(allCustomerCode);
        if (CollectionUtils.isNotEmpty(customerCodeWithInfoName)) {
            allCustomerCodeCopy.removeAll(customerCodeWithInfoName);
        }
        List<String> selfAllSelfCustomerCodeCopy = new ArrayList<>(selfAllSelfCustomerCode);
        if (CollectionUtils.isNotEmpty(selfCustomerCodeWithInfoName)) {
            allCustomerCodeCopy.removeAll(selfCustomerCodeWithInfoName);
        }
        return (List<String>) CollectionUtils.intersection(allCustomerCodeCopy, selfAllSelfCustomerCodeCopy);
    }

    private List<String> findCustomerCodeWithOutSpecificInfoInMainButInTemp(Set<String> allCustomerCode,
                                                                            Set<String> customerCodeWithInfoName,
                                                                            Set<String> selfCustomerCodeWithInfoName) {
        List<String> allCustomerCodeCopy = new ArrayList<>(allCustomerCode);
        if (CollectionUtils.isNotEmpty(customerCodeWithInfoName)) {
            allCustomerCodeCopy.removeAll(customerCodeWithInfoName);
        }

        return (List<String>) CollectionUtils.intersection(allCustomerCodeCopy, selfCustomerCodeWithInfoName);
    }

}
