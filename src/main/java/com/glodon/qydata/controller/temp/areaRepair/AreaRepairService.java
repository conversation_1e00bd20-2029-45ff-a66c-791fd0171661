package com.glodon.qydata.controller.temp.areaRepair;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.entity.system.TbArea;
import com.glodon.qydata.mapper.system.TbAreaMapper;
import com.glodon.qydata.service.system.IAreaService;
import com.glodon.qydata.util.AMapApiUtil;
import com.glodon.qydata.util.AreaUtil;
import com.glodon.qydata.util.RedisUtil;
import com.glodon.qydata.vo.amap.AMapDistrict;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.glodon.qydata.service.system.impl.AreaServiceImpl.getAbbrSpell;
import static com.glodon.qydata.service.system.impl.AreaServiceImpl.getFullSpell;


@Service
@Slf4j
public class AreaRepairService {
    @Resource
    private TbAreaMapper areaMapper;
    @Resource
    private IAreaService areaService;
    @Resource
    RedisUtil redisUtil;

    public void init(Map<String, String> invalidDistrictMap, Map<String, String> specialCodeMap, List<AreaDiff> relatedList) {
        // 省直辖县级行政单位、自治区直辖县级行政单位，建议存成省挂市的两级结构，第3级置成无效
        invalidDistrictMap.put("河南省-济源市-济源市", "省直辖县级行政单位");
        invalidDistrictMap.put("湖北省-天门市-天门市", "省直辖县级行政单位");
        invalidDistrictMap.put("湖北省-潜江市-潜江市", "省直辖县级行政单位");
        invalidDistrictMap.put("湖北省-仙桃市-仙桃市", "省直辖县级行政单位");
        invalidDistrictMap.put("湖北省-神农架林区-神农架林区", "省直辖县级行政单位");
        invalidDistrictMap.put("新疆维吾尔自治区-石河子市-石河子市", "自治区直辖县级行政单位");
        invalidDistrictMap.put("新疆维吾尔自治区-图木舒克市-图木舒克市", "自治区直辖县级行政单位");
        invalidDistrictMap.put("新疆维吾尔自治区-五家渠市-五家渠市", "自治区直辖县级行政单位");
        invalidDistrictMap.put("新疆维吾尔自治区-阿拉尔市-阿拉尔市", "自治区直辖县级行政单位");

        // 层级有改变，建议置成无效
        invalidDistrictMap.put("贵州省-毕节市-毕节市", "2011年12月27日，国务院批准撤销毕节地区和县级毕节市，设立地级毕节市，并将原县级毕节市的行政区域改为七星关区");
        invalidDistrictMap.put("广东省-肇庆市-肇庆市", "肇庆市在1988年升格为地级市之前是县级市");
        invalidDistrictMap.put("新疆维吾尔自治区-克拉玛依市-克拉玛依市", "1958年：设立为县级市;1982年：升格为地级市;1984年：被改为自治区直辖的不设区的县级市;1990年：恢复为地级市");
        invalidDistrictMap.put("浙江省-衢州市-衢州市", "衢州市在1985年之前并非省辖市，而是县级市或县级单位。1985年5月，衢州市正式升格为省辖市。");
        invalidDistrictMap.put("甘肃省-嘉峪关市-嘉峪关市", "嘉峪关市620200是甘肃省下辖的地级市，不设县区级行政区划，直接管理街道和镇");
        invalidDistrictMap.put("安徽省-巢湖", "巢湖市曾是地级市，2011年被撤销地级建制，其辖区被划归合肥、芜湖、马鞍山三市。此后设立县级巢湖市，由安徽省直辖、合肥市代管。");

        // 挂接错误，建议置成无效
        invalidDistrictMap.put("安徽省-阜阳市-蚌山区", "挂接错误");  // 蚌山区属于蚌埠市，从未归属过阜阳市。
        invalidDistrictMap.put("安徽省-阜阳市-淮上区", "挂接错误");  // 淮上区属于蚌埠市，从未归属过阜阳市。
        invalidDistrictMap.put("安徽省-阜阳市-龙子湖区", "挂接错误");  // 龙子湖区属于蚌埠市，从未归属过阜阳市。
        invalidDistrictMap.put("安徽省-阜阳市-禹会区", "挂接错误");  // 禹会区属于蚌埠市，从未归属过阜阳市。
        invalidDistrictMap.put("安徽省-蚌埠市-庐江县", "挂接错误");  // 庐江县属于合肥市，从未归属过蚌埠市。
        invalidDistrictMap.put("安徽省-蚌埠市-和县", "挂接错误");  // 和县属于马鞍山市，从未归属过蚌埠市。
        invalidDistrictMap.put("安徽省-蚌埠市-居巢区", "挂接错误");  // 居巢区属于巢湖市，从未归属过蚌埠市。
        invalidDistrictMap.put("安徽省-蚌埠市-无为县", "挂接错误");  // 无为县属于芜湖市，从未归属过蚌埠市。
        invalidDistrictMap.put("安徽省-蚌埠市-含山县", "挂接错误");  // 含山县属于马鞍山市，从未归属过蚌埠市。

        //行政管理区(又称“功能区”)，不属于行政区划单位。直接置成无效
        // 户口簿未使用，建议置成无效
        invalidDistrictMap.put("内蒙古自治区-鄂尔多斯市-恩格贝生态示范区", "功能区"); //-1
        invalidDistrictMap.put("内蒙古自治区-鄂尔多斯市-鄂尔多斯高新技术产业开发区", "功能区"); //-1
        invalidDistrictMap.put("江苏省-宿迁市-洋河新区", "功能区");  //-1
        invalidDistrictMap.put("江苏省-宿迁市-苏州宿迁工业园区", "功能区");  //-1
        invalidDistrictMap.put("江苏省-淮安市-淮安生态文旅区", "功能区");  //-1
        invalidDistrictMap.put("贵州省-铜仁市-大龙经济开发区", "功能区");  //-1
        invalidDistrictMap.put("湖南省-益阳市-益阳东部新区", "功能区");   //-1
        invalidDistrictMap.put("湖南省-益阳市-益阳高新区", "功能区");  //-1
        invalidDistrictMap.put("湖南省-怀化市-洪江区", "功能区");   //-1
        invalidDistrictMap.put("湖南省-益阳市-大通湖区", "功能区");    //-1
        invalidDistrictMap.put("浙江省-舟山群岛新区", "国家级综合功能区");   //-1
        // 户口簿已使用，保留并生成特殊编码
        specialCodeMap.put("河南省-郑州市-经济技术开发区", "功能区");
        specialCodeMap.put("河南省-郑州市-高新开发区", "功能区");
        specialCodeMap.put("河南省-郑州市-郑东新区", "功能区");
        specialCodeMap.put("河南省-郑州市-航空港经济综合实验区", "功能区");
        specialCodeMap.put("河南省-郑州市-出口加工区", "功能区");
        specialCodeMap.put("河南省-平顶山市-高新技术开发区", "功能区");
        specialCodeMap.put("广东省-惠州市-大亚湾区", "功能区");
        specialCodeMap.put("内蒙古自治区-通辽市-经济技术开发区", "功能区");
        specialCodeMap.put("内蒙古自治区-锡林郭勒盟-乌拉盖管理区", "功能区");
        specialCodeMap.put("内蒙古自治区-鄂尔多斯市-空港园区", "功能区");
        specialCodeMap.put("内蒙古自治区-呼和浩特市-经济技术开发区", "功能区");
        specialCodeMap.put("内蒙古自治区-呼和浩特市-林格尔新区", "功能区");
        specialCodeMap.put("江苏省-南通市-通州湾江海联动开发示范区", "功能区");
        specialCodeMap.put("江苏省-南通市-南通苏锡通科技产业园区", "功能区");
        specialCodeMap.put("江苏省-南通市-南通经济技术开发区", "功能区");
        specialCodeMap.put("江苏省-泰州市-医药高新区", "功能区");
        specialCodeMap.put("江苏省-宿迁市-湖滨新区", "功能区");
        specialCodeMap.put("江苏省-宿迁市-宿迁经济技术开发区", "功能区");
        specialCodeMap.put("江苏省-无锡市-江苏无锡经济开发区", "功能区");
        specialCodeMap.put("江苏省-盐城市-经济技术开发区", "功能区");
        specialCodeMap.put("江苏省-盐城市-盐南高新区", "功能区");
        specialCodeMap.put("江苏省-淮安市-淮安经济技术开发区", "功能区");
        specialCodeMap.put("江苏省-淮安市-淮安工业园区", "功能区");
        specialCodeMap.put("江苏省-苏州市-新区", "功能区");
        specialCodeMap.put("江苏省-苏州市-开发区", "功能区");
        specialCodeMap.put("江苏省-苏州市-园区", "功能区");
        specialCodeMap.put("河北省-唐山市-高新区", "功能区");
        specialCodeMap.put("河北省-秦皇岛市-经济技术开发区", "功能区");
        specialCodeMap.put("河北省-承德市-开发区", "功能区");
        specialCodeMap.put("河北省-沧州市-渤海新区", "功能区");
        specialCodeMap.put("河北省-沧州市-沧州高新", "功能区");
        specialCodeMap.put("河北省-沧州市-沧州经济开发区", "功能区");
        specialCodeMap.put("河北省-沧州市-沧州临港经济技术开发区", "功能区");
        specialCodeMap.put("河北省-衡水市-高新区", "功能区");
        specialCodeMap.put("河北省-衡水市-滨湖新区", "功能区");
        specialCodeMap.put("河北省-邯郸市-冀南新区", "功能区");
        specialCodeMap.put("河北省-邯郸市-经济技术开发区", "功能区");
        specialCodeMap.put("河北省-保定市-白沟新城", "功能区");
        specialCodeMap.put("河北省-保定市-高新区", "功能区");
        specialCodeMap.put("湖北省-武汉市-经济开发区", "功能区");
        specialCodeMap.put("辽宁省-大连市-开发区", "功能区");
        specialCodeMap.put("山东省-烟台市-开发区", "功能区");
        specialCodeMap.put("山西省-阳泉市-阳泉高新技术产业开发区", "功能区");
        specialCodeMap.put("吉林省-长春市-经济技术开发区", "功能区");
        specialCodeMap.put("吉林省-长春市-高新技术开发区", "功能区");
        specialCodeMap.put("吉林省-长春市-净月潭开发区", "功能区");
        specialCodeMap.put("吉林省-长春市-汽车产业开发区", "功能区");
        specialCodeMap.put("吉林省-四平市-高新技术产业开发区", "功能区");
        specialCodeMap.put("西藏自治区-昌都市-西藏昌都经济开发区", "功能区");
        specialCodeMap.put("天津市-市辖区-经济开发区", "功能区");
        specialCodeMap.put("甘肃省-兰州市-兰州高新区", "功能区");
        specialCodeMap.put("甘肃省-兰州市-兰州经开区", "功能区");
        specialCodeMap.put("甘肃省-兰州市-兰州新区", "功能区");
        specialCodeMap.put("甘肃省-天水市-天水经开区", "功能区");
        specialCodeMap.put("河北省-唐山市-芦台区", "功能区"); // 河北省唐山市并没有“芦台区”这一行政区划单位，与之相关的可能是唐山市芦台经济技术开发区
        specialCodeMap.put("河北省-唐山市-汉沽区", "功能区"); // 河北省唐山市并没有“芦台区”这一行政区划单位，与之相关的可能是唐山市汉沽管理区
        specialCodeMap.put("河北省-唐山市-海港区", "功能区"); // 秦皇岛市有海港区；在唐山市的行政区划中，相关区域被称为唐山海港经济开发区
        specialCodeMap.put("贵州省-遵义市-新蒲新区", "功能区");
        specialCodeMap.put("贵州省-铜仁市-铜仁高新技术产业开发区", "功能区");
        specialCodeMap.put("贵州省-安顺市-黄果树旅游区", "功能区");
        specialCodeMap.put("贵州省-安顺市-安顺经济技术开发区", "功能区");
        specialCodeMap.put("贵州省-贵阳市-新天园区", "功能区");
        specialCodeMap.put("重庆市-市辖区-万盛经济技术开发区", "功能区");
        specialCodeMap.put("福建省-泉州市-清濛开发区", "功能区");
        specialCodeMap.put("湖南省-长沙市-开发区", "功能区");
        specialCodeMap.put("湖南省-株洲市-云龙示范区", "功能区");
        specialCodeMap.put("辽宁省-沈抚新区", "功能区");

        // 不规范的行政区划，建议置成无效
        invalidDistrictMap.put("香港特别行政区-新界", "不是行政区划单位的规范表述，而是对香港特别行政区地理区域的非正式划分。");
        invalidDistrictMap.put("香港特别行政区-九龙", "不是行政区划单位的规范表述，而是对香港特别行政区地理区域的非正式划分。");
        invalidDistrictMap.put("香港特别行政区-香港岛", "不是行政区划单位的规范表述，而是对香港特别行政区地理区域的非正式划分。");
        invalidDistrictMap.put("澳门特别行政区-氹仔岛", "并不是严格意义上的行政区划单位，但它们是澳门特别行政区的重要地理区域组成部分");
        invalidDistrictMap.put("澳门特别行政区-澳门半岛", "并不是严格意义上的行政区划单位，但它们是澳门特别行政区的重要地理区域组成部分");
        invalidDistrictMap.put("澳门特别行政区-路环岛", "并不是严格意义上的行政区划单位，但它们是澳门特别行政区的重要地理区域组成部分");
        invalidDistrictMap.put("澳门特别行政区-澳门-路环", "并不是严格意义上的行政区划单位，但它们是澳门特别行政区的重要地理区域组成部分");
        invalidDistrictMap.put("澳门特别行政区-澳门-凼仔", "并不是严格意义上的行政区划单位，但它们是澳门特别行政区的重要地理区域组成部分");
        invalidDistrictMap.put("澳门特别行政区-澳门-澳门半岛", "并不是严格意义上的行政区划单位，但它们是澳门特别行政区的重要地理区域组成部分");
        invalidDistrictMap.put("澳门特别行政区-澳门-路凼城", "“路氹”和“路凼”通常被交替使用，指代同一个区域，是澳门特别行政区的一个重要新兴区域");

        // city级直接挂了street级，直接置成无效
        invalidDistrictMap.put("广东省-中山市-中山港街道", "乡级单位");
        invalidDistrictMap.put("广东省-中山市-五桂山街道", "乡级单位");
        invalidDistrictMap.put("广东省-中山市-东区街道", "乡级单位");
        invalidDistrictMap.put("广东省-中山市-石岐街道", "乡级单位");
        invalidDistrictMap.put("广东省-中山市-环城街道", "乡级单位");
        invalidDistrictMap.put("广东省-中山市-西区街道", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-南城区", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-常平镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-东坑镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-企石镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-中堂镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-清溪镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-望牛墩镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-凤岗镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-茶山镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-沙田镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-寮步镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-石龙镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-麻涌镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-厚街镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-长安镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-石碣镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-石排镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-虎门镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-道滘镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-洪梅镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-谢岗镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-桥头镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-大朗镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-黄江镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-塘厦镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-万江区", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-横沥镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-樟木头镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-大岭山镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-莞城区", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-东城区", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-高埗镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-光村镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-东成镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-和庆镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-王五镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-大成镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-那大镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-白马井镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-南丰镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-海头镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-雅星镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-排浦镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-新州镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-峨蔓镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-兰洋镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-中和镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-木棠镇", "乡级单位");
        invalidDistrictMap.put("江苏省-苏州市-玉山镇", "乡级单位");
        invalidDistrictMap.put("江苏省-苏州市-周庄镇", "乡级单位");
        invalidDistrictMap.put("江苏省-苏州市-花桥镇", "乡级单位");
        invalidDistrictMap.put("江苏省-苏州市-周市镇", "乡级单位");
        invalidDistrictMap.put("江苏省-苏州市-巴城镇", "乡级单位");
        invalidDistrictMap.put("江苏省-苏州市-陆家镇", "乡级单位");
        invalidDistrictMap.put("江苏省-苏州市-淀山湖镇", "乡级单位");
        invalidDistrictMap.put("江苏省-苏州市-张浦镇", "乡级单位");
        invalidDistrictMap.put("江苏省-苏州市-千灯镇", "乡级单位");
        invalidDistrictMap.put("江苏省-苏州市-锦溪镇", "乡级单位");
        invalidDistrictMap.put("浙江省-金华市-佛堂镇", "乡级单位");
        invalidDistrictMap.put("浙江省-金华市-上溪镇", "乡级单位");
        invalidDistrictMap.put("浙江省-金华市-苏溪镇", "乡级单位");
        invalidDistrictMap.put("浙江省-金华市-大陈镇", "乡级单位");
        invalidDistrictMap.put("浙江省-金华市-义亭镇", "乡级单位");
        invalidDistrictMap.put("浙江省-金华市-赤岸镇", "乡级单位");

        // 需要挂到重庆郊县下，建议直接置成无效
        invalidDistrictMap.put("重庆市-市辖区-铜梁县", "挂到重庆郊县下");
        invalidDistrictMap.put("重庆市-市辖区-云阳县", "挂到重庆郊县下");
        invalidDistrictMap.put("重庆市-市辖区-丰都县", "挂到重庆郊县下");
        invalidDistrictMap.put("重庆市-市辖区-巫溪县", "挂到重庆郊县下");
        invalidDistrictMap.put("重庆市-市辖区-酉阳土家族苗族自治县", "挂到重庆郊县下");
        invalidDistrictMap.put("重庆市-市辖区-秀山土家族苗族自治县", "挂到重庆郊县下");
        invalidDistrictMap.put("重庆市-市辖区-忠县", "挂到重庆郊县下");
        invalidDistrictMap.put("重庆市-市辖区-巫山县", "挂到重庆郊县下");
        invalidDistrictMap.put("重庆市-市辖区-奉节县", "挂到重庆郊县下");
        invalidDistrictMap.put("重庆市-市辖区-石柱土家族自治县", "挂到重庆郊县下");
        invalidDistrictMap.put("重庆市-市辖区-垫江县", "挂到重庆郊县下");
        invalidDistrictMap.put("重庆市-市辖区-城口县", "挂到重庆郊县下");
        invalidDistrictMap.put("重庆市-市辖区-彭水苗族土家族自治县", "挂到重庆郊县下");

        // 可以关联的地区
        relatedList.add(new AreaDiff("河南省-洛阳市-偃师市", "410381", "河南省-洛阳市-偃师区", "2021年3月，撤销县级偃师市，设立洛阳市偃师区"));
        relatedList.add(new AreaDiff("河南省-洛阳市-孟津县", "410322", "河南省-洛阳市-孟津区", "2021年，撤销孟津县和吉利区，设立洛阳市孟津区"));
        relatedList.add(new AreaDiff("河南省-洛阳市-吉利区", "410306", "河南省-洛阳市-孟津区", "2021年，撤销吉利区并入孟津区"));
        relatedList.add(new AreaDiff("河南省-三门峡市-陕县", "411222", "河南省-三门峡市-陕州区", "2015年，撤销陕县设立陕州区"));
        relatedList.add(new AreaDiff("河南省-许昌市-许昌县", "411023", "河南省-许昌市-建安区", "2014年，撤销许昌县设立建安区"));
        relatedList.add(new AreaDiff("河南省-郑州市-邙山区", "410190", "河南省-郑州市-惠济区", "2005年，撤销邙山区并入惠济区"));
        relatedList.add(new AreaDiff("河南省-周口市-淮阳县", "411626", "河南省-周口市-淮阳区", "2019年，撤销淮阳县设立淮阳区"));
        relatedList.add(new AreaDiff("河南省-新乡市-长垣县", "410728", "河南省-新乡市-长垣市", "2019年，撤销长垣县设立县级长垣市"));
        relatedList.add(new AreaDiff("河南省-开封市-金明区", "410211", "河南省-开封市-龙亭区", "2014年，撤销开封市龙亭区和金明区，设立新的开封市龙亭区"));
        relatedList.add(new AreaDiff("河南省-开封市-开封县", "410221", "河南省-开封市-祥符区", "2014年，撤销开封县设立祥符区"));

        relatedList.add(new AreaDiff("广东省-肇庆市-高要市", "441283", "广东省-肇庆市-高要区", "高要市已于2015年撤销，设立肇庆市高要区"));
        relatedList.add(new AreaDiff("广东省-阳江市-阳东县", "441723", "广东省-阳江市-阳东区", "阳东县已于2014年撤销，设立阳江市阳东区"));
        relatedList.add(new AreaDiff("广东省-茂名市-电白县", "440923", "广东省-茂名市-电白区", "2014年，电白县与茂港区合并设立茂名市电白区"));
        relatedList.add(new AreaDiff("广东省-茂名市-茂港区", "440903", "广东省-茂名市-电白区", "2014年，茂港区与电白县合并设立茂名市电白区"));
        relatedList.add(new AreaDiff("广东省-潮州市-潮安县", "445121", "广东省-潮州市-潮安区", "潮安县于2013年6月28日撤销，设立潮州市潮安区"));
        relatedList.add(new AreaDiff("广东省-揭阳市-揭东县", "445221", "广东省-揭阳市-揭东区", "揭东县于2013年撤销，设立揭阳市揭东区"));
        relatedList.add(new AreaDiff("广东省-清远市-清新县", "441827", "广东省-清远市-清新区", "清新县于2013年撤销，设立清远市清新区"));
        relatedList.add(new AreaDiff("广东省-韶关市-曲江县", "440221", "广东省-韶关市-曲江区", "曲江县于2013年撤销，设立韶关市曲江区"));
        relatedList.add(new AreaDiff("广东省-云浮市-云安县", "445323", "广东省-云浮市-云安区", "云安县于2014年撤销，设立云浮市云安区"));

        relatedList.add(new AreaDiff("黑龙江省-大兴安岭地区-漠河县", "232723", "黑龙江省-大兴安岭地区-漠河市", "漠河县已于2018年撤销，设立县级漠河市"));
        relatedList.add(new AreaDiff("黑龙江省-哈尔滨市-双城市", "230113", "黑龙江省-哈尔滨市-双城区", "双城市已于2014年撤销，设立哈尔滨市双城区"));
        relatedList.add(new AreaDiff("黑龙江省-哈尔滨市-动力区", "230107", "黑龙江省-哈尔滨市-香坊区", "动力区已于2004年撤销，并入香坊区"));
        relatedList.add(new AreaDiff("黑龙江省-哈尔滨市-太平区", "230105", "黑龙江省-哈尔滨市-南岗区", "太平区已于2004年撤销，并入南岗区"));
        relatedList.add(new AreaDiff("黑龙江省-伊春市-红星区", "230715", "黑龙江省-伊春市-丰林县", "红星区已于2019年撤销，与新青区、五营区合并设立丰林县"));
        relatedList.add(new AreaDiff("新疆维吾尔自治区-阿克苏地区-库车县", "652923", "新疆维吾尔自治区-阿克苏地区-库车市", "库车县已于2019年12月撤销，设立县级库车市"));

        relatedList.add(new AreaDiff("湖北省-十堰市-郧县", "420321", "湖北省-十堰市-郧阳区", "郧县于2014年12月撤销，设立十堰市郧阳区"));
        relatedList.add(new AreaDiff("湖北省-荆门市-京山县", "420821", "湖北省-荆门市-京山市", "京山县于2018年撤销，设立荆门市京山市"));
        relatedList.add(new AreaDiff("湖北省-荆州市-监利县", "421023", "湖北省-荆州市-监利市", "监利县于2020年撤销，设立荆州市监利市"));

        relatedList.add(new AreaDiff("辽宁省-大连市-普兰店市", "210282", "辽宁省-大连市-普兰店区", "普兰店市已于2015年撤销，设立大连市普兰店区"));
        relatedList.add(new AreaDiff("辽宁省-沈阳市-浑南新区", "210112", "辽宁省-沈阳市-浑南区", "浑南新区现为沈阳市浑南区"));
        relatedList.add(new AreaDiff("辽宁省-沈阳市-辽中县", "210122", "辽宁省-沈阳市-辽中区", "辽中县已于2016年撤销，设立沈阳市辽中区"));
        relatedList.add(new AreaDiff("辽宁省-盘锦市-大洼县", "211121", "辽宁省-盘锦市-大洼区", "大洼县已于2014年撤销，设立盘锦市大洼区"));

        relatedList.add(new AreaDiff("山东省-东营市-垦利县", "370521", "山东省-东营市-垦利区", "垦利县已于2016年撤销，设立东营市垦利区"));
        relatedList.add(new AreaDiff("山东省-滨州市-沾化县", "371621", "山东省-滨州市-沾化区", "沾化县已于2014年9月撤销，设立滨州市沾化区"));
        relatedList.add(new AreaDiff("山东省-滨州市-邹平县", "371626", "山东省-滨州市-邹平市", "邹平县已于2018年撤销，设立滨州市邹平市"));
        relatedList.add(new AreaDiff("山东省-聊城市-茌平县", "371523", "山东省-聊城市-茌平区", "茌平县已于2019年9月撤销，设立聊城市茌平区"));
        relatedList.add(new AreaDiff("山东省-烟台市-蓬莱市", "370684", "山东省-烟台市-蓬莱区", "蓬莱市已于2020年6月撤销，设立烟台市蓬莱区"));
        relatedList.add(new AreaDiff("山东省-烟台市-长岛县", "370634", "山东省-烟台市-蓬莱区", "长岛县已于2020年6月撤销，合并至烟台市蓬莱区"));
        relatedList.add(new AreaDiff("山东省-威海市-文登市", "371081", "山东省-威海市-文登区", "文登市已于2014年撤销，设立威海市文登区"));
        relatedList.add(new AreaDiff("山东省-青岛市-即墨市", "370282", "山东省-青岛市-即墨区", "即墨市已于2017年撤销，设立青岛市即墨区"));
        relatedList.add(new AreaDiff("山东省-青岛市-四方区", "370205", "山东省-青岛市-市北区", "四方区已于2012年撤销，并入青岛市市北区"));
        relatedList.add(new AreaDiff("山东省-青岛市-胶南市", "370283", "山东省-青岛市-黄岛区", "胶南市已于2012年撤销，设立青岛市黄岛区"));
        relatedList.add(new AreaDiff("山东省-德州市-陵县", "371421", "山东省-德州市-陵城区", "陵县已于2014年撤销，设立德州市陵城区"));
        relatedList.add(new AreaDiff("山东省-菏泽市-定陶县", "371721", "山东省-菏泽市-定陶区", "定陶县已于2016年撤销，设立菏泽市定陶区"));
        relatedList.add(new AreaDiff("山东省-济宁市-市中区", "370802", "山东省-济宁市-任城区", "济宁市市中区已于2013年撤销，并入新的济宁市任城区"));
        relatedList.add(new AreaDiff("山东省-济宁市-兖州市", "370881", "山东省-济宁市-兖州区", "兖州市已于2013年撤销，设立济宁市兖州区"));
        relatedList.add(new AreaDiff("山东省-临沂市-苍山县", "371322", "山东省-临沂市-兰陵县", "苍山县已于2014年更名为兰陵县"));

        relatedList.add(new AreaDiff("上海市-市辖区-崇明县", "310230", "上海市-市辖区-崇明区", "崇明县已于2016年撤销，设立上海市崇明区"));
        relatedList.add(new AreaDiff("上海市-市辖区-闸北区", "310108", "上海市-市辖区-静安区", "闸北区已于2015年撤销，并入上海市静安区"));
        relatedList.add(new AreaDiff("上海市-市辖区-卢湾区", "310103", "上海市-市辖区-黄浦区", "卢湾区已于2011年撤销，并入上海市黄浦区"));
        relatedList.add(new AreaDiff("上海市-市辖区-南汇区", "310119", "上海市-市辖区-浦东新区", "南汇区已于2009年撤销，并入上海市浦东新区"));

        relatedList.add(new AreaDiff("贵州省-黔西南布依族苗族自治州-兴仁县", "522322", "贵州省-黔西南布依族苗族自治州-兴仁市", "兴仁县已于2018年8月撤销，设立县级兴仁市"));
        relatedList.add(new AreaDiff("贵州省-贵阳市-小河区", "520114", "贵州省-贵阳市-花溪区", "小河区已于2012年11月撤销，并入贵阳市花溪区"));
        relatedList.add(new AreaDiff("贵州省-毕节市-黔西县", "520522", "贵州省-毕节市-黔西市", "黔西县已于2021年3月撤销，设立县级黔西市"));
        relatedList.add(new AreaDiff("贵州省-遵义市-务川县", "520384", "贵州省-遵义市-务川仡佬族苗族自治县", "务川县在更名为务川仡佬族苗族自治县后，行政区划代码并未发生变化"));
        relatedList.add(new AreaDiff("贵州省-铜仁市-玉屏县", "520622", "贵州省-铜仁市-玉屏侗族自治县", "玉屏县在1984年更名为玉屏侗族自治县，但行政区划代码未发生变化"));
        relatedList.add(new AreaDiff("贵州省-铜仁市-印江县", "520625", "贵州省-铜仁市-印江土家族苗族自治县", "印江县在1986年12月更名为印江土家族苗族自治县，行政区划代码未发生变化"));
        relatedList.add(new AreaDiff("贵州省-铜仁市-松桃县", "520628", "贵州省-铜仁市-松桃苗族自治县", "松桃县在1956年更名为松桃苗族自治县，行政区划代码未发生变化"));
        relatedList.add(new AreaDiff("贵州省-铜仁市-沿河县", "520627", "贵州省-铜仁市-沿河土家族自治县", "沿河县在1986年更名为沿河土家族自治县，行政区划代码未发生变化"));

        relatedList.add(new AreaDiff("安徽省-铜陵市-铜陵县", "340721", "安徽省-铜陵市-义安区", "铜陵县已于2015年撤销，设立义安区"));
        relatedList.add(new AreaDiff("安徽省-铜陵市-铜官山区", "340702", "安徽省-铜陵市-铜官区", "铜官山区已于2015年撤销，设立铜官区"));
        relatedList.add(new AreaDiff("安徽省-铜陵市-狮子山区", "340703", "安徽省-铜陵市-铜官区", "狮子山区已于2015年撤销，其区域并入铜官区"));
        relatedList.add(new AreaDiff("安徽省-芜湖市-繁昌县", "340222", "安徽省-芜湖市-繁昌区", "繁昌县已于2020年撤销，设立繁昌区"));
        relatedList.add(new AreaDiff("安徽省-宣城市-广德县", "341822", "安徽省-宣城市-广德市", "广德县已于2019年撤销，设立县级广德市"));

        relatedList.add(new AreaDiff("福建省-福州市-长乐市", "350182", "福建省-福州市-长乐区", "长乐市已于2017年撤销，设立长乐区"));
        relatedList.add(new AreaDiff("福建省-漳州市-龙海市", "350681", "福建省-漳州市-龙海区", "龙海市已于2021年撤销，设立龙海区"));
        relatedList.add(new AreaDiff("福建省-漳州市-长泰县", "350625", "福建省-漳州市-长泰区", "长泰县已于2021年撤销，设立长泰区"));
        relatedList.add(new AreaDiff("福建省-三明市-梅列区", "350402", "福建省-三明市-三元区", "梅列区已于2021年撤销，设立新的三元区"));
        relatedList.add(new AreaDiff("福建省-三明市-沙县", "350427", "福建省-三明市-沙县区", "沙县已于2021年撤销，设立沙县区"));
        relatedList.add(new AreaDiff("福建省-龙岩市-永定县", "350822", "福建省-龙岩市-永定区", "永定县已于2014年撤销，设立永定区"));
        relatedList.add(new AreaDiff("福建省-南平市-建阳市", "350784", "福建省-南平市-建阳区", "建阳市已于2014年撤销，设立建阳区"));

        relatedList.add(new AreaDiff("湖南省-永州市-祁阳县", "431121", "湖南省-永州市-祁阳市", "祁阳县已于2021年撤销，设立县级祁阳市"));

        relatedList.add(new AreaDiff("江苏省-连云港市-赣榆县", "320721", "江苏省-连云港市-赣榆区", "赣榆县已改为赣榆区"));
        relatedList.add(new AreaDiff("江苏省-连云港市-新浦区", "320703", "江苏省-连云港市-海州区", "新浦区已并入海州区"));
        relatedList.add(new AreaDiff("江苏省-南京市-白下区", "320103", "江苏省-南京市-秦淮区", "白下区已并入秦淮区"));
        relatedList.add(new AreaDiff("江苏省-苏州市-吴江市", "320584", "江苏省-苏州市-吴江区", "吴江市已改为吴江区"));
        relatedList.add(new AreaDiff("江苏省-扬州市-江都市", "321088", "江苏省-扬州市-江都区", "江都市已改为江都区"));
        relatedList.add(new AreaDiff("江苏省-常州市-金坛市", "320482", "江苏省-常州市-金坛区", "金坛市已改为金坛区"));
        relatedList.add(new AreaDiff("江苏省-常州市-戚墅堰区", "320405", "江苏省-常州市-武进区", "戚墅堰区已并入武进区"));
        relatedList.add(new AreaDiff("江苏省-苏州市-沧浪区", "320502", "江苏省-苏州市-姑苏区", "沧浪区已并入姑苏区"));
        relatedList.add(new AreaDiff("江苏省-苏州市-金阊区", "320504", "江苏省-苏州市-姑苏区", "金阊区已并入姑苏区"));
        relatedList.add(new AreaDiff("江苏省-苏州市-平江区", "320503", "江苏省-苏州市-姑苏区", "平江区已并入姑苏区"));

        relatedList.add(new AreaDiff("青海省-玉树藏族自治州-玉树县", "632723", "青海省-玉树藏族自治州-玉树市", "玉树县已改为玉树市"));
        relatedList.add(new AreaDiff("青海省-黄南藏族自治州-同仁县", "632321", "青海省-黄南藏族自治州-同仁市", "同仁县已改为同仁市"));
        relatedList.add(new AreaDiff("青海省-西宁市-湟中县", "630122", "青海省-西宁市-湟中区", "湟中县已改为湟中区"));

        relatedList.add(new AreaDiff("广西壮族自治区-百色市-田阳县", "451021", "广西壮族自治区-百色市-田阳区", "田阳县已改为田阳区"));
        relatedList.add(new AreaDiff("广西壮族自治区-百色市-平果县", "451023", "广西壮族自治区-百色市-平果市", "平果县已改为平果市"));
        relatedList.add(new AreaDiff("广西壮族自治区-百色市-靖西县", "451025", "广西壮族自治区-百色市-靖西市", "靖西县已改为靖西市"));
        relatedList.add(new AreaDiff("广西壮族自治区-河池市-宜州市", "451281", "广西壮族自治区-河池市-宜州区", "宜州市已改为宜州区"));
        relatedList.add(new AreaDiff("广西壮族自治区-柳州市-柳江县", "450221", "广西壮族自治区-柳州市-柳江区", "柳江县已改为柳江区"));
        relatedList.add(new AreaDiff("广西壮族自治区-桂林市-临桂县", "450322", "广西壮族自治区-桂林市-临桂区", "临桂县已改为临桂区"));
        relatedList.add(new AreaDiff("广西壮族自治区-桂林市-荔浦县", "450331", "广西壮族自治区-桂林市-荔浦市", "荔浦县已改为荔浦市"));
        relatedList.add(new AreaDiff("广西壮族自治区-南宁市-武鸣县", "450122", "广西壮族自治区-南宁市-武鸣区", "武鸣县已改为武鸣区"));
        relatedList.add(new AreaDiff("广西壮族自治区-南宁市-横县", "450127", "广西壮族自治区-南宁市-横州市", "横县已改为横州市"));
        relatedList.add(new AreaDiff("广西壮族自治区-梧州市-蝶山区", "450403", "广西壮族自治区-梧州市-万秀区", "蝶山区已并入万秀区"));

        relatedList.add(new AreaDiff("浙江省-宁波市-奉化市", "330213", "浙江省-宁波市-奉化区", "奉化市已改为奉化区"));
        relatedList.add(new AreaDiff("浙江省-宁波市-江东区", "330204", "浙江省-宁波市-鄞州区", "江东区已撤销，其行政区域划归宁波市鄞州区"));
        relatedList.add(new AreaDiff("浙江省-台州市-玉环县", "331021", "浙江省-台州市-玉环市", "玉环县已改为玉环市"));
        relatedList.add(new AreaDiff("浙江省-温州市-洞头县", "330322", "浙江省-温州市-洞头区", "洞头县已改为洞头区"));
        relatedList.add(new AreaDiff("浙江省-金华市-市区", "330702", "浙江省-金华市-婺城区", "金华市区主要指婺城区，属于金华市的市辖区"));
        relatedList.add(new AreaDiff("浙江省-绍兴市-上虞市", "330682", "浙江省-绍兴市-上虞区", "上虞市已改为绍兴市上虞区"));

        relatedList.add(new AreaDiff("河北省-唐山市-滦县", "130223", "河北省-唐山市-滦州市", "滦县已改为滦州市，新编码为130281"));
        relatedList.add(new AreaDiff("河北省-唐山市-唐海县", "130224", "河北省-唐山市-曹妃甸区", "唐海县已改为曹妃甸区，新编码为130209"));
        relatedList.add(new AreaDiff("河北省-秦皇岛市-抚宁县", "130323", "河北省-秦皇岛市-抚宁区", "抚宁县已改为抚宁区，新编码为130306"));
        relatedList.add(new AreaDiff("河北省-承德市-平泉县", "130823", "河北省-承德市-平泉市", "平泉县已改为平泉市，新编码为130881"));
        relatedList.add(new AreaDiff("河北省-衡水市-冀州市", "131181", "河北省-衡水市-冀州区", "冀州市已改为冀州区，新编码为131103"));

        relatedList.add(new AreaDiff("河北省-邯郸市-永年县", "130423", "河北省-邯郸市-永年区", "永年县已改为永年区"));
        relatedList.add(new AreaDiff("河北省-邯郸市-从台区", "130403", "河北省-邯郸市-丛台区", "正确的名称是丛台区"));
        relatedList.add(new AreaDiff("河北省-邯郸市-肥乡县", "130428", "河北省-邯郸市-肥乡区", "肥乡县已改为肥乡区"));

        relatedList.add(new AreaDiff("河北省-保定市-新市区", "130602", "河北省-保定市-竞秀区", "新市区已更名为竞秀区"));
        relatedList.add(new AreaDiff("河北省-保定市-满城县", "130621", "河北省-保定市-满城区", "满城县已改为满城区"));
        relatedList.add(new AreaDiff("河北省-保定市-南市区", "130602", "河北省-保定市-莲池区", "南市区已并入莲池区"));
        relatedList.add(new AreaDiff("河北省-保定市-北市区", "130603", "河北省-保定市-莲池区", "北市区已并入莲池区"));
        relatedList.add(new AreaDiff("河北省-保定市-清苑县", "130622", "河北省-保定市-清苑区", "清苑县已改为清苑区"));
        relatedList.add(new AreaDiff("河北省-保定市-徐水县", "130625", "河北省-保定市-徐水区", "徐水县已改为徐水区"));

        relatedList.add(new AreaDiff("河北省-张家口市-万全县", "130729", "河北省-张家口市-万全区", "万全县已改为万全区"));
        relatedList.add(new AreaDiff("河北省-张家口市-崇礼县", "130733", "河北省-张家口市-崇礼区", "崇礼县已改为崇礼区"));
        relatedList.add(new AreaDiff("河北省-张家口市-宣化县", "130731", "河北省-张家口市-宣化区", "宣化县已改为宣化区"));

        relatedList.add(new AreaDiff("甘肃省-平凉市-华亭县", "620824", "甘肃省-平凉市-华亭市", "华亭县已改为华亭市"));

        relatedList.add(new AreaDiff("四川省-绵阳市-北川县", "510726", "四川省-绵阳市-北川羌族自治县", "北川县已改为北川羌族自治县"));
        relatedList.add(new AreaDiff("四川省-凉山彝族自治州-会理县", "513425", "四川省-凉山彝族自治州-会理市", "会理县已改为会理市"));

        relatedList.add(new AreaDiff("天津市-市辖区-蓟县", "120225", "天津市-市辖区-蓟州区", "蓟县已改为蓟州区"));
        relatedList.add(new AreaDiff("天津市-市辖区-静海县", "120223", "天津市-市辖区-静海区", "静海县已改为静海区"));
        relatedList.add(new AreaDiff("天津市-市辖区-宁河县", "120221", "天津市-市辖区-宁河区", "宁河县已改为宁河区"));
        relatedList.add(new AreaDiff("天津市-市辖区-汉沽区", "120116", "天津市-市辖区-滨海新区", "汉沽区已并入滨海新区"));
        relatedList.add(new AreaDiff("天津市-市辖区-塘沽区", "120116", "天津市-市辖区-滨海新区", "塘沽区已并入滨海新区"));
        relatedList.add(new AreaDiff("天津市-市辖区-大港区", "120116", "天津市-市辖区-滨海新区", "大港区已并入滨海新区"));

        relatedList.add(new AreaDiff("北京市-市辖区-密云县", "110228", "北京市-市辖区-密云区", "密云县已改为密云区"));
        relatedList.add(new AreaDiff("北京市-市辖区-延庆县", "110229", "北京市-市辖区-延庆区", "延庆县已改为延庆区"));

        relatedList.add(new AreaDiff("西藏自治区-山南市-错那县", "540530", "西藏自治区-山南市-错那市", "撤销错那县，设立县级错那市"));
        relatedList.add(new AreaDiff("西藏自治区-山南市-乃东县", "542221", "西藏自治区-山南市-乃东区", "撤销乃东县，设立乃东区"));
        relatedList.add(new AreaDiff("西藏自治区-林芝市-米林县", "540422", "西藏自治区-林芝市-米林市", "撤销米林县，设立县级米林市"));

        relatedList.add(new AreaDiff("吉林省-长春市-九台市", "220113", "吉林省-长春市-九台区", "九台市已于2014年撤销，设立长春市九台区，行政区域代码保持不变"));

        relatedList.add(new AreaDiff("山西省-大同市-大同县", "140221", "山西省-大同市-云州区", "大同县已撤销，设立大同市云州区"));
        relatedList.add(new AreaDiff("山西省-大同市-矿区", "140203", "山西省-大同市-云冈区", "矿区已撤销，设立大同市云冈区"));
        relatedList.add(new AreaDiff("山西省-大同市-城区", "140202", "山西省-大同市-平城区", "城区已撤销，设立大同市平城区"));
        relatedList.add(new AreaDiff("山西省-大同市-南郊区", "140211", "山西省-大同市-云冈区", "南郊区作为行政区划被撤销，其部分地区被划入新的行政区，南郊区古店镇划归云冈区"));
        relatedList.add(new AreaDiff("山西省-长治市-城区", "140402", "山西省-长治市-潞州区", "长治市城区已撤销，设立潞州区"));
        relatedList.add(new AreaDiff("山西省-长治市-郊区", "140411", "山西省-长治市-潞州区", "长治市郊区已撤销，设立潞州区"));
        relatedList.add(new AreaDiff("山西省-长治市-长治县", "140421", "山西省-长治市-上党区", "长治县已撤销，设立上党区"));
        relatedList.add(new AreaDiff("山西省-长治市-屯留县", "140424", "山西省-长治市-屯留区", "屯留县已撤销，设立屯留区"));
        relatedList.add(new AreaDiff("山西省-长治市-潞城市", "140481", "山西省-长治市-潞城区", "潞城市已撤销，设立潞城区"));
        relatedList.add(new AreaDiff("山西省-晋中市-太谷县", "140726", "山西省-晋中市-太谷区", "太谷县已撤销，设立晋中市太谷区，行政区域管辖范围和政府驻地不变"));

        relatedList.add(new AreaDiff("云南省-昭通市-水富县", "530628", "云南省-昭通市-水富市", "水富县已改为水富市"));
        relatedList.add(new AreaDiff("云南省-曲靖市-马龙县", "530321", "云南省-曲靖市-马龙区", "马龙县已改为马龙区"));
        relatedList.add(new AreaDiff("云南省-曲靖市-沾益县", "530320", "云南省-曲靖市-沾益区", "沾益县已改为沾益区"));
        relatedList.add(new AreaDiff("云南省-红河哈尼族彝族自治州-弥勒县", "532526", "云南省-红河哈尼族彝族自治州-弥勒市", "弥勒县已改为弥勒市"));
        relatedList.add(new AreaDiff("云南省-红河哈尼族彝族自治州-蒙自县", "532522", "云南省-红河哈尼族彝族自治州-蒙自市", "蒙自县已改为蒙自市"));
        relatedList.add(new AreaDiff("云南省-玉溪市-江川县", "530422", "云南省-玉溪市-江川区", "江川县已改为江川区"));
        relatedList.add(new AreaDiff("云南省-玉溪市-澄江县", "530422", "云南省-玉溪市-澄江市", "撤销澄江县，设立县级澄江市"));
        relatedList.add(new AreaDiff("云南省-迪庆藏族自治州-香格里拉县", "533426", "云南省-迪庆藏族自治州-香格里拉市", "香格里拉县已改为香格里拉市"));
        relatedList.add(new AreaDiff("云南省-楚雄彝族自治州-禄丰县", "532331", "云南省-楚雄彝族自治州-禄丰市", "撤销禄丰县，设立县级禄丰市"));
        relatedList.add(new AreaDiff("云南省-昆明市-石林县", "530126", "云南省-昆明市-石林彝族自治县", "石林县已改为石林彝族自治县，行政区划代码未变"));

        relatedList.add(new AreaDiff("江西省-南昌市-红谷滩新区", "360125", "江西省-南昌市-红谷滩区", "红谷滩新区在早期作为南昌市的一个功能区；2019年12月经国务院批复设立为红谷滩"));
        relatedList.add(new AreaDiff("江西省-赣州市-龙南县", "360727", "江西省-赣州市-龙南市", "龙南县于2020年6月经国务院批准撤销，设立县级龙南市"));
        relatedList.add(new AreaDiff("江西省-赣州市-南康市", "360782", "江西省-赣州市-南康区", "南康市于2013年11月经国务院批准撤销，设立赣州市南康区"));
        relatedList.add(new AreaDiff("江西省-抚州市-东乡县", "361029", "江西省-抚州市-东乡区", "东乡县已改为东乡区"));
        relatedList.add(new AreaDiff("江西省-鹰潭市-余江县", "360622", "江西省-鹰潭市-余江区", "余江县已改为余江区"));
        relatedList.add(new AreaDiff("江西省-九江市-星子县", "360427", "江西省-九江市-庐山市", "星子县已改为庐山市"));
        relatedList.add(new AreaDiff("江西省-九江市-庐山区", "360402", "江西省-九江市-濂溪区", "庐山区已改为濂溪区"));
        relatedList.add(new AreaDiff("江西省-九江市-九江县", "360421", "江西省-九江市-柴桑区", "九江县已改为柴桑区"));
        relatedList.add(new AreaDiff("江西省-上饶市-上饶县", "361121", "江西省-上饶市-广信区", "上饶县已改为广信区"));

        relatedList.add(new AreaDiff("陕西省-汉中市-南郑县", "610721", "陕西省-汉中市-南郑区", "南郑县已改为南郑区"));
        relatedList.add(new AreaDiff("陕西省-西安市-高陵县", "610126", "陕西省-西安市-高陵区", "高陵县已改为高陵区"));
        relatedList.add(new AreaDiff("陕西省-咸阳市-彬县", "610429", "陕西省-咸阳市-彬州市", "彬县已改为彬州市"));
        relatedList.add(new AreaDiff("陕西省-宝鸡市-凤翔县", "610322", "陕西省-宝鸡市-凤翔区", "凤翔县已改为凤翔区"));
        relatedList.add(new AreaDiff("陕西省-安康市-旬阳县", "610928", "陕西省-安康市-旬阳市", "旬阳县已改为旬阳市"));

        // 名称有异常的，可以识别出关联关系
        relatedList.add(new AreaDiff("四川省-攀枝花市-西  区", "510403", "四川省-攀枝花市-西区", "名称多空格，攀枝花市西区"));
        relatedList.add(new AreaDiff("四川省-攀枝花市-东  区", "510402", "四川省-攀枝花市-东区", "名称多空格，攀枝花市东区"));
        relatedList.add(new AreaDiff("云南省-昆明市-寻甸回族彝族自治县 ", "530129", "云南省-昆明市-寻甸回族彝族自治县", "名称多空格，寻甸回族彝族自治县行政区划代码未变"));
        relatedList.add(new AreaDiff("黑龙江省-齐齐哈尔市-梅里斯达斡尔区", "230208", "黑龙江省-齐齐哈尔市-梅里斯达斡尔族区", "名称不规范，梅里斯达斡尔族区（有时简称为梅里斯区，但“梅里斯达斡尔区”并非正式名称）"));
        relatedList.add(new AreaDiff("内蒙古自治区-呼伦贝尔市-莫力达瓦", "150722", "内蒙古自治区-呼伦贝尔市-莫力达瓦达斡尔族自治旗", "名称不规范，正式名称：莫力达瓦达斡尔族自治旗"));
        relatedList.add(new AreaDiff("河南省-郑州市-管城区", "410191", "河南省-郑州市-管城回族区", "名称不规范，管城区（全称管城回族区）编码未变更。原先库里存的【410191】是河南省郑州市的郑州经济技术开发区的统计用代码"));
        relatedList.add(new AreaDiff("新疆维吾尔自治区-伊犁哈萨克自治州-察布查尔", "654022", "新疆维吾尔自治区-伊犁哈萨克自治州-察布查尔锡伯自治县", "名称不规范，正式名称：察布查尔锡伯自治县"));
        relatedList.add(new AreaDiff("新疆维吾尔自治区-喀什地区-塔什库尔干", "653131", "新疆维吾尔自治区-喀什地区-塔什库尔干塔吉克自治县", "名称不规范，正式名称：塔什库尔干塔吉克自治县"));
        relatedList.add(new AreaDiff("黑龙江省-齐齐哈尔市-铁峰区", "230204", "黑龙江省-齐齐哈尔市-铁锋区", "错别字，铁峰区”应为“铁锋区”的误写，因原齐齐哈尔铁路局座落于辖区内而得名，寓意“铁路先锋区”"));
        relatedList.add(new AreaDiff("安徽省-阜阳市-颖上县", "341226", "安徽省-阜阳市-颍上县", "错别字，颍上县的正确写法为“颍上县”。其中，“颍”字左半部分下方是“水”，与颍河有关"));
    }

    @Transactional(rollbackFor = Exception.class)
    public void dealHistory() {
        log.info("开始处理历史数据...");
        // 加载配置数据
        Map<String, String> invalidDistrictMap = new HashMap<>();
        Map<String, String> specialCodeMap = new HashMap<>();
        List<AreaDiff> relatedList = new ArrayList<>();
        init(invalidDistrictMap, specialCodeMap, relatedList);
        // 读取数据库
        List<TbArea> dbAreaList = areaMapper.getAreaList(Constants.AREA_ONLY_VALID);
        AreaUtil.setNamePath(dbAreaList);
        Map<String, TbArea> namePathMap = dbAreaList.stream().collect(Collectors.toMap(TbArea::getNamePath, Function.identity()));
        // 获取高德数据
        List<AMapDistrict> gdList = AMapApiUtil.getAllDistricts();
        Map<String, AMapDistrict> gdNamePathMap = gdList.stream().collect(Collectors.toMap(x -> AreaUtil.adjustNamePathForSpecialRegions(x.getNamePath()), Function.identity()));
        // 处理特殊逻辑
        specialNode(namePathMap);
        // 名称能对应上的，维护area_code、parent_area_code、longitude、latitude、ord字段
        updateByGd(namePathMap, gdNamePathMap);
        // 直接置成无效的数据（层级有改变、挂接错误、未使用的功能区、street级、不规范的行政区划、台湾省下挂）
        toInvalid(namePathMap, invalidDistrictMap);
        // 能对应到现行区划单位的，维护历史编码和现行编码，然后置成无效。
        needRelated(relatedList, namePathMap, gdNamePathMap);
        // 户口簿已使用的功能区，保留并生成特殊编码
        specialCode(specialCodeMap, namePathMap);
        // 检测是否全部覆盖
        List<TbArea> notUpdateList = dbAreaList.stream().filter(x -> !x.isFlag()).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(notUpdateList)) {
            throw new RuntimeException("未能更新的区域" + notUpdateList.stream().map(TbArea::getNamePath).collect(Collectors.toList()));
        }
        // 批量更新
        areaMapper.batchUpdate(dbAreaList);
        // 比较差异并新增缺少的地点
        compareAndInsert(namePathMap, gdNamePathMap);
        // 更新父级编码
        updateParentCode(namePathMap);
        // 清缓存
        redisUtil.delete(RedisKeyEnum.ALL_AREA, Constants.AREA_ALL);
        redisUtil.delete(RedisKeyEnum.ALL_AREA, Constants.AREA_ONLY_VALID);

        log.info("地区历史数据处理完成...");
    }

    private void updateParentCode(Map<String, TbArea> namePathMap) {
        List<TbArea> updateCodeList = new ArrayList<>();
        for (Map.Entry<String, TbArea> entry : namePathMap.entrySet()) {
            String namePath = entry.getKey();
            TbArea node = entry.getValue();
            if (Objects.equals(node.getLevel(), 0) || node.getDisplayable() == (byte) 0) {
                continue;
            }

            if (!namePath.contains("-")){
                node.setParentAreaCode(namePathMap.get("中国").getAreaCode());
            }else {
                // 获取父节点的namePath
                String parentNamePath = namePath.substring(0, namePath.lastIndexOf("-"));
                TbArea parentNode = namePathMap.get(parentNamePath);
                // 检查父节点是否存在
                if (parentNode == null) {
                    throw new RuntimeException("找不到父节点：" + parentNamePath);
                }
                node.setParentAreaCode(parentNode.getAreaCode());
            }
            updateCodeList.add(node);
        }
        if (CollUtil.isNotEmpty(updateCodeList)){
            areaMapper.batchUpdate(updateCodeList);
        }
    }

    public static void main(String[] args) {
        List<AMapDistrict> gdList = AMapApiUtil.getAllDistricts();
        Map<String, AMapDistrict> gdNamePathMap = gdList.stream()
                .collect(Collectors.toMap(AMapDistrict::getNamePath, Function.identity()));

        for (Map.Entry<String, AMapDistrict> entry : gdNamePathMap.entrySet()) {
            String namePath = entry.getKey();
            AMapDistrict node = entry.getValue();

            // 跳过省份级别的节点
            if ("province".equals(node.getLevel())) {
                continue;
            }

            // 获取父节点的namePath
            String parentNamePath = namePath.substring(0, namePath.lastIndexOf("-"));
            AMapDistrict parentNode = gdNamePathMap.get(parentNamePath);

            // 检查父节点是否存在
            if (parentNode == null) {
                log.error("错误：找不到父节点 {} 对应的区县 {}", parentNamePath, namePath);
                continue;
            }

            // 获取当前节点和父节点的编码
            String code = node.getAdcode();
            String parentCode = parentNode.getAdcode();

            checkCode(namePath, parentNamePath, code, parentCode);
        }
    }

    private static void checkCode(String namePath, String parentNamePath, String code, String parentCode) {
        // 检查编码格式是否为六位数
        if (code == null
//                || code.length() != 6
        ) {
            log.error("错误：区县 {} 的编码 {} 不是六位数", namePath, code);
            return;
        }

        if (parentCode == null
//                || parentCode.length() != 6
        ) {
            log.error("错误：父节点 {} 的编码 {} 不是六位数", parentNamePath, parentCode);
            return;
        }

        // 通过namePath获取节点级别
        String[] levels = namePath.split("-");
        int nodeLevel = levels.length;
        if (nodeLevel != 2 && nodeLevel != 3) {
            log.error("错误：未知的节点级别 {} 对应的区县 {}", nodeLevel, namePath);
            return;
        }

        // 检查编码规则
        String expectedParentCode;
        if (nodeLevel == 2) { // 假设3级是city级别
            expectedParentCode = code.substring(0, 2) + "0000"; // 提取前两位作为预期的省份编码
        } else if (nodeLevel == 3) { // 假设2级是district级别
            expectedParentCode = code.substring(0, 4) + "00"; // 提取前四位作为预期的市编码
        } else {
            log.error("错误：未知的节点级别 {} 对应的区县 {}", nodeLevel, namePath);
            return;
        }

        if (!expectedParentCode.equals(parentCode)) {
            log.error("错误：区县 {} 的编码 {} 不符合父节点 {} 的编码 {}", namePath, code, parentNamePath, parentCode);
        } else {
//            log.info("验证通过：区县 {} 的编码 {} 符合父节点 {} 的编码 {}", namePath, code, parentNamePath, parentCode);
        }
    }

    private void specialNode(Map<String, TbArea> namePathMap) {
        // 顶级节点维护编码和经纬度
        TbArea country = namePathMap.get("中国");
        country.setAreaCode("100000");
        country.setLongitude(116.3683244);
        country.setLatitude(39.915085);
        checkAndSetFlag(country);

        // 台湾、香港、澳门，city级虚拟节点
        TbArea xg = namePathMap.get("香港特别行政区-香港");
        xg.setAreaCode("810000VIRTUAL");
        checkAndSetFlag(xg);
        TbArea am = namePathMap.get("澳门特别行政区-澳门");
        am.setAreaCode("820000VIRTUAL");
        checkAndSetFlag(am);
        TbArea tw = namePathMap.get("台湾-台湾");
        tw.setAreaCode("710000VIRTUAL");
        checkAndSetFlag(tw);

        TbArea other = namePathMap.get("其他地区");
        checkAndSetFlag(other);
    }

    private void specialCode(Map<String, String> specialCodeMap, Map<String, TbArea> namePathMap) {
        Set<String> generatedCodes = new HashSet<>();
        for (String key : specialCodeMap.keySet()) {
            TbArea tbArea = namePathMap.get(key);
            if (tbArea == null) {
                throw new RuntimeException("specialCodeMap数据有误：" + key + "对应的区域不存在");
            }
            String namePath = tbArea.getNamePath();
            String parentNamePath = namePath.substring(0, namePath.lastIndexOf("-"));
            TbArea parentTbArea = namePathMap.get(parentNamePath);
            if (parentTbArea == null) {
                throw new RuntimeException("parentTbArea找不到：" + parentNamePath);
            }
            if (parentTbArea.getDisplayable() == (byte) 0 || !parentTbArea.isFlag() || StrUtil.isBlank(parentTbArea.getAreaCode())) {
                throw new RuntimeException("parentTbArea不可用：" + parentNamePath);
            }

            String baseCode = parentTbArea.getAreaCode().substring(0, 4) + tbArea.getAbbrSpell();
            String areaCode = baseCode + "-GNQ";
            int suffix = 1;
            while (generatedCodes.contains(areaCode)) {
                areaCode = baseCode + suffix++ + "-GNQ";
            }
            tbArea.setAreaCode(areaCode);
            tbArea.setOrd(999999);
            tbArea.setSort((short) 99);
            generatedCodes.add(areaCode);
            checkAndSetFlag(tbArea);
        }
    }

    private void updateByGd(Map<String, TbArea> namePathMap, Map<String, AMapDistrict> gdNamePathMap) {
        for (Map.Entry<String, TbArea> entry : namePathMap.entrySet()) {
            String key = entry.getKey();
            TbArea dbNode = entry.getValue();
            AMapDistrict gdNode = gdNamePathMap.get(key);
            if (gdNode == null) {
                continue;
            }
            dbNode.setAreaCode(gdNode.getAdcode());
            Pair<Double, Double> longitudeAndLatitudeAsDouble = AMapApiUtil.getLongitudeAndLatitudeAsDouble(gdNode);
            dbNode.setLongitude(longitudeAndLatitudeAsDouble.getKey());
            dbNode.setLatitude(longitudeAndLatitudeAsDouble.getValue());
            dbNode.setOrd(gdNode.getOrd());
            dbNode.setSort((short) gdNode.getSort());
            checkAndSetFlag(dbNode);
        }
    }

    private void needRelated(List<AreaDiff> relatedList, Map<String, TbArea> namePathMap, Map<String, AMapDistrict> gdNamePathMap) {
        Map<String, AreaDiff> needRelatedMap = relatedList.stream().collect(Collectors.toMap(AreaDiff::getNamePath, Function.identity()));
        for (Map.Entry<String, AreaDiff> entry : needRelatedMap.entrySet()) {
            String key = entry.getKey();
            AreaDiff areaDiff = entry.getValue();
            TbArea tbArea = namePathMap.get(key);
            if (tbArea == null) {
                throw new RuntimeException("relatedList里的历史信息有误：" + key + "对应的区域不存在");
            }

            if (StrUtil.isBlank(tbArea.getAreaCode())) {
                log.warn("历史编码为空：{}-{}", key, areaDiff.getAreaCode());
            }

            if (StrUtil.isNotBlank(tbArea.getAreaCode()) && !tbArea.getAreaCode().equals(areaDiff.getAreaCode())) {
                log.error("历史编码不一致：{}-{}-{}", key, tbArea.getAreaCode(), areaDiff.getAreaCode());
            }
//            tbArea.setAreaCode(areaDiff.getAreaCode()); // 需要维护吗？  不维护置成无效地点的area_code，原先有编码的保留，配置的也不一定准确。
            tbArea.setDisplayable((byte) 0);

            if (Objects.equals(areaDiff.getNamePath(), areaDiff.getNewNamePath())) {
                throw new RuntimeException("relatedList里的历史信息有误：" + key + "对应的区域名称与现行名称一致");
            }

            AMapDistrict gdNode = gdNamePathMap.get(areaDiff.getNewNamePath());
            if (gdNode == null) {
                throw new RuntimeException("relatedList里的现行信息有误：" + key + "对应的区域不存在");
            }

            tbArea.setRelatedAreaCode(gdNode.getAdcode());
            checkAndSetFlag(tbArea);
        }
    }

    private void checkAndSetFlag(TbArea tbArea) {
        if (tbArea.isFlag()) {
            throw new RuntimeException("已更新过，重复更新：" + tbArea.getNamePath());
        }
        tbArea.setFlag(true);
    }

    private void toInvalid(Map<String, TbArea> namePathMap, Map<String, String> invalidDistrictMap) {
        Set<String> toInvalidKeySet = Stream.concat(
                namePathMap.keySet().stream().filter(x -> x.startsWith("台湾") && !"台湾".equals(x) && !"台湾-台湾".equals(x)),
                invalidDistrictMap.keySet().stream()
        ).collect(Collectors.toSet());

        for (String key : toInvalidKeySet) {
            TbArea tbArea = namePathMap.get(key);
            if (tbArea == null) {
                throw new RuntimeException("invalidDistrictMap数据有误：" + key + "对应的区域不存在");
            }
            tbArea.setDisplayable((byte) 0);
            checkAndSetFlag(tbArea);
        }
    }

    private void compareAndInsert(Map<String, TbArea> namePathMap, Map<String, AMapDistrict> gdNamePathMap) {
        // 对比差异，应该只存在需要新增的数据
        AreaDiffResult areaDiffResult = areaService.compareHandler();
        List<AreaDiff> addList = areaDiffResult.getAddList();
        List<AreaDiff> delList = areaDiffResult.getDelList();
        List<AreaDiff> nameChangeList = areaDiffResult.getNameChangeList();
        List<AreaDiff> dataChangeList = areaDiffResult.getDataChangeList();
        if (CollUtil.isNotEmpty(delList)) {
            throw new RuntimeException("数据处理有误，存在删除区域：" + delList.stream().map(AreaDiff::getNamePath).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(nameChangeList)) {
            throw new RuntimeException("数据处理有误，存在名称变更区域：" + nameChangeList.stream().map(AreaDiff::getNamePath).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(dataChangeList)) {
            throw new RuntimeException("数据处理有误，存在信息变更区域：" + dataChangeList.stream().map(AreaDiff::getNamePath).collect(Collectors.toList()));
        }
        // 新增缺少的地点
        if (CollUtil.isNotEmpty(addList)) {
            // 确保先父后子
            addList.sort(Comparator.comparing(AreaDiff::getAreaCode));
            // 取最大id,是否向上1000取整。只执行一次，查库写死，111009。
            AtomicInteger maxId = new AtomicInteger(12000);
            // 取最大areaId,是否向上1000取整。只执行一次，查库写死，13009。
            AtomicInteger maxAreaId = new AtomicInteger(14000);
            // 插入的集合
            List<TbArea> insertList = new ArrayList<>();

            for (AreaDiff add : addList) {
                String newNamePath = AreaUtil.adjustNamePathForSpecialRegions(add.getNewNamePath());
                AMapDistrict gdNode = gdNamePathMap.get(newNamePath);
                if (gdNode == null) {
                    throw new RuntimeException("新增区域：" + newNamePath + "不存在");
                }
                if (!Objects.equals(gdNode.getAdcode(), add.getNewAreaCode())) {
                    throw new RuntimeException("新增区域：" + newNamePath + "的adcode与区域编码不一致");
                }
                String parentNamePath = newNamePath.substring(0, newNamePath.lastIndexOf("-"));
                TbArea parentArea = namePathMap.get(parentNamePath);
                if (parentArea == null) {
                    throw new RuntimeException("新增区域：" + newNamePath + "的父级区域不存在");
                }
                if (parentArea.getDisplayable() == (byte) 0) {
                    throw new RuntimeException("新增区域：" + newNamePath + "的父级区域不可用");
                }
                TbArea tbArea = new TbArea();
                tbArea.setId(maxId.getAndIncrement());
                tbArea.setAreaid(String.valueOf(maxAreaId.getAndIncrement()));
                tbArea.setAreaCode(gdNode.getAdcode());
                tbArea.setName(gdNode.getName());
                if ("澳门大学横琴校区(由澳门实施管辖)".equals(gdNode.getName())) {
                    tbArea.setShortName("澳门大学横琴校区");
                } else {
                    tbArea.setShortName(gdNode.getName());
                }
                tbArea.setAbbrSpell(getAbbrSpell(tbArea.getShortName()));
                tbArea.setFullSpell(getFullSpell(tbArea.getShortName()));
                tbArea.setOrd(gdNode.getOrd());
                tbArea.setSort((short) gdNode.getSort());
                tbArea.setDisplayable((byte) 1);
                tbArea.setPid(parentArea.getAreaid());
                tbArea.setParentId(parentArea.getId());
                tbArea.setLevel(parentArea.getLevel() + 1);
                Pair<Double, Double> longitudeAndLatitudeAsDouble = AMapApiUtil.getLongitudeAndLatitudeAsDouble(gdNode);
                tbArea.setLongitude(longitudeAndLatitudeAsDouble.getKey());
                tbArea.setLatitude(longitudeAndLatitudeAsDouble.getValue());
                namePathMap.put(newNamePath, tbArea);
                insertList.add(tbArea);
            }
            areaMapper.batchInsert(insertList);
        }
    }
}
