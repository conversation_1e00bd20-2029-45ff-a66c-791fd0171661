package com.glodon.qydata.controller.temp.refreshEnterpriseId;

import com.alibaba.fastjson.JSONArray;
import com.glodon.qydata.vo.common.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description: 给已经初始化过项目信息的企业，在【工程分类】下方增加一行【产品定位】
 * @date 2022/7/18 14:14
 */
@RestController
@RequestMapping("/basicInfo/dataDeal")
@Slf4j
public class TempRefreshEntIdController {

    @Autowired
    private TempRefreshEntIdHandler tempRefreshEntIdHandler;

    /**
     * 刷新数据
     * @return
     */
    //@PostMapping("/refreshSysZbUser")
    public ResponseVo refreshSysZbUser(Boolean all) {
        Runnable r = () -> {
            log.info("---------------开始刷新企业id----------");
            tempRefreshEntIdHandler.refreshSysZbUser(all != null && all);
            log.info("---------------刷新企业id成功-------------");
        };
        new Thread(r).start();
        return ResponseVo.success();
    }

    @GetMapping("/getEntInfo")
    public ResponseVo getEntInfo(String globalIds){
        JSONArray result = tempRefreshEntIdHandler.getEntInfo(globalIds);
        return ResponseVo.success(result);
    }

    /**
     * 刷新大表
     * @return
     */
    //@PostMapping("/refreshBigTable")
    public ResponseVo refreshBigTable(String tables) {
        Runnable r = () -> {
            log.info("---------------开始刷新大表企业id----------");
            tempRefreshEntIdHandler.refreshBigTable(tables);
            log.info("---------------刷新大表企业id成功-------------");
        };
        new Thread(r).start();
        return ResponseVo.success();
    }

    /**
     * 验证企业编码和企业id对应关系
     * @return
     */
    //@PostMapping("/sysQyCodeIdVerify")
    public ResponseVo sysQyCodeIdVerify(int flag) {
        Runnable r = () -> {
            log.info("---------------开始验证----------");
            tempRefreshEntIdHandler.sysQyCodeIdVerify(flag);
            log.info("---------------验证成功-------------");
        };
        new Thread(r).start();
        return ResponseVo.success();
    }

    /**
     * 修复更新时间
     * @return
     */
    //@PostMapping("/repairUpdateTime")
    public ResponseVo repairUpdateTime(Integer size) {
        Runnable r = () -> {
            log.info("---------------开始修复----------");
            tempRefreshEntIdHandler.repairUpdateTime(size);
            log.info("---------------修复成功-------------");
        };
        new Thread(r).start();
        return ResponseVo.success();
    }

}
