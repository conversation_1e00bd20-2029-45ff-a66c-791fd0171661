package com.glodon.qydata.controller.temp.optionconvert;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;


@Service
@Slf4j
public class TempOptionConvertService {
    @Resource
    @Qualifier("delSelfExecutor")
    private ThreadPoolTaskExecutor delSelfExecutor;
    @Resource
    private TempDbService tempDbService;

    public static final String PROJECT = "project";
    public static final String PROJECT_SELF = "projectSelf";
    public static final String CONTRACT = "contract";
    public static final String CONTRACT_SELF = "contractSelf";
    public static final String FEATURE = "feature";
    public static final String FEATURE_SELF = "featureSelf";
    public static final String DESC = "desc";
    public static final String DESC_SELF = "descSelf";

    public void optionFormatUpgrade(String customerCodes, String type) {
        Set<String> customerCodeSet = getCustomerCodeSet(customerCodes, type);

        if (CollUtil.isEmpty(customerCodeSet)) {
            log.info("{}-已经没有需要处理的企业", type);
            return;
        }

        AtomicInteger atomicInteger = new AtomicInteger();
        List<String> errorLogList = new ArrayList<>(); // 用于收集错误日志
        CountDownLatch countDownLatch = new CountDownLatch(customerCodeSet.size());

        customerCodeSet.forEach(customerCode ->
                delSelfExecutor.execute(() -> {
                    try {
                        singleCustomerUpgrade(customerCode, type, atomicInteger, customerCodeSet.size(), errorLogList);
                    } finally {
                        countDownLatch.countDown();
                    }
                })
        );

        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt(); // 重置线程的中断状态
            // 处理异常，可以记录日志或执行其他的补偿措施
        }

        // 最后输出所有错误日志
        if (!errorLogList.isEmpty()) {
            log.error("\n{}-处理过程中出现错误：\n{}", type, String.join(";\n", errorLogList));
        }

        log.info("{}-处理完成，共处理{}个企业", type, customerCodeSet.size());
    }

    private void singleCustomerUpgrade(String customerCode, String type, AtomicInteger atomicInteger, int totalSize, List<String> errorLogList) {
        try {
            int currentCount = atomicInteger.getAndIncrement();
            if (currentCount % 100 == 0) {
                log.info("处理进度：{}/{}", currentCount, totalSize);
            }

            tempDbService.updateByType(customerCode, type);
        } catch (Exception e) {
            String errorMessage = String.format("刷新企业%s的%s数据出错：%s", customerCode, type, e.getMessage());
//            log.error(errorMessage, e);
            // 将错误信息添加到错误日志中
            errorLogList.add(errorMessage);
        }
    }

    private Set<String> getCustomerCodeSet(String customerCodes, String type) {
        // 传入customerCodes就直接用，没传入就查询各表里的customerCode
        Set<String> customerCodeSet = StringUtils.isNotBlank(customerCodes)
                ? new HashSet<>(Arrays.asList(customerCodes.split(",")))
                : tempDbService.getCustomerCodeByTable(type);
        // 从记录表里查询已处理的企业编码
        List<String> processedCustomerCodes = tempDbService.getProcessedCustomerCodes(type);
        // 去掉已处理的企业编码
        processedCustomerCodes.forEach(customerCodeSet::remove);
        return customerCodeSet;
    }
}
