package com.glodon.qydata.controller.temp.optionconvert;

import com.glodon.qydata.common.annotation.PassAuth;
import com.glodon.qydata.vo.common.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

import static com.glodon.qydata.common.constant.Constants.StandardsProjectOrContractInfoConstants.BUILT_IN_DATA_CUSTOMER_CODE;
import static com.glodon.qydata.common.constant.Constants.ZbFeatureConstants.SYSTEM_CUSTOMER_CODE;
import static com.glodon.qydata.controller.temp.optionconvert.TempOptionConvertService.*;


/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/basicInfo/dataDeal")
@Slf4j
public class TempOptionConvertController {
    @Resource
    private TempOptionConvertService tempOptionConvertService;

    @PassAuth
    @GetMapping("/systemDataOptionFormatUpgrade")
    public ResponseVo systemDataOptionUpgrade() {
        log.info("开始执行内置数据枚举值存储格式升级任务");
        tempOptionConvertService.optionFormatUpgrade(BUILT_IN_DATA_CUSTOMER_CODE, PROJECT);
        tempOptionConvertService.optionFormatUpgrade(BUILT_IN_DATA_CUSTOMER_CODE, CONTRACT);
        tempOptionConvertService.optionFormatUpgrade(SYSTEM_CUSTOMER_CODE, FEATURE);
        tempOptionConvertService.optionFormatUpgrade(SYSTEM_CUSTOMER_CODE, DESC);
        log.info("内置数据枚举值存储格式升级任务执行完毕");
        return ResponseVo.success();
    }

    @PassAuth
    @GetMapping("/optionFormatUpgrade")
    public ResponseVo optionFormatUpgrade(String customerCodes) {
        Runnable r = () -> {
            log.info("1.开始执行项目信息任务");
            tempOptionConvertService.optionFormatUpgrade(customerCodes, PROJECT);
            log.info("2.开始执行项目信息暂存表任务");
            tempOptionConvertService.optionFormatUpgrade(customerCodes, PROJECT_SELF);
            log.info("3.开始执行合同信息任务");
            tempOptionConvertService.optionFormatUpgrade(customerCodes, CONTRACT);
            log.info("4.开始执行合同信息暂存表任务");
            tempOptionConvertService.optionFormatUpgrade(customerCodes, CONTRACT_SELF);
            log.info("5.开始执行工程特征任务");
            tempOptionConvertService.optionFormatUpgrade(customerCodes, FEATURE);
            log.info("6.开始执行工程特征暂存表任务");
            tempOptionConvertService.optionFormatUpgrade(customerCodes, FEATURE_SELF);
            log.info("7.开始执行建造标准任务");
            tempOptionConvertService.optionFormatUpgrade(customerCodes, DESC);
            log.info("8.开始执行建造标准暂存表任务");
            tempOptionConvertService.optionFormatUpgrade(customerCodes, DESC_SELF);
            log.info("枚举值存储格式升级任务执行完毕");
        };
        new Thread(r).start();
        return ResponseVo.success();
    }
}