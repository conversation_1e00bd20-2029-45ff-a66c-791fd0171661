package com.glodon.qydata.controller.temp.areaRepair;

import com.glodon.qydata.common.annotation.PassAuth;
import com.glodon.qydata.vo.common.ResponseVo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

@RestController
@RequestMapping("/basicInfo/areaRepair")
public class AreaRepairController {

    @Resource
    AreaRepairService areaRepairService;

    @PassAuth
    @GetMapping("/dealHistory")
    public ResponseVo dealHistory() {
        areaRepairService.dealHistory();
        return ResponseVo.success();
    }
}
