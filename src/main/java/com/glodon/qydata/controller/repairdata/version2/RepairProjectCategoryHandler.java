package com.glodon.qydata.controller.repairdata.version2;

import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.controller.repairdata.common.BaseRepairDataHandler;
import com.glodon.qydata.controller.repairdata.common.RepairConst;
import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.standard.category.CommonProjCategoryMapper;
import com.glodon.qydata.mapper.repairdata.TempProjectCategoryMapper;
import com.glodon.qydata.service.standard.category.impl.CommonProjectCategoryUsedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @description: 修复工程分类重复数据
 * @date 2023/4/21 14:14
 */
@Service
@Slf4j
public class RepairProjectCategoryHandler extends BaseRepairDataHandler<StandardsProjectInfoEntity> {
    @Autowired
    CommonProjCategoryMapper commonProjCategoryMapper;

    @Autowired
    TempProjectCategoryMapper tempProjectCategoryMapper;

    @Autowired
    private CommonProjectCategoryUsedService projectCategoryUsedService;
    /**
     * 检查数据是否需要修复
     * @param customerCode
     * @return 是否需要修复
     */
    @Override
    public boolean isNeedRepair(String customerCode){
        Integer useType = projectCategoryUsedService.getUsedCategoryType(customerCode);

        // 有错误编码需要修复
        if(StringUtils.isNotEmpty(tempProjectCategoryMapper.selectErroCategoryCode(customerCode, Arrays.asList(1, useType)))) {
            return true;
        }
        // 有重复数据需要修复
        Integer repeatCount = tempProjectCategoryMapper.selectRepeatRecord(customerCode);
        return repeatCount != null && repeatCount > 0;
    }

    @Override
    public boolean repairData(String customerCode) {
        // 获取当前用户类型
        Integer useType = projectCategoryUsedService.getUsedCategoryType(customerCode);
        List<Integer> typeList = new ArrayList<>();
        typeList.add(1);
        if(!useType.equals(1)) {
            typeList.add(useType);
        }
        // 依次处理三个类型的数据
        Arrays.asList(1, 2, 3).forEach(type -> {
            List<CommonProjCategory> categoryList = commonProjCategoryMapper.selectAll(customerCode, type,  Constants.CategoryConstants.WHETHER_TRUE);
            if(!typeList.contains(type)) {
                // 刷数据导致多余的数据不进行处理
                return;
            } else {
                repairRepeatCategory(categoryList);
                repairCategoryCode(categoryList);
            }
            // 构造需要修复的数据集合
            List<CommonProjCategory> repairCodeList = categoryList.stream().filter(item -> item.getRepairCategorycode1() != null ||
                    item.getRepairCategorycode2() != null || item.getRepairCategorycode3() != null || item.getRepairCategorycode4() != null)
                    .collect(Collectors.toList());
            repairCodeList.forEach(item -> {
                tempProjectCategoryMapper.updateCategory(item);
                tempProjectCategoryMapper.syncSelfCategory(item);
            } );
            // 构造需要修复的数据集合
            List<Integer> repeatCategoryIds = categoryList.stream().filter(item ->
                    RepairConst.data_status_invalid.equals(item.getInvalid())).map(CommonProjCategory::getId).collect(Collectors.toList());
            if (!repeatCategoryIds.isEmpty()) {
                tempProjectCategoryMapper.setCategoryInvalid(customerCode, repeatCategoryIds);
                tempProjectCategoryMapper.setSelfCategoryInvalid(customerCode, repeatCategoryIds);
            }
        });
        return false;
    }



    /**
     * 获取刷数据误加的工程分类
     * @param categoryList
     * @return
     */
    List<CommonProjCategory> repairRepeatCategory(List<CommonProjCategory> categoryList) {
        Map<String, List<CommonProjCategory>> groupMap = categoryList.stream().collect(Collectors.groupingBy(entity -> buildDelimiter(entity.getQyCode(), String.valueOf(entity.getType()), entity.getCommonprojcategoryid(),
                entity.getCategoryname())));
        groupMap.keySet().forEach(key -> {
            List<CommonProjCategory> repeatData = groupMap.get(key);
            if(repeatData.size() < 2) {
                return;
            }
            // 排序将删除项放在前，优先将删除项标记为无效
            List<CommonProjCategory> sortList = repeatData.stream().sorted(Comparator.comparing(CommonProjCategory::getIsDeleted).reversed()).collect(Collectors.toList());
            // 重复数据标记为待删除
            for(int i = 0; i < sortList.size(); ++ i) {
                // 保留一条没删除的有效数据
                if(sortList.stream().filter(item -> !RepairConst.data_status_invalid.equals(item.getInvalid())).count() <= 1) {
                    break;
                }
                sortList.get(i).setInvalid(RepairConst.data_status_invalid);
            }
        });
        return categoryList;
    }
    List<CommonProjCategory> repairCategoryCode(List<CommonProjCategory> categoryList) {
        categoryList.forEach(item -> {
            String categorycode1 = item.getCommonprojcategoryid().substring(0, 3);
            if(!categorycode1.equals(item.getCategorycode1())) {
                item.setRepairCategorycode1(item.getCategorycode1());
                item.setCategorycode1(categorycode1);
            }
            if(item.getLevel() > 1) {
                String categorycode2 = item.getCommonprojcategoryid().substring(0, 6);
                if(!categorycode2.equals(item.getCategorycode2())) {
                    item.setRepairCategorycode2(item.getCategorycode2());
                    item.setCategorycode2(categorycode2);
                }
            }
            if(item.getLevel() > 2) {
                String categorycode3 = item.getCommonprojcategoryid().substring(0, 9);
                if(!categorycode3.equals(item.getCategorycode3())) {
                    item.setRepairCategorycode3(item.getCategorycode3());
                    item.setCategorycode3(categorycode3);
                }
            }
            if(item.getLevel() > 3) {
                String categorycode4 = item.getCommonprojcategoryid().substring(0, 12);
                if(!categorycode4.equals(item.getCategorycode4())) {
                    item.setRepairCategorycode4(item.getCategorycode4());
                    item.setCategorycode4(categorycode4);
                }
            }
        });

        return null;
    }
    @Override
    public boolean checkDataAfterRepair(String customerCode) {
        if(isNeedRepair(customerCode)) {
            throw new BusinessException("工程分类修复结果不及预期");
        }
        return false;
    }
}
