package com.glodon.qydata.controller.repairdata.version2;

import com.glodon.qydata.controller.repairdata.common.BaseRepairDataHandler;
import com.glodon.qydata.controller.repairdata.common.RepairConst;
import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.standard.projectOrContractInfo.StandardsProjectInfoMapper;
import com.glodon.qydata.mapper.temp.TempStandardsProjectInfoMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @description: 修复项目信息和合同信息重复数据
 * @date 2023/4/21 14:14
 */
@Service
@Slf4j
public class RepairProjectInfoHandler extends BaseRepairDataHandler<StandardsProjectInfoEntity> {
    @Resource
    StandardsProjectInfoMapper standardsProjectInfoMapper;
    @Resource
    TempStandardsProjectInfoMapper tempProjectInfoMapper;


    /**
     * 检查数据是否需要修复
     * @param customerCode
     * @return 是否需要修复
     */
    @Override
    public boolean isNeedRepair(String customerCode){
        Integer repeatCount = tempProjectInfoMapper.selectRepeatRecord(customerCode);
        return repeatCount != null && repeatCount > 0;
    }

    @Override
    public boolean repairData(String customerCode) {
        List<StandardsProjectInfoEntity> projectInfoList = standardsProjectInfoMapper.selectAllByCustomerCode(customerCode);

        Map<String, List<StandardsProjectInfoEntity>> groupMap = projectInfoList.stream().collect(Collectors.groupingBy(entity ->
                buildDelimiter(entity.getCustomerCode(), String.valueOf(entity.getStandardDataType()), entity.getTypeCode(),  entity.getName(), String.valueOf(entity.getInvalid()))));
        groupMap.keySet().forEach(key -> {
            List<StandardsProjectInfoEntity> repeatData = groupMap.get(key);
            if(repeatData.size() < 2) {
                return;
            }
            List<StandardsProjectInfoEntity> sortList = repeatData.stream().sorted(Comparator.comparing(StandardsProjectInfoEntity::getIsDeleted).reversed()).collect(Collectors.toList());
            // 重复数据标记为待删除
            for(int i = 0; i < sortList.size(); ++ i) {
                if(sortList.stream().filter(item -> !RepairConst.data_status_invalid.equals(item.getInvalid())).count() <= 1) {
                    break;
                }
                sortList.get(i).setInvalid(RepairConst.data_status_invalid);
            }
        });
        // 项目信息和合同信息
        List<Long> repairIds = projectInfoList.stream().filter(item -> RepairConst.data_status_invalid.equals(item.getInvalid())).map(StandardsProjectInfoEntity::getId).collect(Collectors.toList());
        if(repairIds.isEmpty()) {
            return false;
        }
        tempProjectInfoMapper.setProjectInfoInvalId(customerCode, repairIds);
        tempProjectInfoMapper.synsSelfProjectInfo(customerCode, repairIds);
        return true;
    }

    @Override
    public boolean checkDataAfterRepair(String customerCode) {
        if(isNeedRepair(customerCode)) {
            throw new BusinessException("项目信息修复后仍重复");
        }
        return false;
    }
}
