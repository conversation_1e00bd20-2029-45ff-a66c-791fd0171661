package com.glodon.qydata.controller.repairdata.version3;

import com.glodon.qydata.vo.common.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> @description: 修复错误数据
 * @date 2023/4/21 14:14
 */
@RestController
@RequestMapping("/basicInfo/repair/erroCategory")
@Slf4j
public class RepairErroCategoryController {
    @Autowired
    private RepairErroCategoryManagerImpl repairRrroCategoryManagerImpl;


    @GetMapping("")
    public ResponseVo<Boolean> repaiCategory(){
        Runnable r = () -> {
            log.info("开始修复");
            List<String> customerCodeList = repairRrroCategoryManagerImpl.selectErroDataCustomerCode();
            customerCodeList.forEach(customerCode -> {
                boolean isRepairSuccess = false;
                try {
                    isRepairSuccess = repairRrroCategoryManagerImpl.repair(customerCode);
                } catch (Exception e) {
                    e.printStackTrace();
                    log.info("修复发生异常，企业编码:{}", customerCode);
                }
                repairRrroCategoryManagerImpl.logRepairStatus(customerCode, isRepairSuccess);
            });
            log.info("修复完成");
        };
        new Thread(r).start();
        return ResponseVo.success();
    }
    @GetMapping("/one")
    public ResponseVo<Boolean> repaiRrepeatDataByCustomerCode(@RequestParam(value = "customerCode") String customerCode){
        log.info("开始修复{}", customerCode);
        repairRrroCategoryManagerImpl.repair(customerCode);
        log.info("修复完成{}", customerCode);
        return ResponseVo.success();
    }

    @GetMapping("/logErro")
    public ResponseVo<Boolean> checkData(){
        Runnable r = () -> {
            repairRrroCategoryManagerImpl.logErroDataCustomerCode();
            log.info("记录完成");
        };
        new Thread(r).start();
        return ResponseVo.success();
    }
}
