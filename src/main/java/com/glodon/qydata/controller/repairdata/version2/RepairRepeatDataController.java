package com.glodon.qydata.controller.repairdata.version2;

import com.glodon.qydata.vo.common.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> @description: 修复错误数据
 * @date 2023/4/21 14:14
 */
@RestController
@RequestMapping("/basicInfo/repair/repeatData")
@Slf4j
public class RepairRepeatDataController {
    @Autowired
    private RepairRepeatDataManagerImpl repairRepeatDataManagerImpl;


    @GetMapping("")
    public ResponseVo<Boolean> repaiRrepeatData(){
        Runnable r = () -> {
            log.info("开始修复");
            List<String> customerCodeList = repairRepeatDataManagerImpl.selectErroDataCustomerCode();
            customerCodeList.forEach(customerCode -> {
                boolean isRepairSuccess = false;
                try {
                    isRepairSuccess = repairRepeatDataManagerImpl.repair(customerCode);
                } catch (Exception e) {
                    e.printStackTrace();
                    log.info("修复发生异常，企业编码:{}", customerCode);
                }
                repairRepeatDataManagerImpl.logRepairStatus(customerCode, isRepairSuccess);
            });
            log.info("修复完成");
        };
        new Thread(r).start();
        return ResponseVo.success();
    }
    @GetMapping("/one")
    public ResponseVo<Boolean> repaiRrepeatDataByCustomerCode(@RequestParam(value = "customerCode") String customerCode){
        log.info("开始修复{}", customerCode);
        repairRepeatDataManagerImpl.repair(customerCode);
        log.info("修复完成{}", customerCode);
        return ResponseVo.success();
    }

    @GetMapping("/logErro")
    public ResponseVo<Boolean> checkData(){
        repairRepeatDataManagerImpl.logErroDataCustomerCode();
        return ResponseVo.success();
    }
}
