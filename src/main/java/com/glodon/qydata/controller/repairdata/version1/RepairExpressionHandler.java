package com.glodon.qydata.controller.repairdata.version1;

import com.glodon.qydata.controller.repairdata.common.BaseRepairDataHandler;
import com.glodon.qydata.controller.repairdata.common.RepairConst;
import com.glodon.qydata.entity.standard.expression.ZbStandardsExpression;
import com.glodon.qydata.entity.standard.feature.ProjectFeature;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.repairdata.TempRepairExpressionMapper;
import com.glodon.qydata.mapper.repairdata.TempRepairFeatureMapper;
import com.glodon.qydata.mapper.standard.expression.ZbStandardsExpressionMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @description: 修复计算口径重复数据
 * @date 2023/4/21 14:14
 */
@Service
@Slf4j
public class RepairExpressionHandler extends BaseRepairDataHandler<ZbStandardsExpression> {
    @Resource
    ZbStandardsExpressionMapper zbStandardsExpressionMapper;

    @Resource
    TempRepairExpressionMapper tempRepairExpressionMapper;

    @Resource
    TempRepairFeatureMapper tempRepairFeatureMapper;

    private boolean isRepeatData(String customerCode) {
        Integer repeatCount = tempRepairExpressionMapper.selectRepeatRecord(customerCode);
        return repeatCount != null && repeatCount > 0;
    }
    private boolean isRepeatName(String customerCode) {
        Integer repeatCount = tempRepairExpressionMapper.selectRepeatName(customerCode);
        return repeatCount != null && repeatCount > 0;
    }
    private boolean isRepeatCode(String customerCode) {
        Integer repeatCount = tempRepairExpressionMapper.selectRepeatCode(customerCode);
        return repeatCount != null && repeatCount > 0;
    }
    @Override
    public boolean isNeedRepair(String customerCode){
        if(isRepeatData(customerCode)) {
            return true;
        }
        if(isRepeatCode(customerCode)) {
            return true;
        }
        if(isRepeatName(customerCode)) {
            return true;
        }
        return false;
    }

    /**
     * 找到最重要的计算口径（被工程特征引用最多的）
     * @param repeatData
     * @return
     */
    int findImportantExpressionIndex(List<ZbStandardsExpression> repeatData,  Map<Long, List<ProjectFeature>> projectFeatureMap) {
        // 找出被计算口径引用最多的计算口径
        int maxRefIndex = 0;
        for(int i = 1; i < repeatData.size(); ++ i) {
            // 没有被引用
            if(!projectFeatureMap.containsKey(repeatData.get(i).getId())) {
                continue;
            }
            // maxRefIndex对应数据没有被引用，或者当前数
            if (!projectFeatureMap.containsKey(repeatData.get(maxRefIndex).getId()) ||
                    projectFeatureMap.get(repeatData.get(i).getId()).size() > projectFeatureMap.get(repeatData.get(maxRefIndex).getId()).size()) {
                maxRefIndex = i;
            }
        }
        return 0;
    }

    /**
     * 获取修复计算口径关联的工程特征
     * @param repeatGroupMap
     * @return
     */
    List<ProjectFeature> getFeatureListOfRepairExpression(String customerCode, Map<String, List<ZbStandardsExpression>> repeatGroupMap) {
        List<Long> expressionIds = new ArrayList<>();
        repeatGroupMap.keySet().forEach(key -> {
            List<ZbStandardsExpression> repeatData = repeatGroupMap.get(key);
            if(repeatData.size() < 2) {
                return;
            }
            repeatData.stream().forEach(entity -> expressionIds.add(entity.getId()));
        });
        if(expressionIds.size() > 0) {
            return tempRepairExpressionMapper.selectFeature(customerCode, expressionIds);
        }
        return new ArrayList<>();
    }
    ProjectFeature buildUpdateFeature(ProjectFeature feature, ZbStandardsExpression expression, ZbStandardsExpression dinvalidExpression) {
        ProjectFeature featureEntity = null;
        if(expression.getExpressionCode().equals(dinvalidExpression.getExpressionCode())) {
            featureEntity = new ProjectFeature();
            featureEntity.setId(feature.getId());
            featureEntity.setRepairExpressionId(feature.getExpressionId());
            featureEntity.setExpressionId(expression.getId());
        }
        return featureEntity;
    }

    /**
     * 修复名称和编码都重复的数据
     * @param customerCode
     * @param list
     * @return
     */
    List<ProjectFeature> repairInitRepeat(String customerCode, List<ZbStandardsExpression> list) {
        Map<String, List<ZbStandardsExpression>> repeatGroupMap = list.stream().collect(Collectors.groupingBy(entity -> buildDelimiter(entity.getQyCode(), String.valueOf(entity.getType()), String.valueOf(entity.getIsExpression()),
                entity.getTypeCode(), entity.getName(), entity.getExpressionCode(), String.valueOf(entity.getInvalid()))));
        // 获取所有需要修复的计算口径对应的工程特征
        List<ProjectFeature> allRefFeatureList = getFeatureListOfRepairExpression(customerCode, repeatGroupMap);
        // 构造计算口径id与对应工程特征map
        Map<Long, List<ProjectFeature>> projectFeatureMap = allRefFeatureList.stream().collect(Collectors.groupingBy(ProjectFeature::getExpressionId));
        List<ProjectFeature> updateFeatureList = new ArrayList<>();
        repeatGroupMap.keySet().forEach(key -> {
            List<ZbStandardsExpression> repeatData = repeatGroupMap.get(key);
            if(repeatData.size() < 2) {
                return;
            }
            // 找出被计算口径引用最多的计算口径
            int maxRefIndex = findImportantExpressionIndex(repeatData, projectFeatureMap);

            for(int i = 0; i < repeatData.size(); ++ i) {
                // 被引用最多的计算口径保留
                if(maxRefIndex == i) {
                    continue;
                }
                // 标记计算口径为无效
                this.setExpressionInvalid(repeatData.get(i));
            }
        });
        return updateFeatureList;
    }

    /**
     * 修复楼层数与地上楼层数 code相同问题
     */
    @Override
    public boolean repairSystemDataErro() {
        String customerCode = "-100";
        // List<ZbStandardsExpression> list = zbStandardsExpressionMapper.selectListByCustomCode(customerCode, null)
        //    .stream().filter(item-> RepairConst.data_status_valid.equals(item.getInvalid())).collect(Collectors.toList())
        // select count(1) from zb_project_feature_standards where name = '楼层数'  查询结果为0  生产环境 工程特征没有楼层数而是引用的地上层数
        // select * from zb_project_feature_standards where expression_code ='K_4681759288353358097' limit 100 查询无数据
        // select * from zb_project_feature_standards where expression_code ='K_4763065480228410135' limit 100 查询无数据
        // select * from zb_project_feature_standards where name ='机械车位数' limit 1 查询无数据
        // 基于以上结果删除 楼层数 和不是计算口径的机械车位数（K_4681759288353358097）
        repairData(customerCode);
        checkDataAfterRepair(customerCode);
        return true;
    }

    boolean repairRepeatName(List<ZbStandardsExpression> list) {
        Map<String, List<ZbStandardsExpression>> repeatGroupMap = list.stream().filter(item -> RepairConst.data_status_valid.equals(item.getInvalid())).collect(Collectors.groupingBy(entity ->
                buildDelimiter(entity.getQyCode(), String.valueOf(entity.getType()), entity.getTypeCode(),
                    entity.getName(), String.valueOf(entity.getInvalid()))
        ));

        repeatGroupMap.keySet().forEach(key -> {
            List<ZbStandardsExpression> repeatData = repeatGroupMap.get(key);
            if(repeatData.size() < 2) {
                return;
            }
            // 1.系统内置计算口径不能删除
            // 2.计算口径优先保留
            // 3.机械车位数 删除编码为K_4681759288353358097的数据
            // 风险：改名可能导致 修改前发布工程特征映射到的计算口径 与改名后映射的计算口径不一致。此问题无法避免,
            List<ZbStandardsExpression> sortList = repeatData.stream().sorted(Comparator.comparing(ZbStandardsExpression::getIsExpression)).collect(Collectors.toList());
            // 如果找到系统内置的计算口技则其他的全部改名
            for (int i = 0; i< sortList.size();++i) {
                // 有效数据只有一个不再进行删除或者改名了
                if(sortList.stream().filter(item -> item.getRepairName() == null && RepairConst.data_status_valid.equals(item.getInvalid())).collect(Collectors.toList()).size() <= 1){
                    break;
                }
                ZbStandardsExpression entity = sortList.get(i);
                // 指定机械车位数删除
                if(entity.getExpressionCode().equals("K_4681759288353358097") && entity.getName().equals("机械车位数") && !entity.getIsExpression().equals(1)) {
                    this.setExpressionInvalid(entity);
                    continue;
                }
                // 系统内置计算口径不处理
                if(entity.getExpressionIsFromSystem().equals(1) && entity.getIsExpression().equals(1)) {
                    continue;
                }
                entity.setRepairName(entity.getName());
                int seqFlag = i+1;
                entity.setName(entity.getName()+"("+ seqFlag + ")");
            }
        });
        return false;
    }
    boolean repairRepeatCode(List<ZbStandardsExpression> list) {
        Map<String, List<ZbStandardsExpression>> repeatGroupMap = list.stream().filter(item -> RepairConst.data_status_valid.equals(item.getInvalid())).collect(Collectors.groupingBy((entity) -> buildDelimiter(entity.getQyCode(), String.valueOf(entity.getType()),
                entity.getTypeCode(), entity.getExpressionCode(), String.valueOf(entity.getInvalid()))));
        repeatGroupMap.keySet().forEach(key -> {
            List<ZbStandardsExpression> repeatData = repeatGroupMap.get(key);
            if(repeatData.size() < 2) {
                return;
            }
            List<ZbStandardsExpression> sortList = repeatData.stream().sorted(Comparator.comparing(ZbStandardsExpression::getIsExpression)).collect(Collectors.toList());
            // 优先处理下固定的几个编码
            codeHandle(sortList);
            // 再处理下其他场景，优先保留计算口径
            for (int i = 0; i< sortList.size();++i) {
                // 有效数据只有一个不再进行删除
                if(sortList.stream().filter(item -> RepairConst.data_status_valid.equals(item.getInvalid())).count() <= 1){
                    break;
                }
                ZbStandardsExpression entity = sortList.get(i);
                this.setExpressionInvalid(entity);
            }
        });
        return false;
    }
    /**
     * @Description 方法抽提
     **/
    private void codeHandle(List<ZbStandardsExpression> sortList) {
        for (int i = 0; i< sortList.size();++i) {
            // 有效数据只有一个不再进行删除
            if(sortList.stream().filter(item -> RepairConst.data_status_valid.equals(item.getInvalid())).count() <= 1){
                break;
            }
            ZbStandardsExpression entity = sortList.get(i);
            // 系统内置计算口径不处理
            if(entity.getExpressionIsFromSystem().equals(1) && entity.getIsExpression().equals(1) && "number".equals(entity.getTypeCode())) {
                continue;
            }
            // 有问题的数据expression_code一定是  K_4697545049907398932 ，K_4697545049907398969， K_4745577563580006724，K_4745577564209152324 其中K_4697545049907398932是内置重复
            // 抽样语句 select * from zb_standards_expression where qy_code ='1-106IVLJ' and expression_code in ('K_4697545049907398932', 'K_4697545049907398969', 'K_4745577563580006724', 'K_4745577564209152324')
            if((entity.getExpressionCode().equals("K_4697545049907398969") && entity.getName().equals("红线外硬景面积(m2)")) ||
                    (entity.getExpressionCode().equals("K_4745577563580006724") && entity.getName().equals("示范绿化面积(m2)")) ||
                    (entity.getExpressionCode().equals("K_4745577564209152324") && entity.getName().equals("示范硬景面积(m2)")) ||
                    // 修复因内置数据楼层数与地上层数编码一致问题
                    // 系统内置标记有问题 查询语句：select * from zb_standards_expression where qy_code ='gms_yuzl_2' and expression_code ="K_4697545049907398932"order by `type` ,name
                    (entity.getExpressionCode().equals("K_4697545049907398932") && entity.getName().equals("楼层数"))) {
                this.setExpressionInvalid(entity);
            }
        }
    }
    void setExpressionInvalid (ZbStandardsExpression entity) {
        entity.setInvalid(RepairConst.data_status_invalid);
    }
    List<ProjectFeature> buildSyncFeature(String customerCode, List<ZbStandardsExpression> expressionList) {
        List<ZbStandardsExpression> repairExpressionList = expressionList.stream().
                filter(item -> RepairConst.data_status_invalid.equals(item.getInvalid())).collect(Collectors.toList());

        List<Long> repairIdList = repairExpressionList.stream().map(ZbStandardsExpression::getId)
                .collect(Collectors.toList());
        List<ProjectFeature> refFeatureList = tempRepairExpressionMapper.selectFeature(customerCode, repairIdList);
        // 构造计算口径id与对应工程特征map
        Map<Long, List<ProjectFeature>> projectFeatureMap = refFeatureList.stream().collect(Collectors.groupingBy(ProjectFeature::getExpressionId));
        // 保留的计算口径map key 为type 和编码组合
        Map<String, List<ZbStandardsExpression>> expressionMap = expressionList.stream().filter(item -> RepairConst.data_status_valid.equals(item.getInvalid()))
                .collect(Collectors.groupingBy(entity -> buildDelimiter(String.valueOf(entity.getType()), entity.getExpressionCode())));


        List<ProjectFeature> buildSyncFeature = new ArrayList<>();
        repairExpressionList.forEach(expression -> {
            // 没有引用则
            if(!projectFeatureMap.containsKey(expression.getId())) {
                return;
            }
            String key = buildDelimiter(String.valueOf(expression.getType()), expression.getExpressionCode());

            if (!expressionMap.containsKey(key)) {
                throw new BusinessException("计算口径删除后，到不到替代的");
            }
            if (expressionMap.get(key).size() > 1) {
                throw new BusinessException("计算口径相同编码存在多个");
            }

            projectFeatureMap.get(expression.getId()).forEach(projectFeature -> {
                buildSyncFeature.add(buildUpdateFeature(projectFeature, expressionMap.get(key).get(0), expression));
            });
        });
        return buildSyncFeature;
    }
    @Override
    public boolean repairData(String customerCode) {
        List<ZbStandardsExpression> expressionInfoList = zbStandardsExpressionMapper.selectListByCustomCode(customerCode, null)
                .stream().filter(item-> RepairConst.data_status_valid.equals(item.getInvalid())).collect(Collectors.toList());

        // 修复名称和编码重复数据
        if(isRepeatData(customerCode)) {
            repairInitRepeat(customerCode, expressionInfoList);
        }
        // 修复编码重复但是名称不重复问题（一个code对应多个name）
        if(isRepeatCode(customerCode)) {
            repairRepeatCode(expressionInfoList);
        }

        // 修复名称重复但是编码不重复问题（一个name对应多个code）有67家企业
        if(isRepeatName(customerCode)) {
            repairRepeatName(expressionInfoList);
        }
        List<ZbStandardsExpression> repairExpressionList = expressionInfoList.stream().filter(item -> RepairConst.data_status_invalid.equals(item.getInvalid())).collect(Collectors.toList());
        List<Long> repairIdList = repairExpressionList.stream().map(ZbStandardsExpression::getId)
                .collect(Collectors.toList());
        if(!repairIdList.isEmpty()) {
            List<ProjectFeature> updateFeatureList = buildSyncFeature(customerCode, expressionInfoList);
            if(!updateFeatureList.isEmpty()) {
                tempRepairFeatureMapper.updateBatchById(customerCode, updateFeatureList);
            }
            tempRepairExpressionMapper.setExpressionInvalid(customerCode, repairIdList);
            tempRepairExpressionMapper.synsSelfExpression(customerCode, repairIdList);
        }
        List<ZbStandardsExpression> repairNameExpressionList = expressionInfoList.stream().filter(item -> item.getRepairName() != null).collect(Collectors.toList());
        repairNameExpressionList.forEach(item-> {
            tempRepairExpressionMapper.updateExpression(item);
            tempRepairExpressionMapper.updateSelfExpression(item);
        });
        return false;
    }

    @Override
    public boolean checkDataAfterRepair(String customerCode) {
        List<ProjectFeature> features = tempRepairExpressionMapper.checkFeature(customerCode);
        if(!features.isEmpty()) {
            throw new BusinessException("工程特征有引用不到的计算口径");
        }
        if(isNeedRepair(customerCode)) {
            throw  new BusinessException("修复后校验不通过");
        }
        return false;
    }
}
