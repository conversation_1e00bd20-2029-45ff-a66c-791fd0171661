package com.glodon.qydata.controller.repairdata.featrue;

import com.alibaba.fastjson.JSON;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.vo.common.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * packageName com.glodon.qydata.controller.repairdata.featrue
 *
 * @author: yhl
 * @DateTime: 2023/12/26 9:22
 * @Description:
 */
@RestController
@RequestMapping("/basicInfo/repair/feature")
@Slf4j
public class FeatureController {

    @Autowired
    private FeatureService featureService;
    /**
     * 工程特征业态视图修复
     * 0、手动恢复专业视图，修改专业视图的删除状态为非删除（sql,非必须）
     * 1、清空暂存表（sql），并重新生成一份暂存数据（页面操作）
     * 2、执行修复接口
     * 3.手动发布业态视图（页面操作）
     * @return
     */
    @PostMapping("/categoryView")
    public ResponseVo<Boolean> repairFeatureCategoryView(@RequestBody CategoryViewVo categoryViewVo){
        long beginTime = System.currentTimeMillis();
        log.info("开始修复工程特征业态视图：专业ids：{}", JSON.toJSONString(categoryViewVo));
        List<Long> tradeIds = categoryViewVo.getTradeIds();
        if (CollectionUtils.isEmpty(tradeIds)) {
            log.info("专业ids为空，不需要修复");
            return ResponseVo.error(ResponseCode.PARAMETER_ERROR, false);
        }
        featureService.repairFeatureCategoryView(tradeIds);
        log.info("修复工程特征业态视图完成：专业ids：{}，耗时：{}", tradeIds, System.currentTimeMillis() - beginTime);
        return ResponseVo.success(true);
    }
}
