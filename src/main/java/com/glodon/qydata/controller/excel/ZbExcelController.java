package com.glodon.qydata.controller.excel;

import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.common.annotation.Permission;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.service.excel.ExcelOutAndInService;
import com.glodon.qydata.service.excel.ExcelParseStrategyContext;
import com.glodon.qydata.vo.common.ResponseVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * @Class com.glodon.qydata.controller.excel ZbExcelController
 * <AUTHOR>
 * @Email <EMAIL>
 * @Description excel 操作相关
 * @Date 11:24 2022/7/6
 **/
@RestController
@RequestMapping("/basicInfo/excel")
@Slf4j
@Tag(name = "excel相关接口", description = "excel相关接口")
public class ZbExcelController extends BaseController {
    
    @Resource
    private ExcelOutAndInService excelOutAndInService;

    /***
     * @description: 下载excel模板
     * @return java.util.List<com.glodon.qydata.entity.standard.category.CommonProjCategory>
     * @throws
     * <AUTHOR>
     * @date 2022/7/6 11:25
     */
    @Operation(summary = "下载excel模板")
    @GetMapping("/download/{type}")
    public void download(HttpServletResponse response, @PathVariable("type") String type) throws IOException {
        excelOutAndInService.getTemplate(response, type);
    }

    /***
     * @description: excel导入数据
     * @param file 1
     * @param param 2
     * @return com.glodon.qydata.vo.common.ResponseVo
     * @throws
     * <AUTHOR>
     * @date 2022/7/6 15:13
     */
    @Operation(summary = "excel导入数据")
    @Permission
    @PostMapping("/excelInsert")
    public ResponseVo excelInsert(MultipartFile file, @RequestParam Map<String, String> param) {
        String type = param.get("type");
        if (StringUtils.isBlank(type) || !ExcelParseStrategyContext.pathMapper.containsKey(type)){
            return ResponseVo.error("不支持的数据模块");
        }

        try {
            return ResponseVo.success(excelOutAndInService.excelInsert(type, file, param));
        } catch (BusinessException e) {
            return ResponseVo.success(e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseVo.success("文件解析错误");
        }
    }
}
