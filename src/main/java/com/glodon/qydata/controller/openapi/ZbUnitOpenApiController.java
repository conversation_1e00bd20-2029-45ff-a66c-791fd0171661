package com.glodon.qydata.controller.openapi;

import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.common.annotation.ModifyOperation;
import com.glodon.qydata.common.constant.TrustConstants;
import com.glodon.qydata.entity.standard.unit.ZbUnit;
import com.glodon.qydata.service.standard.unit.IZbUnitService;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.standard.unit.SearchUnitVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @description: 单位对外接口
 * @author: wangq
 * @create: 2022-02-16 14:16
 **/
@Slf4j
@RestController
@RequestMapping("/basicInfo/openApi/unit")
@Tag(name = "单位对外接口", description = "单位对外接口")
public class ZbUnitOpenApiController extends BaseController {

    @Autowired
    private IZbUnitService zbUnitService;

    /**
     * @Description 获取单位列表
     * <AUTHOR>
     * @Date 2022/1/17 15:02
     * @param searchUnitVo
     * @return com.glodon.qydata.vo.common.ResponseVo
     **/
    @Operation(summary = "获取单位列表")
    @GetMapping("/getUnitList")
    public ResponseVo getUnitList(SearchUnitVo searchUnitVo){
        try {
            String customerCode = getCustomerCode(TrustConstants.TYPE_UNIT, searchUnitVo.getDestEnterpriseId(), searchUnitVo.getTrustProductSource());
            searchUnitVo.setCustomerCode(customerCode);
            List<ZbUnit> unitList = zbUnitService.getUnitList(searchUnitVo);

            return ResponseVo.success(unitList);
        }catch (Exception e){
            return ResponseVo.error(e.getMessage());
        }
    }

    /**
     * @Description 根据单位id获取单位详细信息
     * <AUTHOR>
     * @Date 2022/1/17 15:12
     * @param unitId
     * @return com.glodon.qydata.vo.common.ResponseVo
     **/
    @Operation(summary = "根据单位id获取单位详细信息")
    @GetMapping("/getDetail")
    public ResponseVo getUnitList(@Validated @NotBlank(message = "单位id不能为空") String unitId,
                                  String destEnterpriseId,
                                  String trustProductSource){
        try {
            String customerCode = getCustomerCode(TrustConstants.TYPE_UNIT, destEnterpriseId, trustProductSource);
            ZbUnit detail = zbUnitService.getDetail(customerCode, unitId);

            return ResponseVo.success(detail);
        }catch (Exception e){
            return ResponseVo.error(e.getMessage());
        }
    }

    /**
     * @Description 新增单位
     * <AUTHOR>
     * @Date 2022/1/17 15:18
     * @param unitName
     * @return com.glodon.qydata.vo.common.ResponseVo
     **/
    @Operation(summary = "新增单位")
    @PostMapping("/addUnit")
    @ModifyOperation
    public ResponseVo addUnit(@Validated @NotBlank(message = "新增单位名称不能为空") String unitName){
        try {
            String globalId = getGlobalId();
            zbUnitService.addUnit(unitName,globalId);
            return ResponseVo.success();
        }catch (Exception e){
            return ResponseVo.error(e.getMessage());
        }
    }
}
