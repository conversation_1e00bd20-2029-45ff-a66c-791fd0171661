package com.glodon.qydata.controller.openapi;


import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.common.constant.TrustConstants;
import com.glodon.qydata.service.standard.projectOrContractInfo.IStandardsUnitService;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.standard.projectOrContractInfo.StandardsUnitShowVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * zb_standards_unit - 主键 前端控制器  -对外接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
@RestController
@RequestMapping("/basicInfo/openApi/standards/unit")
@Tag(name = "企业标准数据单位信息控制层 -对外接口", description = "企业标准数据单位信息控制层 -对外接口")
public class StandardsUnitOpenApiController extends BaseController {
    @Autowired
    private IStandardsUnitService standardsUnitService;

    /**
     * 获取项目/工程规模单位集合
     * @throws
     * @param standardsInfoId 项目/合同信息表数据id
     * @param type 标准类型，1：项目信息，2：合同信息
     * <AUTHOR>
     * @return
     * @date 2021/10/20 14:59
     */
    @Operation(summary = "获取项目/工程规模单位集合")
    @GetMapping
    public ResponseVo<List<StandardsUnitShowVo>> get(@RequestParam Long standardsInfoId,
                                                     @RequestParam Integer type,
                                                     String destEnterpriseId,
                                                     String trustProductSource) {
        String customerCode = getCustomerCode(TrustConstants.TYPE_FEATURE, destEnterpriseId, trustProductSource);
        return ResponseVo.success(standardsUnitService.initAndGetUnits(standardsInfoId, type, customerCode));
    }


    /**
     * 根据项目规模/工程规模id获取默认选中的单位
     * @throws
     * @param infoId
     * <AUTHOR>
     * @return {@link com.glodon.qydata.vo.common.ResponseVo<java.lang.String>}
     * @date 2022/2/27 19:05
     */
    @Operation(summary = "根据项目规模/工程规模id获取默认选中的单位")
    @GetMapping("/getChecked")
    public ResponseVo<String> getDefaultCheck(@RequestParam Long infoId,
                                              String destEnterpriseId,
                                              String trustProductSource) {
        String customerCode = getCustomerCode(TrustConstants.TYPE_UNIT, destEnterpriseId, trustProductSource);
        return ResponseVo.success(standardsUnitService.getDefaultCheck(infoId, customerCode));
    }

    /**
     * 设置默认选中项目/工程规模单位
     * @throws
     * @param infoId
     * @param unitName
     * <AUTHOR>
     * @return {@link ResponseVo< Void>}
     * @date 2021/11/8 9:22
     */
    @Operation(summary = "设置默认选中项目/工程规模单位")
    @PutMapping("/check")
    public ResponseVo<Void> setDefaultCheck(@RequestParam Long infoId,
                                            @RequestParam String unitName,
                                            String destEnterpriseId,
                                            String trustProductSource) {
        String customerCode = getCustomerCode(TrustConstants.TYPE_UNIT, destEnterpriseId, trustProductSource);
        standardsUnitService.setDefaultCheck(infoId, unitName, customerCode);
        return ResponseVo.success();
    }
}
