package com.glodon.qydata.controller.openapi;

import cn.hutool.core.util.StrUtil;
import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.constant.TrustConstants;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.entity.standard.trade.ZbStandardsTrade;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.standard.trade.ZbStandardsTradeMapper;
import com.glodon.qydata.service.standard.category.CommonProjCategoryService;
import com.glodon.qydata.service.standard.category.impl.CommonProjectCategoryUsedService;
import com.glodon.qydata.service.standard.feature.IProjectFeatureService;
import com.glodon.qydata.util.CategoryUtil;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.standard.feature.ProjectFeatureCategoryViewVO;
import com.glodon.qydata.vo.standard.feature.ProjectFeatureFilterVO;
import com.glodon.qydata.vo.standard.feature.ProjectFeatureResultVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @description: 工程特征控制层 -对外接口
 * <AUTHOR>
 * @date 2021/10/21 17:59
 */
@Slf4j
@RestController
@RequestMapping("/basicInfo/openApi/standards/zbFeature")
@Tag(name = "工程特征控制层 -对外接口", description = "工程特征控制层 -对外接口")
public class ProjectFeatureOpenApiController extends BaseController {

    @Autowired
    private IProjectFeatureService projectFeatureService;

    @Autowired
    private ZbStandardsTradeMapper tradeMapper;

    @Autowired
    private CommonProjectCategoryUsedService projectCategoryUsedService;

    @Autowired
    private CommonProjectCategoryUsedService commonProjectCategoryUsedService;

    @Autowired
    private CommonProjCategoryService commonProjCategoryService;

    /**
     * 对外提供-根据工程分类查询工程特征信息
     * @param categoryCode 工程分类编码
     * @param isShowNotUsing 是否展示未启用  0 只展示启用的  1 展示未启用的，即全部
     * @param isSkipUserName 是否跳过用户名称赋值（0：不跳过；1：跳过；默认为跳过）
     * @param destEnterpriseId 基于委托-需要查询的企业id
     * @param trustProductSource 基于委托-委托来源 ysbz,gsbz
     * <AUTHOR>
     * @date 2021/11/12 14:09
     */
    @Operation(summary = "对外提供-根据工程分类查询工程特征信息")
    @GetMapping("/getByCategoryCode")
    public ResponseVo<List<ProjectFeatureCategoryViewVO>> getByCategoryCode(@RequestParam String categoryCode,
                                                                            Integer isShowNotUsing,
                                                                            Integer isSkipUserName,
                                                                            String destEnterpriseId,
                                                                            String trustProductSource) {
        String customerCode = getCustomerCode(TrustConstants.TYPE_FEATURE, destEnterpriseId, trustProductSource);
        ProjectFeatureFilterVO filterVO = new ProjectFeatureFilterVO();
        filterVO.setViewType(Constants.ZbFeatureConstants.ViewType.CATEGORY_VIEW);
        filterVO.setCategoryCode(categoryCode);
        filterVO.setIsShowNotUsing(isShowNotUsing);
        filterVO.setIsSkipUserName(isSkipUserName);

        // 分类视图   查询某企业的某个分类的特征。根据专业排序显示
        return ResponseVo.success(projectFeatureService.featureCategoryView(customerCode, filterVO));
    }

    /**
     * 对外提供-根据工程专业id或专业编码查询工程特征信息
     * @param tradeId 专业id
     * @param tradeCode 专业编码
     * @param isMini 是否剔除大字段 projectType
     * @param isSkipUserName 是否跳过用户名称赋值（0：不跳过；1：跳过；默认为跳过）
     * @param destEnterpriseId 基于委托-需要查询的企业id
     * @param trustProductSource 基于委托-委托来源 ysbz,gsbz
     * <AUTHOR>
     * @date 2021/11/16 11:46
     */
    @Operation(summary = "对外提供-根据工程专业id或专业编码查询工程特征信息")
    @GetMapping("/getByTrade")
    public ResponseVo<List<ProjectFeatureResultVO>> getByTrade(Long tradeId,
                                                               String tradeCode,
                                                               Integer isMini,
                                                               Integer isSkipUserName,
                                                               String destEnterpriseId,
                                                               String trustProductSource) {
        String customerCode = getCustomerCode(TrustConstants.TYPE_FEATURE, destEnterpriseId, trustProductSource);

        if (tradeId == null && StringUtils.isNotEmpty(tradeCode)){
            ZbStandardsTrade standardsTrade = tradeMapper.selectListByCusAndTradeCode(customerCode, tradeCode);
            if (Objects.isNull(standardsTrade)){
                throw new BusinessException(ResponseCode.PARAMETER_ERROR, "未找到对应的专业");
            }
            tradeId = standardsTrade.getId();
        }

        ProjectFeatureFilterVO filterVO = new ProjectFeatureFilterVO();
        filterVO.setViewType(Constants.ZbFeatureConstants.ViewType.TRADE_VIEW);
        filterVO.setTradeId(tradeId);
        filterVO.setIsSkipUserName(isSkipUserName);

        List<ProjectFeatureResultVO> resultVO = projectFeatureService.featureTradeView(customerCode, filterVO);

        // 剔除大字段
        if (isMini != null && isMini == Constants.ZbFeatureConstants.WHETHER_TRUE){
            resultVO.forEach(item -> item.setProjectType(null));
        }

        // 专业视图
        return ResponseVo.success(resultVO);
    }

    /**
     * @description: 对外提供-根据工程特征ID查询工程特征信息
     * @param
     * @return
     * <AUTHOR>
     * @date 2021/11/12 14:33
     */
    @Operation(summary = "对外提供-根据工程特征ID查询工程特征信息")
    @GetMapping("/getFeatureById")
    public ResponseVo<ProjectFeatureResultVO> getFeatureById(Long featureId) {
        String customerCode = getCustomerCode();
        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        List<CommonProjCategory> categoryListTree = CategoryUtil.createProjcategoryTree(commonProjCategoryService.getCategoryListTree(customerCode, type, Constants.CategoryConstants.WHETHER_FALSE));
        return ResponseVo.success(projectFeatureService.getCompleteFeatureByPrimaryKey(featureId, null, null, categoryListTree));
    }

    /**
     * 专业视图数据正常，分类视图数据为空时，同步分类视图数据
     * @throws
     * @param customerCodes 企业编码，逗号连接
     * <AUTHOR>
     * @return {@link ResponseVo< String>}
     * @date 2022/4/14 14:27
     */
    @Operation(summary = "对外提供-专业视图数据正常，分类视图数据为空时，同步分类视图数据")
    @GetMapping("/syncCategoryView")
    public ResponseVo<String> syncCategoryViewFeature(String customerCodes) {
        List<String> customerCodeList = Stream.of(customerCodes.split(StrUtil.COMMA)).distinct().toList();
        Map<String, Integer> customerCodeTypeMap = customerCodeList.parallelStream().collect(Collectors.toMap(Function.identity(), customerCode -> projectCategoryUsedService.getUsedCategoryType(customerCode)));
        projectFeatureService.syncCategoryViewFeature(customerCodeTypeMap);
        return ResponseVo.success("同步完成");
    }
    
    /**
     * @description: 对外提供-根据工程专业id List查询工程特征信息
     * @param
     * @return
     * <AUTHOR>
     * @date 2021/11/16 11:46
     */
    @Operation(summary = "对外提供-根据工程专业id或专业编码查询工程特征信息")
    @PostMapping("/getByTrades")
    public ResponseVo<Map<String, List<ProjectFeatureResultVO>>> getByTrades(@RequestBody List<Long> trades) {
        String customerCode = getCustomerCode();
        return ResponseVo.success(projectFeatureService.filterFeatureByTrades(customerCode, trades));
    }

    /**
     * 对外提供-根据工程专业id List查询工程特征信息
     *
     * @param trades              可选参数: 工程专业ID列表，传入的ID将用于查询对应的工程特征信息。若不传id List，则查询该企业下所有工程专业所对应的工程特征信息。
     * @param isNeedCategoryInfo  是否需要工程分类信息的标识，0 表示不需要，1 表示需要，默认值为 0。
     * @return                   返回一个响应对象，包含工程特征信息的映射，键为字符串类型，值为工程特征结果的列表。
     * @Author: zhangj-cl
     * @Date: 2022/12/7 14:51
     */
    @Operation(summary = "对外提供-根据工程专业id或专业编码查询工程特征信息v2")
    @PostMapping("/v2/getByTrades")
    public ResponseVo<Map<String, List<ProjectFeatureResultVO>>> getByTradesV2(@RequestBody(required = false ) List<Long> trades,
                                                                               @RequestParam(required = false, defaultValue = "0") Integer isNeedCategoryInfo) {
        String customerCode = getCustomerCode();
        return ResponseVo.success(projectFeatureService.getByTradesV2(customerCode, trades, isNeedCategoryInfo));
    }

    /**
     * 对外提供-根据工程专业id List查询工程特征信息
     * @param trades 专业id集合
     * @param destEnterpriseId 基于委托-需要查询的企业id
     * @param trustProductSource 基于委托-委托来源 ysbz,gsbz
     * <AUTHOR>
     * @date 2021/11/16 11:46
     */
    @Operation(summary = "对外提供-根据工程专业id或专业编码查询工程特征信息")
    @GetMapping("/getByTrades")
    public ResponseVo<Map<String, List<ProjectFeatureResultVO>>> getByTrades(@RequestParam List<Long> trades,
                                                                             String destEnterpriseId,
                                                                             String trustProductSource) {
        String customerCode = getCustomerCode(TrustConstants.TYPE_FEATURE, destEnterpriseId, trustProductSource);
        return ResponseVo.success(projectFeatureService.filterFeatureByTrades(customerCode, trades));
    }

    /**
     * 对外提供-根据工程专业id List查询工程特征信息
     *
     * @param trades              可选参数: 工程专业ID列表，传入的ID将用于查询对应的工程特征信息。若不传id List，则查询该企业下所有工程专业所对应的工程特征信息。
     * @param destEnterpriseId    基于委托-需要查询的企业id
     * @param trustProductSource  基于委托-委托来源 ysbz,gsbz
     * @param isNeedCategoryInfo  是否需要工程分类信息的标识，0 表示不需要，1 表示需要，默认值为 0。
     * @return                   返回一个响应对象，包含工程特征信息的映射，键为字符串类型，值为工程特征结果的列表。
     */
    @Operation(summary = "对外提供-根据工程专业id或专业编码查询工程特征信息v2")
    @GetMapping("/v2/getByTrades")
    public ResponseVo<Map<String, List<ProjectFeatureResultVO>>> getByTradesV2(@RequestParam(required = false) List<Long> trades,
                                                                               @RequestParam(required = false) String destEnterpriseId,
                                                                               @RequestParam(required = false) String trustProductSource,
                                                                               @RequestParam(required = false, defaultValue = "0") Integer isNeedCategoryInfo) {
        String customerCode = getCustomerCode(TrustConstants.TYPE_FEATURE, destEnterpriseId, trustProductSource);
        return ResponseVo.success(projectFeatureService.getByTradesV2(customerCode, trades, isNeedCategoryInfo));
    }
}
