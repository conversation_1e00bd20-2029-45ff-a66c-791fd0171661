package com.glodon.qydata.controller.openapi;

import com.glodon.qydata.entity.system.TbArea;
import com.glodon.qydata.service.system.IAreaService;
import com.glodon.qydata.vo.common.ResponseVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @description: 地区-对外接口
 * <AUTHOR>
 * @date 2021/12/22 8:44
 */
@RestController
@RequestMapping("/basicInfo/openApi/area")
@Slf4j
@Tag(name = "地区-对外接口", description = "地区-对外接口")
public class TbAreaOpenApiController {

    @Autowired
    private IAreaService areaService;

    /**
     * 获取地区列表
     * @param scope 地区范围：ALL-所有；ONLY_VALID-仅有效；默认仅有效
     * <AUTHOR>
     * @date 2021/10/15 17:19
     */
    @Operation(summary = "获取地区列表")
    @GetMapping("/getAllArea")
    public List<TbArea> getAllArea(@RequestParam(required = false, defaultValue = "ONLY_VALID") String scope){
        return areaService.getAllArea(scope);
    }

    /**
     * 根据获AreaId集合取对应地区
     * @return List<TbArea>
     * <AUTHOR>
     * @date 2022/08/04 14:19
     */
    @Operation(summary = "根据获AreaId集合取对应地区")
    @PostMapping("/getAreaByAreaIds")
    public ResponseVo getAreaByAreaIds(@RequestBody List<String> areaIds){
        List<TbArea> list = areaService.getAreaByAreaIds(areaIds);
        return ResponseVo.success(list);
    }

    /**
     * 根据地区areaId获取下级地区信息,要获取全国信息pid传1
     * @param pid areaId
     * @param scope 地区范围：ALL-所有；ONLY_VALID-仅有效；默认仅有效
     */
    @Operation(summary = "根据地区id获取下级地区信息,要获取全国信息pid传1")
    @GetMapping(value="/getAreaTree")
    public ResponseVo getAreaTree(String pid, @RequestParam(required = false, defaultValue = "ONLY_VALID") String scope){
        try{
            List<TbArea> list = areaService.getAreaTree(pid, scope);
            return ResponseVo.success(list);
        }catch(Exception e){
            log.error("获取地区列表错误!", e);
        }
        return ResponseVo.error();
    }

    /**
     * 根据name和level获取地区信息，支持一级与二级
     * @param name level
     * @return
     */
    @Operation(summary = "根据name和level获取地区信息，支持一级与二级")
    @GetMapping("/getAreaByName")
    public TbArea getAreaByName(@RequestParam("name") String name, @RequestParam("level") String level){
        return areaService.getAreaByName(name,null, level);
    }

    /**
     * 根据name和level获取地区信息后续三级按照名称获取地区，使用此接口
     * @param city level
     * @return
     */
    @Operation(summary = "根据name和level获取地区信息后续三级按照名称获取地区，使用此接口")
    @GetMapping("/getAreaByNameThreeLevel")
    public TbArea getAreaByNameThreeLevel(@RequestParam("city") String city, String area, @RequestParam("level")String level){
        return areaService.getAreaByName(city, area,level);
    }
}
