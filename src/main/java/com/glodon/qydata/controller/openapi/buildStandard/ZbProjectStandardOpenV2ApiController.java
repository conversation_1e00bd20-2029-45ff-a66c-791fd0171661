package com.glodon.qydata.controller.openapi.buildStandard;

import com.github.pagehelper.PageInfo;
import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.constant.TrustConstants;
import com.glodon.qydata.dto.ProjectStandardQueryDto;
import com.glodon.qydata.dto.StandardDetailDto;
import com.glodon.qydata.dto.ZbProjectStandardDetailDto;
import com.glodon.qydata.dto.ZbProjectStandardDetailTreeDto;
import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard;
import com.glodon.qydata.service.standard.buildStandard.IZbProjectStandardService;
import com.glodon.qydata.vo.common.ResponseVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 　　* @description: 项目标准控制层 -对外接口
 　　* <AUTHOR>
 　　* @date 2021/8/16 16:55
 　　*/
@RestController
@Validated
@RequestMapping("/basicInfo/openApi/standards/buildStandard/v2")
@Tag(name = "项目标准控制层 -对外接口V2", description = "项目标准控制层 -对外接口V2")
public class ZbProjectStandardOpenV2ApiController extends BaseController {

    @Autowired
    private IZbProjectStandardService zbProjectStandardService;

    /**
     * 获取企业下的建造标准列表(对外接口)
     * @param isShowDelete 是否展示已删除数据（0：展示不包含已删除的；1：展示包含已删除的 ；不传值，默认为0。）
     * @return {@link ResponseVo< List<  ZbProjectStandard >>}
     * @throws
     * <AUTHOR>
     * @date 2021/8/17 9:32
     */
    @Operation(summary = "获取企业下的建造标准列表")
    @GetMapping("/standardList/foreign")
    public ResponseVo<List<ZbProjectStandard>> getStandardListForeign(@RequestParam @Nullable Integer isShowDelete, String destEnterpriseId, String trustProductSource) {
        String customerCode = getCustomerCode(TrustConstants.TYPE_BUILDSTANDARD, destEnterpriseId, trustProductSource);
        return ResponseVo.success(zbProjectStandardService.standardList(customerCode, isShowDelete, Constants.ORDER_DESC, true));
    }

    /**
     * 获取企业下的建造标准列表(对外接口) 分页查询
     * @param standardQuery
     * @return
     */
    @Operation(summary = "获取企业下的建造标准列表-分页查询")
    @PostMapping("/standardPageList/foreign")
    public ResponseVo getStandardPageListForeign(@RequestBody ProjectStandardQueryDto standardQuery){
        String customerCode = getCustomerCode(TrustConstants.TYPE_BUILDSTANDARD, standardQuery.getDestEnterpriseId(), standardQuery.getTrustProductSource());
        standardQuery.setCustomerCode(customerCode);
        PageInfo<ZbProjectStandard> pageInfo = zbProjectStandardService.standardPageList(standardQuery, true);
        return ResponseVo.success(pageInfo);
    }
    /**
     * 根据建造标准id获取建造标准树形表格(对外接口)
     * @throws
     * @param standardId
     * @param dataType 返回数据的结构（0：原始结构；1：树结构；默认值为 0 原始结构）
     * <AUTHOR>
     * @return {@link ResponseVo< List<  ZbProjectStandardDetailTreeDto >>}
     * @date 2021/8/18 17:06
     */
    @Operation(summary = "根据建造标准id获取建造标准树形表格")
    @GetMapping("/data/foreign/{buildStandardId}")
    public ResponseVo<StandardDetailDto> getDetailTreeForeign(@PathVariable("buildStandardId") Long standardId, @RequestParam @Nullable Integer dataType) {
        return ResponseVo.success(zbProjectStandardService.getDetailList(standardId, dataType,false,true));
    }

    /**
     * 根据categoryCode获取建造标准树形表格(对外接口)
     * @throws
     * @param categoryCode
     * @param dataType 返回数据的结构（0：原始结构；1：树结构；默认值为 0 原始结构）
     * <AUTHOR>
     * @return {@link ResponseVo< List<  ZbProjectStandardDetailTreeDto >>}
     * @date 2021/8/18 17:06
     */
    @Operation(summary = "根据categoryCode获取建造标准树形表格")
    @GetMapping("/data/foreign/getDetailListByCategoryCode")
    public ResponseVo<StandardDetailDto> getDetailListByCategoryCode(@RequestParam(value = "categoryCode") String categoryCode,
                                                                     @RequestParam @Nullable Integer dataType,
                                                                     String destEnterpriseId,
                                                                     String trustProductSource) {
        String customerCode = getCustomerCode(TrustConstants.TYPE_BUILDSTANDARD, destEnterpriseId, trustProductSource);
        return ResponseVo.success(zbProjectStandardService.getDetailListByCategoryCode(customerCode, categoryCode, dataType, true));
    }
    /**
     * 根据业态编码获取其下的建造标准
     * @throws
     * @param code
     * <AUTHOR>
     * @return {@link ResponseVo< List< ZbProjectStandard>>}
     * @date 2022/1/18 10:15
     */
    @Operation(summary = "根据业态编码获取其下的建造标准")
    @GetMapping("/categoryCode")
    public ResponseVo<List<ZbProjectStandard>> getByCategoryCode(@RequestParam @NotNull String code, String destEnterpriseId, String trustProductSource) {
        String customerCode = getCustomerCode(TrustConstants.TYPE_BUILDSTANDARD, destEnterpriseId, trustProductSource);
        return ResponseVo.success(zbProjectStandardService.getByCategoryCode(customerCode, code, true));
    }

    @Operation(summary = "根据末级工程分类与产品定位查找组装后的建造标准树状结构")
    @GetMapping("/getListByPositionAndCategoryCode")
    public ResponseVo<List<ZbProjectStandardDetailDto>> getListByPositionAndCategoryCode(@RequestParam("categoryCode") @NotBlank(message = "工程分类编码不能为空") String categoryCode,
                                                                                         String positionName, String destEnterpriseId, String trustProductSource) {
        String customerCode = getCustomerCode(TrustConstants.TYPE_BUILDSTANDARD, destEnterpriseId, trustProductSource);
        return ResponseVo.success(zbProjectStandardService.getListByCategoryAndPosition(customerCode,categoryCode,positionName));
    }
}
