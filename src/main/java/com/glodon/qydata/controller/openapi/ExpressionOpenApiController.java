package com.glodon.qydata.controller.openapi;

import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.common.annotation.ModifyOperation;
import com.glodon.qydata.common.annotation.Permission;
import com.glodon.qydata.common.constant.OperateConstants;
import com.glodon.qydata.common.constant.TrustConstants;
import com.glodon.qydata.dto.BatchAddExpressionDto;
import com.glodon.qydata.service.standard.category.impl.CommonProjectCategoryUsedService;
import com.glodon.qydata.service.standard.expression.IExpressionService;
import com.glodon.qydata.util.PublishLockerUtil;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.standard.expression.ExpressionResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @description: 计算口径 -对外接口
 * <AUTHOR>
 * @date 2021/11/5 11:24
 */
@Slf4j
@RestController
@RequestMapping("/basicInfo/openApi/standards/expression")
@Tag(name = "计算口径 -对外接口", description = "计算口径 -对外接口")
public class ExpressionOpenApiController extends BaseController {

    @Autowired
    private IExpressionService expressionService;

    @Autowired
    private CommonProjectCategoryUsedService projectCategoryUsedService;

    @Autowired
    private PublishLockerUtil publishLockerUtil;

    /**
     * 对外提供-获取计算口径列表
     * @param isSkipUserName 是否跳过用户名称赋值（0：不跳过；1：跳过；默认为跳过）
     * @param destEnterpriseId  基于委托-需要查询的企业id
     * @param trustProductSource  基于委托-委托来源 ysbz,gsbz
     * <AUTHOR>
     * @date 2021/12/1 11:27
     */
    @Operation(summary = "对外提供-获取计算口径列表")
    @GetMapping(value = "/getExpression")
    public ResponseVo<List<ExpressionResultVo>> getExpression(Integer isSkipUserName, String destEnterpriseId, String trustProductSource) {
        String customerCode = getCustomerCode(TrustConstants.TYPE_EXPRESSION, destEnterpriseId, trustProductSource);
        Integer type = projectCategoryUsedService.getUsedCategoryType(customerCode);
        return ResponseVo.success(expressionService.selectAllExpression(customerCode, type, isSkipUserName));
    }

    /**
     * @description: 对外提供-获取有效的计算口径列表
     * @return com.gcj.common.ResponseVo
     * <AUTHOR>
     * @date 203/12/1 11:27
     */
    @Operation(summary = "对外提供-获取有效的计算口径列表")
    @GetMapping(value = "/getValidExpression")
    public ResponseVo<List<ExpressionResultVo>> getValidExpression(Integer isSkipUserName, String destEnterpriseId, String trustProductSource) {
        String customerCode = getCustomerCode(TrustConstants.TYPE_EXPRESSION, destEnterpriseId, trustProductSource);
        Integer type = projectCategoryUsedService.getUsedCategoryType(customerCode);
        return ResponseVo.success(expressionService.selectExpression(customerCode, type, isSkipUserName));
    }

    @Operation(summary = "批量添加计算口径")
    @Permission
    @ModifyOperation
    @PostMapping(value = "/batchAddExpressions")
    public ResponseVo<String> batchAddExpressions(@RequestBody @Validated BatchAddExpressionDto batchAddExpressionDto) {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();

        try {
            Integer type = projectCategoryUsedService.getUsedCategoryType(customerCode);

            // 1.验证当前是否处于编辑态
            expressionService.checkCanAddExpression(customerCode, type);

            // 2.锁定状态,防止用户点击编辑
            publishLockerUtil.lock(OperateConstants.FEATURE, globalId, customerCode);
            long l = System.currentTimeMillis();
            // 3.添加计算口径
            expressionService.batchAddExpressions(batchAddExpressionDto, globalId, customerCode, type);
            log.info("批量添加口径耗时：{}ms", System.currentTimeMillis() - l);
            return ResponseVo.success();
        } finally {
            publishLockerUtil.unLock(OperateConstants.FEATURE, globalId, customerCode);
        }
    }
}
