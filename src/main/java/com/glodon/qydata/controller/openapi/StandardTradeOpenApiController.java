package com.glodon.qydata.controller.openapi;


import cn.hutool.core.collection.CollUtil;
import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.common.constant.TrustConstants;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.entity.standard.trade.ZbStandardsTrade;
import com.glodon.qydata.service.standard.trade.IStandardsTradeService;
import com.glodon.qydata.vo.common.ResponseVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
　　* @description: 标准打通--工程专业控制层 -对外接口
　　* <AUTHOR>
　　* @date 2021/10/21 19:27
　　*/
@RestController
@RequestMapping("/basicInfo/openApi/standards")
@Tag(name = "工程专业控制层 -对外接口", description = "工程专业控制层 -对外接口")
public class StandardTradeOpenApiController extends BaseController {

    @Autowired
    private IStandardsTradeService tradeService;

    /**
     　　* @description: 对外接口--根据调用用户查看该企业下专业列表，包括删除与未删除，调用方自行处理
     　　* @param
     　　* <AUTHOR>
     　　* @date 2021/10/21 19:27
     　　*/
    @Operation(summary = "对外接口--根据调用用户查看该企业下专业列表，包括删除与未删除，调用方自行处理")
    @GetMapping("/trade/getTradeList")
    public ResponseVo<ZbStandardsTrade> initTradeListDb(
            String destEnterpriseId, String trustProductSource,
            @RequestParam(value = "isShowNotUsing", required = false, defaultValue = "true") Boolean isShowNotUsing,
            @RequestParam(value = "needTag", required = false, defaultValue = "false") Boolean needTag){
        String customerCode = getCustomerCode(TrustConstants.TYPE_TRADE, destEnterpriseId, trustProductSource);
        List<ZbStandardsTrade> returnList = tradeService.initAllListByCustomerCode(customerCode);
        // 过滤已启用的专业
        if (!isShowNotUsing && !CollUtil.isEmpty(returnList)) {
            returnList = returnList.stream().filter(item->(Objects.equals(item.getEnabled(), 1))).collect(Collectors.toList());
        }

        // 调整为带标签的结构
        if (needTag) {
            returnList = tradeService.convertToStandardTradeTree(customerCode, returnList);
        }

        return new ResponseVo(ResponseCode.SUCCESS,returnList);
    }
    @Operation(summary = "对外接口--获取专业描述列表")
    @GetMapping("/trade/getTradeDescriptionList")
    public ResponseVo getTradeDescriptionList(@RequestParam @NotNull List<Long> tradeIds,
                                              String destEnterpriseId,
                                              String trustProductSource){
        String customerCode = getCustomerCode(TrustConstants.TYPE_TRADE, destEnterpriseId, trustProductSource);
        List<ZbStandardsTrade> returnList = tradeService.selectDescriptionListByIds(customerCode, tradeIds);
        return new ResponseVo(ResponseCode.SUCCESS, returnList);
    }
}
