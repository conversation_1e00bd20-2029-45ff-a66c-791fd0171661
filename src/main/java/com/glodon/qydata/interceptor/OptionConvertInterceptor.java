package com.glodon.qydata.interceptor;

import com.glodon.qydata.common.constant.BusinessConstants;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildStandardDetailDesc;
import com.glodon.qydata.entity.standard.feature.ProjectFeature;
import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.standard.buildStandard.ZbStandardsBuildStandardDetailDescMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureSelfMapper;
import com.glodon.qydata.mapper.standard.projectOrContractInfo.StandardsProjectInfoMapper;
import com.glodon.qydata.util.FormatConvertUtil;
import com.glodon.qydata.vo.standard.feature.ProjectFeatureResultVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

/**
 * 枚举选项格式转换拦截器
 * 1. 出库时将枚举选项转换为json字符串
 * 2. 入库时将枚举选项转换为|分隔的字符串
 */
@Slf4j
@Intercepts({
        @Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class})
})
public class OptionConvertInterceptor implements Interceptor {
    // 配置映射表
    private static final Map<String, Map<Class<?>, String>> CONFIG_MAP = new HashMap<>();

    static {
        CONFIG_MAP.put(StandardsProjectInfoMapper.class.getName(), new HashMap<Class<?>, String>() {{
            put(StandardsProjectInfoEntity.class, "selectList");
        }});
        CONFIG_MAP.put(ProjectFeatureMapper.class.getName(), new HashMap<Class<?>, String>() {{
            put(ProjectFeature.class, "option");
            put(ProjectFeatureResultVO.class, "option");
        }});
        CONFIG_MAP.put(ProjectFeatureSelfMapper.class.getName(), new HashMap<Class<?>, String>() {{
            put(ProjectFeature.class, "option");
        }});
        CONFIG_MAP.put(ZbStandardsBuildStandardDetailDescMapper.class.getName(), new HashMap<Class<?>, String>() {{
            put(ZbStandardsBuildStandardDetailDesc.class, "selectList");
        }});
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 获取映射语句和参数
        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        Object parameter = invocation.getArgs()[1];
        String id = mappedStatement.getId();

        // 查找对应的表和字段映射
        Map<Class<?>, String> tableAndFieldNameMap = CONFIG_MAP.entrySet().stream()
                .filter(entry -> id.startsWith(entry.getKey()))
                .map(Map.Entry::getValue)
                .findFirst()
                .orElse(null);

        // 如果找到映射
        if (tableAndFieldNameMap != null) {
            SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();
            if (SqlCommandType.SELECT.equals(sqlCommandType)) {
                // 出库时将枚举选项转换为json字符串
                return processSelect(invocation, tableAndFieldNameMap);
            } else if (SqlCommandType.INSERT.equals(sqlCommandType) || SqlCommandType.UPDATE.equals(sqlCommandType)) {
                // 入库时将枚举选项转换为|分隔的字符串
                long l = System.currentTimeMillis();
                processInsertOrUpdate(parameter, tableAndFieldNameMap);
                log.debug("Insert or update cost {} ms", System.currentTimeMillis() - l);
            }
        }

        return invocation.proceed();
    }

    private Object processSelect(Invocation invocation, Map<Class<?>, String> tableAndFieldNameMap) throws Throwable {
        Object result = invocation.proceed();
        long l = System.currentTimeMillis();
        modifyField(result, tableAndFieldNameMap, BusinessConstants.SELECT_LIST_TO_JSON);
        log.debug("Select list to json cost {} ms", System.currentTimeMillis() - l);
        return result;
    }

    private void processInsertOrUpdate(Object parameter, Map<Class<?>, String> tableAndFieldNameMap) throws BusinessException {
        if (parameter instanceof Map) {
            ((Map<?, ?>) parameter).values().forEach(value -> handleValue(value, tableAndFieldNameMap, BusinessConstants.SELECT_LIST_TO_STR));
        } else {
            modifyField(parameter, tableAndFieldNameMap, BusinessConstants.SELECT_LIST_TO_STR);
        }
    }

    private void handleValue(Object value, Map<Class<?>, String> tableAndFieldNameMap, String convertType) throws BusinessException {
        if (value instanceof List<?>) {
            ((List<?>) value).forEach(item -> modifyFieldValue(item, tableAndFieldNameMap, convertType));
        } else {
            modifyFieldValue(value, tableAndFieldNameMap, convertType);
        }
    }

    private void modifyField(Object result, Map<Class<?>, String> tableAndFieldNameMap, String convertType) throws BusinessException {
        try {
            if (result instanceof List<?>) {
                ((List<?>) result).forEach(item -> modifyFieldValue(item, tableAndFieldNameMap, convertType));
            } else if (result != null) {
                modifyFieldValue(result, tableAndFieldNameMap, convertType);
            } else {
                log.warn("Result is null or unsupported type");
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.warn("Error modifying field: {}", e.getMessage());
        }
    }

    private void modifyFieldValue(Object obj, Map<Class<?>, String> tableAndFieldNameMap, String convertType) throws BusinessException {
        if (obj == null) return;

        Class<?> clazz = obj.getClass();
        for (Map.Entry<Class<?>, String> entry : tableAndFieldNameMap.entrySet()) {
            // 检查对象是否匹配
            if (entry.getKey().isInstance(obj)) {
                modifyFieldValue(obj, clazz, entry.getValue(), convertType);
                break;
            }
        }
    }

    private void modifyFieldValue(Object obj, Class<?> clazz, String fieldName, String convertType) throws BusinessException {
        try {
            Field field = getFieldIncludingSuperclasses(clazz, fieldName);
            field.setAccessible(true);
            String currentValue = (String) field.get(obj);
            String newValue = FormatConvertUtil.convertSelectList(currentValue, convertType);
            if (BusinessConstants.SELECT_LIST_TO_STR.equals(convertType)) {
                validateOptionValue(obj, newValue);
            }
            field.set(obj, newValue);
            log.debug("Field {} modified from {} to {}", fieldName, currentValue, newValue);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            log.warn("处理字段时发生错误: {}", e.getMessage());
        } catch (BusinessException e) {
            throw e;
        }
    }

    private void validateOptionValue(Object obj, String optionValue) throws BusinessException {
        if (StringUtils.isBlank(optionValue)) {
            return;
        }

        int maxOptionLength = obj instanceof ZbStandardsBuildStandardDetailDesc
                ? Constants.STANDARD_MAX_OPTION_LENGTH
                : Constants.MAX_OPTION_LENGTH;

        if (optionValue.length() > maxOptionLength) {
            throw new BusinessException(ResponseCode.OPTION_LENGTH_ERROR);
        }

        int optionCount = countOptions(optionValue);
        if (optionCount > Constants.MAX_OPTION_NUM) {
            throw new BusinessException(ResponseCode.OPTION_NUM_ERROR);
        }
    }

    private int countOptions(String newValue) {
        int count = 1; // 默认至少有一个选项
        for (char ch : newValue.toCharArray()) {
            if (ch == '|' || ch == '丨') {
                count++;
            }
        }
        return count;
    }

    private Field getFieldIncludingSuperclasses(Class<?> clazz, String fieldName) throws NoSuchFieldException {
        Class<?> originalClass = clazz;
        while (clazz != null) {
            try {
                return clazz.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass();
            }
        }
        throw new NoSuchFieldException("Field " + fieldName + " not found in class hierarchy of " + originalClass.getName());
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        // 可选：设置一些属性
    }
}


