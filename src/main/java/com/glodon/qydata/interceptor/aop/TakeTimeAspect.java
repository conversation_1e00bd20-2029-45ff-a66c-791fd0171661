package com.glodon.qydata.interceptor.aop;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.stereotype.Component;

/**
 * 耗时统计
 */
@Slf4j
@Aspect
@Component
public class TakeTimeAspect {

    //统计请求的处理时间
    ThreadLocal<Long> startTime = new ThreadLocal<>();
    ThreadLocal<String> methodName = new ThreadLocal<>();

    /**
     * 带有@TakeTime注解的方法
     */
    @Pointcut("@annotation(com.glodon.qydata.interceptor.aop.TakeTime)")
    public void logPoint() {
        // 空实现
    }
    @Around("logPoint()")
    public Object around (ProceedingJoinPoint joinPoint) throws Throwable {
        String targetName =joinPoint.getTarget().getClass().getSimpleName()+":"+ joinPoint.getSignature().getName();
        long begin = System.currentTimeMillis();
        Object o = joinPoint.proceed();
        long end = System.currentTimeMillis();
        log.info("方法执行时间：{} ms, 方法名：{}",
                end - begin,
                targetName
        );
        return o;
    }

    @Before("logPoint()")
    public void doBefore(JoinPoint joinPoint) throws Throwable {
        //startTime.set(System.currentTimeMillis());
        //methodName.set(joinPoint.getTarget().getClass().getSimpleName() + ":" + joinPoint.getSignature().getName());
    }

    @AfterReturning(returning = "ret", pointcut = "logPoint()")
    public void doAfterReturning(Object ret) {
  //      ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        //记录请求的内容
//        HttpServletRequest request = attributes.getRequest();
//        log.info("请求URL:" + request.getRequestURL().toString());
//        log.info("请求METHOD:" + request.getMethod());
        //处理完请求后，返回内容
        /*log.info("方法执行时间：{} ms, 方法名：{}",
                    System.currentTimeMillis() - startTime.get(),
                    methodName.get()
                    );*/
//        log.info("方法返回值：{}", JSON.toJSONString(ret));
    }
}
