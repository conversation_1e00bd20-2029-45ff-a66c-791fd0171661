package com.glodon.qydata.interceptor.aop;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.TypeUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.common.annotation.BusinessCache;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.util.SpringUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Aspect
@Component
@Slf4j
public class BusinessAop {
    public static final String DEFAULT_BUSINESS_PREFIX = "CUSTOMER_CACHE_V2:";
    private static final String DEFAULT_ANNOTATION_CUSTOMER_CODE = "defaultCustomerCode";
    private static final Pattern pattern = Pattern.compile("\\$\\{(.+?)}");
    private static final String DEFAULT_CUSTOMER_CODE = "-1000000000";
    private static final Long TWO_HOURS_SECONDS = 2 * 60 * 60L;
    private static final Long THIRTY_MINUTES_SECONDS = 30 * 60L;
    private static final Long FIFTEEN_MINUTES_SECONDS = 15 * 60L;

    @Resource(name = "redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;

    @Pointcut("@annotation(com.glodon.qydata.common.annotation.BusinessCache)")
    public void businessAnnotation() {
        // 空实现
    }

    @Around("businessAnnotation()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        BusinessCache annotation = AnnotationUtils.findAnnotation(method, BusinessCache.class);
        assert annotation != null;
        String customerCode = getCustomerCode(joinPoint, annotation);
        Assert.notBlank(customerCode, () -> new BusinessException("获取到的customerCode不能为空"));
        String cacheKey = getCacheKey(customerCode);
        String md5 = calculateMd5(methodSignature, args);
        boolean validateCache = annotation.isInvalidateCache();
        if (validateCache) {
            Object result = joinPoint.proceed();
            redisTemplate.delete(cacheKey);
            log.info("清理缓存");
            return result;
        }
        Object cacheValue = redisTemplate.opsForHash().get(cacheKey, md5);
        if (cacheValue != null) {
            log.debug("当前请求已命中缓存,类名为:[{}] ,请求的方法名称为:[{}]", methodSignature.getDeclaringType().getSimpleName(), method.getName());
            try {
                return getReturnValue(methodSignature, cacheValue);
            } catch (Exception e) {
                // 当实体不一致时导致转换成实体 发生异常，此时应清理缓存重新获取数据
                redisTemplate.delete(cacheKey);
                log.error("获取缓存数据发生异常,cacheKey:{}", cacheKey, e);
            }
        }
        Object result = joinPoint.proceed();
        redisTemplate.opsForHash().put(cacheKey, md5, result);
        // 过期时间为 30分钟 + 15分钟随机时间
        redisTemplate.expire(cacheKey, THIRTY_MINUTES_SECONDS + RandomUtil.randomLong(FIFTEEN_MINUTES_SECONDS), TimeUnit.SECONDS);
        return result;
    }

    public void deleteCustomerCache(String customerCode) {
        String cacheKey = getCacheKey(customerCode);
        redisTemplate.delete(cacheKey);
    }

    private static String calculateMd5(MethodSignature methodSignature, Object[] args) {
        String declaringTypeName = methodSignature.getDeclaringTypeName();
        String argNames = ArrayUtil.join(args, StrUtil.COMMA);
        String md5Key = StrUtil.join(StrUtil.COLON, ListUtil.toList(declaringTypeName, methodSignature.getMethod().getName(), argNames));
        return DigestUtil.md5Hex(md5Key);
    }

    @SneakyThrows
    private Object getReturnValue(MethodSignature methodSignature, Object cacheValue) {
        Type returnType = TypeUtil.getReturnType(methodSignature.getMethod());
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 将JSON对象转换为实体对象
        return objectMapper.convertValue(cacheValue, objectMapper.constructType(returnType));
    }

    private String getCacheKey(String customerCode) {
        return DEFAULT_BUSINESS_PREFIX + customerCode;
    }

    private static String getCustomerCode(ProceedingJoinPoint joinPoint, BusinessCache cache) {
        String customerCode = cache.customerCode();
        if (StrUtil.isEmpty(customerCode) || DEFAULT_CUSTOMER_CODE.equals(customerCode)) {
            return StrUtil.EMPTY;
        }
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Object[] args = joinPoint.getArgs();
        Matcher matcher = pattern.matcher(customerCode);
        if (matcher.find()) {
            customerCode = matcher.group(1);
            Assert.notBlank(customerCode, "提取到的customerCode不能为空");
        }

        if (DEFAULT_ANNOTATION_CUSTOMER_CODE.equals(customerCode)) {
            BaseController bean = SpringUtil.getBean("commonAuthController");
            return bean.getCustomerCode();
        }

        if (StrUtil.contains(customerCode, StrUtil.DOT)) {
            for (Object arg : args) {
                ExpressionParser parser = new SpelExpressionParser();
                // 执行具体逻辑，使用parameterValue参数值
                String parseValue = StrUtil.sub(customerCode, StrUtil.indexOf(customerCode, StrUtil.C_DOT) + 1, customerCode.length());
                String cc = parser.parseExpression("#root." + parseValue).getValue(arg, String.class);
                if (StrUtil.isNotEmpty(cc)) {
                    return cc;
                }
            }
            throw new BusinessException("提取到的customerCode不能为空");
        }

        DefaultParameterNameDiscoverer defaultParameterNameDiscoverer = new DefaultParameterNameDiscoverer();
        String[] parameterNames = defaultParameterNameDiscoverer.getParameterNames(methodSignature.getMethod());
        int index = -1;
        for (int i = 0; i < Objects.requireNonNull(parameterNames).length; i++) {
            if (parameterNames[i].contains(customerCode)) {
                index = i;
            }
        }
        Assert.isFalse(index == -1, () -> new BusinessException("提取到的customerCode不能为空"));
        // 提取到的参数名称对应的值即为customerCode
        return args[index].toString();
    }
}
