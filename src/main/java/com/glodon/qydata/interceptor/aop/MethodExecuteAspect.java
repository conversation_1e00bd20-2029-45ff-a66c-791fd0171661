package com.glodon.qydata.interceptor.aop;

import com.glodon.qydata.util.ThreadLocalUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * @description: 方法信息捕获切面
 * @author: yanyh <EMAIL>
 * @date: 2025/07/31
 */
@Slf4j
@Aspect
@Component
public class MethodExecuteAspect {

    /**
     * 切入点：所有Controller层的方法
     */
    @Pointcut("execution(* com.glodon.qydata.controller..*.*(..))")
    public void controllerMethods() {
        // 空实现
    }

    /**
     * 环绕通知：捕获方法信息并存储到ThreadLocal
     */
    @Around("controllerMethods()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        Method method = methodSignature.getMethod();
        try {
            // 将当前方法信息存储到ThreadLocal
            ThreadLocalUtil.setCurrentMethod(method);
            // 执行原方法
            return joinPoint.proceed();
        } finally {
            // 清理ThreadLocal，避免内存泄漏
            // 注意：这里只清理方法信息，不清理其他ThreadLocal数据
            ThreadLocalUtil.setCurrentMethod(null);
        }
    }
}
