package com.glodon.qydata.entity.repairdata;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName(value = "repair_log")
public class RepairLog implements Serializable {
    private Long id;
    private String customerCode;
    private Integer isEdited;
    private Integer isRepeat;
    private String erroType;
    private Integer repairStatus;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    private Integer version;

}
