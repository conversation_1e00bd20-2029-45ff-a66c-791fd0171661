package com.glodon.qydata.entity.standard.expression;

import com.glodon.qydata.entity.system.QYFlag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: zb_standards_expression
 * <AUTHOR>
 * @date 2021/11/2 8:49
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class ZbStandardsExpression extends QYFlag implements Serializable {

    private static final long serialVersionUID = 4189034123798733891L;

    private Long id;

    /**
     * （历史遗留）口径编码(可作为口径key,同时可固化编码进行数据处理),采用分布式生成器生成
     */
    private String expressionCode;

    /**
     * （历史遗留）计算口径值得类型（0:浮点，1:整数）
     */
    private Integer dataType;

    /**
     * （历史遗留）规则设计，预留字段
     */
    private String rule;

    /**
     * （历史遗留）
     */
    private Integer status;

    /**
     * （历史遗留）口径范围(1,个人，2，企业，0.系统)
     */
    private Integer scope;

    /**
     * 企业编号
     */
    private String qyCode;

    /**
     * 是否使用
     */
    private Integer isUsable;

    /**
     * 是否删除：1已删除；0未删除
     */
    private Integer isDeleted;

    /**
     * 工程特征名称
     */
    private String name;

    /**
     * 数据类型:值为dictionary表中type_code字段等于data_type所对应的code字段的值
     */
    private String typeCode;

    /**
     * 数据类别
     */
    private Integer type;

    /**
     * 工程特征对应的选项，与数据类型连用,数据格式为json字符串：{id:xxx，name：xxx,sort: xxx}
     */
    private String option;

    /**
     * 是否计算口径：1是；0否（数值型，支持设置是否计算口径）
     */
    private Integer isExpression;

    /**
     * 单位
     */
    private String unit;

    /**
     * 计算口径排序
     */
    private Integer expressionOrd;

    /**
     * 计算口径是否启用:1启用；0未启用；
     */
    private Integer expressionIsUsing;

    /**
     * 计算口径是否系统内置：1 是； 0 否
     */
    private Integer expressionIsFromSystem;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建用户ID
     */
    private Long createGlobalId;

    /**
     * 更新用户ID
     */
    private Long updateGlobalId;

    /**
     * 计算口径创建时间
     */
    private Date expressionCreateTime;

    /**
     * 计算口径创建用户ID
     */
    private Long expressionCreateGlobalId;

    /**
     * 计算口径备注
     */
    private String expressionRemark;

    /**
     * _self 特有 关联字段
     */
    private Long originId;

    /**
     * _self 特有 当前个人标准归属于哪个用户
     */
    private Long selfGlobalId;

    private String oldName;

    /**
     * 是否无效 1 无效，空为有效
     */
    private Integer invalid;
    /**
     * 工程特征修复前名称
     */
    private String repairName;

    private String customerCode;
}