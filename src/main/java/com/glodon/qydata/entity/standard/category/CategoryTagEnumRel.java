package com.glodon.qydata.entity.standard.category;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: luoml-b
 * @date: 2024/10/22 14:33
 * @description:
 */
@Data
public class CategoryTagEnumRel implements Serializable {
    private static final long serialVersionUID = -2810598567472979427L;

    private Long id;

    private String categoryCode;

    private Long tagId;

    private Long tagEnumId;

    private String enterpriseId;

    private Long originalId;

    private Date createTime;

    private Date modifyTime;

    @TableField(exist = false)
    private String tagName;

    @TableField(exist = false)
    private String tagEnumName;


    @TableField(exist = false)
    private Boolean mountedNode = false;


}
