package com.glodon.qydata.entity.standard.buildStandard;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 企业标准数据-建造标准定位-细则
 * @TableName zb_standards_build_position_detail
 */
@Data
@TableName(value = "zb_standards_build_position_detail")
public class ZbStandardsBuildPositionDetail implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 定位id
     */
    private Long positionId;

    /**
     *  标准细则-标准说明id
     */
    private Long standardDetailDescId;

    /**
     * 标准细则id
     */
    private Long standardDetailId;

    /**
     * 标准id
     */
    private Long standardId;

    /**
     * 标准说明的值
     */
    private String value;

    /**
     * 企业id
     */
    private Long enterpriseId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    private Long originId;
}
