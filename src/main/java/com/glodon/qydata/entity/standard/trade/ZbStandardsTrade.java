package com.glodon.qydata.entity.standard.trade;

import com.glodon.qydata.entity.system.QYFlag;
import com.glodon.qydata.util.mover.Movable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ZbStandardsTrade extends QYFlag implements Serializable, Movable {
    private Long id;

    private String description;

    private String tradeCode;

    private Byte type;

    private Long creatorId;

    private Date createTime;

    private String customerCode;

    private Integer isDeleted;

    private Integer ord;

    private String referTrade;

    private Long updaterId;

    private Date updateTime;
    
    /**
     * 2022.04.03新增启用字段 0禁用 1启用 默认启用
     */
    private Integer enabled;

    /**
     * 专业选项id
     */
    private Long tradeTagId;

    @Schema(description = "当前节点类型,0:标签 1:专业")
    private Integer nodeType = 1;
    @Schema(description = "标签下的专业")
    private List<ZbStandardsTrade> children;

    private static final long serialVersionUID = 1L;

    /**
     * 是否无效 1 无效，空为有效
     */
    private Integer invalid;

    @Override
    public Long getUniqueIdentity() {
        return id;
    }

    @Override
    public Integer getOrdValue() {
        return ord;
    }

    @Override
    public void setOrdValue(Integer ordValue) {
        this.ord = ordValue;
    }
}