package com.glodon.qydata.entity.standard.buildStandard;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (企业标准数据-建造标准-标准说明表)
 * @TableName zb_standards_build_standard_detail_desc
 */
@Data
@TableName(value = "zb_standards_build_standard_detail_desc")
public class ZbStandardsBuildStandardDetailDesc implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 标准名称
     */
    private String name;

    /**
     * 标准说明
     */
    private String detailDesc;

    /**
     * 建造标准id
     */
    private Long standardId;

    /**
     * 建造标准细则id
     */
    private Long standardDetailId;

    /**
     * 编码
     */
    private String code;

    /**
     * 数据类型： text文本类，number数值类，date日期类，select单选类，selects多选类
     */
    private String typeCode;

    /**
     * 枚举值
     */
    private String selectList;

    /**
     * 专业id
     */
    private Long tradeId;

    /**
     * 专业名称
     */
    private String tradeName;

    /**
     * 排序
     */
    private Integer ord;

    /**
     * 企业id
     */
    private Long enterpriseId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
    @TableField(exist = false)
    private Long originId;

    private static final long serialVersionUID = 1L;


}
