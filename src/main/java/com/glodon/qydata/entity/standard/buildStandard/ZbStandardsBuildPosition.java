package com.glodon.qydata.entity.standard.buildStandard;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 企业标准数据-建造标准定位
 * @TableName zb_standards_build_position
 */
@Data
@TableName(value = "zb_standards_build_position")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ZbStandardsBuildPosition implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     *  建造标准业态表id
     */
    private Long buildCategoryId;

    /**
     * 标准id
     */
    private Long standardId;

    /**
     *  名称
     */
    private String name;

    /**
     * 排序
     */
    private Integer ord;

    /**
     *  0:定位，1工程分类
     */
    private Integer type;

    /**
     * 工程分类编码
     */
    private String categoryCode;

    /**
     * 企业id
     */
    private Long enterpriseId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    private Long originId;
    /**
     * 需要复制的定位的id
     */
    private Long copyDataFromId;

}
