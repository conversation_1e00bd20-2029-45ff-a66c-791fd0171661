package com.glodon.qydata.entity.standard.buildStandard;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * @TableName zb_standards_build_category
 */
@Data
@TableName(value = "zb_standards_build_category")
public class ZbStandardsBuildCategory implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 标准id
     */
    private Long standardId;

    /**
     * 工程分类编码
     */
    private String categoryCode;

    /**
     * 企业id
     */
    private Long enterpriseId;

    /**
     * 排序
     */
    private Integer ord;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    private Long originId;
}
