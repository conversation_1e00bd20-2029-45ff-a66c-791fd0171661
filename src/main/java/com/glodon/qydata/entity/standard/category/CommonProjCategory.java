package com.glodon.qydata.entity.standard.category;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.glodon.qydata.entity.system.QYFlag;
import com.glodon.qydata.util.mover.Movable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 工程分类表
 *
 * <AUTHOR>
 * @date 2021/10/15 17:11
 */
@Data
public class CommonProjCategory extends QYFlag implements Serializable, Movable {

    private static final long serialVersionUID = 1878883670277475440L;

    private Integer id;
    private Integer pid;
    private String commonprojcategoryid;
    private String categoryname;
    private Integer type;
    private String categorycode1;
    private String categorycode2;
    private String categorycode3;
    private String categorycode4;
    private Long level;
    private String mainparamname;
    private String unit;
    private String categoryTypeCode;
    private String categoryTypeName;
    private List<CommonProjCategory> sublist;
    private Integer counter;
    /**
     * 父级分类的commonprojcategoryid
     */
    private String parentId;
    private String privince;
    private Integer ord;
    /**
     * 地区处理标识
     */
    private String provinceProcess;

    @Schema(description = "登陆ID")
    private String globalId;
    @Schema(description = "创建账号")
    private String accountname;
    @Schema(description = "企业编号")
    private String qyCode;
    @Schema(description = "删除标志")
    private Boolean isDeleted;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 是否使用（1，使用；0，未使用）
     */
    private Integer isUsable;


    /**
     * 是否存在类型
     */
    private Map<Integer, Boolean> typeMap = new HashMap<>();


    public CommonProjCategory() {
        // 空构造方法
    }

    public CommonProjCategory(String categoryName) {
        this.categoryname = categoryName;
    }

    /**
     * 是否启用（1，启用；0，禁用）
     */
    private Integer isUsing;

    /**
     * 更新人
     */
    private String updateGlobalId;

    /**
     * 更新账号
     */
    private String updateAccountName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 处理历史数据用，末级最大数值,如，*********，lastMaxNum=3
     */
    private Integer lastMaxNum;

    /**
     * _self 特有 关联字段
     */
    private Integer originId;
    /**
     * 全路径 - 居住建筑/中高层/高层
     */
    private String namePath;
    private String categoryName1;
    private String categoryName2;
    private String categoryName3;
    private String categoryName4;
    /**
     * 是否无效 1 无效，空为有效
     */
    private Integer invalid;
    private String repairCategorycode1;
    private String repairCategorycode2;
    private String repairCategorycode3;
    private String repairCategorycode4;

    private String selfGlobalId;

    /**
     * 全路径，标签专用
     */
    @TableField(exist = false)
    private String fullPath;

    /**
     * 标签关系的id路基，尤标签的业态专用
     */
    @TableField(exist = false)
    private String relIdPath;

    /**
     * 父路径，标签专用
     */
    @TableField(exist = false)
    private String parentPath;

    /**
     * 整体level
     */
    @TableField(exist = false)
    private Integer tagLevel;

    /**
     * 数据类型（0：工程分类；1：标签）
     */
    @TableField(exist = false)
    private Integer dataType;

    /**
     * 全局顺序
     */
    @TableField(exist = false)
    private Integer entiretyOrd;

    /**
     * 标签id，标签专用
     */
    @TableField(exist = false)
    private Long tagId;

    /**
     * 标签枚举，标签专用
     */
    @TableField(exist = false)
    private Long tagEnumId;

    /**
     * 标签绑定的一级分类的code，标签专用
     */
    @TableField(exist = false)
    private String tagHasCategoryCode;

    @Override
    public Long getUniqueIdentity() {
        return Long.parseLong(String.valueOf(id));
    }

    @Override
    public Integer getOrdValue() {
        return ord;
    }

    @Override
    public void setOrdValue(Integer ordValue) {
        this.ord = ordValue;
    }
}
