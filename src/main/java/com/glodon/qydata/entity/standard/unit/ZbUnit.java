package com.glodon.qydata.entity.standard.unit;

import com.glodon.qydata.entity.system.QYFlag;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ZbUnit extends QYFlag implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 主键 **/
    private Long id;

    /** 单位名称 **/
    private String name;

    /** 是否已删除，0：未删除，1：已删除 **/
    private Integer isDeleted;

    /** 企业编码 **/
    private String customerCode;

    /** 是否允许编辑（0：否，1：是） **/
    private Integer isEditable;

    /** 创建人 **/
    private String creatorId;

    /** 创建时间 **/
    private Date createTime;

    /** 更新人 **/
    private String updateId;

    /** 更新时间 **/
    private Date updateTime;

    /** 排序 **/
    private Integer sort;

}