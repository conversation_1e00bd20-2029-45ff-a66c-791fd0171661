package com.glodon.qydata.entity.standard.category;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: luoml-b
 * @date: 2024/10/22 14:24
 * @description:
 */
@Data
public class CategoryTag implements Serializable {
    private static final long serialVersionUID = -1828500550205621081L;

    private Long id;
    private String name;

    private String enterpriseId;

    private Date createTime;

    private Date modifyTime;

    private Long originalId;

    private Integer ord;

    /**
     * 是否能编辑(0:能；1:不能)
     */
    @TableField(exist = false)
    private Integer canEdit;
}
