package com.glodon.qydata.entity.standard.feature;

import com.glodon.qydata.entity.system.QYFlag;
import com.glodon.qydata.util.mover.Movable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * @description: zb_project_feature_category_view_standards
 * <AUTHOR>
 * @date 2021/11/5 8:41
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ProjectFeatureCategoryView extends QYFlag implements Serializable, Movable {
    private static final long serialVersionUID = 462549052237353478L;
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 工程专业id
     */
    private Long tradeId;

    /**
     * 专业工程名称
     */
    private String tradeName;

    /**
     * 工程分类id
     */
    private String categoryCode;

    /**
     * zb_project_feature_standards特征id
     */
    private Long featureId;

    /**
     * 分类视图排序
     */
    private Integer ordCategory;

    /**
     * 企业编码
     */
    private String customerCode;

    /**
     * _self 特有 关联字段
     */
    private Long originId;

    private Integer type;
    /**
     * 是否无效 1 无效，空为有效
     */
    private Integer invalid;

    @Override
    public Long getUniqueIdentity() {
        return featureId;
    }

    @Override
    public Integer getOrdValue() {
        return ordCategory;
    }

    @Override
    public void setOrdValue(Integer ordValue) {
        this.ordCategory = ordValue;
    }
}