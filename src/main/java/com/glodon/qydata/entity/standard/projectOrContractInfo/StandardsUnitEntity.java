package com.glodon.qydata.entity.standard.projectOrContractInfo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.glodon.qydata.entity.system.QYFlag;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * zb_standards_unit - 主键
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("zb_standards_unit")
@Schema(name="StandardsUnitEntity对象", description="zb_standards_unit - 主键")
public class StandardsUnitEntity extends QYFlag implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(name = "主键")
    private Long id;

    @Schema(name = "单位名称")
    private String name;

    @Schema(name = "是否已删除，0：未删除，1：已删除")
    private Integer isDeleted;

    @Schema(name = "企业编码")
    private String customerCode;

    @Schema(name = "对应zb_standards_project_info表的主键")
    private Long zbStandardsProjectInfoId;

    @Schema(name = "单位来源类型，1：项目信息，2：合同信息")
    private Integer unitSourceType;

    @Schema(name = "创建人")
    private Long creatorId;

    @Schema(name = "创建时间")
    private LocalDateTime createTime;

    @Schema(name = "是否默认选中，0：未选中，1：选中")
    private Integer isChecked;
}
