package com.glodon.qydata.entity.sharding;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @author: luoml-b
 * @date: 2023/7/14 10:49
 * @description:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sharding_route")
public class ShardingRoute {

    private int id;

    private String customerCode;

    private String enterpriseId;

    private int tableType;

    private String currentTableName;

    private Date createTime;

    private Date updateTime;
}
