package com.glodon.qydata.common;

import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;

/**
 * <AUTHOR>
 */
public class NotPrivateDeploymentCondition implements Condition {
    public static final String PRIVATE_DEPLOYMENT = "privateDeployment";

    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
        String privateDeployment = context.getEnvironment().getProperty(PRIVATE_DEPLOYMENT);
        return !Boolean.TRUE.toString().equalsIgnoreCase(privateDeployment);
    }
}
