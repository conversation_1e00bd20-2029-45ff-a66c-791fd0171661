package com.glodon.qydata.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @packageName: com.glodon.qydata.config.aop.annotation
 * @className: BusinessCache
 * @author: yanyh <EMAIL>
 * @date: 2023/6/7 18:19
 * @description: 使用的Key值为 模块名:CustomerCode   MD5   值为返回值
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface BusinessCache {
    /**
     * 商户编码
     * 说明： 如果在当前service中无customerCode可以声明 自己获取 样例： ${defaultCustomerCode}
     * <p>
     * ${targetMethodArgName} 使用spel方式进行参数提取
     * <p>
     * default "-1000000000"
     *
     * @return 商户编码
     */
    String customerCode();

    /**
     * 是否需要清理缓存
     *
     * @return 是否需要清理缓存, 默认为不需要
     */
    boolean isInvalidateCache() default false;
}
