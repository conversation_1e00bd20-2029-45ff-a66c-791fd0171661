package com.glodon.qydata.common;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.service.depend.ApiAuthService;
import com.glodon.qydata.service.system.IGlodonUserService;
import com.glodon.qydata.service.trust.TrustService;
import com.glodon.qydata.util.DataDealUtil;
import com.glodon.qydata.util.IPUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

import static com.glodon.qydata.common.constant.HeaderConstants.ENTERPRISE_ID;

/**
 * 公共的Controller
 * Created by weijf on 2021/8/16.
 */
@Slf4j
@Controller
public class BaseController {

    @Autowired
    HttpServletRequest request;
    @Autowired
    private IGlodonUserService glodonUserService;
    @Autowired
    private ApiAuthService apiAuthService;
    @Autowired
    private TrustService trustService;

    /**
     * 获取request对象
     * @return
     * <AUTHOR>
     */
    public static HttpServletRequest getRequest() {
        ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        return attrs.getRequest();
    }

    /**
     * 获取response对象
     * @return
     * <AUTHOR>
     */
    public static HttpServletResponse getResponse() {
        ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        return attrs.getResponse();
    }

    /**
     * 获取当前请求的IP
     * @return
     */
    public String getIP(){
        return IPUtils.getIpAddr(request);
    }

    /**
     * 获取当前登录token
     * @return
     */
    public String getToken(){
        return request.getHeader(Constants.HEADER_TOKEN);
    }

    /**
     * 获取当前登录的施工token
     * @return
     */
    public String getSgToken() {
        return request.getHeader(Constants.HEADER_SG_TOKEN);
    }
    /**
     * 获取当前登录用户ID
     * @return
     */
    public String getGlobalId(){
        String globalId = request.getHeader(Constants.HEADER_USER_ID);
        DataDealUtil.assertGlobalIdIsValid(globalId);
        return globalId;
    }

    /**
     * @description:  获取globalId
     * <AUTHOR>
     * @date 2021/10/22 14:22
     */
    public Long getGlobalIdLong(){
        return Long.parseLong(getGlobalId());
    }

    /**
     * 获取当前登录用户信息
     * @return
     */
    public String getUserName(){
        return glodonUserService.getUserName(getGlobalId(),null);
    }

    /**
     * @description: 获取企业编码
     * @param
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/12/17 15:06
     */
    public String getCustomerCode() {
        return glodonUserService.getCustomerCode(getGlobalId());
    }

    /**
     * 查询委托权限，获取委托企业的企业编码
     * @param basicInfoType
     * @param destEnterpriseId
     * @param trustProductSource
     * @return
     */
    public String getCustomerCode(String basicInfoType, String destEnterpriseId, String trustProductSource) {
        if (StringUtils.isEmpty(destEnterpriseId)){
            getRequest().setAttribute(ENTERPRISE_ID, getEnterpriseId());
            return getCustomerCode();
        }

        Boolean trustDataAuth = trustService.trustDataAuth(getGlobalId(), trustProductSource, basicInfoType, destEnterpriseId);

        if (Boolean.FALSE.equals(trustDataAuth)) {
            throw new BusinessException("没有目标企业的委托权限");
        }

        String customerCode = apiAuthService.getQyCodeByEnterpriseId(destEnterpriseId);
        // 确定使用企业id还是企业编码
        return glodonUserService.setQyFlagCache(customerCode, destEnterpriseId);
    }

    /**
     * 获取enterpriseId
     * @return 返回enterpriseId
     */
    public String getEnterpriseId() {
        //查该帐号的企业ID
        return glodonUserService.getEnterpriseId(getGlobalId(),null);
    }

    /**
     * @description: 获取企业下所有员工的GlobalId和名称的对应关系
     * @param
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/12/17 15:06
     */
    @Deprecated
    public Map<String, String> getGlobalIdNameMap() {
        Map<String, String> globalIdNameMap = new HashMap<>(16);
        String globalId = getGlobalId();
        log.info("查询企业成员开始");
        JSONArray memberList = glodonUserService.getEntMemberList(globalId,null);
        log.info("查询企业成员结束，企业成员数量：{}", memberList.size());
        for (Object o : memberList) {
            JSONObject obj = (JSONObject) JSONObject.toJSON(o);
            globalIdNameMap.put(obj.getString("globalId"), obj.getString("userName"));
        }

        // 企业账号信息加入
        JSONObject entInfo = glodonUserService.getEntInforFromCached(globalId, null);
        if (entInfo != null){
            JSONObject enterprise = entInfo.getJSONObject("enterprise");
            String userName = enterprise.getString("accountName");
            String id = enterprise.getLong("id").toString();
            globalIdNameMap.put(id, userName);
        }

        return globalIdNameMap;
    }

    public Map<String, String> getGlobalIdNameMap(List<String> globalIds) {
        if (CollectionUtils.isEmpty(globalIds)) {
            return Collections.emptyMap();
        }
        globalIds = globalIds.stream().filter(globalId -> globalId != null && !"null".equalsIgnoreCase(globalId)
                && !"-1".equals(globalId) && !"-100".equals(globalId)).collect(Collectors.toList())
                .stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(globalIds)) {
            return Collections.emptyMap();
        }
        Map<String, String> globalIdNameMap = new HashMap<>(16);
        try {
            // 先获取userId，查询用户信息需要根据userId查询，早期用户的GlobalId与userId可能不同
            List<String> userIds = glodonUserService.getUserIdsByGlobalIds(globalIds);

            // 填充Map
            userIds.forEach(userId -> {
                String accountName = glodonUserService.getAccountNameById(userId, getSgToken());
                if (!StringUtils.isEmpty(accountName)) {
                    globalIdNameMap.putIfAbsent(userId, accountName);
                }
            });
        } catch (Exception e) {
            log.error("获取用户中心真实名称发生错误", e);
        }
        return globalIdNameMap;
    }
}
