package com.glodon.qydata.common.enums;

import lombok.AllArgsConstructor;

/**
 * 错误码
 * 参考 org.springframework.http.HttpStatus
 * Created by weijf
 */
@AllArgsConstructor
public enum ResponseCode {
	SUCCESS(200, "成功"),
	//"Internal Server Error"
	ERROR(500, "服务器遇到错误，无法完成请求"),
	//"Bad Request"
	PARAMETER_ERROR(400, "参数错误"),
	//"Unauthorized"
	UN_AUTH(401, "没有权限"),
	//"Forbidden"
	FORBIDDEN(403, "没有权限"),
	//"Not Found"
	NOT_EXIST(404, "请求不存在"),
	// "Method Not Allowed"
	METHOD_NOT_ALLOWED(405 ,"方法不允许"),

	VIP_EXPIRE(406, "权益到期"),

	CHECKING_FAIL(303,"验证失败"),

	//"Multiple Choices"
	LOGIN_AGAIN(300, "请重新登录"),
	//未登录
	NOT_LOGIN(301, "未登录"),
	//其它地方登录
	OTHER_LOGIN(302, "该账号已在其它地方登录，请重新登录!"),
	//名称重复
	NAME_DUPLICATED(304, "名称重复"),
	OPTION_LENGTH_ERROR(305,"枚举值超过限制字数，请调整后再试"),
	OPTION_NUM_ERROR(306,"枚举值上限不超过60个，请调整后再试"),
	//其他失败情况
	FAILURE(601,"操作失败"),
	EDIT_FORBIDDEN(408, "修订被锁住，请稍后再试"),
	STANDARD_ENABLE_CHECK_EXIST(409, "该工程分类下已有启用的建造标准，是否停用其他建造标准，启用当前建造标准？"),

	EXCEL_FORMAT_ERROR(50020,"内容格式不正确，请按模板调整格式后导入"),
	EXCEL_HEADER_INVALID_ERROR(50021,"导入失败，文件格式不正确，请按模板调整格式后导入"),
	EXCEL_NAME_LENGTH_ERROR(50022,"导入失败，名称超过限制字数，请调整后导入"),
	EXCEL_UNIT_LENGTH_ERROR(50023,"导入失败，单位超过限制字数，请调整后导入"),
	EXCEL_REMARK_LENGTH_ERROR(50024,"导入失败，备注超过限制字数，请调整后导入"),
	EXCEL_OPTION_LENGTH_ERROR(50025,"导入失败，枚举值超过限制字数，请调整后导入"),
	EXCEL_OPTION_NUM_ERROR(50026,"导入失败，枚举值上限不超过60个，请调整后再试"),
	EXCEL_BUILD_STANDARD_CODE_ERROR(51021,"导入失败，编码、标准名称未按要求填写，请按导入说明修改后重试"),
	EXCEL_BUILD_STANDARD_NAME_LENGTH_ERROR(51022,"导入失败，标准名称超过限制字数，请调整后导入"),
	EXCEL_BUILD_STANDARD_DESC_LENGTH_ERROR(51023,"导入失败，标准说明超过限制字数，请调整后导入"),

	;

	private Integer code;

	private String message;


	public Integer getCode() {
		return code;
	}

	public String getMessage() {
		return message;
	}
}