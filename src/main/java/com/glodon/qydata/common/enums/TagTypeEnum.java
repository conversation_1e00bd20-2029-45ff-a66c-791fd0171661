package com.glodon.qydata.common.enums;

/**
 * 标签节点类型：区分工程分类标签、专业标签
 */
public enum TagTypeEnum {
    CATEGORY_TAG(0, "工程分类标签"),
    TRADE_TAG(1, "专业标签");

    /**
     * 节点类型
     */
    private Integer tagType;
    /**
     * 节点标识
     */
    private String description;

    TagTypeEnum(Integer tagType, String description){
        this.tagType = tagType;
        this.description = description;
    }

    public Integer getTagType() {
        return this.tagType;
    }
}
