package com.glodon.qydata.common.enums;

import lombok.AllArgsConstructor;

/**
 * 数据redis key 管理枚举类
 **/
@AllArgsConstructor
public enum RedisKeyEnum {

    GCW_TOKEN_KEY(8,"gcw-cloud-token",60*60*3,"广材网获取token"),
    ACCESS_TOKEN_CLIENT_CREDENTIALS(8,"accessToken:clientCredentials",8 * 60 * 60,"广联云-应用级别（client_credentials）accessToken"),
    ACCESS_TOKEN_USER_ID(5,"accessToken:uId:{0}",1* 60 * 60,"广联云-用户级别（uid）accessToken"),
    USER_SESSION(2,"user:session:userId={0}:{1}",60 * 60 * 5,"用户登录信息"),

    //企业信息
    ENT_INFO_BY_ID(8,"userInfo:entInfo:{0}",60 * 60 * 24 * 14,"entInfo 企业信息"),
    DELETED_ACCOUNT_NAME(8,"userInfo:deleteAccountName:{0}", 60 * 60 * 24 * 30,"已删除账号的名称"),
    ENT_MEMBER_NAME(9,"userInfo:entMemberName:{0}",7 * 24 * 60 * 60,"造价云用户姓名"),
    USER_INFO_ASSET_NUM(9,"userInfo:assetNum:{0}_{1}:{2}",60 * 60 * 24,"用户资产"),
    CUSTOMER_CODE(9,"customerCode:{0}", 60 * 10 , "用户所对应的企业编码"),
    CUSTOMER_CODE_BAK(9,"customerCodeBak:{0}", 60 * 60 * 24 , "用户所对应的企业编码"),
    QY_FLAG_INFO(9,"basicInfo:qyFlagNew:{0}", 60 * 10 , "企业编码对应的企业信息"),
    ENT_CUSTOMER_DUP(9,"basicInfo:entCustomerDup:{0}", 60 * 10 , "企业id是否在多编码列表"),
    ACCOUNT_TYPE_KEY(9,"basicInfo:account_type:{0}", 600 * 10 , "账号类型的缓存"),
    //锁
    LOCK_UPDATE_USER(8,"lock:updateUser:{0}",30,"更新用户锁"),
    LOCK_INIT_SELF_DATA_INFO(8,"lock:initSelfData:{0}",30,"初始化暂存标准数据锁"),
    // 使用JedisLock.class 只用到key
    LOCK_INIT_CATEGORY(8,"lock:initCategory:{0}",30,"初始化工程分类锁"),
    LOCK_INIT_PROJECT(8,"lock:initProject:{0}",30,"初始化项目合同信息锁"),
    LOCK_INIT_TRADE_MAIN_QUANTITY_FEATURE(8,"lock:initTradeMainQuantityFeature:{0}",30,"初始化工程专业、主要量指标、工程特征锁"),
    LOCK_INIT_BUILD_STANDARD(8,"lock:initBuildStandard:{0}",30,"初始化建造标准锁"),
    LOCK_PUBLISH_FEATURE(8,"lock:publishFeature:{0}",30,"发布工程特征锁"),
    LOCK_REFRESH_FEATURE(8,"lock:refreshFeature:{0}",30,"刷工程特征数据锁"),
    // 地区数据
    ALL_AREA(5,"basic:allArea:{0}",60*60*24*30,"全部地区"),
    //权限
    USER_RIGHT_INFO(9,"userInfo:right:{0}:{1}:{2}",60 * 60 * 24 * 7,"用户权益信息缓存"),

    USER_API_AUTH_RIGHT_INFO(9,"user:apiAuth:right:{0}:{1}", 60 * 60 * 24, "apiAuth用户权益信息缓存"),

    // 同步市场化计价历史数据使用
    DS_PROJECT_TYPE(8,"ds:projectType",8 * 60 * 60,"projectType的json"),
    DS_SYSTEM_EXPRESSION(8,"ds:systemExpression",8 * 60 * 60,"内置特征项数据"),
    // temp
    TEMP_PROJECT_DATA_DEAL(8,"lock:tempProjectDataDeal:{0}",60 * 60 * 1,"刷数据临时用，执行后可删除，【工程分类】下方增加一行【产品定位】"),
    TEMP_EXPRESSION_DATA_DEAL(8,"lock:tempExpressionDataDeal:{0}",60 * 60 * 1,"刷数据临时用，执行后可删除，刷计算口径内置数据"),
    LOCK_INSERT_TRADE(8,"lock:insertTrade:{0}",30,"新增工程专业锁"),
    TEMP_ENT_ID_DEAL_LOCK(8,"lock:tempEntIdDealLock:{0}", 60 * 60 * 24,"刷数据临时用，执行后可删除"),
    TEMP_ENT_ID_DEAL_STATUS(8,"lock:tempEntIdDealStatus:{0}", 60 * 60 * 24,"刷数据临时用，执行后可删除"),

    SHARDING_CUSTOMER_CODE(9,"sharding:customerCode:{0}:{1}", 60 * 60 * 24 * 30, "分表所对应的企业编码"),

    SHARDING_ROUTE_LOCK(9,"lock:shardingRoute:{0}", 60 * 10, "入路由表锁"),

    PUBLISH_LOCK(9,"lock:publish:{0}:{1}", 90, "修订发布锁"),

    PROJECT_AND_CONTRACT_INFO_DATA_REFRESH_LOCK(8,"basicInfo:dataRefresh:projectAndContractInfo", 60 * 60 * 24 * 7, "合同信息刷新锁"),
    PUBLISH_VERSION_LOCK(8,"lock:publishVersion:{0}",30,"发布工程特征锁"),
    MAIN_ACCOUNT_NAME(9, "userInfo:account:name:{0}", 60 * 60 * 3,  "账号名"),
    AREA_COMPARE_LOCK(8, "lock:areaCompare", 60 * 5,  "地区对比锁");


    /** redis 库索引 */
    private int dbIndex;
    /** redis key */
    private String key;

    private int seconds;
    /** key 说明 */
    private String explain;

    public int getSeconds() {
        return seconds;
    }

    public void setSeconds(int seconds) {
        this.seconds = seconds;
    }

    public int getDbIndex() {
        return dbIndex;
    }

    public void setDbIndex(int dbIndex) {
        this.dbIndex = dbIndex;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getExplain() {
        return explain;
    }

    public void setExplain(String explain) {
        this.explain = explain;
    }

}
