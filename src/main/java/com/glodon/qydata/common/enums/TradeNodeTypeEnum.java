package com.glodon.qydata.common.enums;

/**
 * 专业节点类型枚举
 */
public enum TradeNodeTypeEnum {
    TRADE_TAG_NODE(0, "专业标签"),
    TRADE_NODE(1, "专业节点");

    /**
     * 节点类型
     */
    private Integer nodeType;
    /**
     * 节点标识
     */
    private String description;

    TradeNodeTypeEnum(Integer nodeType, String description){
        this.nodeType = nodeType;
        this.description = description;
    }

    public Integer getNodeType() {
        return this.nodeType;
    }
}
