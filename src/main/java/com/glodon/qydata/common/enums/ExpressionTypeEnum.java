package com.glodon.qydata.common.enums;

/**
 * 数据类型，对应dictionary表中type_name为数据类型的记录，text文本类，number数值类，date日期类，select单选类，selects多选类
 * <AUTHOR>
 * @date 2021/11/11 11:11
 */
public enum ExpressionTypeEnum {
    TYPE_TEXT("text","文本类"),
    TYPE_NUMBER("number","数值类"),
    TYPE_DATE("date","日期类"),
    TYPE_SELECT("select","单选类"),
    TYPE_SELECTS("selects","(多选)");

    private String code;
    private String name;

    ExpressionTypeEnum(String code, String name){
        this.code = code;
        this.name = name;
    }

    /**
     * @description: 根据类型名称获取类型code
     * @param name
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/11/11 11:11
     */
    public static String getCodeByName(String name){
        for (ExpressionTypeEnum value : ExpressionTypeEnum.values()) {
            if (value.getName().equals(name)) {
                return value.getCode();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
