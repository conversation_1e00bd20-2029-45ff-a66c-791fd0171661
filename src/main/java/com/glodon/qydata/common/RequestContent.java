package com.glodon.qydata.common;


import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.util.SpringUtil;
import com.glodon.qydata.util.ThreadLocalUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @className: RequestContent
 * @description: 封装一下
 * @author: zhaoyj-g
 * @date: 2020/9/9
 **/
public class RequestContent {

    public static final String GLOBAL_ID = "globalId";
    public static final String SG_TOKEN = "sgToken";

    /**
     * @Description 获取globalId
     * @param
     * @return java.lang.String
     **/
    public static String getGlobalId() {
        String globalId = null;
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (null == requestAttributes) {
            BaseController controller = getBaseController();
            if (null != controller) {
                globalId = controller.getGlobalId();
            }
        } else {
            globalId = (String) requestAttributes.getAttribute(GLOBAL_ID, RequestAttributes.SCOPE_REQUEST);
            if (null != globalId) {
                return globalId;
            } else {
                BaseController controller = getBaseController();
                if (null != controller) {
                    globalId = controller.getGlobalId();
                    requestAttributes.setAttribute(GLOBAL_ID,globalId,RequestAttributes.SCOPE_REQUEST);
                }
            }
        }
        return globalId;
    }

    public static Long getGlobalIdLong() {
        String globalId = getGlobalId();
        if (StringUtils.isNotEmpty(globalId)){
            return Long.parseLong(globalId);
        }
        return null;
    }

    /**
     * @Description 获取globalId
     * @param
     * @return java.lang.String
     **/
    public static String getSgToken() {
        String sgToken = null;
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (null == requestAttributes) {
            BaseController controller = getBaseController();
            if (null != controller) {
                sgToken = controller.getSgToken();
            }
        } else {
            sgToken = (String) requestAttributes.getAttribute(SG_TOKEN, RequestAttributes.SCOPE_REQUEST);
            if (null != sgToken) {
                return sgToken;
            } else {
                BaseController controller = getBaseController();
                if (null != controller) {
                    sgToken = controller.getSgToken();
                    requestAttributes.setAttribute(SG_TOKEN,sgToken,RequestAttributes.SCOPE_REQUEST);
                }
            }
        }
        return sgToken;
    }

    private static BaseController getBaseController() {
        try {
            return SpringUtil.getBean(Constants.BeanName.BASE_CONTROLLER);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static ApplicationContext getApplicationContext() {
        return SpringUtil.getApplicationContext();
    }

    public static HttpServletRequest getRequest() {
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        if (Objects.nonNull(attributes)) {
            return ((ServletRequestAttributes) attributes).getRequest();
        }
        return null;
    }

    public static HttpServletResponse getResponse() {
        RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
        if (Objects.nonNull(attributes)) {
            return ((ServletRequestAttributes) attributes).getResponse();
        }
        return null;
    }

    /**
     * 获取当前请求的HTTP方法
     * @return HTTP方法名称（GET, POST, PUT, DELETE等）
     */
    public static String getHttpMethod() {
        HttpServletRequest request = getRequest();
        return request != null ? request.getMethod() : null;
    }

    /**
     * 获取当前执行方法的指定注解
     * @param annotationClass 注解类型
     * @param <T> 注解类型
     * @return 注解实例，如果不存在则返回null
     */
    public static <T extends Annotation> T getCurrentMethodAnnotation(Class<T> annotationClass) {
        return ThreadLocalUtil.getCurrentMethodAnnotation(annotationClass);
    }

    /**
     * 获取当前执行的方法
     * @return 当前方法
     */
    public static Method getCurrentMethod() {
        return ThreadLocalUtil.getCurrentMethod();
    }

    /**
     * @description: 获取企业编码
     * @param
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/12/17 15:06
     */
    public static String getCustomerCode() {
        BaseController baseController = SpringUtil.getBean(Constants.BeanName.BASE_CONTROLLER);
        return baseController.getCustomerCode();
    }

    /**
     * @description: 获取企业下所有员工的GlobalId和名称的对应关系
     * @param
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/12/17 15:06
     */
    @Deprecated
    public static Map<String, String> getGlobalIdNameMap() {
        BaseController baseController = SpringUtil.getBean(Constants.BeanName.BASE_CONTROLLER);
        return baseController.getGlobalIdNameMap();
    }

    public static Map<String, String> getGlobalIdNameMap(List<String> globalIds) {
        BaseController baseController = SpringUtil.getBean(Constants.BeanName.BASE_CONTROLLER);
        return baseController.getGlobalIdNameMap(globalIds);
    }
}
