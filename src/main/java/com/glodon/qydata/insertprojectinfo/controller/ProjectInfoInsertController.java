package com.glodon.qydata.insertprojectinfo.controller;


import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.insertprojectinfo.service.ProjectInfoInsertService;
import com.glodon.qydata.vo.common.ResponseVo;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/basicInfo/standards/project")
public class ProjectInfoInsertController extends BaseController {
    @Autowired
    private ProjectInfoInsertService projectInfoInsertService;

    @Operation(summary = "插入数据")
    @GetMapping("/insertProjectInfo")
    public ResponseVo insertProjectInfo() {
        projectInfoInsertService.insertProjectInfo();
        return ResponseVo.success();
    }

    @Operation(summary = "一个企业插入数据")
    @GetMapping("/insertProjectInfoToOne/{customerCode}")
    public ResponseVo insertProjectInfoToOne(@PathVariable String customerCode) {
        projectInfoInsertService.insertProjectInfoToOne(customerCode);
        return ResponseVo.success();
    }

    @GetMapping("/delInvalidProjectInfo")
    public ResponseVo deleteInvalidProjectInfo() {
        projectInfoInsertService.delInvalidProjectInfo();
        return ResponseVo.success();
    }
}
