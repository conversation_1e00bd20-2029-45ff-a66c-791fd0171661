package com.glodon.qydata.insertprojectinfo.entity;

import com.glodon.qydata.entity.system.QYFlag;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
@Data
@NoArgsConstructor
public class StandardsProjectInfoEntity extends QYFlag implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String name;

    private String typeCode;

    private String unit;

    private String selectList;

    private Integer isUsing;
    private Integer isRequired;
    private Integer isExpression;

    private Long creatorId;

    private LocalDateTime createTime;

    private Long updaterId;

    private LocalDateTime updateTime;

    private String remark;

    private String customerCode;

    private Integer isDeleted;

    private Integer ord;

    private Integer standardDataType;

    private Long originId;

    private String globalId;

    public StandardsProjectInfoEntity(String name, String typeCode, String unit, String selectList, Integer isUsing, Integer isRequired, Integer isExpression, String remark, String customerCode, Integer isDeleted, Integer ord, Integer standardDataType, Long originId, String globalId) {
        this.name = name;
        this.typeCode = typeCode;
        this.unit = unit;
        this.selectList = selectList;
        this.isUsing = isUsing;
        this.isRequired = isRequired;
        this.isExpression = isExpression;
        this.remark = remark;
        this.customerCode = customerCode;
        this.isDeleted = isDeleted;
        this.ord = ord;
        this.standardDataType = standardDataType;
        this.originId = originId;
        this.globalId = globalId;
    }
}
