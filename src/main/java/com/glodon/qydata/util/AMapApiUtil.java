package com.glodon.qydata.util;

import cn.hutool.core.lang.Pair;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.glodon.qydata.vo.amap.AMapDistrict;
import com.glodon.qydata.vo.amap.AMapQueryParams;
import com.glodon.qydata.vo.amap.AMapResponse;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 高德地图-行政区域查询
 * 文档地址：<a href="https://lbs.amap.com/api/webservice/guide/api/district"/>
 *
 * <AUTHOR>
 */
@Slf4j
public class AMapApiUtil {
    private static final String BASE_URL = "https://restapi.amap.com/v3/config/district";
    private static final String API_KEY = "5bf5b35f17854ca5a71e96d508aad543";


    /**
     * 构建高德地图行政区查询URL
     *
     * @param params 包含查询参数的对象
     * @return 格式化后的请求URL
     */
    private static String buildDistrictUrl(AMapQueryParams params) {
        // 使用 StringBuilder 构建 URL
        StringBuilder urlBuilder = new StringBuilder(String.format("%s?key=%s", BASE_URL, API_KEY));

        if (params.getKeywords() != null) {
            urlBuilder.append(String.format("&keywords=%s", params.getKeywords()));
        }
        if (params.getSubdistrict() != null) {
            urlBuilder.append(String.format("&subdistrict=%s", params.getSubdistrict()));
        }
        if (params.getExtensions() != null) {
            urlBuilder.append(String.format("&extensions=%s", params.getExtensions()));
        }
        if (params.getPage() != null) {
            urlBuilder.append(String.format("&page=%s", params.getPage()));
        }
        if (params.getOffset() != null) {
            urlBuilder.append(String.format("&offset=%s", params.getOffset()));
        }
        if (params.getFilter() != null) {
            urlBuilder.append(String.format("&filter=%s", params.getFilter()));
        }
        if (params.getCallback() != null) {
            urlBuilder.append(String.format("&callback=%s", params.getCallback()));
        }
        if (params.getOutput() != null) {
            urlBuilder.append(String.format("&output=%s", params.getOutput()));
        }

        return urlBuilder.toString();
    }

    /**
     * 获取行政区信息
     *
     * @param params 包含查询参数的对象
     * @return 高德地图API的响应对象
     */
    public static AMapResponse getDistrict(AMapQueryParams params) {
        String url = buildDistrictUrl(params);
        HttpResponse response = HttpRequest.get(url).execute();
        if (response.isOk()) {
            String result = response.body();
            return JSONUtil.toBean(result, AMapResponse.class);
        } else {
            log.error("请求失败: URL={}, 状态码={}, 响应体={}", url, response.getStatus(), response.body());
        }
        return null;
    }

    public static List<AMapDistrict> getProvince() {
        AMapQueryParams params = new AMapQueryParams();
        params.setSubdistrict(3);
        params.setOffset(9999);
        AMapResponse district = AMapApiUtil.getDistrict(params);
        assert district != null;
        return new ArrayList<>(district.getDistricts().get(0).getDistricts());
    }

    public static void main(String[] args) {
        AMapQueryParams params = new AMapQueryParams();
        params.setSubdistrict(3);
        params.setOffset(9999);

        AMapResponse district = AMapApiUtil.getDistrict(params);

        List<String> collect = district.getDistricts().get(0).getDistricts().stream().map(x -> x.getName()).collect(Collectors.toList());
        log.info(JSONUtil.toJsonStr(collect));
    }

    public static List<AMapDistrict> getAllDistricts() {
        List<AMapDistrict> provinces = getProvince();
        provinces.sort(Comparator.comparing(AMapDistrict::getAdcode));
        List<AMapDistrict> allDistricts = new ArrayList<>();
        for (int i = 0; i < provinces.size(); i++) {
            AMapDistrict province = provinces.get(i);
            province.setOrd(i + 1); // 设置省份的顺序
            province.setSort(i + 1); // 设置省份的sort值
            addSubDistrictsRecursively(province, allDistricts, null, 0); // 初始化时parentNamePath为null
        }
        return allDistricts;
    }

    private static void addSubDistrictsRecursively(AMapDistrict district, List<AMapDistrict> allDistricts, String parentNamePath, int parentOrd) {
        // 构建当前区域的namePath
        String currentNamePath = (parentNamePath == null) ? district.getName() : parentNamePath + "-" + district.getName();
        district.setNamePath(currentNamePath);

        // 设置当前区域的ord值
        int currentOrd = (parentOrd == 0) ? district.getOrd() : parentOrd * 100 + district.getOrd(); // 确保子区域的ord值基于父区域的ord值
        district.setOrd(currentOrd);

        if ("province".equals(district.getLevel()) || "city".equals(district.getLevel()) || "district".equals(district.getLevel())) {
            allDistricts.add(district);
        }

        List<AMapDistrict> subDistricts = district.getDistricts();
        if (subDistricts != null) {
            subDistricts.sort(Comparator.comparing(AMapDistrict::getAdcode));
            for (int i = 0; i < subDistricts.size(); i++) {
                AMapDistrict subDistrict = subDistricts.get(i);
                subDistrict.setOrd(i + 1); // 设置子区域的顺序
                subDistrict.setSort(i + 1); // 同父级下设置sort值
                addSubDistrictsRecursively(subDistrict, allDistricts, currentNamePath, currentOrd);
            }
        }
    }

    private static <T> Pair<T, T> getCenterAsPair(AMapDistrict gdNode, Function<String, T> converter) {
        String[] centerArr = gdNode.getCenter().split(",");
        T longitude = converter.apply(centerArr[0]);
        T latitude = converter.apply(centerArr[1]);
        return new Pair<>(longitude, latitude);
    }

    public static Pair<String, String> getLongitudeAndLatitudeAsString(AMapDistrict gdNode) {
        return getCenterAsPair(gdNode, Function.identity());
    }

    public static Pair<Double, Double> getLongitudeAndLatitudeAsDouble(AMapDistrict gdNode) {
        return getCenterAsPair(gdNode, Double::valueOf);
    }

}
