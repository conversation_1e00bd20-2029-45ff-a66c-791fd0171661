package com.glodon.qydata.util;

import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class DataDealUtil {
    // 判断是否为数字
    public static boolean isNumeric(String str) {
        String bigStr;
        try {
            bigStr = new BigDecimal(str).toString();
        } catch (Exception e) {
            return false;//异常 说明包含非数字。
        }
        return true;
    }

    /**
     　　* @description: 在stringBuffer中最终形成  001,01001,001001001
     解决代码冗余的提取方法
     　　* @param
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/7/23 10:12
     　　*/
    public static void categoryCodeAppend(StringBuffer codeBuffer,String code){
        if(EmptyUtil.isEmpty(code)){
            return;
        }
        if(codeBuffer.length() == 0){
            codeBuffer.append(code);
        }else{
            codeBuffer.append(",").append(code);
        }
    }


    public static void main(String[] args) {

        String str = "adasfAAADFD阿萨德发123";
        int unicodeCount = 0;
        int szCount = 0;
        int zmCount = 0;

        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (c >= '0' && c <= '9') {
                szCount++;
            }else if((c >= 'a' && c<='z') || (c >= 'A' && c<='Z')){
                zmCount++;
            }else{
                unicodeCount++;
            }
        }

        System.out.println(unicodeCount);

        System.out.println(szCount);

        System.out.println(zmCount);

    }

    /**
     * 将一个List均分成n个list,主要通过偏移量来实现的
     * @param source 源集合
     * @param limit 最大值
     * @return
     */
    public static List averageAssign(List source, int limit) {
        if (null == source || source.isEmpty()) {
            return Collections.emptyList();
        }

        List<List> result = new ArrayList<>();
        int listCount = (source.size() - 1) / limit + 1;
        int remaider = source.size() % listCount; // (先计算出余数)
        int number = source.size() / listCount; // 然后是商
        int offset = 0;// 偏移量
        for (int i = 0; i < listCount; i++) {
            List value;
            if (remaider > 0) {
                value = source.subList(i * number + offset, (i + 1) * number + offset + 1);
                remaider--;
                offset++;
            } else {
                value = source.subList(i * number + offset, (i + 1) * number + offset);
            }
            result.add(value);
        }
        return result;

    }

    /**
     * 判断globalId是否有效
     * @param globalId
     * @return
     */
    public static void assertGlobalIdIsValid(String globalId) throws BusinessException  {
        if (StringUtils.isBlank(globalId)) {
            throw new BusinessException(ResponseCode.ERROR, "globalId不能为空");
        }

        Pattern p = Pattern.compile("-?\\d+");
        Matcher matcher = p.matcher(globalId);
        if (!matcher.matches()) {
            throw new BusinessException(ResponseCode.ERROR, "globalId非法");
        }
    }

}
