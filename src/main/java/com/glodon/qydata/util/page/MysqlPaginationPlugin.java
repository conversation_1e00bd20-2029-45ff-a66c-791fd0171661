package com.glodon.qydata.util.page;

import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Signature;

import java.sql.Connection;

@Intercepts(@Signature(type = StatementHandler.class, method = "prepare", args = { Connection.class,Integer.class }))
@SuppressWarnings("squid:S1192")
public class MysqlPaginationPlugin extends BasePaginationPlugin {

	@Override
	public String getCountSql(String sql) {
		int endIndex = sql.contains("page_order") ? sql.indexOf("page_order") : sql.length();
		if (sql.contains("page_from")) {
			return  "select count(1) from " + sql.substring(sql.indexOf("page_from") + 9, endIndex);
		} else if (sql.contains("page_order")) {
			return  "select count(1) from ( " + sql.substring(0, endIndex)+ ") as temp_ ";
		} else {
			return "SELECT COUNT(1) FROM (" + sql + ") as temp_";
		}
	}

	@Override
	public String getPageSql(String sql, PageParameter pageInfo, String sqlType) {
		Integer size = pageInfo.getPageSize();// 一页有多少
		sql = sql.replace("page_from", " from ");
		sql = sql.replace("page_order", " order ");
		if(StringUtils.isNotEmpty(sqlType) && sqlType.equals("Pgsql")){
			return sql + " LIMIT " + size +" offset "+ (pageInfo.getCurrentPage()-1) * size ;
		}
		return sql + " LIMIT " + (pageInfo.getCurrentPage()-1) * size + "," + size;
	}

}
