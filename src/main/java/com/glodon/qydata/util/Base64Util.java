package com.glodon.qydata.util;


import cn.hutool.core.codec.Base64;

/**
 * base64 加密和解密工具
 * Helg
 * 20201110
 */
public class Base64Util {

    /**
     * BASE64解密
     * @throws Exception
     */
    public static String decryptBASE64(String key) throws Exception {
        byte[] bytes = Base64.decode(key);
        return new String(bytes);
    }

    /**
     * BASE64加密
     */
    public static String encryptBASE64(String key) throws Exception {
        byte[] bytes = key.getBytes();
        return Base64.encode(bytes);
    }

}
