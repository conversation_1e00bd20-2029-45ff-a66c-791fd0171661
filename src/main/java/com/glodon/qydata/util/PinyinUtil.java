package com.glodon.qydata.util;

import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;

/**
 * 汉语转换成拼音类
 * Created by weijf on 2020/7/10.
 */
@Slf4j
public class PinyinUtil {
    /**
     * 将文字转为汉语拼音 包含特殊符号
     * @param chineseLanguage 要转成拼音的中文
     */
    private static final String REGX_CN = "[\u4e00-\u9fa5]+"; // 匹配中文
    private static final String REGX_NUM = "[0-9]+"; // 匹配数字
    private static final String REGX_LET = "[a-zA-Z]+"; // 匹配字母
    public static String getPinyinAll(String chineseLanguage){
        if(chineseLanguage == null || "".equals(chineseLanguage)){
            return chineseLanguage;
        }
        char[] clChars = chineseLanguage.trim().toCharArray();
        StringBuilder hanyupinyin = new StringBuilder();
        HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat();
        defaultFormat.setCaseType(HanyuPinyinCaseType.LOWERCASE); // 输出拼音全部小写
        defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE); // 不带声调
        defaultFormat.setVCharType(HanyuPinyinVCharType.WITH_V) ;
        try {
            for (int i=0; i<clChars.length; i++){
                if (String.valueOf(clChars[i]).matches(REGX_CN)){// 如果字符是中文,则将中文转为汉语拼音
                    hanyupinyin.append(PinyinHelper.toHanyuPinyinStringArray(clChars[i], defaultFormat)[0]);
                } else {// 如果字符不是中文,则不转换
                    hanyupinyin.append(clChars[i]);
                }
            }
        } catch (BadHanyuPinyinOutputFormatCombination e) {
//            log.info("字符不能转成汉语拼音");
        }catch (Exception e){
            // do nothing
        }
        return hanyupinyin.toString();
    }

    /**
     * 获取拼音字符串  不包含特殊符号
     * @param chineseLanguage
     * @return
     */
    public static String getPinyinString(String chineseLanguage){
        char[] clChars = chineseLanguage.trim().toCharArray();
        StringBuilder hanyupinyin = new StringBuilder();
        HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat();
        defaultFormat.setCaseType(HanyuPinyinCaseType.LOWERCASE);// 输出拼音全部大写
        defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);// 不带声调
        try {
            for (int i = 0; i < clChars.length; i++) {
                String str = String.valueOf(clChars[i]);
                if (str.matches(REGX_CN)) {// 如果字符是中文,则将中文转为汉语拼音,并取第一个字母
                    hanyupinyin.append(PinyinHelper.toHanyuPinyinStringArray(
                            clChars[i], defaultFormat)[0]);
                } else if (str.matches(REGX_NUM)) {// 如果字符是数字,取数字
                    hanyupinyin.append(clChars[i]);
                } else if (str.matches(REGX_LET)) {// 如果字符是字母,取字母
                    hanyupinyin.append(clChars[i]);
                }
            }
        } catch (BadHanyuPinyinOutputFormatCombination e) {
//            log.error("字符不能转成汉语拼音");
        }catch (Exception e){
            // do nothing
        }
        return hanyupinyin.toString();
    }

    /**
     * 转换拼音字符串中第一个为大写
     * @param chineseLanguage
     * @return
     */
    public static String getFirstLettersUp(String chineseLanguage){
        return getFirstLetters(chineseLanguage ,HanyuPinyinCaseType.UPPERCASE);
    }

    /**
     * 转换拼音字符串第一个为小写
     * @param chineseLanguage
     * @return
     */
    public static String getFirstLettersLow(String chineseLanguage){
        return getFirstLetters(chineseLanguage ,HanyuPinyinCaseType.LOWERCASE);
    }

    /**
     * 获取第一个位置
     * @param chineseLanguage
     * @param caseType
     * @return
     */
    public static String getFirstLetters(String chineseLanguage,HanyuPinyinCaseType caseType) {
        char[] clChars = chineseLanguage.trim().toCharArray();
        StringBuilder hanyupinyin = new StringBuilder();
        HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat();
        defaultFormat.setCaseType(caseType);// 输出拼音全部大写
        defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);// 不带声调
        try {
            for (int i = 0; i < clChars.length; i++) {
                String str = String.valueOf(clChars[i]);
                if (str.matches(REGX_CN)) {// 如果字符是中文,则将中文转为汉语拼音,并取第一个字母
                    hanyupinyin.append(PinyinHelper.toHanyuPinyinStringArray(clChars[i], defaultFormat)[0].substring(0, 1));
                } else if (str.matches(REGX_NUM)) {// 如果字符是数字,取数字
                    hanyupinyin.append(clChars[i]);
                } else if (str.matches(REGX_LET)) {// 如果字符是字母,取字母
                    hanyupinyin.append(clChars[i]);
                } else {// 否则不转换
                    hanyupinyin.append(clChars[i]);//如果是标点符号的话，带着
                }
            }
        } catch (BadHanyuPinyinOutputFormatCombination e) {
//            log.error("字符不能转成汉语拼音");
        }
        return hanyupinyin.toString();
    }


    /**
     * 取第一个汉字的第一个字符
     * @Title: getFirstLetter
     * @return String
     * @throws
     */
    public static String getFirstLetter(String chineseLanguage){
        char[] clChars = chineseLanguage.trim().toCharArray();
        StringBuilder hanyupinyin = new StringBuilder();
        HanyuPinyinOutputFormat defaultFormat = new HanyuPinyinOutputFormat();
        defaultFormat.setCaseType(HanyuPinyinCaseType.UPPERCASE);// 输出拼音全部大写
        defaultFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);// 不带声调
        try {
            String str = String.valueOf(clChars[0]);
            if (str.matches(REGX_CN)) {// 如果字符是中文,则将中文转为汉语拼音,并取第一个字母
                hanyupinyin.append(PinyinHelper.toHanyuPinyinStringArray(
                        clChars[0], defaultFormat)[0].substring(0, 1));
            } else if (str.matches(REGX_NUM)) {// 如果字符是数字,取数字
                hanyupinyin.append(clChars[0]);
            } else if (str.matches(REGX_LET)) {// 如果字符是字母,取字母
                hanyupinyin.append(clChars[0]);
            } else {// 否则不转换

            }
        } catch (BadHanyuPinyinOutputFormatCombination e) {
//            log.error("字符不能转成汉语拼音");
        }
        return hanyupinyin.toString();
    }

    /**
     * 测试程序入口
     * @param args
     */
    public static void main(String[] args) {
        System.out.println(PinyinUtil.getPinyinAll("11243sf测试汉语字符串s&'*%&测试汉语字符串"));
        System.out.println(PinyinUtil.getPinyinString("11243sf测试汉语字符串s&'%&测试汉语字符串"));
        System.out.println(PinyinUtil.getFirstLetter("11243sf测试汉语字符串&'%R测试汉语字符串"));
        System.out.println(PinyinUtil.getFirstLetters("11243sf测试汉语字符串125''21*%^测试汉语字符串",HanyuPinyinCaseType.LOWERCASE));
        System.out.println(PinyinUtil.getFirstLettersUp("11243sf测试汉语字符串*'(%测试汉语字符串"));
    }

}
