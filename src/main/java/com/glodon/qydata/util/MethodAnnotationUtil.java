package com.glodon.qydata.util;

import lombok.extern.slf4j.Slf4j;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;

/**
 * @description: 方法注解获取工具类
 * @author: yany<PERSON> <EMAIL>
 * @date: 2025/07/31
 */
@Slf4j
public class MethodAnnotationUtil {

    /**
     * 通过堆栈跟踪获取调用方法的注解
     * @param annotationClass 注解类型
     * @param <T> 注解类型
     * @return 注解实例，如果不存在则返回null
     */
    public static <T extends Annotation> T getCallerMethodAnnotation(Class<T> annotationClass) {
        try {
            StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
            
            // 从堆栈中找到调用者的方法
            for (int i = 2; i < stackTrace.length; i++) {
                StackTraceElement element = stackTrace[i];
                String className = element.getClassName();
                String methodName = element.getMethodName();
                
                try {
                    Class<?> clazz = Class.forName(className);
                    Method[] methods = clazz.getDeclaredMethods();
                    for (Method method : methods) {
                        if (method.getName().equals(methodName)) {
                            T annotation = method.getAnnotation(annotationClass);
                            if (annotation != null) {
                                return annotation;
                            }
                        }
                    }
                } catch (ClassNotFoundException e) {
                    log.debug("无法找到类: {}", className);
                }
            }
        } catch (Exception e) {
            log.error("获取方法注解时发生错误", e);
        }
        
        return null;
    }

    /**
     * 通过堆栈跟踪获取调用方法的所有注解
     * @return 注解数组
     */
    public static Annotation[] getCallerMethodAnnotations() {
        try {
            StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
            // 从堆栈中找到调用者的方法
            for (int i = 2; i < stackTrace.length; i++) {
                StackTraceElement element = stackTrace[i];
                String className = element.getClassName();
                String methodName = element.getMethodName();
                try {
                    Class<?> clazz = Class.forName(className);
                    Method[] methods = clazz.getDeclaredMethods();
                    for (Method method : methods) {
                        if (method.getName().equals(methodName)) {
                            Annotation[] annotations = method.getAnnotations();
                            if (annotations.length > 0) {
                                return annotations;
                            }
                        }
                    }
                } catch (ClassNotFoundException e) {
                    log.debug("无法找到类: {}", className, e);
                }
            }
        } catch (Exception e) {
            log.error("获取方法注解时发生错误", e);
        }
        return new Annotation[0];
    }
}
