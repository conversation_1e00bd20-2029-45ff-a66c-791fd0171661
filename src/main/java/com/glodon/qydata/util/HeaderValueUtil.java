package com.glodon.qydata.util;

import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * @className: HeaderValueUtil
 * @description: header中数据处理
 * @author: lijj-h
 * @date: 2022/11/23
 **/
@Slf4j
public class HeaderValueUtil {
    public static void addDataToHeader(RequestTemplate requestTemplate, String paramName) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        Map<String, String> threadLocalMap = (Map<String, String>) ThreadLocalUtil.getThreadLocal();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            // globalId获取  用户唯一标识
            String value = request.getHeader(paramName);
            Object valueObject = attributes.getAttribute(paramName, RequestAttributes.SCOPE_REQUEST);
            String valueStr = value != null ? value : (valueObject != null ? valueObject.toString() : null);
            if (valueStr != null) {
                requestTemplate.header(paramName, valueStr);
            }
        } else if (MapUtils.isNotEmpty(threadLocalMap)) {
            if (threadLocalMap.containsKey(paramName)) {
                requestTemplate.header(paramName, threadLocalMap.get(paramName));
            }
        } else {
            log.error("----调用外部接口时，header中无{}数据----", paramName);
        }
//        log.info("请求header={}", JSONObject.toJSONString(requestTemplate.headers()));
    }
}
