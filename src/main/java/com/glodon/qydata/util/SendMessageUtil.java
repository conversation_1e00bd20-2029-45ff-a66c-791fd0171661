package com.glodon.qydata.util;

import cn.hutool.core.thread.ThreadUtil;
import com.glodon.gcdp.sdk.monitor.service.GcostMonitorClient;

public class SendMessageUtil {

    public static void sendMarkdownMessage(String markdownMsg) {
        String[] lines = markdownMsg.split("\n");
        StringBuilder message = new StringBuilder();

        for (String line : lines) {
            if (message.length() + line.length() > 1024) {
                GcostMonitorClient.sendMessage(message.toString());
                ThreadUtil.sleep(1000); // 防止频繁发送短信
                message.setLength(0); // 清空StringBuilder
            }
            message.append(line).append("\n");
        }

        // 发送剩余的内容
        if (message.length() > 0) {
            GcostMonitorClient.sendMessage(message.toString());
        }
    }
}
