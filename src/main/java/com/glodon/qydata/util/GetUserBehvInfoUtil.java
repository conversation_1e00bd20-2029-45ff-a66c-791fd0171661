package com.glodon.qydata.util;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

public class GetUserBehvInfoUtil {

    public static String getUserBehvInfo(){
        //ip地址用于加密使用，没有实际意义
        String ip = "*************";
        Map<String,Object> paramsMap = new HashMap<>();
        paramsMap.put("productId", 43);
        try{
            String url = convertUrl("/event/getEventPoint", ip, JSONObject.toJSONString(paramsMap));

            RestTemplate restTemplate = new RestTemplate();
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class, JSONObject.toJSONString(paramsMap));
            if(response.getStatusCodeValue() == 200){
                if(StringUtils.isNotBlank(response.getBody())){
                    return response.getBody();
                }
            }
        }catch(Exception e){
            e.printStackTrace();
            return null;
        }
        return null;
    }

    /**
     * 请求地址加上签名参数
     * @param uri  请求地址
     * @param params  签名参数
     * @return
     * @throws Exception
     */
    private static String convertUrl(String uri, String ip, String params)throws Exception{
        String from = "gcw";
        String key = "1b533aecf68d49d8b1f97c997143b30f";
        String gcwInterface = "https://gcw-interface.gldjc.com";
        StringBuilder url = new StringBuilder(gcwInterface);
        url.append(uri);
        url.append("?client_ip="+ip);
        url.append("&date="+System.currentTimeMillis());
        url.append("&from="+from);
        String signature = MD5.md5(url.toString()+"&params="+params+"&"+key);
        url.append("&g_signature="+signature);
        url.append("&params={params}");
        return url.toString();
    }
}
