package com.glodon.qydata.util;

import cn.hutool.core.annotation.AnnotationUtil;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;

/**
 * @description: ThreadLocal工具类
 * <AUTHOR>
 * @date: 2022/11/9 16:12
 */
public class ThreadLocalUtil {
    private static final ThreadLocal<Object> THREAD_LOCAL = new ThreadLocal<>();
    private static final ThreadLocal<Method> CURRENT_METHOD_THREAD_LOCAL = new ThreadLocal<>();

    public static Object getThreadLocal() {
        return THREAD_LOCAL.get();
    }

    public static void setThreadLocal(Object object) {
        THREAD_LOCAL.set(object);
    }

    public static void removeData() {
        if (THREAD_LOCAL.get() != null) {
            THREAD_LOCAL.remove();
        }
        if (CURRENT_METHOD_THREAD_LOCAL.get() != null) {
            CURRENT_METHOD_THREAD_LOCAL.remove();
        }
    }

    /**
     * 设置当前执行的方法
     * @param method 当前方法
     */
    public static void setCurrentMethod(Method method) {
        CURRENT_METHOD_THREAD_LOCAL.set(method);
    }

    /**
     * 获取当前执行的方法
     * @return 当前方法
     */
    public static Method getCurrentMethod() {
        return CURRENT_METHOD_THREAD_LOCAL.get();
    }

    /**
     * 获取当前方法的指定注解
     * @param annotationClass 注解类型
     * @param <T> 注解类型
     * @return 注解实例，如果不存在则返回null
     */
    public static <T extends Annotation> T getCurrentMethodAnnotation(Class<T> annotationClass) {
        Method method = getCurrentMethod();
        if (method != null) {
            return AnnotationUtil.getAnnotation(method, annotationClass);
        }
        return null;
    }

    /**
     * 获取当前方法的所有注解
     * @return 注解数组
     */
    public static Annotation[] getCurrentMethodAnnotations() {
        Method method = getCurrentMethod();
        if (method != null) {
            return method.getAnnotations();
        }
        return new Annotation[0];
    }
}
