package com.glodon.qydata.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.glodon.qydata.common.constant.BusinessConstants;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.constant.OperateConstants;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.exception.BusinessException;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class FormatConvertUtil {
    private static final Map<String, Function<String, String>> FUNCTION_MAP = new HashMap<>();

    static {
        FUNCTION_MAP.put(BusinessConstants.SELECT_LIST_TO_STR, FormatConvertUtil::convertJsonArrayToString);
        FUNCTION_MAP.put(BusinessConstants.SELECT_LIST_TO_JSON, FormatConvertUtil::convertStringToJsonArray);
    }

    public static String convertSelectList(String selectList, String convertType) {
        Function<String, String> converter = FUNCTION_MAP.get(convertType);
        if (converter != null) {
            return converter.apply(selectList);
        } else {
            throw new BusinessException("不支持selectList的转换类型，convertType: " + convertType);
        }
    }

    private static String convertJsonArrayToString(String selectList) {
        if (StringUtils.isEmpty(selectList) || !isSpecifyJson(selectList)) {
            return selectList; // 直接返回原始值
        }

        List<JSONObject> jsonObjects = JSONArray.parseArray(selectList, JSONObject.class);

        return jsonObjects.stream()
                .filter(jsonObject -> !Objects.equals(jsonObject.getInteger("isDeleted"), 1)) // 过滤 isDeleted 为 1 的项
                .map(jsonObject -> jsonObject.getString("name").trim()) // 提取 name 并去掉前后空格
                .filter(StringUtils::isNotEmpty) // 过滤空的 name
                .map(name -> name.replace("|", "/")) // 将 name 中的 | 替换为 /
                .collect(Collectors.joining("|")); // 使用 | 连接成字符串
    }


    private static String convertStringToJsonArray(String selectList) {
        if (StringUtils.isEmpty(selectList) || isSpecifyJson(selectList)) {
            return selectList; // 直接返回原始值
        }

        return Arrays.stream(selectList.split("\\|"))
                .map(name -> new JSONObject().fluentPut("name", name).fluentPut("isDeleted", 0))
                .collect(Collectors.toCollection(JSONArray::new))
                .toJSONString();
    }


    public static boolean isSpecifyJson(String input) {
        if (input == null || input.trim().isEmpty()) {
            return false; // 处理空字符串
        }

        // 预检查，判断可能是JSON对象的数组格式
        String inputNoSpace = input.replaceAll("\\s+", ""); // 去除所有空格
        if (inputNoSpace.startsWith("[{") && inputNoSpace.endsWith("}]")) {
            try {
                // 尝试解析为 JSON 数组
                JSONArray.parseArray(input);
                return true; // 成功解析，返回 true
            } catch (JSONException e) {
                return false; // 解析失败，返回 false
            }
        }

        return false; // 不符合 JSON 对象数组的格式
    }

    /**
     * 将分级编码转换为标准格式，如从 1.1.11.2 转换为 001001011002
     *
     * @param code 输入的分级编码
     * @return 转换后的标准格式编码
     * @throws BusinessException 如果输入格式错误
     */
    public static String convertToFormattedCode(String code) throws Exception {
        // 校验输入格式是否符合要求
        if (!isValidCodeFormat(code)) {
            throw new BusinessException("编码格式错误，必须是类似于 '1.1.1' 的格式，每个段不能超过三位，并且最多支持8级。");
        }

        String[] segments = code.split("\\.");
        StringBuilder formattedCode = new StringBuilder();

        for (String segment : segments) {
            try {
                formattedCode.append(formatSegment(Integer.parseInt(segment)));
            } catch (NumberFormatException e) {
                throw new BusinessException("编码格式错误，必须是类似于 '1.1.1' 的格式。");
            }
        }
        return formattedCode.toString();
    }

    /**
     * 校验编码格式
     *
     * @param code 输入的编码
     * @return 是否有效
     */
    private static boolean isValidCodeFormat(String code) {
        // 正则表达式：匹配一个或多个数字，后跟零个或多个（.数字），每个数字段最多三位
        String regex = "^(\\d{1,3})(\\.\\d{1,3})*$";
        String[] segments = code.split("\\.");

        return Pattern.matches(regex, code) && segments.length <= 8;
    }

    /**
     * 将段编码格式化为三位字符
     *
     * @param segment 输入的段编码
     * @return 格式化后的三位编码
     */
    public static String formatSegment(int segment) {
        return String.format("%03d", segment);
    }

    /**
     * 校验输入字符串
     *
     * @param input 输入的字符串
     * @return 用 | 分隔的去重后的字符串
     * @throws BusinessException 如果校验不通过
     */
    public static String validateAndDeduplicate(String input, String type) throws BusinessException {
        // 去除前后空格并校验总长度
        String trimmedInput = input.trim();
        int maxOptionLength = OperateConstants.BUILDING.equals(type) ? Constants.STANDARD_MAX_OPTION_LENGTH : Constants.MAX_OPTION_LENGTH;
        if (trimmedInput.length() > maxOptionLength) {
            throw new BusinessException(ResponseCode.EXCEL_OPTION_LENGTH_ERROR);
        }

        // 用 | 分割字符串并校验项的数量
        String[] items = trimmedInput.split("[|丨]");
        if (items.length > Constants.MAX_OPTION_NUM) {
            throw new BusinessException(ResponseCode.EXCEL_OPTION_NUM_ERROR);
        }

        // 用 LinkedHashSet 去重并保持插入顺序
        Set<String> uniqueItems = new LinkedHashSet<>();
        for (String item : items) {
            String trimmedItem = item.trim();
            if (!trimmedItem.isEmpty()) { // 只添加非空字符串
                uniqueItems.add(trimmedItem);
            }
        }

        // 将去重后的项组合成用 | 分隔的字符串并返回
        return String.join("|", uniqueItems);
    }

}
