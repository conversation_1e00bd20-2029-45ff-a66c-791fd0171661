package com.glodon.qydata.util;

import com.glodon.qydata.entity.system.TbArea;
import com.glodon.qydata.vo.amap.AMapDistrict;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AreaUtil {
    public static final Map<String, String> NAME_COVER_MAP = new HashMap<>();
    public static final Map<String, String> SKIP_NODE = new HashMap<>();
    public static final Map<String, AMapDistrict> VIRTUAL_MAP = new HashMap<>();

    static {
        NAME_COVER_MAP.put("710000", "台湾省");
        NAME_COVER_MAP.put("110100", "北京城区"); //db:市辖区:北京城区
        NAME_COVER_MAP.put("500100", "重庆城区");
        NAME_COVER_MAP.put("120100", "天津城区");
        NAME_COVER_MAP.put("310100", "上海城区");

        // 可接受的差异
        SKIP_NODE.put("中国", "");
        SKIP_NODE.put("其他地区", "");

        VIRTUAL_MAP.put("香港特别行政区", new AMapDistrict("810000", "香港特别行政区", "city"));
        VIRTUAL_MAP.put("澳门特别行政区", new AMapDistrict("820000", "澳门特别行政区", "city"));
        VIRTUAL_MAP.put("台湾省", new AMapDistrict("710000", "台湾省", "city"));
    }

    public static void setNamePath(List<TbArea> dbAreaList) {
        // 创建一个HashMap来存储areaId和TbArea对象的映射关系
        Map<String, TbArea> areaMap = new HashMap<>();
        for (TbArea area : dbAreaList) {
            areaMap.put(area.getAreaid(), area);
        }

        // 给每个区域设置namePath
        for (TbArea area : dbAreaList) {
            area.setNamePath(buildNamePath(area, areaMap));
        }
    }

    public static String buildNamePath(TbArea area, Map<String, TbArea> areaMap) {
        // 递归构建namePath
        if (area.getPid() == null || "".equals(area.getPid()) || "1".equals(area.getPid())) {
            return area.getName();
        } else {
            TbArea parentArea = areaMap.get(area.getPid());
            if (parentArea != null) {
                return buildNamePath(parentArea, areaMap) + "-" + area.getName();
            } else {
                return area.getName(); // 如果找不到父级区域，则只返回自身名称
            }
        }
    }

    /**
     * 校验行政区划代码是否为6位数字
     *
     * @param code 输入的行政区划代码
     * @return 如果代码格式正确，返回true；否则返回false
     */
    public static boolean isValidRegionCode(String code) {
        if (code == null) {
            return false;
        }
        // 检查是否为6位数字
        return code.matches("\\d{6}");
    }

    public static String adjustNamePathForSpecialRegions(String namePath) {
        if (namePath.startsWith("北京市-北京城区")) {
            namePath = namePath.replaceFirst("北京市-北京城区", "北京市-市辖区");
        } else if (namePath.startsWith("重庆市-重庆城区")) {
            namePath = namePath.replaceFirst("重庆市-重庆城区", "重庆市-市辖区");
        } else if (namePath.startsWith("天津市-天津城区")) {
            namePath = namePath.replaceFirst("天津市-天津城区", "天津市-市辖区");
        } else if (namePath.startsWith("上海市-上海城区")) {
            namePath = namePath.replaceFirst("上海市-上海城区", "上海市-市辖区");
        } else if (namePath.startsWith("台湾省")) {
            namePath = namePath.replaceFirst("台湾省", "台湾");
        } else if (namePath.startsWith("澳门特别行政区-")) {
            namePath = namePath.replaceFirst("澳门特别行政区-", "澳门特别行政区-澳门-");
        } else if (namePath.startsWith("香港特别行政区-")) {
            namePath = namePath.replaceFirst("香港特别行政区-", "香港特别行政区-香港-");
        }
        return namePath;
    }
}
