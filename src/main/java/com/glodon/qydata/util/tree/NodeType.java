package com.glodon.qydata.util.tree;

/**
 * 节点类型枚举值
 * <AUTHOR>
 *
 */
public enum NodeType {
	/**
	 * 1-根节点
	 */
	ROOT(1, "根节点"),
	/**
	 * 2-主干节点
	 */
	TRUNK(2, "主干节点"),
	/**
	 * 3-分支节点
	 */
	BRANCH(3, "分支节点"),
	/**
	 * 4-叶节点
	 */
	LEAF(4, "叶节点");
	/**
	 * ID，唯一标示
	 */
	private final Integer id;
	/**
	 * 名称（描述）
	 */
	private final String desc;
	/**
	 * 构造函数
	 * @param desc 名称（描述）
	 * @param id ID，唯一标示
	 */
	NodeType(Integer id, String desc){
		this.id = id;
		this.desc = desc;
	}	
	/**
	 * 节点类型值，整型值
	 * @return 节点类型值
	 */
	public Integer getId(){
		return this.id;
	}
	/**
	 * 节点类型名称，字符串
	 * @return 节点类型名称
	 */
	public String getDesc(){
		return this.desc;
	}	
	
	/**
	 * 格式化编号，输出如：01
	 * @return 格式化后的编号
	 */
	public String formatId(){
		return formatId(3);
	}
	/**
	 * 格式化编号，输出如：01
	 * @param scale 格式化字符串的长度
	 * @return 格式化后的编号
	 */
	public String formatId(Integer scale){
		return String.format("%0" + scale + "d", id);
	}
	/**
	 * 将整型对象转换节点类型枚举对象
	 * @param id 节点类型的整形值
	 * @return 节点类型枚举值
	 */
	public static NodeType getEnum(Integer id){
		for (final NodeType item : NodeType.values()) {
			if (0 == item.getId().compareTo(id)) {
                return item;
            }
		}
		return null;
	}
	/**
	 * 将节点类型的名称转换成节点类型枚举对象
	 * @param desc 模板列类型的名称
	 * @return 节点类型枚举值
	 */
	public static NodeType getEnum(String desc){
		for (final NodeType item : NodeType.values()) {
			if (item.getDesc().equals(desc)) {
                return item;
            }
		}
		return null;
	}
}
