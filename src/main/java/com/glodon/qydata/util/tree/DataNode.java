package com.glodon.qydata.util.tree;

/**
 * 数据节点接口
 * <AUTHOR>
 *
 */
public interface DataNode {
	/**
	 * 获取层次码
	 * @return 层次码
	 */
	public String getLevelcode();
	/**
	 * 设置层次码
	 * @param levelcode 层次码
	 */
	public void setLevelcode(String levelcode);
	/**
	 * 获取序号
	 * @return 序号
	 */
	public String getSerialNo();
	/**
	 * 设置序号
	 * @param serialNo 序号
	 */
	public void setSerialNo(String serialNo);
	/**
	 * 获取节点类型
	 * @return 节点类型
	 */
	public NodeType getNodeType();
	/**
	 * 设置节点类型
	 * @param nodeType 节点类型
	 */
	public void setNodeType(NodeType nodeType);
	/**
	 * 获取是否为叶子节点
	 * @return 是否为叶子节点
	 */
	public Boolean getIsLeaf();
	/**
	 * 设置是否为叶子节点
	 * @param isLeaf 是否为叶子节点
	 */
	public void setIsLeaf(Boolean isLeaf);
	Integer getOrd();
	void setOrd(Integer ord);

	int getLevel();

}
