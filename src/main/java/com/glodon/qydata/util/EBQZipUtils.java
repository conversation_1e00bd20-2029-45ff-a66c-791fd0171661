package com.glodon.qydata.util;

import org.apache.commons.codec.binary.Base64;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.zip.DataFormatException;
import java.util.zip.Deflater;
import java.util.zip.Inflater;

public class EBQZipUtils {

    public static String inflate(String data) {
        byte[] encodedData = Base64.decodeBase64(data);

        Inflater inflater = new Inflater();
        inflater.reset();
        inflater.setInput(encodedData);
        byte[] decodedData = new byte[0];
        ByteArrayOutputStream outStream = new ByteArrayOutputStream(encodedData.length);
        try {
            byte[] decodedBuf = new byte[4096];
            while (!inflater.finished()) {
                int infLen = inflater.inflate(decodedBuf);
                outStream.write(decodedBuf, 0, infLen);
            }
            decodedData = outStream.toByteArray();
        } catch (DataFormatException e) {
            e.printStackTrace();
        } finally {
            try {
                outStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        inflater.end();

        return new String(decodedData, StandardCharsets.UTF_8);
    }

    public static String deflate(String data) {
        Deflater deflater = new Deflater();
        deflater.reset();
        deflater.setInput(data.getBytes());
        deflater.finish();
        byte[] encodeData = new byte[0];
        ByteArrayOutputStream outStream = new ByteArrayOutputStream(data.getBytes().length);
        try {
            byte[] buf = new byte[4096];
            while (!deflater.finished()) {
                int zipLength = deflater.deflate(buf);
                outStream.write(buf, 0, zipLength);
            }
            encodeData = outStream.toByteArray();

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                outStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        deflater.end();

        return Base64.encodeBase64String(encodeData);
    }
}

