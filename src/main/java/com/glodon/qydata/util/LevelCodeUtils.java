package com.glodon.qydata.util;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @className: LevelUtils
 * @author: zhaoyj-g
 * @date: 2020/11/30
 **/
public class LevelCodeUtils {

    private static Pattern pattern = Pattern.compile("\\d{3}");
    public static final String SEPERATE = ".";
    public static String convertToPointLevelCode(String levelCode) {
        if (StringUtils.isBlank(levelCode)) {
            return levelCode;
        }
        Matcher matcher = pattern.matcher(levelCode);
        StringBuilder sb = new StringBuilder();
        while (matcher.find()) {
            sb.append(matcher.group().replaceAll("^0{0,2}","")).append(SEPERATE);
        }
        if (sb.length() > 0) {
            sb.delete(sb.length() - 1,sb.length());
        }
        return sb.toString();
    }

    public static void main(String[] args) {
        String lv = "001010001";
        lv = convertToPointLevelCode(lv);
        System.out.println(lv);
        lv = restoreLevelCode(lv);
        System.out.println(lv);
    }

    /**
     * @Title: restoreLevelCode
     * @Description: 还原科目编码（1.1.1 TO 001001001）
     * @throws
     * @param:
     * @param levelCode 点类型的科目编码
     * @return: java.lang.String
     * @auther: zhaohy-c
     * @date: 2021/6/1 11:14
     */
    public static String restoreLevelCode(String levelCode) {
        if (StringUtils.isBlank(levelCode)) {
            return levelCode;
        }
        String[] codes = levelCode.split("\\"+SEPERATE);
        for (int i = 0; i < codes.length; i++) {
            String code = codes[i];
            int l = code.length();
            if (l == 1) {
                codes[i] = "00" + code;
            } else if (l == 2) {
                codes[i] = "0" + code;
            }
        }
        return String.join("", codes);
    }

}
