package com.glodon.qydata.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 标准打通--主要量指标
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ZbStandardsMainQuantityDto implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 专业id
     */
    private Long tradeId;

    /***
     * 专业名称
     */
    private String tradeName;

    /**
     * 专业编码
     */
    private String tradeCode;

    /**
     * 专业引用编码
     */
    private String referTradeCode;

    /**
     * 主要量指标名称
     */
    private String description;

    /**
     * 主要量指标单位
     */
    private String unit;

    /**
     * 主要量指标备注
     */
    private String remark;

    /**
     * 主要量指标类型
     */
    private Byte type;

    /**
     * 主要量指标企业编码
     */
    private String customerCode;

    /**
     * 主要量指标是否删除标志
     */
    private Integer isDeleted;

    /**
     * 主要量指标排序
     */
    private Integer ord;

    /**
     * 主要量指标创建人id
     */
    private Long createGlobalId;

    /**
     * 主要量指标创建时间
     */
    private Date createTime;

    /**
     * 主要量指标修改人id
     */
    private Long updateGlobalId;

    /**
     * 主要量指标修改时间
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}