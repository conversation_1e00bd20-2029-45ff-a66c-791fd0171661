package com.glodon.qydata.dto;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @ProjectName gcj_zb_company_site
 * @className ZbProjectStandardDetailColDto
 * @description 建造标准行数据
 * @date 2021/8/17
 **/
public class ZbProjectStandardDetailColDto implements Serializable {
    private static final long serialVersionUID = -632344920146493586L;
    /**
     * 标准行id
     */
    private Long id;
    /**
     * 标准行数据名
     */
    private String name;
    /**
     * 描述示例
     */
    private String desc;
    /**
     * 行数据层级
     */
    private Integer level;
    /**
     * 备注
     */
    private String remark;
    /**
     * 节点类型：1:代表兄弟节点，2：代表子节点
     */
    private Integer type;
    /**
     * 参照行ID，即用户点击插入或插入子项时选中的行id
     */
    private Long refId;
    /**
     * 专业工程id
     */
    private Long tradeId;

    public Long getTradeId() {
        return tradeId;
    }

    public void setTradeId(Long tradeId) {
        this.tradeId = tradeId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getRefId() {
        return refId;
    }

    public void setRefId(Long refId) {
        this.refId = refId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
