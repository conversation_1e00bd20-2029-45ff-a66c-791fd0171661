package com.glodon.qydata.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
　　* @description: 建造标准返回业态字典信息
　　* <AUTHOR>
　　* @date 2021/8/18 9:26
　　*/
@Data
public class ZbCategoryDicDto implements Serializable {
    private static final long serialVersionUID = -632344920146493586L;
    /**
     * 业态名
     */
    private String name;
    /**
     * 业态编码
     */
    private String code;
    /**
     * 业态一级编码
     */
    private String firstCode;
    /**
     * 业态层级
     */
    private Integer level;
    /**
     * 此业态是否已经被创建过标准
     */
    private Boolean isUsed;
    /**
     * 排序字段
     */
    private Integer ord;
    /**
     * 此业态是否已经被创建过标准
     */
    private List<ZbCategoryDicDto> children;
    private Boolean isDeleted;
    /**
     * 是否启用（1，启用；0，禁用）
     */
    private Integer isUsing;
}
