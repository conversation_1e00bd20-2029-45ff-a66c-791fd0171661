package com.glodon.qydata.dto;

import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildPositionDetail;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 */
@Data
public class StandardsBuildStandardDetailDescDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 标准名称
     */
    private String name;

    /**
     * 标准说明
     */
    private String detailDesc;

    /**
     * 编码
     */
    private String code;

    /**
     * 数据类型： text文本类，number数值类，date日期类，select单选类，selects多选类
     */
    private String typeCode;

    /**
     * 枚举值
     */
    private String selectList;

    /**
     * 排序
     */
    private Integer ord;

    /**
     * 各定位的值
     */
    private List<ZbStandardsBuildPositionDetail> values;

}
