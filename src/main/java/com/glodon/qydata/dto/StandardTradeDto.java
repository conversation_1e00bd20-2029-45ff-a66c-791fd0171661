package com.glodon.qydata.dto;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
　　* @description: 标准统一--新增专业数据载体
　　* <AUTHOR>
　　* @date 2021/10/21 17:13
　　*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StandardTradeDto {

    /**
     * 专业id，更新时候需要判断名称
     */
    private Long id;

    /**
     * 专业名称
     */
    @NotEmpty(message = "专业名称不能为空")
    @Size(min=1,max=30)
    private String description;

    /**
     * 专业编码，新增为被引用专业编码  更新为本专业编码
     */
    @NotEmpty(message = "专业编码不能为空")
    private String tradeCode;

}
