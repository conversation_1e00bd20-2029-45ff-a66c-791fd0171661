package com.glodon.qydata.dto;

/**
 * <AUTHOR>
 * @Date 2017/12/20 19:25
 * @Description
 */
public class PageForm extends BaseVO {

    private Integer pageNum = 1;

    private Integer pageSize = 20;

    private int totalCount = 0;


    public PageForm() {
    }

    public PageForm(int pageNum, int pageSize) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
    }


    public PageForm(int pageNum, int pageSize, int totalCount) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.totalCount = totalCount;
    }

    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public int getTotalPage() {
        return this.getTotalCount() % this.pageSize > 0 ? this.getTotalCount() / this.pageSize + 1 : this.getTotalCount() / this.pageSize;
    }

    public int getTotalCount() {
        return totalCount;
    }
}
