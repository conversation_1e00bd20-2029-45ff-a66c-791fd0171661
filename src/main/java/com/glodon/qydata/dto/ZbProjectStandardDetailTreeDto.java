package com.glodon.qydata.dto;

import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandardDetail;
import com.glodon.qydata.util.tree.DataNode;
import com.glodon.qydata.util.tree.NodeType;
import com.glodon.qydata.util.tree.TreeDto;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @ProjectName gcj_zb_company_site
 * @className ZbProjectStandardDetailTreeDto
 * @description 建造标准细则树形结构数据
 * @date 2021/8/18
 **/
@SuppressWarnings("squid:S1186") // 禁止sonar空方法检测
public class ZbProjectStandardDetailTreeDto implements Serializable, DataNode, TreeDto<ZbProjectStandardDetailTreeDto> {
    private static final long serialVersionUID = 6899604577645295166L;

    private Long id;

    private String name;

    private String desc;

    private Integer level;

    private String remark;

    private Integer ord;

    private String levelcode;

    private Long tradeId;

    private String tradeName;


    /**
     * 项目划分科目id
     */
    private String itemDivisionSubjectId;

    /**
     * 项目划分科目名称
     */
    private String itemDivisionSubjectName;

    /** 标准说明 */
    private List<StandardsBuildStandardDetailDescDto> description;
    /**
     * 子节点数据
     */
    private List<ZbProjectStandardDetailTreeDto> children;

    public ZbProjectStandardDetailTreeDto(ZbProjectStandardDetail detail) {
        this.id = detail.getId();
        this.name = detail.getName();
        this.level = detail.getLevel();
        this.remark = detail.getRemark();
        this.ord = detail.getOrd();
        this.levelcode = detail.getLevelcode();
        this.tradeId = detail.getTradeId();
        this.tradeName = detail.getTradeName();
        this.itemDivisionSubjectId = detail.getItemDivisionSubjectId();
        this.itemDivisionSubjectName = detail.getItemDivisionSubjectName();
    }

    public ZbProjectStandardDetailTreeDto() {
    }

    @Override
    public String getLevelcode() {
        return this.levelcode;
    }

    @Override
    public void setLevelcode(String levelcode) {
        this.levelcode = levelcode;
    }

    @Override
    public String getSerialNo() {
        return null;
    }

    @Override
    public void setSerialNo(String serialNo) {

    }

    @Override
    public NodeType getNodeType() {
        return null;
    }

    @Override
    public void setNodeType(NodeType nodeType) {

    }

    @Override
    public Boolean getIsLeaf() {
        return false;
    }

    @Override
    public void setIsLeaf(Boolean isLeaf) {

    }

    @Override
    public Integer getOrd() {
        return this.ord;
    }

    @Override
    public void setOrd(Integer ord) {
        this.ord = ord;
    }

    @Override
    public List<ZbProjectStandardDetailTreeDto> getChildren() {
        return children;
    }

    @Override
    public void setChildren(List<ZbProjectStandardDetailTreeDto> children) {
        this.children = children;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Long getTradeId() {
        return tradeId;
    }

    public void setTradeId(Long tradeId) {
        this.tradeId = tradeId;
    }

    @Override
    public int getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getTradeName() {
        return tradeName;
    }

    public void setTradeName(String tradeName) {
        this.tradeName = tradeName;
    }

    public String getItemDivisionSubjectId() {
        return itemDivisionSubjectId;
    }

    public void setItemDivisionSubjectId(String itemDivisionSubjectId) {
        this.itemDivisionSubjectId = itemDivisionSubjectId;
    }

    public String getItemDivisionSubjectName() {
        return itemDivisionSubjectName;
    }

    public void setItemDivisionSubjectName(String itemDivisionSubjectName) {
        this.itemDivisionSubjectName = itemDivisionSubjectName;
    }

    public List<StandardsBuildStandardDetailDescDto> getDescription() {
        return description;
    }

    public void setDescription(List<StandardsBuildStandardDetailDescDto> description) {
        this.description = description;
    }
}
