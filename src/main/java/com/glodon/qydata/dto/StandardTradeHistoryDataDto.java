package com.glodon.qydata.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
　　* @description: 标准统一--新增专业数据载体
　　* <AUTHOR>
　　* @date 2021/10/21 17:13
　　*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StandardTradeHistoryDataDto {

    /**
     * 专业名称
     */
    private String description;

    /**
     * 专业编码，新增为被引用专业编码  更新为本专业编码
     */
    private String tradeCode;

    /**
     *专业类型  0 内置专业  1 新增专业
     */
    private Byte type;

    /**
     * 创建人
     */
    private Long creatorId;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 删除状态
     */
    private Integer isDeleted;

    /**
     * 排序
     */
    private Integer ord;

    /**
     * 引用专业编码
     */
    private String referTrade;

}
