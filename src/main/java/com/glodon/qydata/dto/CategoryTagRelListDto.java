package com.glodon.qydata.dto;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @author: luoml-b
 * @date: 2024/10/22 15:28
 * @description:
 */
@Data
public class CategoryTagRelListDto implements Serializable {
    private static final long serialVersionUID = 5403588774234727960L;

    private List<CategoryTagColumDTO> tableHead;

    private List<Map<String, String>> tableData;

}
