package com.glodon.qydata.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 专业工程启用DTO
 * @date 2023-04-03 15:27
 * @email <EMAIL>
 */
@Schema(description = "专业工程启用DTO")
@Data
public class TradeEnableDTO implements Serializable {

    @Schema(description = "专业id")
    private Long id;

    @Schema(description = "启用状态,默认启用")
    private Integer enabled = 1;

    @Schema(description = "专业标签id")
    private Long tradeTagId;
}
