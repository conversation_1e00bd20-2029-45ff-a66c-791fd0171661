package com.glodon.qydata.config;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.serializer.SerializeConfig;
import com.alibaba.fastjson.serializer.ToStringSerializer;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.glodon.qydata.interceptor.LoginAuthInterceptor;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.*;


/**
 * 系统配置
 */
// 提升mvc配置的优先级
//@EnableWebMvc
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Bean
    LoginAuthInterceptor loginAuthInterceptor(){
        return new LoginAuthInterceptor();
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(new CaseInsensitiveMapMethodArgumentResolver());
    }

    /**
     * 用户请求拦截
     *
     * @param registry
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(loginAuthInterceptor()).addPathPatterns("/**")
                .excludePathPatterns("/","/static/**","/login*","/logout*","/error*","/init/**","/basicInfo/openApi/**"
                        ,"/basicInfo/standards/zbCategory/other/dealHistoryData"
                        ,"/basicInfo/standards/zbFeature/importTz"
                        ,"/basicInfo/init/**"
                        ,"/basicInfo/dataDeal/**"
                        ,"/basicInfo/standards/buildStandard/historyDetailToDesc"
                        ,"/swagger-ui/**", "/v3/api-docs/**", "/swagger-ui.html", "/ebq-actuator/**");
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/static/**").addResourceLocations("classpath:/static/");
    }

    /**
     * Long类型参数传到前端精度丢失的解决方案
     */
    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
        FastJsonHttpMessageConverter fastJsonConverter = new FastJsonHttpMessageConverter();
        FastJsonConfig fjc = new FastJsonConfig();
        SerializeConfig serializeConfig = SerializeConfig.globalInstance;
        serializeConfig.put(Long.class , ToStringSerializer.instance);
        serializeConfig.put(Long.TYPE , ToStringSerializer.instance);
        fjc.setSerializeConfig(serializeConfig);
        fastJsonConverter.setFastJsonConfig(fjc);

        // 设置支持的 MediaType，不要用 application/*+json 或 */*
        List<MediaType> supportedMediaTypes = new ArrayList<>();
        supportedMediaTypes.add(MediaType.APPLICATION_JSON);
        supportedMediaTypes.add(MediaType.TEXT_PLAIN);
        fastJsonConverter.setSupportedMediaTypes(supportedMediaTypes);
        // 提高优先级
        converters.add(0, fastJsonConverter);
    }

    public static class CaseInsensitiveMapMethodArgumentResolver implements HandlerMethodArgumentResolver {

        @Override
        public boolean supportsParameter(MethodParameter parameter) {
            return parameter.hasParameterAnnotation(RequestParam.class) && Map.class.isAssignableFrom(parameter.getParameterType());
        }

        @SuppressWarnings("NullableProblems")
        @Override
        public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer,
                                      NativeWebRequest webRequest, WebDataBinderFactory binderFactory) {
            Map<String, String> caseInsensitiveMap = new HashMap<>();
            HttpServletRequest request = webRequest.getNativeRequest(HttpServletRequest.class);
            if (request != null) {
                request.getParameterMap().forEach((key, values) -> {
                    caseInsensitiveMap.put(key.toLowerCase(), CollUtil.getFirst(Arrays.asList(values)));
                });
            }
            return caseInsensitiveMap;
        }
    }


}
