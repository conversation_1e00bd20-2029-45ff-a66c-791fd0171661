/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.glodon.qydata.config;

import com.alibaba.druid.pool.DruidDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.shardingsphere.driver.api.ShardingSphereDataSourceFactory;
import org.apache.shardingsphere.infra.config.algorithm.AlgorithmConfiguration;
import org.apache.shardingsphere.sharding.api.config.ShardingRuleConfiguration;
import org.apache.shardingsphere.sharding.api.config.rule.ShardingTableRuleConfiguration;
import org.apache.shardingsphere.sharding.api.config.strategy.keygen.KeyGenerateStrategyConfiguration;
import org.apache.shardingsphere.sharding.api.config.strategy.sharding.ComplexShardingStrategyConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.*;

/**
 * ShardingSphere 5.4.1 动态数据源配置类
 * 当privateDeployment=false时启用ShardingSphere分片数据源
 * 使用Java API配置 + SnakeYAML 2.0 解决兼容性问题
 *
 * <AUTHOR> Agent
 * @since 2025-06-18
 */
@Configuration
@ConditionalOnProperty(name = "privateDeployment", havingValue = "false", matchIfMissing = true)
@Slf4j
public class BasicShardingSphereAutoConfiguration {

    @Autowired
    private Environment environment;

    /**
     * 创建真正的ShardingSphere分片数据源
     * 使用Java API手动创建，确保MyBatis使用分片数据源实现动态路由
     */
    @Bean(name = "shardingDataSource")
    public DataSource shardingDataSource() throws SQLException {
        log.info("=== 动态数据源配置 ===");
        log.info("数据源模式: ShardingSphere分片数据源");
        log.info("配置方式: Java API手动创建");
        try {
            // 1. 创建数据源映射
            Map<String, DataSource> dataSourceMap = createDataSourceMap();
            // 2. 创建分片规则配置
            ShardingRuleConfiguration shardingRuleConfig = createShardingRuleConfiguration();
            // 3. 创建属性配置
            Properties props = createShardingSphereProperties();
            // 4. 使用ShardingSphereDataSourceFactory创建真正的分片数据源
            // 使用兼容性更好的API，避免SnakeYAML版本冲突
            DataSource shardingSphereDataSource = ShardingSphereDataSourceFactory.createDataSource(
                    "sharding_db",
                    dataSourceMap,
                    Collections.singletonList(shardingRuleConfig),
                    props);
            log.info("✅ ShardingSphere分片数据源创建成功");
            // 执行数据源健康检查
            performDataSourceHealthCheck(shardingSphereDataSource);
            return shardingSphereDataSource;
        } catch (Exception e) {
            log.error("❌ 创建ShardingSphere分片数据源失败", e);
            throw new SQLException("创建ShardingSphere分片数据源失败", e);
        }
    }

    /**
     * 创建数据源映射
     */
    private Map<String, DataSource> createDataSourceMap() {
        log.info("开始创建数据源映射...");
        Map<String, DataSource> dataSourceMap = new HashMap<>();
        // 从配置文件读取数据源参数
        String url = environment.getProperty("spring.shardingsphere.datasource.ds1.url");
        String username = environment.getProperty("spring.shardingsphere.datasource.ds1.username");
        String password = environment.getProperty("spring.shardingsphere.datasource.ds1.password");
        String driverClassName = environment.getProperty("spring.shardingsphere.datasource.ds1.driverClassName", "com.mysql.cj.jdbc.Driver");
        if (url != null && username != null && password != null) {
            // 创建Druid数据源
            DruidDataSource dataSource = new DruidDataSource();
            dataSource.setUrl(url);
            dataSource.setUsername(username);
            dataSource.setPassword(password);
            dataSource.setDriverClassName(driverClassName);
            // 设置连接池参数
            dataSource.setInitialSize(5);
            dataSource.setMinIdle(5);
            dataSource.setMaxActive(20);
            dataSource.setMaxWait(60000);
            dataSource.setTimeBetweenEvictionRunsMillis(60000);
            dataSource.setMinEvictableIdleTimeMillis(300000);
            dataSource.setValidationQuery("SELECT 1");
            dataSource.setTestWhileIdle(true);
            dataSource.setTestOnBorrow(false);
            dataSource.setTestOnReturn(false);
            dataSourceMap.put("ds1", dataSource);
            log.info("✅ 数据源ds1创建成功: {}", url);
        } else {
            log.error("❌ 数据源配置不完整，请检查以下配置项:");
            if (url == null) log.error("  - spring.shardingsphere.datasource.ds1.url");
            if (username == null) log.error("  - spring.shardingsphere.datasource.ds1.username");
            if (password == null) log.error("  - spring.shardingsphere.datasource.ds1.password");
            throw new RuntimeException("数据源配置不完整");
        }
        log.info("数据源映射创建完成，共{}个数据源", dataSourceMap.size());
        return dataSourceMap;
    }

    /**
     * 创建分片规则配置
     */
    private ShardingRuleConfiguration createShardingRuleConfiguration() {
        log.info("开始创建分片规则配置...");
        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();
        // 创建分片表规则
        Collection<ShardingTableRuleConfiguration> tableRuleConfigs = new ArrayList<>();
        // 配置项目特征分类视图标准表
        tableRuleConfigs.add(createTableRuleConfiguration("zb_project_feature_category_view_standards",
                "ds1.zb_project_feature_category_view_standards,ds1.zb_project_feature_category_view_standards_0", "customer_code"));
        // 配置通用项目分类标准表
        tableRuleConfigs.add(createTableRuleConfiguration("tb_commonprojcategory_standards",
                "ds1.tb_commonprojcategory_standards,ds1.tb_commonprojcategory_standards_0", "qy_code"));
        shardingRuleConfig.setTables(tableRuleConfigs);
        // 配置分片算法
        Map<String, AlgorithmConfiguration> shardingAlgorithms = new HashMap<>();
        Properties algorithmProps = new Properties();
        algorithmProps.setProperty("strategy", "COMPLEX");
        algorithmProps.setProperty("algorithmClassName", "com.glodon.qydata.config.CustomShardingAlgorithm");
        shardingAlgorithms.put("customShardingAlgorithm", new AlgorithmConfiguration("CLASS_BASED", algorithmProps));
        shardingRuleConfig.setShardingAlgorithms(shardingAlgorithms);
        // 配置主键生成算法
        Map<String, AlgorithmConfiguration> keyGenerators = new HashMap<>();
        keyGenerators.put("snowflake", new AlgorithmConfiguration("SNOWFLAKE", new Properties()));
        shardingRuleConfig.setKeyGenerators(keyGenerators);

        log.info("✅ 分片规则配置创建完成，共{}个分片表", tableRuleConfigs.size());
        return shardingRuleConfig;
    }

    /**
     * 创建单个表的分片规则配置
     */
    private ShardingTableRuleConfiguration createTableRuleConfiguration(String logicTable, String actualDataNodes, String shardingColumns) {
        ShardingTableRuleConfiguration tableRuleConfig = new ShardingTableRuleConfiguration(logicTable, actualDataNodes);

        // 设置分片策略
        tableRuleConfig.setTableShardingStrategy(new ComplexShardingStrategyConfiguration(shardingColumns, "customShardingAlgorithm"));

        // 设置主键生成策略
        tableRuleConfig.setKeyGenerateStrategy(new KeyGenerateStrategyConfiguration("id", "snowflake"));

        log.info("✅ 表{}分片规则配置完成", logicTable);
        return tableRuleConfig;
    }

    /**
     * 创建ShardingSphere属性配置
     */
    private Properties createShardingSphereProperties() {
        Properties props = new Properties();

        // 从配置文件读取SQL显示配置
        String sqlShow = environment.getProperty("spring.shardingsphere.props.sql.show", "false");
        props.setProperty("sql-show", sqlShow);

        log.info("✅ ShardingSphere属性配置完成，sql-show={}", sqlShow);
        return props;
    }

    /**
     * 验证ShardingSphere配置
     */
    private void validateShardingSphereConfiguration() {
        log.info("开始验证ShardingSphere配置...");

        // 检查ShardingSphere数据源配置
        String dsNames = environment.getProperty("spring.shardingsphere.datasource.names");
        if (dsNames != null) {
            log.info("✅ ShardingSphere数据源名称: {}", dsNames);
        } else {
            log.warn("⚠️ 未检测到ShardingSphere数据源配置");
        }

        // 检查数据源连接配置
        String url = environment.getProperty("spring.shardingsphere.datasource.ds1.url");
        String username = environment.getProperty("spring.shardingsphere.datasource.ds1.username");
        if (url != null && username != null) {
            log.info("✅ 数据源连接配置完整");
            log.info("数据源URL: {}", url);
            log.info("用户名: {}", username);
        } else {
            log.error("❌ 数据源连接配置不完整");
        }

        log.info("ShardingSphere配置验证完成");
    }

    /**
     * 执行数据源健康检查
     */
    private void performDataSourceHealthCheck(DataSource dataSource) {
        try {
            log.info("开始执行数据源健康检查...");
            try (Connection connection = dataSource.getConnection()) {
                if (connection != null && !connection.isClosed()) {
                    log.info("✅ 数据源连接正常");
                    log.info("数据库URL: {}", connection.getMetaData().getURL());
                    log.info("数据库产品: {}", connection.getMetaData().getDatabaseProductName());
                } else {
                    log.error("❌ 数据源连接异常");
                }
            }
        } catch (SQLException e) {
            log.error("❌ 数据源健康检查失败: {}", e.getMessage());
        }
    }
}
