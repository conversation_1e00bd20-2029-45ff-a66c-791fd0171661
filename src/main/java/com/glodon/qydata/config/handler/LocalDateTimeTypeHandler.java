package com.glodon.qydata.config.handler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.*;
import java.time.LocalDateTime;

/**
 * @className: LocalDateTimeTypeHandler
 * @description: LocalDateTimeTypeHandler
 * @author: zhaoyj-g
 * @date: 2020/7/15
 **/
public class LocalDateTimeTypeHandler extends BaseTypeHandler<LocalDateTime> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i,
                                    LocalDateTime parameter, JdbcType jdbcType) throws SQLException {
        ps.setTimestamp(i, Timestamp.valueOf(parameter));
    }

    @Override
    public LocalDateTime getNullableResult(ResultSet rs, String columnName)
            throws SQLException {
        try {
            Timestamp timestamp = rs.getTimestamp(columnName);
            return getLocalDateTime(timestamp);
        } catch (Exception e) {
            return null;
        }
    }
    private static LocalDateTime getLocalDateTime(Timestamp timestamp) {
        if (timestamp != null) {
            return timestamp.toLocalDateTime();
        }
        return null;
    }
    @Override
    public LocalDateTime getNullableResult(CallableStatement cs, int columnIndex)
            throws SQLException {
        try {
            Timestamp timestamp = cs.getTimestamp(columnIndex);
            return getLocalDateTime(timestamp);
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public LocalDateTime getNullableResult(ResultSet rs, int columnIndex)
            throws SQLException {
        return null;
    }
}
