package com.glodon.qydata.config;

import com.glodon.qydata.interceptor.OptionConvertInterceptor;
import com.glodon.qydata.interceptor.MybatisInterceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MybatisInterceptorConfig {

    /**
     * 解决Interceptor内bean无法注入
     */
    @Bean
    public MybatisInterceptor mybatisTempInterceptor(){
        return new MybatisInterceptor();
    }

  @Bean
    public OptionConvertInterceptor customInterceptor(){
        return new OptionConvertInterceptor();
    }

    @Bean
    public String addMybatisInterceptor(SqlSessionFactory sqlSessionFactory) {
        sqlSessionFactory.getConfiguration().addInterceptor(mybatisTempInterceptor());
        sqlSessionFactory.getConfiguration().addInterceptor(customInterceptor());
        return "interceptor";
    }
}

