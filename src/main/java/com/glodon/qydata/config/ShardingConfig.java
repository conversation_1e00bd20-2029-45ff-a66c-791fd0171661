package com.glodon.qydata.config;

import com.glodon.qydata.common.NotPrivateDeploymentCondition;
import com.glodon.qydata.common.constant.ShardingConstants;
import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.entity.sharding.ShardingRoute;
import com.glodon.qydata.mapper.sharding.ShardingRouteMapper;
import com.glodon.qydata.util.RedisUtil;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @author: luoml-b
 * @date: 2023/7/18 14:17
 * @description: 分片配置类
 */
@Component
@Slf4j
@Conditional(NotPrivateDeploymentCondition.class)
public class ShardingConfig {

    //本地缓存
    private final Cache<String, String> cache = CacheBuilder.newBuilder()
            .maximumSize(ShardingConstants.LOCAL_CACHE_MAX_SIZE) // 设置最大缓存大小
            .expireAfterWrite(ShardingConstants.EXPIRE_TIME, TimeUnit.SECONDS) // 设置读取后过期时间
            .build();
    @Autowired
    private ShardingRouteMapper shardingRouteMapper;

    @Value("#{${sharding_table.current_table_name}}")
    private Map<String, String> tableMaps;

    @Autowired
    private RedisUtil redisUtil;

    /**
     * @description: 获取表名
     * @author: luoml-b
     * @date: 2023/7/18 16:26
     * @param: customerCode
     * @param: tableType
     * @return: java.lang.String
     **/
    public String getTableName(String customerCode, int tableType) {
        //先从本地缓存查询
        if (cache.getIfPresent(customerCode + ShardingConstants.KEY_UNDERLINE + tableType) != null) {
            return cache.getIfPresent(customerCode + ShardingConstants.KEY_UNDERLINE + tableType);
        }
        //查不到去redis查询
        String cacheTableName = redisUtil.getString(RedisKeyEnum.SHARDING_CUSTOMER_CODE, customerCode, String.valueOf(tableType));
        if (StringUtils.isNotBlank(cacheTableName)) {
            //redis查询到之后将数据缓存到本地
            cache.put(customerCode + ShardingConstants.KEY_UNDERLINE + tableType, cacheTableName);
            return cacheTableName;
        }
        ShardingRoute tableNameByCustomerCode = shardingRouteMapper.getTableNameByCustomerCode(customerCode, tableType);
        String resultTableName;
        //路由表找不到则进行初始化
        if (Objects.isNull(tableNameByCustomerCode)) {
            String currentTableName = getCurrentTableName(tableType);
            ShardingRoute insertShardingRoute = ShardingRoute.builder()
                    .currentTableName(currentTableName)
                    .customerCode(customerCode)
                    .tableType(tableType)
                    .createTime(new Date())
                    .build();
            String key = customerCode + ShardingConstants.KEY_UNDERLINE + tableType;
            //使用分布式锁保证不会存在customerCode和tableType都相同的数据同时入路由表
            if (!redisUtil.tryLock(currentTableName, RedisKeyEnum.SHARDING_ROUTE_LOCK, key)) {
                //todo 当前拿不到锁的处理在高并发场景下可能存在不严谨情况，后续出现问题再考虑优化
                return currentTableName;
            }
            try {
                shardingRouteMapper.insert(insertShardingRoute);
            } finally {
                redisUtil.unlock(currentTableName, RedisKeyEnum.SHARDING_ROUTE_LOCK, key);
            }
            resultTableName = currentTableName;
        } else {
            resultTableName = tableNameByCustomerCode.getCurrentTableName();
            redisUtil.setString(RedisKeyEnum.SHARDING_CUSTOMER_CODE, resultTableName, customerCode, String.valueOf(tableType));
            //在redis缓存数据之后将数据缓存到本地
            cache.put(customerCode + ShardingConstants.KEY_UNDERLINE + tableType, resultTableName);
        }
        return resultTableName;
    }

    /**
     * @description: 通过配置文件获取最新分表表名
     * @author: luoml-b
     * @date: 2023/7/18 16:26
     * @param: tableType
     * @return: java.lang.String
     **/
    private String getCurrentTableName(int tableType) {
        return tableMaps.get(String.valueOf(tableType));
    }
}
