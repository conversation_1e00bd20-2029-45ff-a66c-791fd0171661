package com.glodon.qydata.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SpringDocConfig {

    @Bean
    public OpenAPI openAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("Qydata BasicInfo API")
                        .description("Glodon Qydata BasicInfo Project API Documentation")
                        .version("v1.0.0")
                        .license(new License().name("Glodon").url("https://www.glodon.com")));
    }
} 