package com.glodon.qydata.config.idsequence;

import com.glodon.cost.bdp.component.sequence.core.IntegerSequence;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * 分布式主键生成器
 *
 * <AUTHOR>
 * @date 2021/3/8
 */
@Component
//@ConditionalOnProperty(prefix = "spring.shardingsphere.datasource.ds1", name = "url", matchIfMissing = false)
public class IDGenerator implements ApplicationContextAware {
    private static IntegerSequence integerSequence;
    private static Integer defaultIntegerIdStartValue;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        integerSequence = applicationContext.getBean(IntegerSequence.class);
        defaultIntegerIdStartValue = applicationContext.getBean(SequenceConfig.class).getDefaultIntegerIdStartValue();
    }


    /**
     * 下一个整型id
     * （通过指定tableKey实现每张表使用自己的分布式id）
     *
     * @param tableKey 表关键字，当前项目中取实体类名
     * @return 整型id
     */
    public static Integer getNextIntId(String tableKey) {
        return integerSequence.nextVal(tableKey, defaultIntegerIdStartValue);
    }
}
