package com.glodon.qydata.config.idsequence;

import com.alibaba.druid.pool.DruidDataSource;
import com.glodon.cost.bdp.component.distributedlock.core.IDistributedLockFactory;
import com.glodon.cost.bdp.component.sequence.core.IntegerSequence;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * 分布式序列号配置类
 *
 * @author: xinzp
 * @date: 2022/3/11
 */
@Configuration
@Getter
public class SequenceConfig {
    @Value("${sequence.defaultCacheSize}")
    private int defaultCacheSize;

    @Value("${sequence.defaultIntegerIdStartValue}")
    private Integer defaultIntegerIdStartValue;

    @Bean
    public IDistributedLockFactory distributedLockFactory(@Qualifier("masterDataSource") DataSource dataSource) {
        return new DistributedLockMysqlAdaptorFactory(dataSource);
    }


    @Bean
    public IntegerSequence integerSequence(@Qualifier("masterDataSource") DataSource dataSource, IDistributedLockFactory distributedLockFactory) {
        return new IntegerSequence(defaultCacheSize, (DruidDataSource) dataSource, distributedLockFactory);
    }

}
