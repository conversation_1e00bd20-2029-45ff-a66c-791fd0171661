package com.glodon.qydata.mapper.standard.unit;

import com.glodon.qydata.entity.standard.unit.ZbUnit;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
@Repository
public interface ZbUnitMapper {
    
    /**
     * @Description 插入单位
     * <AUTHOR>
     * @Date 2022/1/17 14:28 
     * @param recordTemp
     * @return int
     **/
    int insertSelective(ZbUnit recordTemp);

    /**
     * @Description 根据主键查询单位信息
     * <AUTHOR>
     * @Date 2022/1/17 14:37
     * @param id
     * @return com.glodon.qydata.entity.standard.unit.ZbUnit
     **/
    ZbUnit selectByPrimaryKey(Long id);

    /**
     * @Description 条件查询
     * <AUTHOR>
     * @Date 2022/1/17 14:50
     * @param queryMap
     * @return java.util.List<com.glodon.qydata.entity.standard.unit.ZbUnit>
     **/
    List<ZbUnit> selectByCond(Map queryMap);

    /**
     * @Description 查询该企业下最大排序
     * <AUTHOR>
     * @Date 2022/1/17 16:59
     * @param customerCode
     * @return java.lang.Integer
     **/
    Integer selectMaxSort(@Param("customerCode") String customerCode);

    /**
     * @Description 根据主键更新
     * <AUTHOR>
     * @Date 2022/1/27 9:09
     * @param unit
     * @return int
     **/
    int updateByPrimaryKey(ZbUnit unit);

    int batchInsert(List<ZbUnit> list);
}