package com.glodon.qydata.mapper.standard.projectOrContractInfo;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.glodon.qydata.entity.standard.expression.ZbStandardsExpression;
import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity;
import com.glodon.qydata.util.mover.ElementMover;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * zb_standards_project_info - 主键 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
@Repository
public interface StandardsProjectInfoMapper extends BaseMapper<StandardsProjectInfoEntity>, ElementMover.DatabaseSaver<StandardsProjectInfoEntity> {

    /**
    　　* @description: 获取指定企业所有状态下的项目和合同信息
    　　* @param  customerCode 企业编码
    　　* @return
    　　* <AUTHOR>
    　　* @date 2021/12/9 16:49
    　　*/
    List<StandardsProjectInfoEntity> selectAllByCustomerCode(String customerCode);

    /**
    　　* @description: 项目和合同信息批量保存
    　　* @param
    　　* @return
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/12/9 17:23
    　　*/
    void insertBatch(List<StandardsProjectInfoEntity> list);

    /**
     　　* @description: 物理删除指定企业编码下的专业数据
     　　* @param  List<String> customerCode
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/2 23:27
     　　*/
    void deleteByCustomerCode(String customerCode);

    /**
     * 查询发布数据
     * @param customerCode
     * @param isShowDelete
     * @param type
     * @return
     */
    List<StandardsProjectInfoEntity> selectAllPublishData(@Param("customerCode") String customerCode,
                                                          @Param("isShowDelete") Integer isShowDelete,
                                                          @Param("type") Integer type);

    /**
     * 发布数据批量更新
     * @param list
     */
    void batchUpdatePublish(List<StandardsProjectInfoEntity> list);

    /**
     * 物理删除指定企业编码下的暂存数据
     * @param customerCode
     */
    void deleteSelfByCustomerCode(@Param("customerCode") String customerCode,
                                  @Param("standardDataType") Integer standardDataType);

    /**
     * @description: 暂存项目和合同信息批量保存
     * @param list
     * @return void
     * <AUTHOR>
     * @date 2022/7/11 16:19
     */
    void insertBatchSelf(List<StandardsProjectInfoEntity> list);

    /**
     * @description: 获取企业项目信息中的【产品定位】
     * @param customerCode
     * @return java.util.List<com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity>
     * <AUTHOR>
     * @date 2022/8/2 10:09
     */
    StandardsProjectInfoEntity selectProductPositioning(String customerCode);

    /**
     * @Description: 批量更新ord
     * @param selfList
     * @Author: zhangj-cl
     * @Date: 2022/12/30 14:49
     */
    @Override
    void batchUpdateOrd(@Param("selfList") List<StandardsProjectInfoEntity> selfList);

    List<ZbStandardsExpression> selectExpression(String customerCode);

    List<StandardsProjectInfoEntity> selectSelfList(@Param("customerCode") String customerCode,
                                                    @Param("isShowDelete") Integer isShowDelete,
                                                    @Param("type") Integer type);

    /**
     * @description: 更新父id和ord
     * @author: luoml-b
     * @date: 2024/8/30 15:16
     * @param: list
     **/
    void batchUpdateOrdAndPid(List<StandardsProjectInfoEntity> list);

    void batchDelete(List<Long> list);
    void batchUpdateSelective(@Param("list") List<StandardsProjectInfoEntity> list);
    void batchUpdateSelfSelective(@Param("list") List<StandardsProjectInfoEntity> list);

    Set<String> selectAllCustomerCodes(Integer standardDataType);
    Set<String> selectSelfAllCustomerCodes(Integer standardDataType);
}
