package com.glodon.qydata.mapper.standard.buildStandard;

import com.glodon.qydata.dto.ZbProjectStandardDetailTreeDto;
import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandardDetail;
import com.glodon.qydata.util.mover.ElementMover;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 　　* @description: 项目标准细则mapper
 　　* <AUTHOR>
 　　* @date 2021/8/16 16:49
 　　*/
@Repository
public interface ZbProjectStandardDetailMapper extends ElementMover.DatabaseSaver<ZbProjectStandardDetail> {
    /**
     　　* @description: 新增单个项目标准细则实体
     　　* @param
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/8/16 16:49
     　　*/
    int insert(ZbProjectStandardDetail recordTemp);

    /**
     * 根据父节点层次码及标准id获取所有子节点
     * @throws
     * @param fatherLevelCode 父节点层次码
     * @param standardId 标准id
     * <AUTHOR>
     * @return {@link List<  ZbProjectStandardDetail >}
     * @date 2021/8/17 15:24
     */
    List<ZbProjectStandardDetail> selectBrothersById(@Param("fatherLevelCode") String fatherLevelCode, @Param("standardId") Long standardId);

    /**
     * 根据id获取
     * @throws
     * @param id
     * <AUTHOR>
     * @return {@link ZbProjectStandardDetail}
     * @date 2021/8/17 15:37
     */
    ZbProjectStandardDetail selectById(Long id);

    /**
     * 将某个ord后的所有ord+1
     * @throws
     * @param standardId
     * @param ord
     * <AUTHOR>
     * @return
     * @date 2021/8/17 16:41
     */
    void updateOnePlusOrdByStandardIdAndOrd(@Param("standardId") Long standardId, @Param("ord") Integer ord);

    /**
     * 根据id集合获取细则
     * @throws
     * @param idList
     * <AUTHOR>
     * @return {@link List<  ZbProjectStandardDetail >}
     * @date 2021/8/18 14:57
     */
    List<ZbProjectStandardDetail> selectByIds(List<Long> idList);

    /**
     * 根据标准id获取所有细则
     * @throws
     * @param standardId
     * <AUTHOR>
     * @return {@link List<  ZbProjectStandardDetail >}
     * @date 2021/8/18 15:06
     */
    List<ZbProjectStandardDetail> selectByStandardId(Long standardId);

    /**
     * 根据标准id集合查询所有细则
     * @param standardIdList
     * @return 明细集合
     */
    List<ZbProjectStandardDetail> selectByStandardIdList(List<Long> standardIdList);

  /**
  　　* @description: 根据标准id集合查询所有细则
  　　* @param
  　　* @return
  　　* @throws
  　　* <AUTHOR>
  　　* @date 2021/8/31 9:18
  　　*/
    List<ZbProjectStandardDetail> selectSelfByStandardIdList(List<Long> standardIdList);

    /**
     * 根据id集合批量删除细则
     * @throws
     * @param ids
     * <AUTHOR>
     * @return
     * @date 2021/8/18 15:12
     */
    void deleteSelfByIds(List<Long> ids);

    /**
     * 根据levelcode和标准id清空描述示例
     * @throws
     * @param standardId
     * @param levelcode
     * <AUTHOR>
     * @return
     * @date 2021/9/15 15:27
     */
    void updateDescBlankByLevelCodeAndStandardId(@Param("standardId") Long standardId, @Param("levelcode") String levelcode);

    /**
     * 查询子项
     * @throws
     * @param standardId
     * @param levelcode
     * <AUTHOR>
     * @return {@link List<  ZbProjectStandardDetail >}
     * @date 2021/9/16 10:19
     */
    List<ZbProjectStandardDetail> selectSons(@Param("standardId") Long standardId, @Param("levelcode") String levelcode);

    /**
     * 修改所有子项的专业工程
     * @throws
     * @param detail
     * @param standardId
     * <AUTHOR>
     * @return
     * @date 2021/10/26 11:07
     */
    void updateSonsTrade(@Param("detail") ZbProjectStandardDetail detail, @Param("standardId") Long standardId);

    /**
     * 查询下属的所有项
     * @throws
     * @param standardId
     * @param levelcode
     * <AUTHOR>
     * @return {@link List< ZbProjectStandardDetail>}
     * @date 2021/10/26 14:36
     */
    List<ZbProjectStandardDetail> selectAllSons(@Param("standardId") Long standardId, @Param("levelcode") String levelcode);

    /**
     * 按排序字段升序获取同层级的数据
     * @throws
     * @param detail
     * <AUTHOR>
     * @return {@link List< ZbProjectStandardDetail>}
     * @date 2021/10/26 15:33
     */
    List<ZbProjectStandardDetail> selectSameLevelByOrd(ZbProjectStandardDetail detail);

    /**
     * 根据id集合更新ord加一
     * @throws
     * @param ids
     * @param size 需要修改的值
     * <AUTHOR>
     * @return
     * @date 2021/10/26 16:21
     */
    void updateOnePlusOrdByIds(@Param("ids") List<Long> ids, @Param("size") Integer size);

    /**
     * 根据id集合更新ord
     * @throws
     * @param ids
     * @param size 需要修改的值
     * <AUTHOR>
     * @return
     * @date 2021/10/26 16:33
     */
    void updateOneMinusOrdByIds(@Param("ids") List<Long> ids, @Param("size") Integer size);

    /**
     * 批量保存
     * @throws
     * @param details
     * <AUTHOR>
     * @return
     * @date 2021/10/28 10:39
     */
    void saveBatch(List<ZbProjectStandardDetail> details);

    /**
     * 批量更新ord
     * @throws
     * @param updateOrdList
     * <AUTHOR>
     * @return
     * @date 2021/11/11 16:12
     */
    void updateOrdByList(List<ZbProjectStandardDetail> updateOrdList);

    /**
    　　* @description: 删除指定标准集合下的标准细则信息
    　　* @param  ids 标准id集合下
    　　* @return
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/12/9 19:48
    　　*/
    void deleteByStandardIds(List<Long> ids);

    /**
     * 根据idList 获取暂存列表
     * @param idList
     * @return
     */
    List<ZbProjectStandardDetail> selectSelfByIds(List<Long> idList);

    /**
     * 保存暂存列表
     * @param details
     */
    void saveSelfBatch(List<ZbProjectStandardDetail> details);

    /**
     * 根据标准id获取所有暂存细则
     * @param standardId
     * @return
     */
    List<ZbProjectStandardDetail> selectSelfByStandardId(Long standardId);

    /**
     * 发布时新增数据
     * @param details
     */
    void publishInsert(List<ZbProjectStandardDetail> details);

    /**
     * 删除指定标准集合下的暂存标准细则信息
     * @param ids
     */
    void deleteSelfByStandardIds(List<Long> ids);

    /**
     * 更新发布数据
     * @param standardId
     * @param detail
     */
    void updateSelfStandardDetailColData(@Param("standardId") Long standardId, @Param("detail") ZbProjectStandardDetail detail);
    void batchUpdateOrd(@Param("list") List<ZbProjectStandardDetail> list);

    void batchSubjectDivisionName(@Param("list") List<ZbProjectStandardDetailTreeDto> list);

    ZbProjectStandardDetail selectSubjectDivision(@Param("standardId") Long standardId, @Param("itemDivisionSubjectId") String itemDivisionSubjectId, @Param("id") Long id);

}
