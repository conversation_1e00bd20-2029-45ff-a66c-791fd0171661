package com.glodon.qydata.mapper.standard.feature;

import com.glodon.qydata.entity.standard.feature.ProjectFeature;
import com.glodon.qydata.util.mover.ElementMover;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * 
 * <AUTHOR>
 * @date 2021/11/5 8:44
 */
@Repository
public interface ProjectFeatureSelfMapper extends ElementMover.DatabaseSaver<ProjectFeature> {

    /**
     * 插入
     * @param recordTemp
     * @return int
     * <AUTHOR>
     * @date 2021/11/5 9:07 
     */
    int insertSelective(ProjectFeature recordTemp);
    /***
     * @description: 批量插入
     * @return int
     * @throws
     * <AUTHOR>
     * @date 2022/7/6 15:55
     */
    int insertSelectiveBatch(List<ProjectFeature> list);
    /**
     * 根据工程特征主键ID查询工程特征信息
     * @param id
     * @return com.gcj.zblib.standardData.feature.entity.ProjectFeature
     * <AUTHOR>
     * @date 2021/10/21 20:54
     */
    ProjectFeature selectByPrimaryKey(Long id);
    /**
     * 根据工程特征主键ID查询工程特征信息
     * @param list
     * @return com.gcj.zblib.standardData.feature.entity.ProjectFeature
     * <AUTHOR>
     * @date 2021/10/21 20:54
     */
    List<ProjectFeature> selectByIds(List<String> list);

    /**
     *  更新
     * @param recordTemp
     * @return int
     * <AUTHOR>
     * @date 2021/11/5 9:07 
     */
    int updateByPrimaryKeySelective(ProjectFeature recordTemp);
    /**
     *  更新
     * @return int
     * <AUTHOR>
     * @date 2021/11/5 9:07
     */
    int updateByIdsSelective(List<Long> list);

    /**
     * 校验某个专业下工程特征 名称+类型+枚举值 是否重复(未删除的)
     * @param tradeId
     * @param name
     * @param customerCode
     * @return int
     * <AUTHOR>
     * @date 2021/10/21 20:55
     */
    int countByTradeIdExpressionId(@Param("tradeId") Long tradeId, @Param("name") String name,
                                   @Param("customerCode") String customerCode, @Param("id")Long featureId,
                                   @Param("type") Integer type);

    List<ProjectFeature> countByTradeId(@Param("tradeId") String tradeId, @Param("customerCode") String customerCode, @Param("type") Integer type);

    /**
     * 校验某个专业下工程特征 名称+类型+枚举值 是否重复 (所有的，包括已删除的)
     * @param tradeId
     * @param name
     * @param typeCode
     * @param customerCode
     * @return int
     * <AUTHOR>
     * @date 2021/11/4 10:55
     */
    ProjectFeature findByTradeIdExpressionId(@Param("tradeId") Long tradeId, @Param("name") String name,
                                             @Param("typeCode") String typeCode, @Param("customerCode") String customerCode,
                                             @Param("type") Integer type);
    List<ProjectFeature> findByTradeId(@Param("tradeId") String tradeId, @Param("customerCode") String customerCode, @Param("type") Integer type);

    /**
     * 批量更新ord
     * @param list
     * @return void
     * <AUTHOR>
     * @date 2021/11/5 9:09
     */
    @Override
    void batchUpdateOrd(@Param("list") List<ProjectFeature> list);

    /**
     * 批量更新工程分类
     * @param list
     * @return void
     * <AUTHOR>
     * @date 2024/11/15 9:09
     */
    void batchUpdateFeatureInfo(@Param("list") List<ProjectFeature> list);

    /**
     * 根据主键删除工程特征
     * @param featureId
     * @return void
     * <AUTHOR>
     * @date 2021/11/4 10:56
     */
    void deleteByPrimaryKey(Long featureId);

    /**
     * 查询某企业的所有特征
     * @param customerCode
     * @return java.util.List<com.glodon.zbw.dataManager.standardData.category.domain.CommonProjCategory>
     * <AUTHOR>
     * @date 2021/10/19 14:53
     */
    List<ProjectFeature> selectAll(@Param("customerCode") String customerCode, @Param("type") Integer type);

    List<ProjectFeature> selectBySelfTradeId(@Param("customerCode") String customerCode, @Param("tradeId") Long tradeId, @Param("type") Integer type);

    /**
     * 根据id集合查询
     * @param featureIds
     * @param customerCode
     * @return java.util.List<com.gcj.zblib.standardData.feature.entity.ProjectFeature>
     * <AUTHOR>
     * @date 2022/2/10 16:47
     */
    List<ProjectFeature> selectBySelfFeatureIdList(@Param("featureIds") List<Long> featureIds,
                                                   @Param("customerCode") String customerCode,
                                                   @Param("isShowNotUsing") Integer isShowNotUsing,
                                                   @Param("type") Integer type);

    List<ProjectFeature> selectByCustomCodeAndTradeIds(@Param("customerCode") String customerCode, @Param("tradeIds") List<Long> tradeIds);

    List<ProjectFeature> selectSelfAll(@Param("customerCode") String customerCode, @Param("type") Integer type);

    /**
     * 物理删除指定企业编码下的暂存工程特征专业数据
     * @param customerCode
     */
    void deleteBySelfCustomerCode(@Param("customerCode") String customerCode, @Param("type") Integer type);

    /**
     　　* @description: 工程特征批量保存
     　　* @param
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2022/2/11 18:10
     */
    void insertSelfBatch(@Param("list") List<ProjectFeature> list);

    /**
     　　* 根据专业id物理删除专业下携带的工程特征
     　　* @param  tradeId 专业编码
     　　* @return
     　　* <AUTHOR>
     　　* @date 2021/10/25 10:56
     　　*/
    void deleteByTradeId(Long tradeId);

    List<Long> selectAllGlobalIds(@Param("customerCode") String customerCode);

    List<ProjectFeature> selectAllNoBlobByCustomCode(String customerCode, Integer type);

    void batchUpdateSelective(@Param("list") List<ProjectFeature> list);

    Set<String> selectAllCustomerCodes();


    /**
     * 根据专业查询特征
     * @param tradeIds
     * @return java.util.List<com.gcj.zblib.standardData.feature.entity.ProjectFeature>
     * <AUTHOR>
     * @date 2021/10/25 14:09
     */
    List<ProjectFeature> selectByTradeIds(List<Long> tradeIds);
    List<ProjectFeature> selectByName(@Param("tradeId") Long tradeId, @Param("name") String name,
                                      @Param("customerCode") String customerCode, @Param("type") Integer type);

    Integer selectRecordCount(@Param("customerCode") String customerCode, @Param("type") Integer type);
}