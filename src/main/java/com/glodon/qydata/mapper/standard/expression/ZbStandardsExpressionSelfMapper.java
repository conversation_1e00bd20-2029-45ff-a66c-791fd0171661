package com.glodon.qydata.mapper.standard.expression;

import com.glodon.qydata.entity.standard.expression.ZbStandardsExpression;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * @description:
 * <AUTHOR>
 * @date 2021/11/5 8:50
 */
@Repository
public interface ZbStandardsExpressionSelfMapper {
    /**
     * 插入
     * @param recordTemp
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2021/11/5 8:58
     */
    Long insertSelective(ZbStandardsExpression recordTemp);
    /***
     * @description: 批量插入字典
     * @param recordTemp 1
     * @return java.lang.Long
     * @throws
     * <AUTHOR>
     * @date 2022/7/15 20:50
     */
    Long insertSelectiveList(List<ZbStandardsExpression> recordTemp);

    /**
     * 查询是否存在，按名称+类型+枚举
     * @param customerCode
     * @param name
     * @param typeCode
     * @param option
     * @return com.gcj.zblib.standardData.expression.entity.ZbStandardsExpression
     * <AUTHOR>
     * @date 2021/11/5 8:59
     */
    ZbStandardsExpression findExistByNameTypeOption(String customerCode, String name, String typeCode,
                                                    String option, Long selfGlobalId, Integer type);
    List<ZbStandardsExpression> findExistByGlobalId(String customerCode, Long selfGlobalId, Integer type);

    /**
     * 查询是否存在，按名称+类型
     * @param customerCode
     * @param name
     * @param typeCode
     * @return com.gcj.zblib.standardData.expression.entity.ZbStandardsExpression
     * <AUTHOR>
     * @date 2021/11/5 9:00
     */
    ZbStandardsExpression findExistByNameType(String customerCode, String name, String typeCode, Long selfGlobalId, Integer type);

    /**
     * 根据主键查询
     * @param id
     * @return com.gcj.zblib.standardData.expression.entity.ZbStandardsExpression
     * <AUTHOR>
     * @date 2021/11/5 9:00
     */
    ZbStandardsExpression selectByPrimaryKey(Long id);

    /**
     * 根据id删除计算口径
     * @param expressionId
     * @return void
     * <AUTHOR>
     * @date 2021/11/5 16:38
     */
    void deleteByPrimaryKey(@Param("id") Long expressionId);

    /**
     * 修改
     * @param recordTemp
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2021/11/8 14:28
     */
    void updateSelective(ZbStandardsExpression recordTemp);

    /**
     *批量保存
     * @param list
     * @return void
     * <AUTHOR>
     * @date 2021/11/11 15:47
     */
    void batchInsert(@Param("list") List<ZbStandardsExpression> list);

    /**
     * 获取最大计算口径排序
     * @param customerCode
     * @return int
     * <AUTHOR>
     * @date 2021/11/12 10:47
     */
    int getMaxExpressionOrd(String customerCode, Long selfGlobalId, Integer type);

    List<ZbStandardsExpression> selectListBySelfCustomCode(String customerCode, Long selfGlobalId, Integer type);

    /**
     * 获取暂存的计算口径
     * @throws
     * @param customerCode
     * @param selfGlobalId
     * <AUTHOR>
     * @return {@link List< ZbStandardsExpression>}
     * @date 2022/3/2 14:20
     */
    List<ZbStandardsExpression> selectSelfExpression(String customerCode, Long selfGlobalId, Integer type);

    /**
     * 发布时获取暂存的计算口径
     * @throws
     * @param customerCode
     * @param selfGlobalId
     * <AUTHOR>
     * @return {@link List< ZbStandardsExpression>}
     * @date 2022/3/2 14:20
     */
    List<ZbStandardsExpression> selectSelfExpressionWhenPublish(String customerCode, Long selfGlobalId, Integer type);

    void deleteSelfByCustomerCode(String customerCode, Integer type);

    List<Map> selectAllCustomerCodeGlobalIds();

    List<ZbStandardsExpression> selectAllByCustomCode(String customerCode, Integer type, Long selfGlobalId);

    void batchUpdateSelective(@Param("list") List<ZbStandardsExpression> list);
}