package com.glodon.qydata.mapper.standard.buildStandard;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildStandardDetailDesc;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【zb_standards_build_standard_detail_desc((企业标准数据-建造标准-标准说明表))】的数据库操作Mapper
* @createDate 2022-07-29 19:03:33
* @Entity generator.domain.ZbStandardsBuildStandardDetailDesc
*/
@Repository
public interface ZbStandardsBuildStandardDetailDescMapper extends BaseMapper<ZbStandardsBuildStandardDetailDesc> {

    /**
     * @description: 根据standardId查询某标准下的所有标准说明
     * @param standardId
     * @return java.util.List<com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildStandardDetailDesc>
     * <AUTHOR>
     * @date 2022/8/1 15:35
     */
    List<ZbStandardsBuildStandardDetailDesc> selectByStandardId(Long standardId);

    /**
     * 批量插入暂存数据
     * @param list
     */
    void batchSaveSelf(List<ZbStandardsBuildStandardDetailDesc> list);

    void batchSave(List<ZbStandardsBuildStandardDetailDesc> list);

    /**
     * 批量删除暂存数据
     * @param list
     */
    void batchDelSelf(List<ZbStandardsBuildStandardDetailDesc> list);

    /**
     * 批量删除暂存数据
     * @param list
     */
    void batchDelSelfById(List<Long> list);

    /**
     * 查询根据standarId暂存表数据
     * @param standardId
     * @return
     */
    List<ZbStandardsBuildStandardDetailDesc> selectSelfByStandardId(Long standardId);

    /**
     * @description: 查询细则下的标准说明ord升序
     * @param detailId
     * @return java.util.List<com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandardDetail>
     * <AUTHOR>
     * @date 2022/8/4 10:50
     */
    List<ZbStandardsBuildStandardDetailDesc> selectSelfByDetailIdIAsc(Long detailId);

    /**
     * @description: 批量更新ord
     * @param list
     * @return void
     * <AUTHOR>
     * @date 2022/8/4 16:07
     */
    void batchUpdateOrd(@Param("list") List<ZbStandardsBuildStandardDetailDesc> list);

    /**
     * 根据建造标准id查询列表
     * @param list
     * @return
     */
    List<ZbStandardsBuildStandardDetailDesc> selectSelfByStandardIds(@Param("list") List<Long> list);
    List<ZbStandardsBuildStandardDetailDesc> selectByStandardIds(@Param("list") List<Long> list);

    /**
     * 批量删除企业数据根据建造标准id
     * @param list
     */
    void batchDelByStandardIds(List<Long> list);

    /**
     * 根据建造标准删除暂存表数据
     * @param standardIds
     */
    void batchDelSelfByStandardIds(List<Long> standardIds);
    /**
     * 根据id查询暂存表数据
     * @param id
     * @return
     */
    ZbStandardsBuildStandardDetailDesc selectSelfById(Long id);

    void insertSelf(ZbStandardsBuildStandardDetailDesc selfDesc);

    void updateByPrimaryKey(ZbStandardsBuildStandardDetailDesc selfDesc);
    void batchUpdateSelective(@Param("list") List<ZbStandardsBuildStandardDetailDesc> list);
    void batchUpdateSelfSelective(@Param("list") List<ZbStandardsBuildStandardDetailDesc> list);

}
