package com.glodon.qydata.mapper.standard.category;


import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019/12/17 16:50
 */
@Repository("commonProjCategoryStandardMapper")
public interface CommonProjCategoryMapper {
    
    /**
     * 查询某企业的所有分类
     * @param customerCode
     * @param isShowDelete
     * @return java.util.List<com.glodon.zbw.dataManager.standardData.category.domain.CommonProjCategory>
     * <AUTHOR>
     * @date 2021/10/19 14:53 
     */
    List<CommonProjCategory> selectAll(@Param("customerCode") String customerCode, @Param("type") Integer type, @Param("isShowDelete") Integer isShowDelete);

    /**
     * 查询某企业的所有分类
     * @param customerCode
     * @param categoryCodeList
     * @return java.util.List<com.glodon.zbw.dataManager.standardData.category.domain.CommonProjCategory>
     * <AUTHOR>
     * @date 2021/11/24 20:53
     */
    List<CommonProjCategory> selectAllByCategoryCodeList(@Param("customerCode") String customerCode, @Param("type") Integer type, @Param("list") List<String> categoryCodeList);
    
    /**
     * 查询企业的是否有系统内置的副本
     * @param customerCode
     * @return int
     * <AUTHOR>
     * @date 2021/10/19 14:53 
     */
    int selectInit(@Param("customerCode") String customerCode, @Param("type") Integer type);

    /**
    　　* @description: 查询可使用状态的一级工程分类列表
    　　* @param  customerCode 企业编码
    　　* @return List<CommonProjCategory> 符合条件的工程分类列表
    　　* <AUTHOR>
    　　* @date 2021/11/19 19:10
    　　*/
    List<CommonProjCategory> queryTruthTopCategoryList(String customerCode, Integer type);

    /**
     * 根据主键查询工程分类
     * @param categoryId
     * @return com.glodon.zbw.dataManager.standardData.category.domain.CommonProjCategory
     * <AUTHOR>
     * @date 2021/10/19 14:55 
     */
    CommonProjCategory getCategoryByPrimaryKey(@Param("categoryId") Integer categoryId);
    
    /**
     * 批量插入
     * @param list
     * @return int
     * <AUTHOR>
     * @date 2021/10/19 14:55 
     */
    int saveBatchCommonProjCategory(@Param("list") List<CommonProjCategory> list);

    /**
    　　* @description: 返回该企业下所有一级工程分类的code与名称对应关系
    　　* @param  customerCode
    　　* @return
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/11/22 11:43
    　　*/
    List<CommonProjCategory> mapNameByTypes(@Param("customerCode") String customerCode, @Param("type") Integer type);

    /**
     * @description: 临时--批量更新levelCode,categorycode3
     * @param list
     * @return void
     * <AUTHOR>
     * @date 2021/11/23 17:24
     */
    void batchUpdateLevelCode(@Param("list") List<CommonProjCategory> list);

    void updateLevelCode(CommonProjCategory category);

    /**
     * @description: 临时--查询排序临时表，初始化内置分类的ord
     * @param
     * @return java.util.List<com.glodon.zbw.dataManager.standardData.category.domain.CommonProjCategory>
     * <AUTHOR>
     * @date 2021/11/23 17:59
     */
    List<CommonProjCategory> selectOrdTemp();

    /**
     * @description: 查询有工程分类数据的企业
     * @return java.util.List<com.glodon.zbw.dataManager.standardData.category.domain.CommonProjCategory>
     * <AUTHOR>
     * @date 2021/11/24 18:05
     */
    List<String> selectCustomerCode();

    /**
     * 获取指定code直接下一级的类别列表
     * @param customerCode
     * @param code
     * @param level
     * @return
     */
    List<CommonProjCategory> selectByLevelAndCode(@Param("customerCode") String customerCode, @Param("type") Integer type,
                                                  @Param("code") String code, @Param("level") Integer level);

    /**
     　　* @description: 物理删除指定企业编码下的工程分类数据
     　　* @param  List<String> customerCode
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/2 23:27
     　　*/
    void deleteByCustomerCode(@Param("customerCode") String customerCode, @Param("type") Integer type);

    /**
     * 发布批量更新
     * @param list
     * @return void
     * <AUTHOR> @date 2022/01/10 14:54
     */
    void batchUpdatePublish(@Param("list") List<CommonProjCategory> list);

    void updatePublish(CommonProjCategory category);

    /**
     * 批量更新ord
     * @param list
     * @return void
     * <AUTHOR>
     * @date 2021/10/19 14:54
     */
    void batchUpdateOrd(@Param("list") List<CommonProjCategory> list);

    void updateOrd(CommonProjCategory category);

    /**
     * 根据commonprojcategoryid查询某企业的分类
     * @param commonprojcategoryid
     * @param customerCode
     * @return com.glodon.zbw.dataManager.standardData.category.domain.CommonProjCategory
     * <AUTHOR>
     * @date 2021/10/19 14:53
     */
    CommonProjCategory getByCommonprojcategoryid(String commonprojcategoryid, String customerCode, Integer type);

    List<CommonProjCategory> getByCodeSet(Set<String> codeSet, String customerCode, Integer type);

    /**
     * @description: 查询某一级分类下的所有工程分类（未删除、启用）
     * @param customerCode
     * @param type
     * @param oneLevelCode
     * @return java.util.List<com.glodon.qydata.entity.standard.category.CommonProjCategory>
     * <AUTHOR>
     * @date 2022/8/30 9:45
     */
    List<CommonProjCategory> selectByOneLevelCode(@Param("customerCode") String customerCode, @Param("type") Integer type,
                                                  @Param("oneLevelCode") String oneLevelCode);

    List<String> selectAllCustomerCode();


    /**
     * @description: 临时--获取备份数据
     * @param
     * @return list
     * <AUTHOR>
     * @date 2023/08/25 17:24
     */
    List<CommonProjCategory> selectAllCategoryBackup(Integer size);

    /**
     * @description: 临时--批量更新update_time
     * @param list
     * @return void
     * <AUTHOR>
     * @date 2023/08/25 17:24
     */
    void batchUpdateUpdateTime(@Param("list") List<CommonProjCategory> list);

    void updateUpdateTime(CommonProjCategory category);
    void batchUpdateDelStatus(@Param("list") List<Integer> list);
}
