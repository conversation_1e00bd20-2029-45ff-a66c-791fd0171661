package com.glodon.qydata.mapper.standard.buildStandard;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildPositionDetail;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【zb_standards_build_position_detail(企业标准数据-建造标准定位-细则)】的数据库操作Mapper
* @createDate 2022-08-01 10:57:17
* @Entity generator.domain.ZbStandardsBuildPositionDetail
*/
@Repository
public interface ZbStandardsBuildPositionDetailMapper extends BaseMapper<ZbStandardsBuildPositionDetail> {

    List<ZbStandardsBuildPositionDetail> selectByStandardId(Long standardId);

    /**
    　　* @description: 根据positionId查询对应细则表
    　　* @param  positionId
    　　* @return List<ZbStandardsBuildPositionDetail>
    　　* <AUTHOR>
    　　* @date 2022/8/7 13:27
    　　*/
    List<ZbStandardsBuildPositionDetail> selectByPositionId(Long positionId);

    void batchInsertSelf(List<ZbStandardsBuildPositionDetail> list);

    /**
     * 根据建造标准id，查询结果值
     * @param standardId
     * @return
     */
    List<ZbStandardsBuildPositionDetail> selectSelfByStandardId(Long standardId);

    /**
     * 根据产品定位id查询列表
     * @param positionIds
     * @return
     */
    List<ZbStandardsBuildPositionDetail> selectSelfByPositionIds(List<Long> positionIds);

    /**
     * 根据定位id批量删除数据
     * @param list
     */
    void batchSelfByPositionIds(List<Long> list);

    /**
     * 根据建造标准id查询数据
     * @param standIds
     * @return
     */
    List<ZbStandardsBuildPositionDetail> selectSelfByStandardIds(@Param("list") List<Long> standIds);

    /**
     * 根据建造标准id删除数据
     * @param standardIds
     */
    void deleteByStandardIds(List<Long> standardIds);

    /**
     * 批量保存数据
     * @param positionDetails
     */
    void batchSave(List<ZbStandardsBuildPositionDetail> positionDetails);

    /**
     * 批量删除暂存表数据
     * @param standardIds
     */
    void delSelfByStandardIds(List<Long> standardIds);

    /**
     * 批量删除暂存数据-byDescId
     * @param list
     */
    void batchDelSelfByDescId(List<Long> list);

    /**
     * 批量更新value
     * @param positionDetails
     */
    void batchUpdateValueSelf(List<ZbStandardsBuildPositionDetail> positionDetails);

    /**
     * 根据标准说明id，查询结果值
     * @param descId
     * @return
     */
    List<ZbStandardsBuildPositionDetail> selectSelfByDescId(Long descId);
}




