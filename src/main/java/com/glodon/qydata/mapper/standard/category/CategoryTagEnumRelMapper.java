package com.glodon.qydata.mapper.standard.category;

import com.glodon.qydata.entity.standard.category.CategoryTagEnumRel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: luoml-b
 * @date: 2024/10/22 15:00
 * @description:
 */
@Mapper
public interface CategoryTagEnumRelMapper {

    List<CategoryTagEnumRel> selectByEnterpriseId(@Param("enterpriseId") String enterpriseId);

    void saveBatch(@Param("list") List<CategoryTagEnumRel> list);

    void deleteByEnterpriseId(@Param("enterpriseId") String enterpriseId);

    List<CategoryTagEnumRel> selectNameByEnterpriseId(@Param("enterpriseId") String enterpriseId);
}
