package com.glodon.qydata.mapper.standard.buildStandard;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * 　　* @description: 项目标准mapper
 * 　　* <AUTHOR>
 * 　　* @date 2021/8/16 16:49
 *
 */
@Repository
public interface ZbProjectStandardMapper extends BaseMapper<ZbProjectStandard> {

    /**
     * 　　* @description: 根据实体类型，新增单个项目标准实体
     * 　　* @param recordTemp 标准实体
     * 　　* @return
     * 　　* <AUTHOR>
     * 　　* @date 2021/8/16 16:49
     *
     */
    int insertSelective(ZbProjectStandard recordTemp);

    /**
     * 　　* @description: 根据企业编码、标准名称查找相关记录
     * 　　* @param customerCode 企业编码  name 标准名称
     * 　　* @return
     * 　　* @throws
     * 　　* <AUTHOR>
     * 　　* @date 2021/8/16 17:51
     *
     */
    ZbProjectStandard searchSelfRecordsByCusCodeAndName(@Param("customerCode") String customerCode, @Param("name") String name);

    /**
     * 按企业编码获取建造标准列表
     *
     * @param customerCode
     * @return {@link List<  ZbProjectStandard >}
     * @throws
     * <AUTHOR>
     * @date 2021/8/17 9:31
     */
    List<ZbProjectStandard> selectByCustomerCode(@Param("customerCode") String customerCode,
                                                 @Param("isShowDelete") Integer isShowDelete);

    /**
     * @description: 按企业编码获取建造标准列表(外加内置的-100企业)
     * @param customerCode
     * @param isShowDelete
     * @return java.util.List<com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard>
     * <AUTHOR>
     * @date 2022/8/9 16:36
     */
    List<ZbProjectStandard> selectByCustomerCodeWithOrder(@Param("customerCode") String customerCode,
                                                          @Param("isShowDelete") Integer isShowDelete,
                                                          @Param("createTimeAscOrDesc") Integer createTimeAscOrDesc);


    /**
     * 　　* @description: 根据企业编码、业态编码查找相关记录
     * 　　* @param customerCode 企业编码  categoryCode 业态编码
     * 　　* @return
     * 　　* @throws
     * 　　* <AUTHOR>
     * 　　* @date 2021/8/16 17:51
     *
     */
    ZbProjectStandard searchRecordsByCusCodeAndCateCode(@Param("customerCode") String customerCode,
                                                        @Param("categoryCode") String categoryCode);

    /**
     * 　　* @description: 根据企业和指定业态集合，查找所有集合
     * 　　* @param
     * 　　* @return
     * 　　* @throws
     * 　　* <AUTHOR>
     * 　　* @date 2021/8/17 15:34
     *
     */
    List<ZbProjectStandard> selectListByCusAndCates(@Param("customerCode") String customerCode, @Param("categoryCodeList") List<String> categoryCodeList);

    /**
     * @param customerCode
     * @param categoryCode
     * @return java.util.List<com.gcj.zblib.projectStandard.domain.ZbProjectStandard>
     * @description: 根据企业编码、业态编码查找启用的未删除的相关记录
     * <AUTHOR>
     * @date 2022/08/05 18:14
     */
    List<ZbProjectStandard> selectEnabledListByCusAndCategoryCode(@Param("customerCode") String customerCode, @Param("categoryCode") String categoryCode);
    /**
     * 查询已删除的同名标准
     *
     * @param name
     * @param customerCode
     * @return {@link ZbProjectStandard}
     * @throws
     * <AUTHOR>
     * @date 2021/10/25 15:18
     */
    ZbProjectStandard selectDeletedSameNameStandard(@Param("name") String name, @Param("customerCode") String customerCode);

    /**
     * 根据id删除
     *
     * @param id
     * @return
     * @throws
     * <AUTHOR>
     * @date 2021/10/25 15:48
     */
    void deleteStandardById(Long id);

    /**
     * 根据id查询
     *
     * @param id
     * @return {@link ZbProjectStandard}
     * @throws
     * <AUTHOR>
     * @date 2021/10/27 11:22
     */
    ZbProjectStandard selectSelfStandardById(Long id);
    ZbProjectStandard selectSelfStandard(String customerCode, Long id);

    /**
     * @description: 查询企业的建造标准
     * @param standardId
     * @return com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard
     * <AUTHOR>
     * @date 2022/8/3 17:17
     */
    ZbProjectStandard selectStandardByStandardId(Long standardId);

    /**
     * 根据id更新
     *
     * @param zbProjectStandard
     * @return
     * @throws
     * <AUTHOR>
     * @date 2021/10/27 11:24
     */
    void updateStandardById(ZbProjectStandard zbProjectStandard);

    /**
     * 初始化企业内置标准
     *
     * @return {@link ZbProjectStandard}
     * @throws
     * <AUTHOR>
     * @date 2021/10/28 14:15
     */
    ZbProjectStandard selectBuiltInStandard();

    /**
     * 　　* @description: 批量保存建造标准数据集合
     * 　　* @param
     * 　　* @return
     * 　　* @throws
     * 　　* <AUTHOR>
     * 　　* @date 2021/12/9 11:58
     *
     */
    void batchInsert(List<ZbProjectStandard> list);

    /**
     * 　　* @description: 物理删除指定企业编码下的建造标准数据
     * 　　* @param  List<String> customerCode
     * 　　* @return
     * 　　* @throws
     * 　　* <AUTHOR>
     * 　　* @date 2021/12/2 23:27
     *
     */
    void deleteByCustomerCode(@Param("customerCode") String customerCode);

    /**
     * 根据企业编码和业态编码查询建造标准
     *
     * @param customerCode
     * @param code
     * @param isShowDeleted 是否展示已删除的建造标准
     * @return {@link List< ZbProjectStandard>}
     * @throws
     * <AUTHOR>
     * @date 2022/1/18 13:54
     */
    List<ZbProjectStandard> selectByCategoryCode(@Param("customerCode") String customerCode,
                                                 @Param("code") String code,
                                                 @Param("isShowDeleted") Integer isShowDeleted);

    /**
     * 暂存表插入记录
     *
     * @param recordTemp
     * @return
     */
    int selfInsertSelective(ZbProjectStandard recordTemp);

    /**
     * 根据暂存用户id获取建造标准列表
     *
     * @param customerCode
     * @return {@link List<  ZbProjectStandard >}
     * @throws
     * <AUTHOR>
     * @date 2021/8/17 9:31
     */
    List<ZbProjectStandard> selectSelfByCustomerCode(@Param("customerCode") String customerCode, @Param("isShowDelete") Integer isShowDelete);

    /**
     * 批量保存建造标准数据集合
     *
     * @param list
     */
    void selfBatchInsert(List<ZbProjectStandard> list);

    /**
     　　* @description: 物理删除指定企业编码下的建造标准数据
     　　* @param  List<String> customerCode
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/2 23:27
     　　*/
    /**
     * @param customerCode
     */
    void deleteSelfByCustomerCode(@Param("customerCode") String customerCode);

    /**
     * 根据id更新
     *
     * @param zbProjectStandard
     * @return
     * @throws
     * <AUTHOR>
     * @date 2021/10/27 11:24
     */
    void updateSelfStandardById(ZbProjectStandard zbProjectStandard);

    /**
     * 修改已编辑标记
     * @param standardId
     * @return
     * @throws
     * <AUTHOR>
     * @date 2022/1/27 18:09
     */
    void updateIsUpdated(@Param("standardId") Long standardId);

    /**
     * 批量更新已发布标准版本号
     * @throws
     * @param updateVersionList
     * <AUTHOR>
     * @return
     * @date 2022/1/27 18:56
     */
    void batchUpdateVersion(List<ZbProjectStandard> updateVersionList);

    /**
     * 查询所有的暂存数据
     * @return
     */
    List<ZbProjectStandard> selectSelfAll();

    List<ZbProjectStandard> selectSelfEnabledByCategoryCode(@Param("customerCode") String customerCode, @Param("categoryCode") String categoryCode);

    void updateSelfDisableByCategoryCode(@Param("customerCode") String customerCode, @Param("categoryCode") String categoryCode);

    Set<String> selectAllCustomerCodes();

    Set<String> selectSelfAllCustomerCodes();

    void batchUpdateUsingAndCategory(@Param("customerCode") String customerCode, @Param("updateList") List<ZbProjectStandard> updateList);
    void batchUpdateUsingAndCategorySelf(@Param("customerCode") String customerCode, @Param("updateList") List<ZbProjectStandard> updateList);
}
