package com.glodon.qydata.mapper.standard.feature;

import com.glodon.qydata.entity.standard.expression.ZbStandardsExpression;
import com.glodon.qydata.entity.standard.feature.ProjectFeature;
import com.glodon.qydata.vo.standard.feature.ProjectFeatureResultVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 
 * <AUTHOR>
 * @date 2021/11/5 8:44
 */
@Repository("featureStandardMapper")
public interface ProjectFeatureMapper {

    /**
     * 根据工程特征主键ID查询工程特征信息
     * @param id
     * @return com.gcj.zblib.standardData.feature.entity.ProjectFeature
     * <AUTHOR>
     * @date 2021/10/21 20:54
     */
    ProjectFeature selectByPrimaryKey(Long id);

    /**
    　　* 根据专业id物理删除专业下携带的工程特征
    　　* @param  tradeId 专业编码
    　　* @return
    　　* <AUTHOR>
    　　* @date 2021/10/25 10:56
    　　*/
    void deleteByTradeId(Long tradeId);

    /**
    　　* 批量保存工程特征数据
    　　* @param   List<ProjectFeature> 需要保存的功能特征列表
    　　* @return
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/10/25 16:56
    　　*/
    void batchInsert(List<ProjectFeature> list);

    /**
     * 根据专业查询特征
     * @param tradeId
     * @return java.util.List<com.gcj.zblib.standardData.feature.entity.ProjectFeature>
     * <AUTHOR>
     * @date 2021/10/25 14:09
     */
    List<ProjectFeature> selectByTradeId(@Param("tradeId") Long tradeId, @Param("type") Integer type);

    /**
     * 根据id集合查询
     * @param featureIds
     * @param customerCode
     * @return java.util.List<com.gcj.zblib.standardData.feature.entity.ProjectFeature>
     * <AUTHOR>
     * @date 2021/10/25 14:09
     */
    List<ProjectFeature> selectByFeatureIdList(@Param("featureIds") List<Long> featureIds, @Param("customerCode") String customerCode,
                                               @Param("isShowNotUsing") Integer isShowNotUsing, @Param("type") Integer type);

    /**
     * 查询某企业的所有特征
     * @param customerCode
     * @return java.util.List<com.glodon.zbw.dataManager.standardData.category.domain.CommonProjCategory>
     * <AUTHOR>
     * @date 2021/10/19 14:53
     */
    List<ProjectFeature> selectAll(@Param("customerCode") String customerCode, @Param("type") Integer type);

    /**
     * 根据企业编码删除
     * @param customerCode
     * @return void
     * <AUTHOR>
     * @date 2021/11/9 10:42
     */
    void deleteByCustomerCode(String customerCode, Integer type);

    /**
     　　* @description: 工程特征批量保存
     　　* @param
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2022/2/11 18:10
     */
    void insertBatch(@Param("list") List<ProjectFeature> list);

    void updateBatch(@Param("list") List<ProjectFeature> list);

    /*
     * Description: <br>根据专业和type查询特征
     * @Param: [tradeId, type]
     * @Return: java.util.List<com.glodon.qydata.entity.standard.feature.ProjectFeature>
     * @Author: lisp-c
     * @Date: 2022/3/14 11:10
     * @version V1.0
     */
    List<ProjectFeature> selectByTradeIdAndType(@Param("tradeId") Long tradeId,@Param("type") Integer type);

    /**
     * @Description: 通过专业id集合获取工程特征, 若集合为空，则查询该企业所有专业的工程特征信息
     * @param customerCode
     * @param type
     * @param tradeIds
     * @return java.util.List<com.glodon.qydata.vo.standard.feature.ProjectFeatureResultVO>
     * @Author: zhangj-cl
     * @Date: 2022/12/8 17:13
     */
    List<ProjectFeatureResultVO> selectTradeProjectFeature(@Param("customerCode") String customerCode,
                                                           @Param("type") Integer type,
                                                           @Param("tradeIds") List<Long> tradeIds,
                                                           @Param("isNeedCategoryInfo") Integer isNeedCategoryInfo
                                                           );

    /**
     * 查询企业的是否有系统内置的副本
     * @param customerCode
     * @return int
     * <AUTHOR>
     * @date 2021/11/11 14:53
     */
    int selectInit(@Param("customerCode") String customerCode, @Param("type") Integer type);

    /**
     * 根据企业编码查询未删除的工程特征
     * @throws
     * @param customerCode
     * @param type
     * <AUTHOR>
     * @return {@link List< ProjectFeature>}
     * @date 2022/4/14 15:17
     */
    List<ProjectFeature> selectByCustomeCode(String customerCode, Integer type, Integer isShowDelete);

    List<ProjectFeature> selectNoBlobByCustomerCode(String customerCode, Integer type, Integer isShowDelete);

    /**
     * 查询企业对应的所有用户类型
     * @param customerCode
     * @return
     */
    List<Map> selectUserTypesAndOrderByCustomerCode(@Param("customerCode") String customerCode);

    /**
     * 查询所有的企业编码
     * @return
     */
    List<String> selectAllCustomerCode();

    /**
     * 查询所有的企业编码,包括已删除的
     * @return
     */
    Set<String> selectAllCustomerCodes();

    void batchUpdateSelective(@Param("list") List<ProjectFeature> list);

    List<ZbStandardsExpression> selectExpression(String customerCode, Integer type);
    /**
     * 查询同专业下【名称+类型】重复的数据
     * @return
     */
    List<ProjectFeature> selectRepeat(String customerCode);

    List<String> selectDistinctCustomerCode();

    /**
     * 查询专业下指定名称的特征项
     * @param customerCode
     * @param type
     * @param tradeIds
     * @param names
     * @return
     */
    List<ProjectFeature> selectTradeFeatureByCondition(@Param("customerCode") String customerCode,
                                                       @Param("type") Integer type,
                                                       @Param("tradeIds") List<Long> tradeIds,
                                                       @Param("names") List<String> names);
    Integer selectTradeFeatureMaxOrd(@Param("customerCode") String customerCode, @Param("type") Integer type, @Param("tradeId") Long tradeId);
}