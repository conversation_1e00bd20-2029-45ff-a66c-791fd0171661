package com.glodon.qydata.mapper.repairdata;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 专业修复mapper（非业务使用）
 */
public interface TempTradeMapper {
    Integer selectRepeatRecord(@Param("customerCode") String customerCode);
    Long selectValidTradeId(@Param("customerCode") String customerCode, @Param("tradeIds") List<Long> tradeIds);
    void setTradeInvalId(@Param("customerCode") String customerCode, @Param("ids") List<Long> ids);
    void setFeatureInvalid(@Param("customerCode") String customerCode, @Param("tradeIds") List<Long> tradeIds);
    void setSelfFeatureInvalid(@Param("customerCode") String customerCode, @Param("tradeIds") List<Long> tradeIds);
    void setCategoryViewInvalid(@Param("customerCode") String customerCode, @Param("tradeIds") List<Long> tradeIds);
    void setSelfCategoryViewInvalid(@Param("customerCode") String customerCode, @Param("tradeIds") List<Long> tradeIds);
    String selectValidFeature(@Param("customerCode") String customerCode);
    String selectValidFeatureCategoryView(@Param("customerCode") String customerCode);
    List<Long> selectGlobalIds(@Param("customerCode") String customerCode);
}
