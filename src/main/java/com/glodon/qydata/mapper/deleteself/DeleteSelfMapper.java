package com.glodon.qydata.mapper.deleteself;

import com.glodon.qydata.entity.deleteself.SelfEntity;
import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard;
import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.entity.standard.expression.ZbStandardsExpression;
import com.glodon.qydata.entity.standard.feature.ProjectFeature;
import com.glodon.qydata.entity.standard.mainQuantity.ZbStandardsMainQuantity;
import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 数据修复公共mapper（非业务使用）
 */
@Mapper
public interface DeleteSelfMapper {

    List<SelfEntity> selectCategoryAll(@Param("customerCode") String customerCode);

    void deleteCategory(@Param("globalIds") List<String> globalIds);

    List<SelfEntity> selectProjectOrContractAll(@Param("type") int type, @Param("customerCode") String customerCode);

    void deleteProjectOrContract(@Param("globalIds") List<String> globalIds, @Param("type") int type);

    List<SelfEntity> selectFeatureAll(@Param("customerCode") String customerCode);

    void deleteFeature(@Param("globalIds") List<String> globalIds);

    void deleteFeatureView(@Param("globalIds") List<String> globalIds);

    List<SelfEntity> selectBuildStandardAll(@Param("customerCode") String customerCode);

    List<Long> selectBuildStandardId(@Param("tempGlobalIds") List<String> tempGlobalIds);

    void deleteBuildStandardDetailDesc(@Param("ids") List<Long> ids);

    void deleteBuildStandardDetail(@Param("ids") List<Long> ids);

    void deleteBuildStandardCategory(@Param("ids") List<Long> ids);

    void deleteBuildStandardPositionDetail(@Param("ids") List<Long> ids);

    void deleteBuildStandardPosition(@Param("ids") List<Long> ids);

    void deleteBuildStandard(@Param("globalIds") List<String> globalIds);

    List<SelfEntity> selectMainQuantityAll(@Param("customerCode") String customerCode);

    void deleteMainQuantity(@Param("globalIds") List<String> globalIds);

    List<SelfEntity> selectExpressionAll(@Param("customerCode") String customerCode);

    void deleteExpression(@Param("globalIds") List<String> globalIds);
}
