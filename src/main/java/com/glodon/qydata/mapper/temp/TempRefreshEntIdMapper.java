package com.glodon.qydata.mapper.temp;

import com.glodon.qydata.controller.temp.refreshEnterpriseId.TempSysQyCodeIdVerify;
import com.glodon.qydata.controller.temp.refreshEnterpriseId.TempSysZbUser;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @className: SysZbUserMapper
 * @description: 用户
 * @author: niecw-a
 * @date: 2023/7/25
 **/
@Repository
public interface TempRefreshEntIdMapper {

    /**
     * @description: 查找所有
     * <AUTHOR>
     * @date 2023/7/25 16:59
     */
    List<TempSysZbUser> selectAll();

    /**
     * @description: 查找企业id为空
     * <AUTHOR>
     * @date 2023/7/25 16:59
     */
    List<TempSysZbUser> selectByEntIdIsNull();

    /**
     * @description: 查找企业id为空
     * <AUTHOR>
     * @date 2023/7/25 16:59
     */
    void batchInsertOther(List<TempSysZbUser> list);

    /**
     * @description: 查找所有 tb_commonprojcategory_standards
     * <AUTHOR>
     * @date 2023/8/15 16:59
     * @param table
     * @param qyCodeName
     */
    List<String> selectQYCodeFromTable(@Param("table")String table, @Param("qyCodeName")String qyCodeName);

    /**
     * @description: 按企业更新 tb_commonprojcategory_standards
     * <AUTHOR>
     * @date 2023/8/15 16:59
     */
    void updateTableNewQYCode(@Param("table")String table, @Param("qyCodeName")String qyCodeName, @Param("qyCodeValue")String qyCodeValue);

    /**
     * @description: 查找验证企业id
     * <AUTHOR>
     * @date 2023/7/25 16:59
     */
    List<TempSysQyCodeIdVerify> selectVerifyEntId();

    /**
     * @description: 插入验证临时表
     * <AUTHOR>
     * @date 2023/7/25 16:59
     */
    void batchInsertOtherVerify(List<TempSysQyCodeIdVerify> list);

}
