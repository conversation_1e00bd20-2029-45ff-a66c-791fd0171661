package com.glodon.qydata.mapper.sharding;

import com.glodon.qydata.entity.sharding.ShardingRoute;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: luoml-b
 * @date: 2023/7/14 10:46
 * @description:
 */
@Repository
public interface ShardingRouteMapper {

    ShardingRoute getTableNameByCustomerCode(@Param("customerCode") String customerCode,
                                             @Param("tableType") int tableType);

    int insertBatch(List<ShardingRoute> shardingRoute);

    int insert(ShardingRoute shardingRoute);
}
