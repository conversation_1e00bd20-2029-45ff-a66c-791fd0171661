package com.glodon.qydata.mapper.system;

import com.glodon.qydata.service.system.DataCleanupService.TableSpaceInfo;
import com.glodon.qydata.service.system.DataCleanupService.QyCodeGroupStats;
import com.glodon.qydata.service.system.DataCleanupService.RecordIdInfo;
import com.glodon.qydata.service.system.DataCleanupService.CustomerCodeGroupStats;
import com.glodon.qydata.service.system.DataCleanupService.CustomerCodeRecordIdInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 数据清理Mapper
 * 用于数据清理相关的数据库操作
 */
@Repository
public interface DataCleanupMapper {

    /**
     * 统计表记录数量
     * 
     * @param tableName 表名
     * @return 记录数量
     */
    Long countTableRecords(@Param("tableName") String tableName);



    /**
     * 按qy_code分组统计记录数
     * 
     * @param tableName 表名
     * @return qy_code分组统计结果
     */
    List<QyCodeGroupStats> getQyCodeGroupStats(@Param("tableName") String tableName);

    /**
     * 收集指定qy_code的所有记录ID
     *
     * @param tableName 表名
     * @param qyCode 企业编码
     * @return 记录ID列表
     */
    List<RecordIdInfo> collectRecordIdsByQyCode(@Param("tableName") String tableName, @Param("qyCode") String qyCode);

    /**
     * 批量收集所有qy_code的记录ID（优化版）
     *
     * @param tableName 表名
     * @return qy_code和记录ID的映射列表
     */
    List<RecordIdInfo> collectAllRecordIds(@Param("tableName") String tableName);

    /**
     * 删除指定qy_code的记录（物理删除）
     * 
     * @param tableName 表名
     * @param qyCode 企业编码
     * @return 删除的记录数
     */
    int deleteRecordsByQyCode(@Param("tableName") String tableName, @Param("qyCode") String qyCode);

    /**
     * 软删除指定qy_code的记录
     * 
     * @param tableName 表名
     * @param qyCode 企业编码
     * @return 更新的记录数
     */
    int softDeleteRecordsByQyCode(@Param("tableName") String tableName, @Param("qyCode") String qyCode);

    /**
     * 批量删除指定qy_code的记录（物理删除）
     * 
     * @param tableName 表名
     * @param qyCodes 企业编码列表
     * @return 删除的记录数
     */
    int batchDeleteRecordsByQyCodes(@Param("tableName") String tableName, @Param("qyCodes") List<String> qyCodes);

    /**
     * 批量软删除指定qy_code的记录
     * 
     * @param tableName 表名
     * @param qyCodes 企业编码列表
     * @return 更新的记录数
     */
    int batchSoftDeleteRecordsByQyCodes(@Param("tableName") String tableName, @Param("qyCodes") List<String> qyCodes);

    /**
     * 获取数据库基本信息
     * 
     * @return 数据库信息
     */
    Map<String, Object> getDatabaseInfo();

    /**
     * 测试数据库连接
     * 
     * @return 测试结果
     */
    Integer testConnection();

    /**
     * 获取指定表的记录数统计（按条件）
     * 
     * @param tableName 表名
     * @param whereCondition WHERE条件
     * @return 记录数
     */
    Long countTableRecordsWithCondition(@Param("tableName") String tableName, @Param("whereCondition") String whereCondition);

    /**
     * 获取所有不同的qy_code
     * 
     * @param tableName 表名
     * @return qy_code列表
     */
    List<String> getDistinctQyCodes(@Param("tableName") String tableName);

    /**
     * 获取指定qy_code的记录详情
     * 
     * @param tableName 表名
     * @param qyCode 企业编码
     * @param limit 限制条数
     * @return 记录详情列表
     */
    List<Map<String, Object>> getRecordDetailsByQyCode(@Param("tableName") String tableName, 
                                                       @Param("qyCode") String qyCode, 
                                                       @Param("limit") Integer limit);

    /**
     * 验证表是否存在
     *
     * @param tableName 表名
     * @return 是否存在（1存在，0不存在）
     */
    Integer checkTableExists(@Param("tableName") String tableName);

    // ==================== 新增：支持 customer_code 字段的方法 ====================

    /**
     * 按customer_code分组统计记录数
     *
     * @param tableName 表名
     * @return customer_code分组统计结果
     */
    List<CustomerCodeGroupStats> getCustomerCodeGroupStats(@Param("tableName") String tableName);

    /**
     * 收集指定customer_code的所有记录ID
     *
     * @param tableName 表名
     * @param customerCode 企业编码
     * @return 记录ID列表
     */
    List<CustomerCodeRecordIdInfo> collectRecordIdsByCustomerCode(@Param("tableName") String tableName, @Param("customerCode") String customerCode);

    /**
     * 批量收集所有customer_code的记录ID（优化版）
     *
     * @param tableName 表名
     * @return customer_code和记录ID的映射列表
     */
    List<CustomerCodeRecordIdInfo> collectAllRecordIdsByCustomerCode(@Param("tableName") String tableName);

    /**
     * 删除指定customer_code的记录（物理删除）
     *
     * @param tableName 表名
     * @param customerCode 企业编码
     * @return 删除的记录数
     */
    int deleteRecordsByCustomerCode(@Param("tableName") String tableName, @Param("customerCode") String customerCode);

    /**
     * 软删除指定customer_code的记录
     *
     * @param tableName 表名
     * @param customerCode 企业编码
     * @return 更新的记录数
     */
    int softDeleteRecordsByCustomerCode(@Param("tableName") String tableName, @Param("customerCode") String customerCode);

    /**
     * 批量删除指定customer_code的记录（物理删除）
     *
     * @param tableName 表名
     * @param customerCodes 企业编码列表
     * @return 删除的记录数
     */
    int batchDeleteRecordsByCustomerCodes(@Param("tableName") String tableName, @Param("customerCodes") List<String> customerCodes);

    /**
     * 批量软删除指定customer_code的记录
     *
     * @param tableName 表名
     * @param customerCodes 企业编码列表
     * @return 更新的记录数
     */
    int batchSoftDeleteRecordsByCustomerCodes(@Param("tableName") String tableName, @Param("customerCodes") List<String> customerCodes);

    /**
     * 获取所有不同的customer_code
     *
     * @param tableName 表名
     * @return customer_code列表
     */
    List<String> getDistinctCustomerCodes(@Param("tableName") String tableName);

    /**
     * 获取指定customer_code的记录详情
     *
     * @param tableName 表名
     * @param customerCode 企业编码
     * @param limit 限制条数
     * @return 记录详情列表
     */
    List<Map<String, Object>> getRecordDetailsByCustomerCode(@Param("tableName") String tableName,
                                                             @Param("customerCode") String customerCode,
                                                             @Param("limit") Integer limit);

    // ==================== 新增：支持 zb_standards_build_standard 表组的方法 ====================

    /**
     * 获取建设标准表的 qy_code 分组统计（特殊处理，因为该表使用 customer_code 字段）
     *
     * @param tableName 表名
     * @return qy_code分组统计结果
     */
    List<QyCodeGroupStats> getBuildStandardQyCodeGroupStats(@Param("tableName") String tableName);

    /**
     * 收集建设标准表的所有记录ID（基于 customer_code 字段）
     *
     * @param tableName 表名
     * @return 记录ID列表
     */
    List<RecordIdInfo> collectAllBuildStandardRecordIds(@Param("tableName") String tableName);

    /**
     * 收集建设标准表指定qy_code的记录ID（基于 customer_code 字段）
     *
     * @param tableName 表名
     * @param qyCode 企业编码
     * @return 记录ID列表
     */
    List<RecordIdInfo> collectBuildStandardRecordIdsByQyCode(@Param("tableName") String tableName, @Param("qyCode") String qyCode);

    /**
     * 删除建设标准主表指定qy_code的记录（基于 customer_code 字段）
     *
     * @param tableName 表名
     * @param qyCode 企业编码
     * @return 删除的记录数
     */
    int deleteBuildStandardRecordsByQyCode(@Param("tableName") String tableName, @Param("qyCode") String qyCode);

    /**
     * 删除建设标准子表指定qy_code的记录（通过 standard_id 关联）
     *
     * @param tableName 表名
     * @param qyCode 企业编码
     * @return 删除的记录数
     */
    int deleteBuildStandardDetailRecordsByQyCode(@Param("tableName") String tableName, @Param("qyCode") String qyCode);

    // ==================== 新增：基于ID列表的批量删除方法 ====================

    /**
     * 收集建设标准主表中需要删除的记录ID
     *
     * @param qyCode 企业编码
     * @return 主表记录ID列表
     */
    List<Long> collectBuildStandardMainTableIdsByQyCode(@Param("qyCode") String qyCode);

    /**
     * 根据主表ID列表删除子表记录
     *
     * @param tableName 表名
     * @param mainTableIds 主表ID列表
     * @return 删除的记录数
     */
    int deleteBuildStandardDetailRecordsByIds(@Param("tableName") String tableName, @Param("mainTableIds") List<Long> mainTableIds);

    /**
     * 根据ID列表删除主表记录
     *
     * @param mainTableIds 主表ID列表
     * @return 删除的记录数
     */
    int deleteBuildStandardMainRecordsByIds(@Param("mainTableIds") List<Long> mainTableIds);


}
