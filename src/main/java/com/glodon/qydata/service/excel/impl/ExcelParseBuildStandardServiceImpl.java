package com.glodon.qydata.service.excel.impl;


import com.glodon.qydata.common.RequestContent;
import com.glodon.qydata.common.constant.BusinessConstants;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.constant.OperateConstants;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandardDetail;
import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildStandardDetailDesc;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.service.excel.ExcelParseService;
import com.glodon.qydata.service.excel.ExcelParseUtil;
import com.glodon.qydata.util.FormatConvertUtil;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;

import static com.glodon.qydata.common.constant.BusinessConstants.SELECT_LIST_TO_JSON;

/**
 * <AUTHOR>
 */
@Service("excelParseBuildStandardServiceImpl")
public class ExcelParseBuildStandardServiceImpl implements ExcelParseService {
    @Resource
    private ExcelSaveService excelSaveService;

    @Override
    public String modelType() {
        return OperateConstants.BUILDING;
    }

    /**
     * 解析excel数据并保存到数据库
     *
     * @date 2024/12/6
     */
    @Override
    public String parse(List<List<String>> excelData, Map<String, String> param) {
        try {
            parseExcel(excelData, param);
        } catch (BusinessException e) {
            return e.getMessage();
        }
        return "";
    }

    public void parseExcel(List<List<String>> excelData, Map<String, String> param) {
        if (param.get(BusinessConstants.STANDARD_ID) == null) {
            throw new BusinessException(ResponseCode.PARAMETER_ERROR);
        }
        String customerCode = RequestContent.getCustomerCode();
        Long standardId = Long.parseLong(param.get(BusinessConstants.STANDARD_ID));

        String parentCode = null;
        String parentName = null;
        Long parentDetailId = null;
        int detailOrd = 1;
        int descIndex = 1;
        Set<String> uniqueCodes = new HashSet<>();
        Set<String> formattedCodes = new HashSet<>();
        Set<String> uniqueDescNames = new HashSet<>();
        Date currDate = new Date();
        List<ZbProjectStandardDetail> detailList = new ArrayList<>();
        List<ZbStandardsBuildStandardDetailDesc> descList = new ArrayList<>();

        for (List<String> row : excelData) {
            String code = row.get(1).trim();
            String name = row.get(2).trim();
            String descName = row.get(3).trim();
            String fieldType = row.get(4).trim();
            String option = row.get(5).trim();
            String remark = row.get(6).trim();

            if (ExcelParseUtil.isRowEmpty(row, new int[]{1, 2, 3, 4, 5, 6}) || uniqueCodes.contains(code)) {
                continue;
            }

            if (StringUtils.isNotBlank(code) && StringUtils.isNotBlank(name)) {
                String formattedCode;
                try {
                    formattedCode = FormatConvertUtil.convertToFormattedCode(code);
                } catch (Exception e) {
                    throw new BusinessException(ResponseCode.EXCEL_BUILD_STANDARD_CODE_ERROR);
                }
                if (name.length() > Constants.STANDARD_MAX_NAME_LENGTH) {
                    throw new BusinessException(ResponseCode.EXCEL_BUILD_STANDARD_NAME_LENGTH_ERROR);
                }
                ExcelParseUtil.validateRemark(remark);

                Long detailId = SnowflakeIdUtils.getNextId();
                detailList.add(createDetail(detailId, standardId, formattedCode, name, remark, detailOrd++, currDate));
                parentCode = formattedCode;
                parentName = name;
                parentDetailId = detailId;
                descIndex = 1;
                uniqueDescNames.clear();
                uniqueCodes.add(code);
                formattedCodes.add(formattedCode);
            } else if(StringUtils.isBlank(code) && StringUtils.isBlank(name)){
                if (StringUtils.isBlank(parentCode) || StringUtils.isBlank(parentName)) {
                    throw new BusinessException(ResponseCode.EXCEL_BUILD_STANDARD_CODE_ERROR);
                }
            }else {
                throw new BusinessException(ResponseCode.EXCEL_BUILD_STANDARD_CODE_ERROR);
            }

            if (ExcelParseUtil.isRowEmpty(row, new int[]{3, 4, 5}) || uniqueDescNames.contains(descName)) {
                continue;
            }

            if (StringUtils.isNotBlank(descName)) {
                if (descName.length() > Constants.MAX_DESC_LENGTH) {
                    throw new BusinessException(ResponseCode.EXCEL_BUILD_STANDARD_DESC_LENGTH_ERROR);
                }
                String typeCode = ExcelParseUtil.getTypeCodeNoDate(fieldType);

                option = ExcelParseUtil.validateOption(typeCode, option, modelType());
                descList.add(createDesc(standardId, parentDetailId, descName, typeCode, option, descIndex++, currDate));
                uniqueDescNames.add(descName);
            }
        }

        checkCodeRelation(detailList, formattedCodes);
        checkNameDuplicate(detailList, descList);

        excelSaveService.saveData(detailList, descList, customerCode, standardId);
    }

    private ZbProjectStandardDetail createDetail(Long detailId, Long standardId, String levelCode, String name, String remark, int detailOrd, Date currDate) {
        ZbProjectStandardDetail detail = new ZbProjectStandardDetail();
        detail.setId(detailId);
        detail.setStandardId(standardId);
        detail.setName(name);
        detail.setLevelcode(levelCode);
        detail.setLevel(levelCode.length() / 3);
        detail.setRemark(remark);
        detail.setOrd(detailOrd);
        detail.setCreateTime(currDate);
        return detail;
    }

    private ZbStandardsBuildStandardDetailDesc createDesc(Long standardId, Long detailId, String descName, String typeCode, String option, int descIndex, Date currDate) {
        ZbStandardsBuildStandardDetailDesc desc = new ZbStandardsBuildStandardDetailDesc();
        desc.setId(SnowflakeIdUtils.getNextId());
        desc.setStandardId(standardId);
        desc.setStandardDetailId(detailId);
        desc.setName(Constants.DESC_PREFIX + descIndex);
        desc.setDetailDesc(descName);
        desc.setCode(FormatConvertUtil.formatSegment(descIndex));
        desc.setOrd(descIndex);
        desc.setCreateTime(currDate);
        desc.setTypeCode(typeCode);
        if (ExcelParseUtil.isSelectType(typeCode) && StringUtils.isNotBlank(option)) {
            desc.setSelectList(FormatConvertUtil.convertSelectList(option, SELECT_LIST_TO_JSON));
        }
        return desc;
    }

    private void checkCodeRelation(List<ZbProjectStandardDetail> detailList, Set<String> uniqueCodes) {
        boolean hasInvalidParent = detailList.stream()
                .anyMatch(detail -> !ExcelParseUtil.hasValidParent(detail.getLevelcode(), detail.getLevel(), uniqueCodes));

        if (hasInvalidParent) {
            throw new BusinessException(ResponseCode.EXCEL_BUILD_STANDARD_CODE_ERROR);
        }
    }

    private void checkNameDuplicate(List<ZbProjectStandardDetail> detailList, List<ZbStandardsBuildStandardDetailDesc> descList) {
        Map<String, Set<String>> parentToChildNamesMap = new HashMap<>();
        Set<Long> removedIds = new HashSet<>(); // 用于记录剔除的 ID

        Iterator<ZbProjectStandardDetail> iterator = detailList.iterator();

        while (iterator.hasNext()) {
            ZbProjectStandardDetail detail = iterator.next();
            String levelCode = detail.getLevelcode();
            String name = detail.getName();
            Long id = detail.getId();

            String parentCode = levelCode.length() > 3 ? levelCode.substring(0, levelCode.length() - 3) : "-1";

            // 初始化名称集合并检查是否重复
            Set<String> names = parentToChildNamesMap.computeIfAbsent(parentCode, k -> new HashSet<>());
            if (!names.add(name)) {
                removedIds.add(id);
                iterator.remove();
            }
        }

        // 从 descList 中剔除对应的 ID
        descList.removeIf(desc -> removedIds.contains(desc.getStandardDetailId()));
    }

}
