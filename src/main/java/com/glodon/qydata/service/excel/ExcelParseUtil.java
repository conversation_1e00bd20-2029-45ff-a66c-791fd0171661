package com.glodon.qydata.service.excel;

import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.enums.ExpressionTypeEnum;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.util.FormatConvertUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Set;

import static com.glodon.qydata.common.constant.Constants.ZbExpressionConstants.*;

/**
 * @Class com.glodon.qydata.service.excel ExcelParseUtil
 * <AUTHOR>
 * @Email <EMAIL>
 * @Description 公共方法
 * @Date 11:34 2022/7/6
 **/
public class ExcelParseUtil {
    /***
     * @description: 类型转code
     * @param typeStr 1
     * @return java.lang.String
     * @throws
     * <AUTHOR>
     * @date 2022/7/7 11:01
     */
    public static String getTypeCode(String typeStr) {
        if (StringUtils.isBlank(typeStr)) {
            return ExpressionTypeEnum.TYPE_TEXT.getCode();
        }
        switch (typeStr) {
            case "数值":
                return ExpressionTypeEnum.TYPE_NUMBER.getCode();
            case "日期":
                return ExpressionTypeEnum.TYPE_DATE.getCode();
            case "单选":
                return ExpressionTypeEnum.TYPE_SELECT.getCode();
            case "多选":
                return ExpressionTypeEnum.TYPE_SELECTS.getCode();
            default:
                return ExpressionTypeEnum.TYPE_TEXT.getCode();
        }
    }

    public static String getTypeCodeNoDate(String typeStr) {
        if (StringUtils.isBlank(typeStr)) {
            return ExpressionTypeEnum.TYPE_TEXT.getCode();
        }
        switch (typeStr) {
            case "数值":
                return ExpressionTypeEnum.TYPE_NUMBER.getCode();
            case "单选":
                return ExpressionTypeEnum.TYPE_SELECT.getCode();
            case "多选":
                return ExpressionTypeEnum.TYPE_SELECTS.getCode();
            default:
                return ExpressionTypeEnum.TYPE_TEXT.getCode();
        }
    }

    public static boolean isRowEmpty(List<String> row, int[] columnsToCheck) {
        // 检查指定列是否都是空
        for (int column : columnsToCheck) {
            // 如果有列的索引超出 row 的范围，认为该行有数据而不为空
            if (column >= row.size() || !row.get(column).trim().isEmpty()) {
                return false; // 只要有一个列不空，或者超出范围，返回 false
            }
        }
        return true; // 所有指定列都是空
    }

    public static boolean isNumberType(String typeCode) {
        return TYPE_NUMBER.equals(typeCode);
    }

    public static boolean isSelectType(String typeCode) {
        return TYPE_SELECT.equals(typeCode) || TYPE_SELECTS.equals(typeCode);
    }

    public static String getRemark(String remark) {
        return StringUtils.isBlank(remark) ? "excel导入" : remark + "|excel导入";
    }

    public static boolean hasValidParent(String code, int level, Set<String> validCodes) {
        StringBuilder keyBuilder = new StringBuilder();

        for (int i = 0; i < level * 3; i += 3) {
            String segment = code.substring(i, Math.min(i + 3, code.length()));
            keyBuilder.append(segment);

            if (!validCodes.contains(keyBuilder.toString())) {
                return false;
            }
        }

        return true;
    }

    /**
     * 校验名称的有效性
     *
     * @param name 输入的名称
     * @throws BusinessException 如果校验不通过
     */
    public static void validateName(String name) throws BusinessException {
        if (name.isEmpty()) {
            throw new BusinessException(ResponseCode.EXCEL_FORMAT_ERROR);
        }
        if (name.length() > Constants.MAX_NAME_LENGTH) {
            throw new BusinessException(ResponseCode.EXCEL_NAME_LENGTH_ERROR);
        }
    }

    /**
     * 校验备注的有效性
     *
     * @param remark 输入的备注
     * @throws BusinessException 如果校验不通过
     */
    public static void validateRemark(String remark) throws BusinessException {
        if (remark.length() > Constants.MAX_REMARK_LENGTH) {
            throw new BusinessException(ResponseCode.EXCEL_REMARK_LENGTH_ERROR);
        }
    }

    /**
     * 校验单位的有效性
     *
     * @param typeCode 输入的类型代码
     * @param unit 输入的单位
     * @throws BusinessException 如果校验不通过
     */
    public static void validateUnit(String typeCode, String unit) throws BusinessException {
        if (ExcelParseUtil.isNumberType(typeCode) && unit.length() > Constants.MAX_UNIT_LENGTH) {
            throw new BusinessException(ResponseCode.EXCEL_UNIT_LENGTH_ERROR);
        }
    }

    /**
     * 校验选项的有效性
     *
     * @param typeCode 输入的类型代码
     * @param option 输入的选项
     * @throws BusinessException 如果校验不通过
     */
    public static String validateOption(String typeCode, String option, String type) throws BusinessException {
        if (ExcelParseUtil.isSelectType(typeCode)) {
            return FormatConvertUtil.validateAndDeduplicate(option, type);
        }
        return option; // 如果不是选项类型，返回原值
    }
}
