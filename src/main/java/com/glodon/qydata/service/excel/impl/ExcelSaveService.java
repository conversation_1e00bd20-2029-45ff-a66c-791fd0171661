package com.glodon.qydata.service.excel.impl;


import cn.hutool.core.collection.CollUtil;
import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard;
import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandardDetail;
import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildStandardDetailDesc;
import com.glodon.qydata.entity.standard.feature.ProjectFeature;
import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.standard.buildStandard.ZbProjectStandardDetailMapper;
import com.glodon.qydata.mapper.standard.buildStandard.ZbProjectStandardMapper;
import com.glodon.qydata.mapper.standard.buildStandard.ZbStandardsBuildPositionDetailMapper;
import com.glodon.qydata.mapper.standard.buildStandard.ZbStandardsBuildStandardDetailDescMapper;
import com.glodon.qydata.service.standard.feature.IProjectFeatureSelfService;
import com.glodon.qydata.service.standard.projectOrContractInfo.IStandardsProjectInfoService;
import com.glodon.qydata.service.standard.unit.IZbUnitService;
import com.glodon.qydata.vo.standard.feature.ProjectFeatureAddVO;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.glodon.qydata.common.constant.Constants.StandardsProjectOrContractInfoConstants.PROJECT_INFO;

@Service
public class ExcelSaveService {
    @Resource
    private IStandardsProjectInfoService standardsProjectInfoServiceImpl;
    @Resource
    private IProjectFeatureSelfService projectFeatureSelfService;
    @Resource
    private IZbUnitService zbUnitService;
    @Resource
    private ZbProjectStandardMapper zbProjectStandardMapper;
    @Resource
    private ZbProjectStandardDetailMapper projectStandardDetailMapper;
    @Resource
    private ZbStandardsBuildStandardDetailDescMapper standardsBuildStandardDetailDescMapper;
    @Resource
    private ZbStandardsBuildPositionDetailMapper standardsBuildPositionDetailMapper;

    @Transactional(rollbackFor = Exception.class)
    public void saveData(List<StandardsProjectInfoEntity> entityList, Integer standardDataType, String globalId, String customerCode) {
        if (CollUtil.isEmpty(entityList) || standardDataType == null || StringUtils.isBlank(globalId) || StringUtils.isBlank(customerCode)) {
            return;
        }
        // 先插入数据
        List<StandardsProjectInfoEntity> insertedList = standardsProjectInfoServiceImpl.addInfoDataList(entityList, standardDataType, globalId, customerCode);
        // 再插入单位信息
        if (PROJECT_INFO.equals(standardDataType)) {
            List<String> unitList = insertedList.stream().map(StandardsProjectInfoEntity::getUnit).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            zbUnitService.batchAddUnitIfNotExist(unitList, globalId, customerCode);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveData(List<ProjectFeatureAddVO> entityList, String globalId, String customerCode, Long tradeId) {
        if (CollUtil.isEmpty(entityList) || StringUtils.isBlank(globalId) || StringUtils.isBlank(customerCode) || tradeId == null) {
            return;
        }
        // 先插入数据
        List<ProjectFeature> insertedList = projectFeatureSelfService.addList(entityList, tradeId, customerCode);
        // 再插入单位信息
        List<String> unitList = insertedList.stream().map(ProjectFeature::getUnit).distinct().collect(Collectors.toList());
        zbUnitService.batchAddUnitIfNotExist(unitList, globalId, customerCode);
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveData(List<ZbProjectStandardDetail> detailList, List<ZbStandardsBuildStandardDetailDesc> descList, String customerCode, Long standardId) {
        if ((CollUtil.isEmpty(detailList) && CollUtil.isEmpty(descList)) || StringUtils.isBlank(customerCode) || standardId == null) {
            return;
        }

        ZbProjectStandard standard = zbProjectStandardMapper.selectSelfStandard(customerCode, standardId);
        if (standard == null) {
            throw new BusinessException("标准不存在");
        }

        // 删除旧数据
        deleteOldData(standardId);

        // 保存新数据
        saveNewData(detailList, descList);
    }

    private void deleteOldData(Long standardId) {
        List<Long> standardIds = Collections.singletonList(standardId);
        projectStandardDetailMapper.deleteSelfByStandardIds(standardIds);
        standardsBuildStandardDetailDescMapper.batchDelSelfByStandardIds(standardIds);
        standardsBuildPositionDetailMapper.delSelfByStandardIds(standardIds);
    }

    private void saveNewData(List<ZbProjectStandardDetail> detailList, List<ZbStandardsBuildStandardDetailDesc> descList) {
        if (CollUtil.isNotEmpty(detailList)) {
            projectStandardDetailMapper.saveSelfBatch(detailList);
        }
        if (CollUtil.isNotEmpty(descList)) {
            standardsBuildStandardDetailDescMapper.batchSaveSelf(descList);
        }
    }
}
