package com.glodon.qydata.service.excel;


import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * @Class com.glodon.qydata.service.excel ExcelOutAndInService
 * <AUTHOR>
 * @Email <EMAIL>
 * @Description 导入导出相关
 * @Date 11:34 2022/7/6
 **/
public interface ExcelOutAndInService {
    /***
     * @description: 下载模板
     * @param type 1
     * @return
     * @throws
     * <AUTHOR>
     * @date 2022/7/6 11:35
     */
    void getTemplate(HttpServletResponse response, String type) throws IOException;

    /***
     * @description: excel导入
     * @param file 1
     * @param param 2
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2022/7/6 15:13
     */
    String excelInsert(String type, MultipartFile file, Map<String, String> param);
}
