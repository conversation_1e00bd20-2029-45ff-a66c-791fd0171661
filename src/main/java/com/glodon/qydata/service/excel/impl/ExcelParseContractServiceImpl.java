package com.glodon.qydata.service.excel.impl;


import com.glodon.qydata.common.RequestContent;
import com.glodon.qydata.common.constant.OperateConstants;
import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.service.excel.ExcelParseService;
import com.glodon.qydata.service.excel.ExcelParseUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;

import static com.glodon.qydata.common.constant.Constants.StandardsProjectOrContractInfoConstants.CONTRACT_INFO;

/**
 * @Class com.glodon.qydata.service.excel ExcelOutAndInService
 * <AUTHOR>
 * @Email <EMAIL>
 * @Description 解析excel
 * @Date 11:34 2022/7/6
 **/
@Service("excelParseContractServiceImpl")
public class ExcelParseContractServiceImpl implements ExcelParseService {
    @Resource
    private ExcelSaveService excelSaveService;

    @Override
    public String modelType() {
        return OperateConstants.CONTRACT;
    }

    /***
     * @description: 解析excel数据并保存到数据库
     * @param excelData 1
     * @return
     * @throws
     * <AUTHOR>
     * @date 2022/7/6 11:35
     */
    @Override
    public String parse(List<List<String>> excelData, Map<String, String> param) {
        try {
            parseExcel(excelData, param);
        } catch (BusinessException e) {
            return e.getMessage();
        }
        return "";
    }

    public void parseExcel(List<List<String>> excelData, Map<String, String> params) {
        String globalId = RequestContent.getGlobalId();
        String customerCode = RequestContent.getCustomerCode();
        Set<String> uniqueNames = new HashSet<>();
        List<StandardsProjectInfoEntity> newEntities = new ArrayList<>();

        for (List<String> row : excelData) {
            String name = row.get(1).trim();
            String fieldType = row.get(2).trim();
            String option = row.get(3).trim();
            String remark = row.get(4).trim();

            String typeCode = ExcelParseUtil.getTypeCode(fieldType);
            remark = ExcelParseUtil.getRemark(remark);

            if (ExcelParseUtil.isRowEmpty(row, new int[]{1, 2, 3, 4}) || uniqueNames.contains(name)) {
                continue;
            }

            ExcelParseUtil.validateName(name);
            ExcelParseUtil.validateRemark(remark);
            option = ExcelParseUtil.validateOption(typeCode, option, modelType());

            StandardsProjectInfoEntity newEntity = createEntity(name, typeCode, option, remark);
            newEntities.add(newEntity);
            uniqueNames.add(name);
        }

        excelSaveService.saveData(newEntities, CONTRACT_INFO, globalId, customerCode);
    }

    private StandardsProjectInfoEntity createEntity(String name, String typeCode, String option, String remark) {
        StandardsProjectInfoEntity entity = new StandardsProjectInfoEntity();
        entity.setIsRequired(0);
        entity.setIsUsing(1);
        entity.setName(name);
        entity.setRemark(remark);
        entity.setStandardDataType(CONTRACT_INFO);
        entity.setTypeCode(typeCode);
        if (ExcelParseUtil.isSelectType(typeCode) && StringUtils.isNotBlank(option)) {
            entity.setSelectList(option);
        }
        return entity;
    }
}
