package com.glodon.qydata.service.excel;


import com.glodon.qydata.common.constant.OperateConstants;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Class com.glodon.qydata.service.excel ExcelOutAndInService
 * <AUTHOR>
 * @Email <EMAIL>
 * @Description 策略注入
 * @Date 11:34 2022/7/6
 **/
@Component
public class ExcelParseStrategyContext {
    public static Map<String, String> pathMapper = new HashMap<>();
    public static Map<String, ExcelParseService> pathInstance = new HashMap<>();
    public static final Map<String, String[]> HEADER_MAP = new HashMap<>();
    @Resource(name = "excelParseCategoryServiceImpl")
    private ExcelParseService excelParseCategoryServiceImpl;
    @Resource(name = "excelParseProjectServiceImpl")
    private ExcelParseService excelParseProjectServiceImpl;
    @Resource(name = "excelParseContractServiceImpl")
    private ExcelParseService excelParseContractServiceImpl;
    @Resource(name = "excelParseZbfeatureServiceImpl")
    private ExcelParseService excelParseZbfeatureServiceImpl;
    @Resource(name = "excelParseBuildStandardServiceImpl")
    private ExcelParseService excelParseBuildStandardServiceImpl;

    static {
        pathMapper.put(OperateConstants.CATEGORY, "工程分类-Excel模板.xlsx");
        pathMapper.put(OperateConstants.PROJECT, "项目信息-Excel模板.xlsx");
        pathMapper.put(OperateConstants.CONTRACT, "合同信息-Excel模板.xlsx");
        pathMapper.put(OperateConstants.FEATURE, "工程特征-Excel模板 .xlsx");
        pathMapper.put(OperateConstants.BUILDING, "建造标准-Excel模板.xlsx");

        HEADER_MAP.put(OperateConstants.CATEGORY, new String[]{"序号", "编码", "分类名称", "备注"});
        HEADER_MAP.put(OperateConstants.PROJECT, new String[]{"序号", "名称", "数据类型", "单位", "枚举值", "备注"});
        HEADER_MAP.put(OperateConstants.CONTRACT, new String[]{"序号", "名称", "数据类型", "枚举值", "备注"});
        HEADER_MAP.put(OperateConstants.FEATURE, new String[]{"序号", "名称", "数据类型", "单位", "枚举值", "备注"});
        HEADER_MAP.put(OperateConstants.BUILDING, new String[]{"序号", "编码", "标准名称", "标准说明", "数据类型", "枚举值", "备注"});
    }

    /***
     * @description: 策略调用
     * @param data 1
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2022/7/6 15:13
     */
    public String contextMethod(String type, List<List<String>> data, Map<String, String> param) {
        if (pathInstance.size() == 0) {
            pathInstance.putIfAbsent(OperateConstants.CATEGORY, excelParseCategoryServiceImpl);
            pathInstance.putIfAbsent(OperateConstants.PROJECT, excelParseProjectServiceImpl);
            pathInstance.putIfAbsent(OperateConstants.CONTRACT, excelParseContractServiceImpl);
            pathInstance.putIfAbsent(OperateConstants.FEATURE, excelParseZbfeatureServiceImpl);
            pathInstance.putIfAbsent(OperateConstants.BUILDING, excelParseBuildStandardServiceImpl);
        }
        // 解析插入
        return pathInstance.get(type).parse(data, param);
    }
}
