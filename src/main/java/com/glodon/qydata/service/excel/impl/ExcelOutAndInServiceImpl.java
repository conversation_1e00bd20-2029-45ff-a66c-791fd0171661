package com.glodon.qydata.service.excel.impl;

import com.glodon.qydata.service.excel.ExcelOutAndInService;
import com.glodon.qydata.service.excel.ExcelParseStrategyContext;
import com.glodon.qydata.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

/**
 * @Class com.glodon.qydata.service.excel.impl CommonProjCategorySelfServiceImpl
 * <AUTHOR>
 * @Email <EMAIL>
 * @Description 导入导出相关
 * @Date 11:34 2022/7/6
 **/
@Service
@Slf4j
public class ExcelOutAndInServiceImpl implements ExcelOutAndInService {
    @Resource
    ExcelParseStrategyContext excelParseStrategyContext;

    /***
     * @description: 下载模板
     * @param type 1
     * @return
     * @throws
     * <AUTHOR>
     * @date 2022/7/6 11:35
     */
    @Override
    public void getTemplate(HttpServletResponse response, String type) throws IOException {
        if (!ExcelParseStrategyContext.pathMapper.containsKey(type)) {
            return;
        }
        try {
            InputStream inputStream = this.getClass().getResourceAsStream("/excel/" + ExcelParseStrategyContext.pathMapper.get(type));
            //强制下载不打开
            response.setContentType("application/force-download");
            OutputStream out = response.getOutputStream();
            //使用URLEncoder来防止文件名乱码或者读取错误
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(ExcelParseStrategyContext.pathMapper.get(type), "UTF-8"));
            int b = 0;
            byte[] buffer = new byte[1000000];
            while (b != -1) {
                b = inputStream.read(buffer);
                if (b != -1) out.write(buffer, 0, b);
            }
            inputStream.close();
            out.close();
            out.flush();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /***
     * @description: excel导入
     * @param file 1
     * @param param 2
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2022/7/6 15:13
     */
    @Override
    public String excelInsert(String type, MultipartFile file, Map<String, String> param) {
        List<List<String>> excelResult = ExcelUtil.readExcel(file, type);
        return excelParseStrategyContext.contextMethod(type, excelResult, param);
    }
}
