package com.glodon.qydata.service.standard.feature.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.glodon.qydata.common.RequestContent;
import com.glodon.qydata.common.annotation.BusinessCache;
import com.glodon.qydata.common.constant.BusinessConstants;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.constant.OperateConstants;
import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.entity.standard.expression.ZbStandardsExpression;
import com.glodon.qydata.entity.standard.feature.ProjectFeature;
import com.glodon.qydata.entity.standard.feature.ProjectFeatureCategoryView;
import com.glodon.qydata.entity.standard.trade.ZbStandardsTrade;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.standard.expression.ZbStandardsExpressionSelfMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureCategoryViewMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureCategoryViewSelfMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureSelfMapper;
import com.glodon.qydata.mapper.standard.trade.ZbStandardsTradeMapper;
import com.glodon.qydata.service.init.self.InitFeatureSelfService;
import com.glodon.qydata.service.standard.category.CommonProjCategoryService;
import com.glodon.qydata.service.standard.category.impl.CommonProjectCategoryUsedService;
import com.glodon.qydata.service.standard.expression.IExpressionService;
import com.glodon.qydata.service.standard.feature.FeatureCommonHandler;
import com.glodon.qydata.service.standard.feature.IProjectFeatureSelfService;
import com.glodon.qydata.service.standard.feature.IProjectFeatureService;
import com.glodon.qydata.service.system.PublishInfoService;
import com.glodon.qydata.util.CategoryUtil;
import com.glodon.qydata.util.RedisUtil;
import com.glodon.qydata.util.SpringUtil;
import com.glodon.qydata.util.mover.ElementMover;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import com.glodon.qydata.vo.standard.feature.*;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.glodon.qydata.common.constant.Constants.DB_BATCH_COUNT_1000;

/**
 * @program: ideaworkspace
 * @description: 工程特征业务逻辑处理层
 * @author: zhaohy-c
 * @create: 2021-01-12 16:37
 */
@Service
@Slf4j
public class ProjectFeatureSelfServiceImpl implements IProjectFeatureSelfService {
    @Autowired
    private ProjectFeatureSelfMapper projectFeatureSelfMapper;

    @Autowired
    private ProjectFeatureMapper projectFeatureMapper;

    @Autowired
    private ProjectFeatureCategoryViewSelfMapper projectFeatureCategoryViewSelfMapper;

    @Autowired
    private ProjectFeatureCategoryViewMapper projectFeatureCategoryViewMapper;

    @Autowired
    private ZbStandardsExpressionSelfMapper zbStandardsExpressionSelfMapper;

    @Autowired
    private ZbStandardsTradeMapper tradeMapper;

    @Autowired
    private CommonProjectCategoryUsedService projectCategoryUsedService;

    @Autowired
    private InitFeatureSelfService initFeatureSelfService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private FeatureCommonHandler featureCommonHandler;

    @Autowired
    private IExpressionService expressionService;

    @Resource
    private ProjectFeatureSelfServiceImpl self;

    @Resource
    private PublishInfoService publishInfoServiceImpl;
    @Resource
    private IProjectFeatureService projectFeatureService;

    /**
     * @description: 新增工程特征
     * @param featureAddVO
     * @return com.gcj.zblib.standardData.feature.entity.ProjectFeature
     * <AUTHOR>
     * @date 2021/10/21 20:52
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long add(ProjectFeatureAddVO featureAddVO){
        String customerCode = RequestContent.getCustomerCode();
        Long globalId = RequestContent.getGlobalIdLong();
        Integer type = featureAddVO.getType();
        if (type == null) {
            type = projectCategoryUsedService.getUsedCategoryType(customerCode);
        }

        // 校验工程特征名称
        validatedName(featureAddVO.getName());
        // 校验工程特征备注
        validateRemark(featureAddVO.getRemark());
        // 校验该专业下工程特征 名称 是否重复
        validatedNotDelete(featureAddVO.getTradeId(), featureAddVO.getName(), customerCode, null,
                type, featureAddVO.getViewType(), featureAddVO.getCategoryCode(),featureAddVO.getBaseFeatureId());

        // 不同分类相同专业下工程特征合并
        Long featureId = self.mergeCateGoryCode(featureAddVO.getTradeId(), featureAddVO.getName(), customerCode,
                type, featureAddVO.getViewType(), featureAddVO.getCategoryCode(), featureAddVO.getBaseFeatureId());
        if (featureId != null) {
            return featureId;
        }

        // 构造新增实体
        ProjectFeature projectFeature = new ProjectFeature();
        BeanUtils.copyProperties(featureAddVO, projectFeature);
        projectFeature.setCustomerCode(customerCode);
        projectFeature.setType(type);
        projectFeature.setCreateGlobalId(globalId);

        ZbStandardsTrade zbStandardsTrade = tradeMapper.selectById(featureAddVO.getTradeId());
        if (zbStandardsTrade == null){
            throw new BusinessException(ResponseCode.PARAMETER_ERROR, "该用户下无此专业，请核对专业id");
        }
        projectFeature.setTradeName(zbStandardsTrade.getDescription());

        // 如果已删除的中有重复的特征，用已删除的id，没有就新生成
        ProjectFeature deletedFeature = getDeletedFeatureId(featureAddVO.getTradeId(), featureAddVO.getName(), featureAddVO.getTypeCode(), customerCode, type);
        if (Objects.nonNull(deletedFeature)){
            Long deletedFeatureId = deletedFeature.getId();
            projectFeature.setId(deletedFeatureId);
            projectFeature.setOriginId(deletedFeature.getOriginId());
            // 物理删除已逻辑删除的同名记录
            projectFeatureSelfMapper.deleteByPrimaryKey(deletedFeatureId);
            projectFeatureCategoryViewSelfMapper.deleteByFeatureId(deletedFeatureId);
        }else {
            projectFeature.setId(SnowflakeIdUtils.getNextId());
        }

        dealOrdAndCategoryView(projectFeature, featureAddVO, customerCode);

        // 转成新projectType
        String projectType = projectFeature.getProjectType();
        if (StrUtil.isNotEmpty(projectType)) {
            List<CommonProjCategory> categoryListTree = featureAddVO.getCategoryListTree();
            String newProjectType = CategoryUtil.projectTypeConvert(categoryListTree, projectType, BusinessConstants.PROJECT_TYPE_CONVERT_NEW);
            projectFeature.setProjectType(newProjectType);
        }

        projectFeature.setIsSync(Constants.ZbFeatureConstants.WHETHER_FALSE);
        projectFeature.setIsDefault(Constants.ZbFeatureConstants.WHETHER_FALSE);
        projectFeature.setIsRequired(Constants.ZbFeatureConstants.WHETHER_FALSE);
        projectFeature.setIsFromSystem(Constants.ZbFeatureConstants.WHETHER_FALSE);
        projectFeature.setIsDeleted(Constants.ZbFeatureConstants.WHETHER_FALSE);
        projectFeature.setIsExpression(Constants.ZbFeatureConstants.WHETHER_FALSE);
        projectFeature.setIsSearchCondition(Constants.ZbFeatureConstants.WHETHER_FALSE);

        projectFeatureSelfMapper.insertSelective(projectFeature);
        return projectFeature.getId();
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ProjectFeature> addList(List<ProjectFeatureAddVO> featureAddVOList, Long tradeId, String customerCode){
        Integer type = projectCategoryUsedService.getUsedCategoryType(customerCode);
        String projectType = projectFeatureService.categoryAllCode(customerCode, type, null);
//        工程特征记录
        List<ProjectFeature> projectFeatureList = new ArrayList<>();
//        特征分类对应表记录
        List<ProjectFeatureCategoryView> projectFeatureCategoryViewList= new ArrayList<>();
//        工程分类字段表
        List<ZbStandardsExpression> dicList = new ArrayList<>();
        List<String> msg = new ArrayList<>();
//        缓存标记 只需要查一次
        boolean flg = true;
//        分类顺序递增
        int index = 0;
        // 获取该专业下特征的分类视图，并按分类分组，做排序用
        List<ProjectFeatureCategoryView> featureCategoryViewList = projectFeatureCategoryViewSelfMapper.selectByCategoryAndTrade(
                customerCode, tradeId, null, type);
        Map<String, List<ProjectFeatureCategoryView>> groupMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(featureCategoryViewList)){
            groupMap = featureCategoryViewList.stream().collect(Collectors.groupingBy(ProjectFeatureCategoryView::getCategoryCode));
        }
//        重复校验
        List<ProjectFeature> repeatList = projectFeatureSelfMapper.countByTradeId(String.valueOf(tradeId),  customerCode, type);
//        删除数据处理
        List<ProjectFeature> delList = projectFeatureSelfMapper.findByTradeId(String.valueOf(tradeId), customerCode, type);
        for (int i = 0; i < featureAddVOList.size(); i++) {
            try {
                ProjectFeatureAddVO projectFeatureAddVO = featureAddVOList.get(i);
                projectFeatureAddVO.setType(type);
                projectFeatureAddVO.setProjectType(projectType);
                ProjectFeature projectFeature = this.setProjectFeatureByCache(projectFeatureAddVO, flg, repeatList, delList);
                if (!projectFeatureList.isEmpty()) {
                    flg = false;
                    projectFeature.setOrdTrade(1 + projectFeatureList.get(projectFeatureList.size() - 1).getOrdTrade());
                }

                List<ProjectFeatureCategoryView> itemList = addCategoryViewList(projectFeature, groupMap, index);
                index ++;
                if (itemList != null && !itemList.isEmpty()) {
                    projectFeatureCategoryViewList.addAll(itemList);
                }
                projectFeatureList.add(projectFeature);
            } catch (BusinessException e) {
                msg.add(e.getMessage());
            }
        }
        if (!dicList.isEmpty()) {
            zbStandardsExpressionSelfMapper.insertSelectiveList(dicList);
        }
        if (!projectFeatureCategoryViewList.isEmpty()) {
            projectFeatureCategoryViewSelfMapper.saveBatch(projectFeatureCategoryViewList);
        }
        if (!projectFeatureList.isEmpty()) {
            projectFeatureSelfMapper.insertSelectiveBatch(projectFeatureList);
        }
        return projectFeatureList;
    }
    /***
     * @description: setProjectFeatureList
     * @param featureAddVO 1
     * @param orderFlg 缓存标记 只需要查一次
     * @param repeatList 4
     * @param delList 5
     * @return com.glodon.qydata.entity.standard.feature.ProjectFeature
     * @throws
     * <AUTHOR>
     * @date 2022/7/15 20:47
     */
    private ProjectFeature setProjectFeatureByCache(ProjectFeatureAddVO featureAddVO, boolean orderFlg,
                                                    List<ProjectFeature> repeatList, List<ProjectFeature> delList) {
        // 名称处理
        String name = featureAddVO.getName();
        // 校验
        validatedName(name);
        validateRemark(featureAddVO.getRemark());
        // 构造新增实体
        ProjectFeature projectFeature = new ProjectFeature();
        BeanUtils.copyProperties(featureAddVO, projectFeature);

        Optional<ProjectFeature> existRepeat = repeatList.stream().filter(item -> name.equals(item.getName())).findAny();
        if (existRepeat.isPresent()) {
            throw new BusinessException(ResponseCode.NAME_DUPLICATED, "工程特征名称重复，请修改");
        }

        Optional<ProjectFeature> existDel = delList.stream().filter(item -> name.equals(item.getName())).findAny();
        if (existDel.isPresent()) {
            projectFeature.setId(existDel.get().getId());
            projectFeature.setOriginId(existDel.get().getOriginId());
            // 物理删除已逻辑删除的同名记录
            projectFeatureSelfMapper.deleteByPrimaryKey(existDel.get().getId());
            projectFeatureCategoryViewSelfMapper.deleteByFeatureId(existDel.get().getId());
        }

        if (orderFlg) {
            List<ProjectFeature> projectFeatures = projectFeatureSelfMapper.selectBySelfTradeId(projectFeature.getCustomerCode(), projectFeature.getTradeId(), featureAddVO.getType());
            Integer max = projectFeatures.stream().map(ProjectFeature::getOrdTrade).filter(Objects::nonNull).max(Comparator.comparingInt(o -> o)).orElse(0);
            projectFeature.setOrdTrade(max + 1);
        }
        // 如果已删除的中有重复的特征，用已删除的id，没有就新生成
        projectFeature.setId(SnowflakeIdUtils.getNextId());
        // 分类视图关系表
        projectFeature.setIsSync(Constants.ZbFeatureConstants.WHETHER_FALSE);
        projectFeature.setIsDefault(Constants.ZbFeatureConstants.WHETHER_FALSE);
        projectFeature.setIsRequired(Constants.ZbFeatureConstants.WHETHER_FALSE);
        projectFeature.setIsFromSystem(Constants.ZbFeatureConstants.WHETHER_FALSE);
        projectFeature.setIsDeleted(Constants.ZbFeatureConstants.WHETHER_FALSE);
        projectFeature.setIsExpression(Constants.ZbFeatureConstants.WHETHER_FALSE);
        projectFeature.setIsSearchCondition(Constants.ZbFeatureConstants.WHETHER_FALSE);
        return projectFeature;
    }
    private List<ProjectFeatureCategoryView> addCategoryViewList(ProjectFeature projectFeature, Map<String, List<ProjectFeatureCategoryView>> groupMap, int index){
        // 工程分类
        String projectType = projectFeature.getProjectType();
        Set<String> categoryCodeSet = CategoryUtil.getCategoryCode(projectType);
        List<ProjectFeatureCategoryView> projectFeatureCategoryViewList = new ArrayList<>();

        for (String categoryCode : categoryCodeSet) {
            ProjectFeatureCategoryView projectFeatureCategoryView = new ProjectFeatureCategoryView();
            projectFeatureCategoryView.setTradeId(projectFeature.getTradeId());
            projectFeatureCategoryView.setTradeName(projectFeature.getTradeName());
            projectFeatureCategoryView.setCategoryCode(categoryCode);
            projectFeatureCategoryView.setFeatureId(projectFeature.getId());
            projectFeatureCategoryView.setType(projectFeature.getType());
            projectFeatureCategoryView.setCustomerCode(projectFeature.getCustomerCode());
            // 排序字段
            List<ProjectFeatureCategoryView> projectFeatures = Optional.ofNullable(groupMap.get(categoryCode)).orElse(new ArrayList<>());
                // 插入到最后
            Integer max = projectFeatures.stream().map(ProjectFeatureCategoryView::getOrdCategory).filter(Objects::nonNull).max(Comparator.comparingInt(o -> o)).orElse(0);
            projectFeatureCategoryView.setOrdCategory(index + max + 1);
            projectFeatureCategoryViewList.add(projectFeatureCategoryView);
        }
        if (CollectionUtils.isNotEmpty(projectFeatureCategoryViewList)) {
            return projectFeatureCategoryViewList;
        }
        return null;
    }

    /**
     * 处理排序和分类视图
     * @param projectFeature
     * @param featureAddVO
     */
    private void dealOrdAndCategoryView(ProjectFeature projectFeature, ProjectFeatureAddVO featureAddVO, String customerCode) {
        // 根据选中的特征，处理排序
        Long baseFeatureId = featureAddVO.getBaseFeatureId();
        // 该专业下的所有特征
        List<ProjectFeature> projectFeatures = projectFeatureSelfMapper.selectBySelfTradeId(customerCode, featureAddVO.getTradeId(), projectFeature.getType());

        if (Constants.ZbFeatureConstants.ViewType.CATEGORY_VIEW.equals(featureAddVO.getViewType())) {
            // 在分类视图下新增
            // 特征表的处理
            Integer max = projectFeatures.stream().map(ProjectFeature::getOrdTrade).filter(Objects::nonNull).max(Comparator.comparingInt(o -> o)).orElse(0);
            projectFeature.setOrdTrade(max + 1);
            // 分类视图表的处理
            addCategoryView(projectFeature, baseFeatureId);
        }else {
            // 在专业视图下新增
            if (Objects.isNull(baseFeatureId)){
                // 插入到最后
                Integer max = projectFeatures.stream().map(ProjectFeature::getOrdTrade).filter(Objects::nonNull).max(Comparator.comparingInt(o -> o)).orElse(0);
                projectFeature.setOrdTrade(max + 1);
            }else {
                // 插入到选中的之后
                ProjectFeature baseFeature = projectFeatures.stream().filter(x -> x.getId().equals(baseFeatureId)).findFirst().orElse(null);
                if (Objects.isNull(baseFeature)){
                    throw new BusinessException(ResponseCode.ERROR, "选中的工程特征已被删除，或不存在，请检查");
                }
                Integer baseOrd = baseFeature.getOrdTrade();
                projectFeature.setOrdTrade(baseOrd + 1);
                // 后续ord+1
                List<ProjectFeature> otherOrdChangeList = otherOrdChange(baseOrd, projectFeatures);
                if (CollectionUtils.isNotEmpty(otherOrdChangeList)){
                    projectFeatureSelfMapper.batchUpdateOrd(otherOrdChangeList);
                }
            }
            // 分类视图关系表
            addCategoryView(projectFeature, null);
        }
    }

    /**
     * @description: 填充 1.名称+类型+枚举值 2.创建人、更新人姓名
     * @param featureId
     * @return com.gcj.zblib.standardData.feature.vo.ProjectFeatureResultVO
     * <AUTHOR>
     * @date 2021/11/3 18:09
     */
    @Override
    public ProjectFeatureResultVO getCompleteFeatureByPrimaryKey(Long featureId, Integer viewType, String categoryCode, List<CommonProjCategory> categoryListTree){
        ProjectFeatureResultVO resultVO = new ProjectFeatureResultVO();
        ProjectFeature newProjectFeature = getFeatureByPrimaryKey(featureId);
        BeanUtils.copyProperties(newProjectFeature, resultVO);
        // 转换projectType为老结构 mark_projectType
        if (StringUtils.isNotBlank(resultVO.getProjectType())) {
            resultVO.setProjectType(CategoryUtil.projectTypeConvert(categoryListTree, resultVO.getProjectType(), BusinessConstants.PROJECT_TYPE_CONVERT_OLD));
        }

        // 创建人、更新人姓名
        //获取某个企业人员的globalId和姓名的映射关系
        Map<String, String> globalIdNameMap = RequestContent.getGlobalIdNameMap(Arrays.asList(String.valueOf(resultVO.getCreateGlobalId()), String.valueOf(resultVO.getUpdateGlobalId())));
        if (Objects.nonNull(resultVO.getCreateGlobalId()) && globalIdNameMap.containsKey(resultVO.getCreateGlobalId().toString())) {
            resultVO.setCreateGlobalName(globalIdNameMap.get(resultVO.getCreateGlobalId().toString()));
        }
        if (Objects.nonNull(resultVO.getUpdateGlobalId()) && globalIdNameMap.containsKey(resultVO.getUpdateGlobalId().toString())) {
            resultVO.setUpdateGlobalName(globalIdNameMap.get(resultVO.getUpdateGlobalId().toString()));
        }
        // 分类视图的projectType处理
        if (Constants.ZbFeatureConstants.ViewType.CATEGORY_VIEW.equals(viewType)) {
            resultVO.setProjectType(featureCommonHandler.getDealProjectType(resultVO.getProjectType(), categoryCode, Constants.ZbFeatureConstants.JSON_DEAL_1));
        }
        // 内置数据创建时间不展示
        if (Constants.DEFAULT.equals(resultVO.getIsDefault())){
            resultVO.setCreateTime(null);
        }


        return resultVO;
    }

    /**
     * @description: 后续ord+1
     * @param baseOrd
     * @param projectFeatureList
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/10/25 10:25
     */
    private List<ProjectFeature> otherOrdChange(Integer baseOrd, List<ProjectFeature> projectFeatureList){
        if (CollectionUtils.isEmpty(projectFeatureList)) {
            return null;
        }
        List<ProjectFeature> collect = projectFeatureList.stream()
                .filter(x -> x.getOrdTrade() > baseOrd).collect(Collectors.toList());
        collect.forEach(x -> x.setOrdTrade(x.getOrdTrade() + 1));
        return collect;
    }

    /**
     * @description: 后续ord+1
     * @param baseOrd
     * @param featureCategoryViewList
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/10/25 10:25
     */
    private List<ProjectFeatureCategoryView> otherOrdChangeView(Integer baseOrd, List<ProjectFeatureCategoryView> featureCategoryViewList){
        if (CollectionUtils.isEmpty(featureCategoryViewList)) {
            return Collections.emptyList();
        }
        List<ProjectFeatureCategoryView> collect = featureCategoryViewList.stream()
                .filter(x -> x.getOrdCategory() > baseOrd).collect(Collectors.toList());
        collect.forEach(x -> x.setOrdCategory(x.getOrdCategory() + 1));
        return collect;
    }

    /**
     * @description: 插入分类视图
     * @param projectFeature
     * @return void
     * <AUTHOR>
     * @date 2021/10/25 11:14
     */
    @Transactional(rollbackFor = Exception.class)
    public void addCategoryView(ProjectFeature projectFeature, Long baseFeatureId){
        // 工程分类
        String projectType = projectFeature.getProjectType();
        Set<String> categoryCodeSet = CategoryUtil.getCategoryCode(projectType);
        List<ProjectFeatureCategoryView> projectFeatureCategoryViewList = new ArrayList<>();
        // 获取该专业下特征的分类视图，并按分类分组，做排序用
        List<ProjectFeatureCategoryView> featureCategoryViewList = projectFeatureCategoryViewSelfMapper.selectByCategoryAndTrade(
                projectFeature.getCustomerCode(), projectFeature.getTradeId(), null, projectFeature.getType());

        Map<String, List<ProjectFeatureCategoryView>> groupMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(featureCategoryViewList)){
            groupMap = featureCategoryViewList.stream().collect(Collectors.groupingBy(ProjectFeatureCategoryView::getCategoryCode));
        }

        for (String categoryCode : categoryCodeSet) {
            ProjectFeatureCategoryView projectFeatureCategoryView = new ProjectFeatureCategoryView();
            projectFeatureCategoryView.setTradeId(projectFeature.getTradeId());
            projectFeatureCategoryView.setCategoryCode(categoryCode);
            projectFeatureCategoryView.setFeatureId(projectFeature.getId());
            projectFeatureCategoryView.setType(projectFeature.getType());
            projectFeatureCategoryView.setCustomerCode(projectFeature.getCustomerCode());
            // 排序字段
            List<ProjectFeatureCategoryView> projectFeatures = Optional.ofNullable(groupMap.get(categoryCode)).orElse(new ArrayList<>());
            if (Objects.isNull(baseFeatureId)){
                // 插入到最后
                Integer max = projectFeatures.stream().map(ProjectFeatureCategoryView::getOrdCategory)
                        .filter(Objects::nonNull).max(Comparator.comparingInt(o -> o)).orElse(0);
                projectFeatureCategoryView.setOrdCategory(max + 1);
            }else {
                // 插入到选中的之后
                ProjectFeatureCategoryView baseFeatureView = projectFeatures.stream()
                        .filter(x -> x.getFeatureId().equals(baseFeatureId)).findFirst().orElse(null);

                if (Objects.isNull(baseFeatureView)){
                    throw new BusinessException(ResponseCode.ERROR, "选中的工程特征已被删除，或不存在，请检查");
                }
                Integer baseOrd = baseFeatureView.getOrdCategory();
                projectFeatureCategoryView.setOrdCategory(baseOrd + 1);
                // 后续ord+1
                List<ProjectFeatureCategoryView> otherOrdChangeList = otherOrdChangeView(baseOrd, projectFeatures);
                if (CollectionUtils.isNotEmpty(otherOrdChangeList)){
                    // 分类视图下插入才会执行，categoryCodeSet的size也是1，循环也只会执行一次
                    projectFeatureCategoryViewSelfMapper.batchUpdateOrd(otherOrdChangeList);
                }
            }
            projectFeatureCategoryViewList.add(projectFeatureCategoryView);
        }
        if (CollectionUtils.isNotEmpty(projectFeatureCategoryViewList)) {
            projectFeatureCategoryViewSelfMapper.saveBatch(projectFeatureCategoryViewList);
        }
    }

    /**
     * @description: 根据工程特征ID删除工程特征
     * @param deleteVO
     * @return void
     * <AUTHOR>
     * @date 2021/10/21 21:00
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(ProjectFeatureDeleteVO deleteVO, String customerCode) {
        List<ProjectFeature> projectFeatureDBList = projectFeatureSelfMapper.selectByIds(Arrays.asList(deleteVO.getId().split(StrUtil.COMMA)));

        List<Long> ids = new ArrayList<>();
        if (Constants.ZbFeatureConstants.ViewType.CATEGORY_VIEW.equals(deleteVO.getViewType())) {
            // 分类视图下，删除的是特征下的一个分类
            projectFeatureDBList.forEach(projectFeatureDB -> {
                Integer type = projectCategoryUsedService.getUsedCategoryType(customerCode);
                List<CommonProjCategory> categoryListTree = CategoryUtil.createProjcategoryTree(commonProjCategoryService.getCategoryListTree(customerCode, type, Constants.CategoryConstants.WHETHER_FALSE));
                String projectType = projectFeatureDB.getProjectType();
                projectType = CategoryUtil.projectTypeConvert(categoryListTree, projectType, BusinessConstants.PROJECT_TYPE_CONVERT_OLD);
                String categoryCode = deleteVO.getCategoryCode();
                String resultProjectType = featureCommonHandler.getDealProjectType(projectType, categoryCode, Constants.ZbFeatureConstants.JSON_DEAL_0);
                // 删除分类视图数据
                // 修改特征表里的工程分类数据
                ProjectFeature projectFeature = new ProjectFeature();
                projectFeature.setId(projectFeatureDB.getId());
                ids.add(projectFeatureDB.getId());
                projectFeature.setProjectType(CategoryUtil.projectTypeConvert(categoryListTree, resultProjectType, BusinessConstants.PROJECT_TYPE_CONVERT_NEW));
                projectFeatureSelfMapper.updateByPrimaryKeySelective(projectFeature);
            });
            if (CollUtil.isNotEmpty(ids)) {
                projectFeatureCategoryViewSelfMapper.batchDeleteFeatureIds(ids, deleteVO.getCategoryCode());
            }else {
                log.warn("工程特征ID为空，无法删除！");
            }
        }else {
            // 删除特征表里的数据
            projectFeatureDBList.forEach(projectFeatureDB -> {
                ids.add(projectFeatureDB.getId());
            });
            if (CollUtil.isNotEmpty(ids)) {
                projectFeatureCategoryViewSelfMapper.deleteByFeatureIds(ids);
                projectFeatureSelfMapper.updateByIdsSelective(ids);
            } else {
                log.warn("工程特征ID为空，无法删除！");
            }
        }
    }

    /**
     * @description: 根据项目特征id查询项目特征信息
     * @param id
     * @return com.gcj.zblib.standardData.feature.entity.ProjectFeature
     * <AUTHOR>
     * @date 2021/10/21 21:05
     */
    public ProjectFeature getFeatureByPrimaryKey(Long id) {
        if (id == null) {
            throw new BusinessException(ResponseCode.PARAMETER_ERROR, "项目特征ID不能为空");
        }
        ProjectFeature projectFeature = projectFeatureSelfMapper.selectByPrimaryKey(id);
        if (null == projectFeature) {
            //此工程特征已被删除，或不存在，请检查
            throw new BusinessException(ResponseCode.ERROR, "此工程特征已被删除，或不存在，请检查");
        }
        return projectFeature;
    }

    /**
     * @description: 校验工程特征名称
     * @param name
     * @return void
     * <AUTHOR>
     * @date 2021/10/21 20:20
     */
    public void validatedName(String name) {
        if (StringUtils.isEmpty(name)) {
            throw new BusinessException(ResponseCode.PARAMETER_ERROR, "项目特征名称不能为空！");
        }
        //校验名称长度（含汉字）长度不能超过30个字符
        if (name.length() > Constants.MAX_NAME_LENGTH) {
            throw new BusinessException(ResponseCode.PARAMETER_ERROR, "项目特征名称长度不能超过30个字符！");
        }
    }

    /**
     * @description: 校验工程特征备注
     * @param remark
     * @return void
     * <AUTHOR>
     * @date 2021/10/21 20:20
     */
    public void validateRemark(String remark) {
        //注释不能超过200个字符
        if (StringUtils.isNotEmpty(remark) && remark.length() > Constants.MAX_REMARK_LENGTH) {
            throw new BusinessException(ResponseCode.PARAMETER_ERROR, "注释长度不能超过200个字符！");
        }
    }

    /**
     * @param tradeId
     * @param name
     * @param customerCode
     * @return void
     * @description: 校验某个专业下工程特征 名称+类型+枚举值 是否重复（未删除的）
     * <AUTHOR>
     * @date 2021/11/02 20:20
     */
    @Transactional(rollbackFor = Exception.class)
    public void validatedNotDelete(Long tradeId, String name, String customerCode, Long featureId, Integer type, Integer viewType, String categoryCode, Long baseFeatureId) {
        int count;

        if (Constants.ZbFeatureConstants.ViewType.CATEGORY_VIEW.equals(viewType)) {
            count = projectFeatureCategoryViewSelfMapper.countByCategoryCodeExpressionId(tradeId, name, customerCode, featureId, type, categoryCode);
        } else {
            count = projectFeatureSelfMapper.countByTradeIdExpressionId(tradeId, name, customerCode, featureId, type);
        }

        if (count != 0) {
            throw new BusinessException(ResponseCode.NAME_DUPLICATED, "当前专业已存在同名字段，请在专业视图检查后再试！");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Long mergeCateGoryCode(Long tradeId, String name, String customerCode, Integer type, Integer viewType, String categoryCode, Long baseFeatureId) {

        if (!Constants.ZbFeatureConstants.ViewType.CATEGORY_VIEW.equals(viewType)) {
            return null;
        }
        // 校验是够在不同分类下有相同的工程特征
        List<ProjectFeature> projectFeatures = projectFeatureSelfMapper.selectByName(tradeId, name, customerCode, type);
        if (CollUtil.isEmpty(projectFeatures)) {
            return null;
        }
        for (ProjectFeature projectFeature : projectFeatures) {
            String projectType = projectFeature.getProjectType();
            JSONObject newProjectType = JSON.parseObject(projectType);

            JSONArray newCategoryArray = null;
            if (!StrUtil.isEmpty(projectType)) {
                newCategoryArray = newProjectType.getJSONArray(BusinessConstants.PROJECT_TYPE);
            } else {
                newProjectType = new JSONObject();
                newCategoryArray = new JSONArray();
                newProjectType.put(BusinessConstants.PROJECT_TYPE, newCategoryArray);
            }


            if (!newCategoryArray.contains(categoryCode)) {
                newCategoryArray.add(categoryCode);
                JSONObject currentCategoryJson = new JSONObject();
                JSONArray currentCategoryArray = new JSONArray();
                JSONArray categoryCodeArray = new JSONArray();
                categoryCodeArray.add(categoryCode);
                currentCategoryArray.add(categoryCodeArray);
                currentCategoryJson.put(BusinessConstants.PROJECT_TYPE, currentCategoryArray.toJSONString());
                // 添加projectType
                projectFeature.setProjectType(currentCategoryJson.toJSONString());
                self.addCategoryView(projectFeature, baseFeatureId);

                // 转新projectType
                if (newProjectType == null) {
                    projectFeature.setProjectType(currentCategoryJson.toJSONString());
                } else {
                    projectFeature.setProjectType(newProjectType.toJSONString());
                }
            }
        }
        projectFeatureSelfMapper.batchUpdateSelective(projectFeatures);
        return projectFeatures.get(0).getId();
    }


    /**
     * @description: 获取已删除的特征id
     * @param tradeId
     * @param name
     * @param typeCode
     * @param customerCode
     * @return void
     * <AUTHOR>
     * @date 2021/11/04 10:20
     */
    public ProjectFeature getDeletedFeatureId(Long tradeId, String name, String typeCode, String customerCode, Integer type) {
        ProjectFeature byTradeIdExpressionId = projectFeatureSelfMapper.findByTradeIdExpressionId(tradeId, name, typeCode, customerCode, type);
        return byTradeIdExpressionId;
    }

    /**
     * @description: 上移下移
     * @param upOrDownVO
     * @return void
     * <AUTHOR>
     * @date 2021/11/12 15:57
     */
    @Override
    public void downOrUp(ProjectFeatureUpOrDownVO upOrDownVO){
        Integer type = upOrDownVO.getType();
        if (type == null) {
            type = projectCategoryUsedService.getUsedCategoryType(RequestContent.getCustomerCode());
        }

        if (Constants.ZbFeatureConstants.ViewType.CATEGORY_VIEW.equals(upOrDownVO.getViewType())){
            // 分类视图
            String categoryCode = upOrDownVO.getCategoryCode();
            if (StringUtils.isEmpty(categoryCode)){
                throw new BusinessException(ResponseCode.PARAMETER_ERROR, "分类视图上下移时，分类编码不能为空");
            }
            moveUpOrDownCategoryView(upOrDownVO.getFeatureId(), upOrDownVO.getOperate(), upOrDownVO.getTradeId(), categoryCode, type);
        }else {
            // 专业视图
            moveUpOrDown(upOrDownVO.getFeatureId(), upOrDownVO.getOperate(), upOrDownVO.getTradeId(), type);
        }
    }

    /**
     * 专业视图-上下移
     * @param featureId
     * @param operate
     * @param tradeId
     * @param type
     */
    public void moveUpOrDown(Long featureId, String operate, Long tradeId, Integer type) {
        String customerCode = RequestContent.getCustomerCode();
        List<ProjectFeature> selfProjectFeatures = projectFeatureSelfMapper.selectBySelfTradeId(customerCode, tradeId, type);
        String moveType = Constants.ZbFeatureConstants.OPERATE_UP.equals(operate)? Constants.MOVE_TYPE_UP : Constants.MOVE_TYPE_DOWN;
        ElementMover.move(selfProjectFeatures, featureId, moveType, projectFeatureSelfMapper);
    }

    /**
     * 分类视图-上下移
     * @param featureId
     * @param operate
     * @param tradeId
     * @param categoryCode
     * @param type
     */
    public void moveUpOrDownCategoryView(Long featureId, String operate, Long tradeId, String categoryCode, Integer type) {
        String customerCode = RequestContent.getCustomerCode();
        List<ProjectFeatureCategoryView> categoryViews = projectFeatureCategoryViewSelfMapper.selectByCategoryAndTrade(customerCode, tradeId, categoryCode, type);
        String moveType = Constants.ZbFeatureConstants.OPERATE_UP.equals(operate)? Constants.MOVE_TYPE_UP : Constants.MOVE_TYPE_DOWN;
        ElementMover.move(categoryViews, featureId, moveType, projectFeatureCategoryViewSelfMapper);
    }

    /**
     * @description: 更新工程特征信息
     * @param updateVO
     * @return com.gcj.zblib.standardData.feature.entity.ProjectFeature
     * <AUTHOR>
     * @date 2021/10/24 19:54
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long update(ProjectFeatureUpdateVO updateVO) {
        String customerCode = RequestContent.getCustomerCode();
        Long globalId = RequestContent.getGlobalIdLong();
        Integer type = updateVO.getType();
        if (type == null) {
            type = projectCategoryUsedService.getUsedCategoryType(customerCode);
        }

        Long featureId = updateVO.getId();

        // 校验
        validatedName(updateVO.getName());
        validateRemark(updateVO.getRemark());
        // 校验工程特征信息-校验该专业下工程特征 名称 是否重复
        validatedNotDelete(updateVO.getTradeId(), updateVO.getName(), customerCode,
                featureId, type, updateVO.getViewType(), updateVO.getCategoryCode(),null);

        // 构造更新实体
        ProjectFeature projectFeature = new ProjectFeature();
        BeanUtils.copyProperties(updateVO, projectFeature);

        ZbStandardsTrade zbStandardsTrade = tradeMapper.selectById(updateVO.getTradeId());
        if (zbStandardsTrade == null){
            throw new BusinessException(ResponseCode.PARAMETER_ERROR, "该用户下无此专业，请核对专业id");
        }
        projectFeature.setTradeName(zbStandardsTrade.getDescription());

        dealCategoryAndView(projectFeature, updateVO, customerCode, type);

        projectFeature.setUpdateTime(new Date());
        projectFeature.setUpdateGlobalId(globalId);

        //projectType转换成新结构存储 mark_projectType
        if (StringUtils.isNotBlank(projectFeature.getProjectType())) {
            projectFeature.setProjectType(CategoryUtil.projectTypeConvert(updateVO.getCategoryListTree(), projectFeature.getProjectType(), BusinessConstants.PROJECT_TYPE_CONVERT_NEW));
        }
        projectFeatureSelfMapper.updateByPrimaryKeySelective(projectFeature);

        return featureId;
    }

    private void dealCategoryAndView(ProjectFeature projectFeature, ProjectFeatureUpdateVO updateVO, String customerCode, Integer type){
        ProjectFeature projectFeatureDB = getFeatureByPrimaryKey(projectFeature.getId());

        String projectType = updateVO.getProjectType();
        String oldProjectType = projectFeatureDB.getProjectType();
        //转换成之前的的projectType mark_projectType
        if (StringUtils.isNotBlank(oldProjectType)) {
            oldProjectType = CategoryUtil.projectTypeConvert(updateVO.getCategoryListTree(), oldProjectType, BusinessConstants.PROJECT_TYPE_CONVERT_OLD);
        }

        // 修改分类
        if (Constants.ZbFeatureConstants.ViewType.CATEGORY_VIEW.equals(updateVO.getViewType())){
            // 分类视图
            // 把库里的projectType剔除该一级分类的所有分类。然后放入新的。
            projectType = featureCommonHandler.dealProjectType(oldProjectType, updateVO.getCategoryCode(), projectType);
            projectFeature.setProjectType(projectType);
        }

        if (!projectType.equals(oldProjectType)){
            Set<String> categoryCodeSet = CategoryUtil.getCategoryCode(projectType);
            Set<String> oldCategoryCodeSet = CategoryUtil.getCategoryCode(oldProjectType);

            Set<String> result = new HashSet<>();
            // 旧的比新的多的，删除旧的
            result.addAll(oldCategoryCodeSet);
            result.removeAll(categoryCodeSet);
            if (CollectionUtils.isNotEmpty(result)){
                projectFeatureCategoryViewSelfMapper.batchDeleteFeatureCategory(projectFeature.getId(), result);
            }
            // 新的比旧的多的，新增
            result.clear();
            result.addAll(categoryCodeSet);
            result.removeAll(oldCategoryCodeSet);

            // 获取该专业下特征的分类视图，并按分类分组，做排序用
            List<ProjectFeatureCategoryView> featureCategoryViewList = projectFeatureCategoryViewSelfMapper.selectByCategoryAndTrade(
                    customerCode, projectFeature.getTradeId(), null, updateVO.getType());
            Map<String, List<ProjectFeatureCategoryView>> groupMap = null;
            if (CollectionUtils.isNotEmpty(featureCategoryViewList)){
                groupMap = featureCategoryViewList.stream().collect(Collectors.groupingBy(ProjectFeatureCategoryView::getCategoryCode));
            }

            List<ProjectFeatureCategoryView> projectFeatureCategoryViewList = new ArrayList<>();
            for (String categoryCode : result) {
                ProjectFeatureCategoryView projectFeatureCategoryView = new ProjectFeatureCategoryView();
                projectFeatureCategoryView.setTradeId(projectFeature.getTradeId());
                projectFeatureCategoryView.setTradeName(projectFeature.getTradeName());
                projectFeatureCategoryView.setCategoryCode(categoryCode);
                projectFeatureCategoryView.setFeatureId(projectFeature.getId());
                projectFeatureCategoryView.setType(type);
                projectFeatureCategoryView.setCustomerCode(customerCode);
                // 插入到最后
                int max = 0;
                if (groupMap != null && groupMap.containsKey(categoryCode)){
                    max = groupMap.get(categoryCode).stream().map(ProjectFeatureCategoryView::getOrdCategory).filter(Objects::nonNull).max(Comparator.comparingInt(o -> o)).orElse(0);
                }
                projectFeatureCategoryView.setOrdCategory(max + 1);
                projectFeatureCategoryViewList.add(projectFeatureCategoryView);
            }
            if (CollectionUtils.isNotEmpty(projectFeatureCategoryViewList)){
                projectFeatureCategoryViewSelfMapper.saveBatch(projectFeatureCategoryViewList);
            }
        }
    }


    @Autowired
    private CommonProjCategoryService commonProjCategoryService;

    @Override
    @Transactional
    public List<ProjectFeature> getSelfFeatureTrade(String customerCode, Integer type, Long tradeId) {
        // 暂存专业视图
        List<ProjectFeature> selfProjectFeatures = projectFeatureSelfMapper.selectBySelfTradeId(customerCode, tradeId, type);

        if (CollectionUtils.isEmpty(selfProjectFeatures)) {
            initFeatureSelfService.initData(customerCode, type);
            selfProjectFeatures = projectFeatureSelfMapper.selectBySelfTradeId(customerCode, tradeId, type);
        }

        //转换projectType mark_projectType
        if (CollUtil.isNotEmpty(selfProjectFeatures)) {
            List<CommonProjCategory> categoryListTree = CategoryUtil.createProjcategoryTree(commonProjCategoryService.getCategoryListTree(customerCode, type, Constants.CategoryConstants.WHETHER_FALSE));
            selfProjectFeatures.forEach(item -> item.setProjectType(CategoryUtil.projectTypeConvert(categoryListTree, item.getProjectType(), BusinessConstants.PROJECT_TYPE_CONVERT_OLD)));
        }
        return selfProjectFeatures;
    }

    @Override
    public List<ProjectFeatureResultVO> getSelfFeatureTradeView(String customerCode, ProjectFeatureFilterVO filterVO) {
        Long tradeId = filterVO.getTradeId();
        if (tradeId == null){
            throw new BusinessException(ResponseCode.PARAMETER_ERROR, "专业TradeId不能为空...");
        }

        Integer type = filterVO.getType();
        if (type == null){
            type = projectCategoryUsedService.getUsedCategoryType(customerCode);
        }

        List<ProjectFeature> selfFeatureTrade = getSelfFeatureTrade(customerCode, type, tradeId);
        return featureCommonHandler.convertToResultVO(selfFeatureTrade, filterVO.getViewType(), filterVO.getCategoryCode(), filterVO.getIsSkipUserName());
    }

    @Override
    public List<ProjectFeatureCategoryViewVO> getSelfFeatureCategoryView(String customerCode, ProjectFeatureFilterVO filterVO) {
        String categoryCode = filterVO.getCategoryCode();
        if (StringUtils.isEmpty(categoryCode)){
            throw new BusinessException(ResponseCode.PARAMETER_ERROR, "工程分类编码不能为空...");
        }

        Integer type = filterVO.getType();
        if (type == null){
            type = projectCategoryUsedService.getUsedCategoryType(customerCode);
        }

        List<ProjectFeatureCategoryView> categoryViews =
                projectFeatureCategoryViewSelfMapper.selectBySelfCategoryAndCustomerCode(filterVO.getCategoryCode(), customerCode, type);

        if (CollectionUtils.isEmpty(categoryViews)){
            // 没有该分类下的特征，查询该用户分类视图的所有暂存数据，如果都没有，说明需要初始化
            List<ProjectFeatureCategoryView> allFeatureCategoryViews =
                    projectFeatureCategoryViewSelfMapper.selectBySelfCategoryAndCustomerCode(null, customerCode, type);
            if (CollectionUtils.isEmpty(allFeatureCategoryViews)){
                //根据专业视图生暂存成分类视图暂存
                List<ProjectFeature> projectFeatures = getSelfFeatureTrade(customerCode, type, null);
                if (CollectionUtils.isNotEmpty(projectFeatures)){
                    categoryViews = projectFeatureCategoryViewSelfMapper.selectBySelfCategoryAndCustomerCode(filterVO.getCategoryCode(), customerCode, type);
                }
            }
        }

        if (CollectionUtils.isEmpty(categoryViews)){
            return new ArrayList<>();
        }

        List<Long> featureIds = categoryViews.stream().map(ProjectFeatureCategoryView::getFeatureId).collect(Collectors.toList());
        // 该分类下的特征
        List<ProjectFeature> projectFeatures = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(featureIds)){
            projectFeatures = projectFeatureSelfMapper.selectBySelfFeatureIdList(featureIds, customerCode, filterVO.getIsShowNotUsing(), type);
        }

        //转换projectType mark_projectType
        if (CollUtil.isNotEmpty(projectFeatures)) {
            List<CommonProjCategory> categoryListTree = CategoryUtil.createProjcategoryTree(commonProjCategoryService.getCategoryListTree(customerCode, type, Constants.CategoryConstants.WHETHER_FALSE));
            projectFeatures.forEach(projectFeature -> projectFeature.setProjectType(CategoryUtil.projectTypeConvert(categoryListTree, projectFeature.getProjectType(), BusinessConstants.PROJECT_TYPE_CONVERT_OLD)));
        }

        return featureCommonHandler.convertToCategoryViewVO(projectFeatures, categoryViews, customerCode, filterVO.getViewType(), filterVO.getCategoryCode(), filterVO.getIsSkipUserName());
    }

    @Override
    @BusinessCache(customerCode = "${customerCode}", isInvalidateCache = true)
    public void publish(String customerCode, String globalId) {
        Integer type = projectCategoryUsedService.getUsedCategoryType(customerCode);

        // 加锁
        String lockValue = redisUtil.generateLockValue();

        if (!redisUtil.tryLockRetry(lockValue, RedisKeyEnum.LOCK_PUBLISH_FEATURE, customerCode)) {
            throw new BusinessException("发布失败，请稍后重试...");
        }

        try {
            // 发布
            ProjectFeatureSelfServiceImpl projectFeatureSelfServiceImpl = SpringUtil.getBean("projectFeatureSelfServiceImpl");
            projectFeatureSelfServiceImpl.executePublish(customerCode, globalId, type);
        } finally {
            redisUtil.unlock(lockValue, RedisKeyEnum.LOCK_PUBLISH_FEATURE, customerCode);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void executePublish(String customerCode, String globalId, Integer type) {
        Map<Long, Long> newAndOldFeatureIdMap = new HashMap<>(16);

        List<ProjectFeature> selfProjectFeatures = projectFeatureSelfMapper.selectSelfAll(customerCode, type);

        if (CollUtil.isEmpty(selfProjectFeatures)){
            return;
        }

        // 校验枚举值为空的单选、多选
        validateNullOption(selfProjectFeatures);

        List<ProjectFeature> insertList = new ArrayList<>();
        List<ProjectFeature> updateList = new ArrayList<>();

        selfProjectFeatures.forEach(item -> {
            Long originId = item.getOriginId();
            if (originId == null) {
                if (Constants.ZbFeatureConstants.WHETHER_FALSE == item.getIsDeleted()){
                    insertList.add(item);
                }
            } else {
                newAndOldFeatureIdMap.put(item.getId(), originId);
                item.setId(originId);
                updateList.add(item);
            }
        });

        // 将数据存入正式表
        if (CollectionUtils.isNotEmpty(insertList)){
            List<List<ProjectFeature>> insertLists = Lists.partition(insertList, DB_BATCH_COUNT_1000);
            insertLists.forEach(x -> projectFeatureMapper.insertBatch(x));
        }
        if (CollectionUtils.isNotEmpty(updateList)){
            projectFeatureMapper.updateBatch(updateList);
        }
        projectFeatureSelfMapper.deleteBySelfCustomerCode(customerCode, type);

        // 查询分类视图暂存数据
        List<ProjectFeatureCategoryView> selfCategoryViews = projectFeatureCategoryViewSelfMapper.selectBySelfCustomerCode(customerCode, type);

        selfCategoryViews.forEach(item -> {
            Long featureId = item.getFeatureId();
            if (newAndOldFeatureIdMap.containsKey(featureId)){
                item.setFeatureId(newAndOldFeatureIdMap.get(featureId));
            }
        });
        projectFeatureCategoryViewMapper.deleteByCustomerCode(customerCode, type);
        if (CollectionUtils.isNotEmpty(selfCategoryViews)){
            List<List<ProjectFeatureCategoryView>> insetLists = Lists.partition(selfCategoryViews, DB_BATCH_COUNT_1000);
            insetLists.forEach(x -> projectFeatureCategoryViewMapper.saveBatch(x));

        }
        projectFeatureCategoryViewSelfMapper.deleteBySelfCustomerCode(customerCode, type);

        expressionService.expressionSyn(customerCode, type);
        publishInfoServiceImpl.updateVersion(customerCode, globalId, OperateConstants.FEATURE);
    }

    /**
     * 校验枚举值为空的单选、多选
     * @throws
     * @param selfProjectFeatures
     * <AUTHOR>
     * @return
     * @date 2022/7/20 11:00
     */
    private void validateNullOption(List<ProjectFeature> selfProjectFeatures) {
        long count = selfProjectFeatures.parallelStream()
                .filter(x -> Constants.ZbFeatureConstants.WHETHER_FALSE == x.getIsDeleted())
                .filter(x -> {
                    String typeCode = x.getTypeCode();
                    boolean isSelect = Constants.ZbExpressionConstants.TYPE_SELECT.equals(typeCode)
                            || Constants.ZbExpressionConstants.TYPE_SELECTS.equals(typeCode);
                    boolean isOptionBlank = StringUtils.isBlank(x.getOption());
                    return isSelect && isOptionBlank;
                }).count();
        if (count > 0) {
            throw new BusinessException("单选和多选类型工程特征的枚举值不能为空，请检查");
        }
    }

    /**
     * 按专业复用特征到当前分类下
     */
    @Override
    public void batchInsertFeature(ProjectFeatureBatchAddVO featureTradeVO){
        if (StringUtils.isEmpty(featureTradeVO.getCategoryCode()) || CollUtil.isEmpty(featureTradeVO.getTradeIds())) {
            return;
        }

        String categoryCode = featureTradeVO.getCategoryCode();
        String customerCode = RequestContent.getCustomerCode();
        Long globalId = RequestContent.getGlobalIdLong();

        Integer type = projectCategoryUsedService.getUsedCategoryType(RequestContent.getCustomerCode());


        // 查询当前分类已经关联的专业
        List<ProjectFeatureCategoryView> featureCategoryViews = projectFeatureCategoryViewSelfMapper.selectBySelfCategoryAndCustomerCode(categoryCode, customerCode, type);
        // 当前工程分类下已有的专业和特征
        Set<Long> tradeIds = new HashSet<>();
        Set<Long> featureIds = new HashSet<>();
        featureCategoryViews.forEach(feature-> {
            tradeIds.add(feature.getTradeId());
            featureIds.add(feature.getFeatureId());
        });

        Integer max = featureCategoryViews.stream().map(ProjectFeatureCategoryView::getOrdCategory)
                .filter(Objects::nonNull).max(Comparator.comparingInt(o -> o)).orElse(0);

        // 按专业查询所有的特征项
        List<ProjectFeature> features = projectFeatureSelfMapper.selectByCustomCodeAndTradeIds(customerCode, featureTradeVO.getTradeIds());
        List<ProjectFeature> updateFeatures = new ArrayList<>();
        List<ProjectFeatureCategoryView> addFeatures = new ArrayList<>();
        // 特征暂存修改工程分类
        for (ProjectFeature feature : features) {
            Long tradeId = feature.getTradeId();
            Long featureId = feature.getId();
            // 如果
            if (!featureIds.contains(featureId)) {
                // 如果专业已经被引用工程分类不用修改
                String projectType = feature.getProjectType();
                String newProjectType = CategoryUtil.projectTypeConvert(featureTradeVO.getCategoryListTree(), projectType, BusinessConstants.PROJECT_TYPE_CONVERT_NEW);

                String finalProjectType = featureCommonHandler.addNewCategoryCode(newProjectType, categoryCode);

                // 特征关联的工程分类
                feature.setProjectType(finalProjectType);
                // 更改账号
                feature.setUpdateGlobalId(globalId);
                // 更改时间
                feature.setUpdateTime(new Date());

                updateFeatures.add(feature);

                // 分类和特征引用关系
                ProjectFeatureCategoryView categoryView = new ProjectFeatureCategoryView();
                categoryView.setTradeId(tradeId);
                categoryView.setCategoryCode(categoryCode);
                categoryView.setType(type);
                categoryView.setFeatureId(featureId);
                categoryView.setQyCodeOld(feature.getQyCodeOld());
                categoryView.setTradeName(feature.getTradeName());
                categoryView.setOrdCategory(++max);
                categoryView.setInvalid(feature.getInvalid());
                categoryView.setCustomerCode(customerCode);
                categoryView.setQyFlag(feature.getQyFlag());
                addFeatures.add(categoryView);
            }
        }

        // 更新暂存表工程特征对应的分类
        if (!CollUtil.isEmpty(updateFeatures)) {
            projectFeatureSelfMapper.batchUpdateFeatureInfo(updateFeatures);
        }

        // 添加业态视图对应的特征记录
        if (!CollUtil.isEmpty(addFeatures)) {
            projectFeatureCategoryViewSelfMapper.saveBatch(addFeatures);
        }

    }
}
