package com.glodon.qydata.service.standard.buildStandard;

import com.glodon.qydata.dto.StandardPositionDto;
import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard;
import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildPosition;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【zb_standards_build_position(企业标准数据-建造标准定位)】的数据库操作Service
* @createDate 2022-08-01 10:57:17
*/
public interface ZbStandardsBuildPositionService{
    /**
     * 给建造标准设置产品定位
     * @param returnList
     */
    void setCategoryAndPositions(List<ZbProjectStandard> returnList, String customerCode, boolean isSetCategoryAndPosition);

    /**
     * 根据建造标准查询定位列表
     * @param standardId
     * @param customerCode
     * @return
     */
    List<StandardPositionDto> getPositionList(Long standardId, String customerCode, boolean isSelf);

    /**
     * 保存定位信息
     * @param customerCode
     * @param globalId
     * @param standardId
     * @param standardPositionDtoList
     */
    void savePosition(String customerCode, String globalId, Long standardId, List<StandardPositionDto> standardPositionDtoList);

    /**
     * 根据建造标准查询暂存表数据
     * @param standIds
     * @return
     */
    List<ZbStandardsBuildPosition> selectSelfByStandardIds(List<Long> standIds);

    /**
     * 根据建造标准删除数据
     * @param standardIds
     */
    void delByStandardIds(List<Long> standardIds);

    /**
     * 批量保存数据
     * @param buildPositions
     */
    void batchSave(List<ZbStandardsBuildPosition> buildPositions);

    /**
     * 根据建造标准删除暂存表数据
     * @param standardIds
     */
    void delSelfByStandardIds(List<Long> standardIds);

    /**
     　　* @description: 根据工程分类编码与标准id集合查询对应position数据,取创建时间最新的一条
     　　* @param [categoryCode, standardIds]
     　　* @return void
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2022/8/5 16:55
     　　*/
    ZbStandardsBuildPosition getPositionByCategoryAndStandardsIds(String categoryCode,String positionName,List<Long> standardIds);
}
