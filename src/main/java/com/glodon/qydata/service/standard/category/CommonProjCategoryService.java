package com.glodon.qydata.service.standard.category;

import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.vo.common.ResponseVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2021/10/15 17:18
 */
public interface CommonProjCategoryService {
    
    /**
     * 获取所有工程类别信息
     * @param customerCode
     * @return java.util.List<com.glodon.zbw.dataManager.standardData.category.domain.CommonProjCategory>
     * <AUTHOR>
     * @date 2021/10/19 14:59 
     */
    List<CommonProjCategory> getCategoryListTree(String customerCode, Integer type, Integer isShowDelete) throws BusinessException;

    /**
     * 根据主键查询工程分类
     * @param categoryId
     * @return com.glodon.zbw.dataManager.standardData.category.domain.CommonProjCategory
     * <AUTHOR>
     * @date 2021/10/19 14:59 
     */
    CommonProjCategory getCategoryById(Integer categoryId);

    /**
     * @Description 根据分类编码查询工程分类
     * <AUTHOR>
     * @Date 2022/2/16 17:58
     * @param categoryCode
     * @return com.glodon.qydata.entity.standard.category.CommonProjCategory
     **/
    CommonProjCategory getCategoryByCode(String categoryCode, String customerCode);
    
    /**
     * 获取工程分类列表-是否删除/是否树结构
     * @param isShowDelete
     * @param dataType
     * @param customerCode
     * @param isSkipUserName
     * @return java.util.List<com.glodon.zbw.dataManager.standardData.category.domain.CommonProjCategory>
     * <AUTHOR>
     * @date 2021/10/19 14:59 
     */
    List<CommonProjCategory> getCategoryList(Integer isShowDelete, Integer dataType ,Integer isUsing, String customerCode, Integer type, Integer isSkipUserName, Integer levelLimit, String categoryCode1, Boolean needTag);

/**
　　* @description: 查询指定企业下的所有一级工程分类
　　* @param  customerCode 企业编码
　　* @return List<CommonProjCategory> 符合条件的工程分类列表
　　* @throws
　　* <AUTHOR>
　　* @date 2021/11/19 18:18
　　*/
    List<CommonProjCategory> getTopOneList( String customerCode, Integer type);

    /**
     * @description: 工程分类历史数据处理
     * @param
     * @return void
     * <AUTHOR>
     * @date 2021/11/24 18:10
     */
    void dealHistoryData();

    /**
     * @description: 获取某企业的所有分类
     * @param isShowDelete
     * @param customerCode
     * @return java.util.List<com.glodon.zbw.dataManager.standardData.category.domain.CommonProjCategory>
     * <AUTHOR>
     * @date 2021/11/29 16:08
     */
    List<CommonProjCategory> getAllCategoryList(String customerCode, Integer type, Integer isShowDelete);

    /**
     * @description: 获取指定code直接下一级的类别列表
     * @param code
     * @param level
     * @return java.util.List<com.glodon.zbw.dataManager.standardData.category.domain.CommonProjCategory>
     * <AUTHOR>
     * @date 2021/11/30 16:46
     */
    List<CommonProjCategory> selectByLevelAndCode(String customerCode, String code, Integer level);

    /**
     　　* @description: 将旧企业工程分类数据复制到新企业工程分类标准
     　　* @param  oldCustomerCode:原企业编码  newCustomerCode 新企业编码
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/9 9:55
     　　*/
    void initCategoryData(String oldCustomerCode,String newCustomerCode);

    /**
     　　* @description: 根据企业编码删除所有工程分类标准
     　　* @param  customerCode 企业编码
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/9 19:41
     　　*/
    void deleteByCustomerCode(String customerCode);

    /**
     * 获取系统内置的所有一级分类的名称和id映射关系，用于导入特征内置数据
     * @return Map<String, Integer>
     * <AUTHOR>
     * @date 2021/11/10 17:19
     */
    Map<String, List<List<String>>> getSystemCategory();

    Map<String, CommonProjCategory> getCommonProjCategoryMap(String customerCode);

    ResponseVo<String> deleteHuiguoData();

    ResponseVo<String> migrationHuiguoData();

    /*
     * Description: <br>获取系统内置的所有一级分类的名称和id映射关系，用于导入特征内置数据
     * @Param: []
     * @Return: String
     * @Author: lisp-c
     * @Date: 2022/3/11 17:52
     * @version V1.0
     */
    String getSystemBuiltCategory(List<CommonProjCategory> categoryList);

    List<CommonProjCategory> getCategoryByCodes(Set<String> categoryCodeSet , String customerCode);

    /**
     * @description: 查询某一级分类下的所有工程分类（未删除、启用）
     * @param customerCode
     * @param oneLevelCode
     * @return java.util.List<com.glodon.qydata.entity.standard.category.CommonProjCategory>
     * <AUTHOR>
     * @date 2022/8/30 9:45
     */
    List<CommonProjCategory> selectByOneLevelCode(String customerCode, String oneLevelCode);

    List<String> getAllGlobalIds(List<CommonProjCategory> categories);
}
