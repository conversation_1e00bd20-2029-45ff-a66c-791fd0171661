package com.glodon.qydata.service.standard.projectOrContractInfo;

import com.baomidou.mybatisplus.extension.service.IService;
import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsUnitEntity;
import com.glodon.qydata.vo.standard.projectOrContractInfo.StandardsUnitShowVo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * zb_standards_unit - 主键 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
public interface IStandardsUnitService extends IService<StandardsUnitEntity> {

    /**
     * 获取项目/工程规模单位集合
     * @throws
     * @param standardsInfoId 项目/合同信息表数据id
     * <AUTHOR>
     * @return {@link List< StandardsUnitEntity>}
     * @date 2021/10/20 15:02
     */
    List<StandardsUnitShowVo> initAndGetUnits(Long standardsInfoId, Integer type, String customerCode);

    /**
     * 新增项目/工程规模单位
     * @throws
     * @param entity
     * <AUTHOR>
     * @return {@link StandardsUnitEntity < Void>}
     * @date 2021/10/20 15:42
     */
    StandardsUnitEntity addUnit(StandardsUnitEntity entity);

    /**
     * 修改项目/工程规模单位
     * @throws
     * @param entity
     * <AUTHOR>
     * @return {@link StandardsUnitEntity}
     * @date 2021/10/20 16:14
     */
    StandardsUnitEntity updateUnit(StandardsUnitEntity entity);

    /**
     * 删除项目/工程规模单位
     * @throws
     * @param id
     * <AUTHOR>
     * @return
     * @date 2021/10/20 16:40
     */
    void deleteUnit(Long id);

    /**
     * 设置默认选中项目/工程规模单位
     * @throws
     * @param id
     * @param unitName
     * <AUTHOR>
     * @return
     * @date 2021/11/8 9:24
     */
    void setDefaultCheck(Long id, String unitName, String customerCode);

    /**
     　　* @description: 将旧企业项目/合同标准数据复制到新企业项目/合同标准
     　　* @param  Map<Long,Long> 项目信息id关系map  key:oldId   value:newId  oldCustomerCode:原企业编码  newCustomerCode 新企业编码
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/9 9:55
     　　*/
    void initProjectInfoDate(Map<Long,Long> projectInfoIdRelationMap, String oldCustomerCode, String newCustomerCode);

    /**
     　　* @description: 根据企业编码删除所有项目信息下的单位标准
     　　* @param  customerCode 企业编码
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/9 19:41
     　　*/
    void deleteByCustomerCode(String customerCode);

    /**
     * 根据项目规模/工程规模id获取默认选中的单位
     * @throws
     * @param infoId
     * <AUTHOR>
     * @return {@link String}
     * @date 2022/2/27 19:03
     */
    String getDefaultCheck(Long infoId, String customerCode);
}
