package com.glodon.qydata.service.standard.expression.impl;

import com.glodon.qydata.common.RequestContent;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.entity.standard.expression.ZbStandardsExpression;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.standard.expression.ZbStandardsExpressionSelfMapper;
import com.glodon.qydata.service.standard.expression.IExpressionSelfService;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import com.glodon.qydata.vo.standard.expression.ExpressionResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 工程特征名称类型表及计算口径
 * @date 2021/11/2 17:15
 */
@Service
@Slf4j
public class ExpressionSelfServiceImpl implements IExpressionSelfService {

    @Autowired
    private ZbStandardsExpressionSelfMapper zbStandardsExpressionSelfMapper;


    @Override
    public Long addData(String name, String typeCode, String option, String customCode, Long globalId, Integer type) {
        // 查询是否存在（名称+类型+枚举值）
        ZbStandardsExpression exist;
        // 单项和多选需要考虑枚举值是否相等
        if (Constants.ZbExpressionConstants.TYPE_SELECT.equals(typeCode) || Constants.ZbExpressionConstants.TYPE_SELECTS.equals(typeCode)) {
            exist = zbStandardsExpressionSelfMapper.findExistByNameTypeOption(customCode, name, typeCode, option, globalId, type);
        }else {
            exist = zbStandardsExpressionSelfMapper.findExistByNameType(customCode, name, typeCode, globalId, type);
        }

        if (Objects.nonNull(exist)) {
            return exist.getId();
        }

        // 不存在就新增
        ZbStandardsExpression zbStandardsExpression = new ZbStandardsExpression();
        zbStandardsExpression.setId(SnowflakeIdUtils.getNextId());
        zbStandardsExpression.setExpressionCode("K_"+SnowflakeIdUtils.getNextId());
        zbStandardsExpression.setQyCode(customCode);
        zbStandardsExpression.setName(name);
        zbStandardsExpression.setTypeCode(typeCode);
        zbStandardsExpression.setType(type);
        zbStandardsExpression.setOption(option);
        zbStandardsExpression.setCreateGlobalId(globalId);
        zbStandardsExpression.setIsExpression(Constants.ZbFeatureConstants.WHETHER_FALSE);
        zbStandardsExpression.setIsDeleted(Constants.ZbFeatureConstants.WHETHER_FALSE);
        zbStandardsExpression.setIsUsable(Constants.ZbFeatureConstants.WHETHER_FALSE);
        zbStandardsExpression.setExpressionIsFromSystem(Constants.ZbFeatureConstants.WHETHER_FALSE);
        zbStandardsExpression.setExpressionIsUsing(Constants.ZbFeatureConstants.WHETHER_FALSE);
        zbStandardsExpression.setSelfGlobalId(globalId);
        zbStandardsExpressionSelfMapper.insertSelective(zbStandardsExpression);
        return zbStandardsExpression.getId();
    }
    /***
     * @description: 批量处理字典
     * @param name 1
 * @param typeCode 2
 * @param option 3
 * @param customCode 4
 * @param globalId 5
 * @param type 6
 * @param zbStandardsExpressionList 7
 * @param list 8
     * @return java.lang.Long
     * @throws
     * <AUTHOR>
     * @date 2022/7/15 20:51
     */
    @Override
    public Long addDataByCache(String name, String typeCode, String option, String customCode, Long globalId, Integer type, List<ZbStandardsExpression> zbStandardsExpressionList, List<ZbStandardsExpression> list) {
        // 查询是否存在（名称+类型+枚举值）
        Optional<ZbStandardsExpression> exist;
        // 单项和多选需要考虑枚举值是否相等
        if (Constants.ZbExpressionConstants.TYPE_SELECT.equals(typeCode) || Constants.ZbExpressionConstants.TYPE_SELECTS.equals(typeCode)) {
            exist = zbStandardsExpressionList.stream().filter(item -> item.getName().equals(name) && typeCode.equals(item.getTypeCode()) && item.getOption().equals(option)).findAny();
        }else {
            exist = zbStandardsExpressionList.stream().filter(item -> item.getName().equals(name) && typeCode.equals(item.getTypeCode())).findAny();
        }

        if (exist.isPresent()) {
            return exist.get().getId();
        }

        // 不存在就新增
        ZbStandardsExpression zbStandardsExpression = new ZbStandardsExpression();
        zbStandardsExpression.setId(SnowflakeIdUtils.getNextId());
        zbStandardsExpression.setExpressionCode("K_"+SnowflakeIdUtils.getNextId());
        zbStandardsExpression.setQyCode(customCode);
        zbStandardsExpression.setName(name);
        zbStandardsExpression.setTypeCode(typeCode);
        zbStandardsExpression.setType(type);
        zbStandardsExpression.setOption(option);
        zbStandardsExpression.setCreateGlobalId(globalId);
        zbStandardsExpression.setIsExpression(Constants.ZbFeatureConstants.WHETHER_FALSE);
        zbStandardsExpression.setIsDeleted(Constants.ZbFeatureConstants.WHETHER_FALSE);
        zbStandardsExpression.setIsUsable(Constants.ZbFeatureConstants.WHETHER_FALSE);
        zbStandardsExpression.setExpressionIsFromSystem(Constants.ZbFeatureConstants.WHETHER_FALSE);
        zbStandardsExpression.setExpressionIsUsing(Constants.ZbFeatureConstants.WHETHER_FALSE);
        zbStandardsExpression.setSelfGlobalId(globalId);
        list.add(zbStandardsExpression);
        return zbStandardsExpression.getId();
    }

    @Override
    public ZbStandardsExpression selectByPrimaryKey(Long id) {
        ZbStandardsExpression zbStandardsExpression = zbStandardsExpressionSelfMapper.selectByPrimaryKey(id);
        if (Objects.isNull(zbStandardsExpression)){
            throw new BusinessException(ResponseCode.ERROR, "该数据不存在");
        }
        return zbStandardsExpression;
    }

    @Override
    public List<ExpressionResultVo> selectSelfExpression(String customerCode, Integer type) {
        Long globalId = Long.parseLong(RequestContent.getGlobalId());
        List<ExpressionResultVo> list = new ArrayList<>();
        List<ZbStandardsExpression> zbStandardsExpressions = zbStandardsExpressionSelfMapper.selectSelfExpression(customerCode, globalId, type);

        if (CollectionUtils.isEmpty(zbStandardsExpressions)) {
            return null;
        }

        convertExpressions(list, zbStandardsExpressions);

        return list;
    }

    private void convertExpressions(List<ExpressionResultVo> list, List<ZbStandardsExpression> zbStandardsExpressions) {
        //获取某个企业人员的globalId和姓名的映射关系
        List<String> globalIds = zbStandardsExpressions.stream().map(expression -> String.valueOf(expression.getExpressionCreateGlobalId())).distinct().collect(Collectors.toList());
        Map<String, String> globalIdNameMap = RequestContent.getGlobalIdNameMap(globalIds);
        for (ZbStandardsExpression zbStandardsExpression : zbStandardsExpressions) {
            ExpressionResultVo expressionResultVo = new ExpressionResultVo();
            BeanUtils.copyProperties(zbStandardsExpression, expressionResultVo);
            Long createGlobalId = zbStandardsExpression.getExpressionCreateGlobalId();
            if (Objects.nonNull(createGlobalId) && globalIdNameMap.containsKey(createGlobalId.toString())) {
                expressionResultVo.setExpressionCreateGlobalName(globalIdNameMap.get(createGlobalId.toString()));
            }
            list.add(expressionResultVo);
        }
    }

    @Override
    public void deleteByPrimaryKey(Long id) {

        ZbStandardsExpression zbStandardsExpression = selectByPrimaryKey(id);
        Integer isFromSystem = zbStandardsExpression.getExpressionIsFromSystem();
        if (isFromSystem == Constants.ZbExpressionConstants.WHETHER_TRUE){
            throw new BusinessException(ResponseCode.ERROR, "默认计算口径项不可删除");
        }
        zbStandardsExpressionSelfMapper.deleteByPrimaryKey(id);
    }

    @Override
    public void addExpressionDict(Long expressionId, long globalId) {
        ZbStandardsExpression zbStandardsExpression = selectByPrimaryKey(expressionId);
        Integer isExpression = zbStandardsExpression.getIsExpression();
        // 1、相同走历史，应该保持不变  2、删除又新增，走新增项的创建人，单位清空，启用状态回到默认
        if (isExpression == Constants.ZbExpressionConstants.WHETHER_TRUE){
            return;
        }
        if (!Constants.ZbExpressionConstants.TYPE_NUMBER.equals(zbStandardsExpression.getTypeCode())) {
            throw new BusinessException(ResponseCode.ERROR, "数值类才能设置计算口径!");
        }
        int maxExpressionOrd = zbStandardsExpressionSelfMapper.getMaxExpressionOrd(zbStandardsExpression.getQyCode(), globalId, zbStandardsExpression.getType());
        ZbStandardsExpression update = new ZbStandardsExpression();
        update.setId(expressionId);
        update.setIsExpression(Constants.ZbExpressionConstants.WHETHER_TRUE);
        update.setExpressionCreateGlobalId(globalId);
        update.setExpressionCreateTime(new Date());
        update.setUnit("");
        update.setExpressionIsUsing(Constants.ZbExpressionConstants.WHETHER_FALSE);
        update.setExpressionOrd(maxExpressionOrd + 1);
        zbStandardsExpressionSelfMapper.updateSelective(update);
    }

    @Override
    public void enable(Long expressionId, Integer operate, String unit) {

        ZbStandardsExpression zbStandardsExpression = selectByPrimaryKey(expressionId);
        // 自定义插入项进入字典表后默认不启用，启用前需先输入单位，仅支持单位的输入、是否启用、删除编辑权限；
        if (operate == Constants.ZbExpressionConstants.OPERATE_ENABLE && zbStandardsExpression.getExpressionIsFromSystem() == Constants.ZbExpressionConstants.WHETHER_FALSE &&
                StringUtils.isEmpty(unit)) {
            throw new BusinessException(ResponseCode.ERROR, "启用前需先输入单位!");
        }
        ZbStandardsExpression update = new ZbStandardsExpression();
        update.setId(expressionId);
        update.setUnit(unit);
        update.setExpressionIsUsing(operate);
        zbStandardsExpressionSelfMapper.updateSelective(update);
    }

    @Override
    public List<ZbStandardsExpression> selectListBySelfCustomCode(String customerCode, Long selfGlobalId, Integer type) {
        return zbStandardsExpressionSelfMapper.selectListBySelfCustomCode(customerCode, selfGlobalId, type);
    }
}
