package com.glodon.qydata.service.standard.category.impl;

import com.glodon.qydata.entity.standard.category.CommonProjectCategoryUsed;
import com.glodon.qydata.mapper.standard.category.CommonProjCategoryUsedMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Hashtable;
import java.util.List;
import java.util.Map;

@Service("CommonProjectCategoryUsedService")
@Slf4j
public class CommonProjectCategoryUsedService {
    private Map<String, Integer> categoryUsedMapper;

    @Autowired
    private CommonProjCategoryUsedMapper commonProjCategoryUsedMapper;

    public void initData() {
        if (categoryUsedMapper != null) {
            return;
        }

        categoryUsedMapper = new Hashtable<>();
        List<CommonProjectCategoryUsed> categoryUseds =  commonProjCategoryUsedMapper.selectAll();
        for (CommonProjectCategoryUsed item: categoryUseds) {
            categoryUsedMapper.put(item.getQyCode(), item.getType());
        }
    }

    public Integer getUsedCategoryType(String qyCode) {
        initData();

        Integer type = categoryUsedMapper.get(qyCode);
        return type == null ? 1 : type;
    }

    public int size() {
        initData();
        return categoryUsedMapper.size();
    }
}
