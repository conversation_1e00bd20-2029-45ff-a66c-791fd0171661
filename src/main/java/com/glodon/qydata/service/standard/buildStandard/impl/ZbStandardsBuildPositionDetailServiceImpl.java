package com.glodon.qydata.service.standard.buildStandard.impl;

import com.glodon.qydata.common.RequestContent;
import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildPositionDetail;
import com.glodon.qydata.mapper.standard.buildStandard.ZbStandardsBuildPositionDetailMapper;
import com.glodon.qydata.service.standard.buildStandard.ZbStandardsBuildPositionDetailService;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【zb_standards_build_position_detail(企业标准数据-建造标准定位-细则)】的数据库操作Service实现
* @createDate 2022-08-01 10:57:17
*/
@Service
public class ZbStandardsBuildPositionDetailServiceImpl implements ZbStandardsBuildPositionDetailService {
    @Autowired
    private ZbStandardsBuildPositionDetailMapper standardsBuildPositionDetailMapper;
    @Override
    public List<ZbStandardsBuildPositionDetail> selectSelfByPositionIds(List<Long> positionIds) {
        return standardsBuildPositionDetailMapper.selectSelfByPositionIds(positionIds);
    }

    @Override
    public void batchSaveSelf(List<ZbStandardsBuildPositionDetail> positionDetails) {
        standardsBuildPositionDetailMapper.batchInsertSelf(positionDetails);
    }

    @Override
    public void batchSelfByPositionIds(List<Long> delPositionIds) {
        standardsBuildPositionDetailMapper.batchSelfByPositionIds(delPositionIds);
    }

    @Override
    public List<ZbStandardsBuildPositionDetail> selectSelfByStandardIds(List<Long> standIds) {
        return standardsBuildPositionDetailMapper.selectSelfByStandardIds(standIds);
    }

    @Override
    public void delByStandardIds(List<Long> standardIds) {
        if(CollectionUtils.isEmpty(standardIds)){
            return;
        }
        standardsBuildPositionDetailMapper.deleteByStandardIds(standardIds);
    }

    @Override
    public void batchSave(List<ZbStandardsBuildPositionDetail> positionDetails) {
        if(CollectionUtils.isEmpty(positionDetails)){
            return;
        }
        standardsBuildPositionDetailMapper.batchSave(positionDetails);
    }

    @Override
    public void delSelfByStandardIds(List<Long> standardIds) {
        if(CollectionUtils.isEmpty(standardIds)){
            return;
        }
        standardsBuildPositionDetailMapper.delSelfByStandardIds(standardIds);
    }

    @Override
    public void insertOrUpdateSelf(Long standardId, Long standardDetailId, Long standardDetailDescId, List<ZbStandardsBuildPositionDetail> positionDetails) {
        if (CollectionUtils.isEmpty(positionDetails)){
            return;
        }

        // 插入或更新定位的值
        List<ZbStandardsBuildPositionDetail> insert = new ArrayList<>();
        List<ZbStandardsBuildPositionDetail> update = new ArrayList<>();

        for (ZbStandardsBuildPositionDetail detail : positionDetails) {
            if (detail.getId() == null){
                insert.add(detail);
            }else {
                update.add(detail);
            }
        }

        if (CollectionUtils.isNotEmpty(insert)){
            for (ZbStandardsBuildPositionDetail positionDetail : insert) {
                positionDetail.setId(SnowflakeIdUtils.getNextId());
                positionDetail.setStandardId(standardId);
                positionDetail.setStandardDetailId(standardDetailId);
                positionDetail.setStandardDetailDescId(standardDetailDescId);
            }

            standardsBuildPositionDetailMapper.batchInsertSelf(insert);
        }

        if (CollectionUtils.isNotEmpty(update)){
            standardsBuildPositionDetailMapper.batchUpdateValueSelf(update);
        }
    }

    @Override
    public List<ZbStandardsBuildPositionDetail> selectSelfByDescId(Long descId) {
        return standardsBuildPositionDetailMapper.selectSelfByDescId(descId);
    }
}




