package com.glodon.qydata.service.standard.expression.impl;

import cn.hutool.core.collection.CollUtil;
import com.glodon.qydata.common.RequestContent;
import com.glodon.qydata.common.annotation.BusinessCache;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.constant.OperateConstants;
import com.glodon.qydata.common.enums.ExpressionTypeEnum;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.dto.BatchAddExpressionDto;
import com.glodon.qydata.dto.ExpressionItem;
import com.glodon.qydata.entity.standard.expression.ZbStandardsExpression;
import com.glodon.qydata.entity.standard.feature.ProjectFeature;
import com.glodon.qydata.entity.standard.feature.ProjectFeatureCategoryView;
import com.glodon.qydata.entity.standard.trade.ZbStandardsTrade;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.standard.expression.ZbStandardsExpressionMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureCategoryViewMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureCategoryViewSelfMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureSelfMapper;
import com.glodon.qydata.mapper.standard.projectOrContractInfo.StandardsProjectInfoMapper;
import com.glodon.qydata.mapper.standard.trade.ZbStandardsTradeMapper;
import com.glodon.qydata.service.init.enterprise.InitTradeService;
import com.glodon.qydata.service.standard.category.impl.CommonProjectCategoryUsedService;
import com.glodon.qydata.service.standard.expression.IExpressionService;
import com.glodon.qydata.service.standard.feature.FeatureCommonHandler;
import com.glodon.qydata.service.standard.unit.IZbUnitService;
import com.glodon.qydata.service.system.PublishInfoService;
import com.glodon.qydata.util.PublishLockerUtil;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import com.glodon.qydata.vo.standard.expression.ExpressionResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.glodon.qydata.common.constant.Constants.WHETHER_FALSE;
import static com.glodon.qydata.common.constant.Constants.ZbFeatureConstants.WHETHER_TRUE;

/**
 * <AUTHOR>
 * @description: 工程特征名称类型表及计算口径
 * @date 2021/11/2 17:15
 */
@Service
@Slf4j
public class ExpressionServiceImpl implements IExpressionService {

    @Autowired
    private ZbStandardsExpressionMapper zbStandardsExpressionMapper;

    @Autowired
    private InitTradeService initTradeService;

    @Autowired
    private StandardsProjectInfoMapper standardsProjectInfoMapper;

    @Autowired
    private ProjectFeatureMapper projectFeatureMapper;

    @Autowired
    private ProjectFeatureCategoryViewMapper projectFeatureCategoryViewMapper;

    @Autowired
    private ProjectFeatureSelfMapper projectFeatureSelfMapper;

    @Autowired
    private ProjectFeatureCategoryViewSelfMapper projectFeatureCategoryViewSelfMapper;

    @Autowired
    private CommonProjectCategoryUsedService commonProjectCategoryUsedService;

    @Autowired
    private ZbStandardsTradeMapper tradeMapper;

    @Autowired
    private IZbUnitService zbUnitService;

    @Autowired
    PublishInfoService publishInfoServiceImpl;

    @Autowired
    private FeatureCommonHandler featureCommonHandler;

    @Autowired
    private PublishLockerUtil publishLockerUtil;

    @Override
    public ZbStandardsExpression selectByPrimaryKey(Long id) {
        ZbStandardsExpression zbStandardsExpression = zbStandardsExpressionMapper.selectByPrimaryKey(id);
        if (Objects.isNull(zbStandardsExpression)){
            throw new BusinessException(ResponseCode.ERROR, "该数据不存在");
        }
        return zbStandardsExpression;
    }

    @Override
    public List<ZbStandardsExpression> selectListByCustomCode(String customerCode, Integer type) {
        return zbStandardsExpressionMapper.selectListByCustomCode(customerCode, type);
    }

    @Override
    @BusinessCache(customerCode = "${customerCode}")
    public List<ExpressionResultVo> selectExpression(String customerCode, Integer type, Integer isSkipUserName) {
        List<ExpressionResultVo> list = new ArrayList<>();
        List<ZbStandardsExpression> zbStandardsExpressions = zbStandardsExpressionMapper.selectExpression(customerCode, type);

        zbStandardsExpressions = getZbStandardsExpressions(customerCode, type, zbStandardsExpressions);

        convertExpressions(list, zbStandardsExpressions, isSkipUserName);
        return list;
    }

    @Override
    public List<ExpressionResultVo> selectAllExpression(String customerCode, Integer type, Integer isSkipUserName) {
        List<ExpressionResultVo> list = new ArrayList<>();
        List<ZbStandardsExpression> zbStandardsExpressions = zbStandardsExpressionMapper.selectAllExpression(customerCode, type);

        zbStandardsExpressions = getZbStandardsExpressions(customerCode, type, zbStandardsExpressions);

        convertExpressions(list, zbStandardsExpressions, isSkipUserName);
        return list;
    }

    private List<ZbStandardsExpression> getZbStandardsExpressions(String customerCode, Integer type, List<ZbStandardsExpression> zbStandardsExpressions) {
        if (CollectionUtils.isEmpty(zbStandardsExpressions)){
            initTradeService.initData(customerCode);
            zbStandardsExpressions = zbStandardsExpressionMapper.selectExpression(customerCode, type);
        }
        return zbStandardsExpressions;
    }
    private void convertExpressions(List<ExpressionResultVo> list, List<ZbStandardsExpression> zbStandardsExpressions, Integer isSkipUserName) {
        //获取某个企业人员的globalId和姓名的映射关系
        Map<String, String> globalIdNameMap = new HashMap<>();
        if (WHETHER_FALSE.equals(isSkipUserName)){
            globalIdNameMap = RequestContent.getGlobalIdNameMap(zbStandardsExpressions.stream().map(expr -> String.valueOf(expr.getExpressionCreateGlobalId())).distinct().collect(Collectors.toList()));
        }
        for (ZbStandardsExpression zbStandardsExpression : zbStandardsExpressions) {
            ExpressionResultVo expressionResultVo = new ExpressionResultVo();
            BeanUtils.copyProperties(zbStandardsExpression, expressionResultVo);
            Long createGlobalId = zbStandardsExpression.getExpressionCreateGlobalId();
            if (Objects.nonNull(createGlobalId) && globalIdNameMap.containsKey(createGlobalId.toString())) {
                expressionResultVo.setExpressionCreateGlobalName(globalIdNameMap.get(createGlobalId.toString()));
            }
            list.add(expressionResultVo);
        }
    }

    // 有一批内置转成非内置的计算口径，按内置的规则处理，总在字典表里，不删除
    public static final List<String> ORIGINAL_SYSTEM = Arrays.asList("楼层总数(m2)", "售楼处硬装面积(m2)", "示范样板房面积(m2)",
            "户均建筑面积(m2)", "实体样板间精装总面积(m2)", "装配式建筑面积(m2)", "驳坎长度(m)", "实体样板间面积(m2)", "基坑支护长度(m)");

    @Override
    public void expressionSyn(String customerCode, Integer type){
        List<ZbStandardsExpression> expressionList = new ArrayList<>();

        // 查询工程特征表的计算口径
        List<ZbStandardsExpression> featureExpressionList = projectFeatureMapper.selectExpression(customerCode, type);

        if (CollectionUtils.isNotEmpty(featureExpressionList)){
            expressionList.addAll(featureExpressionList);
        }

        // 查询项目信息表的计算口径
        List<ZbStandardsExpression> projectExpressionList = standardsProjectInfoMapper.selectExpression(customerCode);

        if (CollectionUtils.isNotEmpty(projectExpressionList)){
            expressionList.addAll(projectExpressionList);
        }

        // 排序，单位取最新的
        List<ZbStandardsExpression> sortList = expressionList.stream().sorted(Comparator.comparing(ZbStandardsExpression::getUpdateTime, Comparator.nullsFirst(Comparator.naturalOrder()))).collect(Collectors.toList());

        List<ZbStandardsExpression> numberExpressionList = zbStandardsExpressionMapper.selectListByCustomCode(customerCode, type);

        // 发布时先调整原有的计算口径类型
        numberExpressionList.stream().filter(x -> !Constants.WHETHER_TRUE.equals(x.getExpressionIsFromSystem()) && !ORIGINAL_SYSTEM.contains(x.getOldName())).forEach(item -> {
            item.setIsExpression(Constants.WHETHER_FALSE);
            item.setUnit("");
        });

        // 添加或更新计算口径
        batchAddOrUpdateExpressions(customerCode, type, numberExpressionList, sortList);
    }

    /**
     * 添加或更新计算口径
     * @param customerCode
     * @param type
     * @param numberExpressionList
     * @param sortList
     */
    private void batchAddOrUpdateExpressions(String customerCode, Integer type, List<ZbStandardsExpression> numberExpressionList, List<ZbStandardsExpression> sortList) {

        Map<String, ZbStandardsExpression> nameMap = numberExpressionList.stream().collect(Collectors.toMap(ZbStandardsExpression::getName, Function.identity(), (v1, v2) -> v2));

        int maxOrd = numberExpressionList.stream()
                .map(ZbStandardsExpression::getExpressionOrd)
                .filter(Objects::nonNull).max(Comparator.comparingInt(o -> o)).orElse(0);

        Map<String, ZbStandardsExpression> insertMap = new HashMap<>();

        for (ZbStandardsExpression sortExpression : sortList) {
            String name = sortExpression.getName();
            if (nameMap.containsKey(name)){
                ZbStandardsExpression expression = nameMap.get(name);
                expression.setIsExpression(Constants.WHETHER_TRUE);
                expression.setUnit(sortExpression.getUnit());
                expression.setIsDeleted(Constants.WHETHER_FALSE);
                expression.setExpressionIsUsing(Constants.WHETHER_TRUE);
            }else {
                if (insertMap.containsKey(name)){
                    ZbStandardsExpression expression = insertMap.get(name);
                    expression.setUnit(sortExpression.getUnit());
                }else {
                    ZbStandardsExpression expression = createExpressionItem(name, sortExpression.getUnit(), customerCode, type, ++maxOrd);
                    insertMap.put(name, expression);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(insertMap.values())){
            List<ZbStandardsExpression> insertList = new ArrayList<>(insertMap.values());
            zbStandardsExpressionMapper.batchInsert(insertList);
        }

        if (CollectionUtils.isNotEmpty(numberExpressionList)){
            zbStandardsExpressionMapper.batchUpdateSelective(numberExpressionList);
        }
    }

    /**
     * 生成一条计算口径记录
     * @param name: 计算口径名
     * @param unit: 计算口径单位
     * @param customerCode: 企业编码
     * @param type: 类型
     * @param ord: 顺序
     * @return
     */
    private ZbStandardsExpression createExpressionItem(String name, String unit, String customerCode, Integer type, Integer ord){
        ZbStandardsExpression expression = new ZbStandardsExpression();
        expression.setId(SnowflakeIdUtils.getNextId());
        expression.setExpressionCode("K_"+ SnowflakeIdUtils.getNextId());
        expression.setName(name);
        expression.setTypeCode(ExpressionTypeEnum.TYPE_NUMBER.getCode());
        expression.setIsExpression(Constants.WHETHER_TRUE);
        expression.setUnit(unit);
        expression.setIsDeleted(Constants.WHETHER_FALSE);
        expression.setQyCode(customerCode);
        expression.setType(type);
        expression.setExpressionIsUsing(Constants.WHETHER_TRUE);
        expression.setExpressionOrd(ord);
        expression.setIsUsable(Constants.WHETHER_FALSE);
        expression.setExpressionIsFromSystem(Constants.WHETHER_FALSE);
        return expression;
    }

    /**
     * 批量添加计算口径
     * @param customerCode
     * @param expressionItems
     * @param type
     * @return
     */
    public Map<String, ZbStandardsExpression> addExpressions(String customerCode, List<ExpressionItem> expressionItems, Integer type) {
        List<ZbStandardsExpression> expressions = new ArrayList<>();
        for (ExpressionItem item : expressionItems) {
            ZbStandardsExpression expression = new ZbStandardsExpression();
            expression.setName(item.getName());
            expression.setUnit(item.getUnit());
            expression.setType(type);
            expressions.add(expression);
        }

        // 添加计算口径
        List<ZbStandardsExpression> allEnterpriseExpression = zbStandardsExpressionMapper.selectListByCustomCode(customerCode, type);
        batchAddOrUpdateExpressions(customerCode, type, allEnterpriseExpression, expressions);

        // 返回最新的计算口径
        List<ZbStandardsExpression> numberExpressionList = zbStandardsExpressionMapper.selectListByCustomCode(customerCode, type);
        return numberExpressionList.stream().collect(Collectors.toMap(ZbStandardsExpression::getName, Function.identity(), (v1, v2) -> v2));

    }

    /**
     * 判断特征暂存表是否存在个人修订的数据
     * @param customerCode
     * @param type
     * @return
     */
    private Boolean isInEditing(String customerCode, Integer type) {
        Integer count = projectFeatureSelfMapper.selectRecordCount(customerCode, type);
        if (count > 0) {
            return true;
        }

        count = projectFeatureCategoryViewSelfMapper.selectRecordCount(customerCode, type);
        if (count > 0) {
            return true;
        }

        return false;
    }

    /**
     * 是否允许添加计算口径
     * @param customerCode
     * @param type
     */
    @Override
    public void checkCanAddExpression(String customerCode, Integer type) {
        // 1.验证当前是否处于编辑态
        publishLockerUtil.makeSureNotLocked(OperateConstants.FEATURE, customerCode);

        // 2.检查暂存表是否有数据
        if (isInEditing(customerCode, type)) {
            throw new BusinessException(ResponseCode.EDIT_FORBIDDEN, "当前企业存在未发布的数据,请稍后添加");
        }
    }

    /**
     * 批量添加计算口径:项目划分标准需要在编辑科目的时候可以添加口径
     * @param customerCode
     * @param batchAddExpressionDto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @BusinessCache(customerCode = "${customerCode}", isInvalidateCache = true)
    public void batchAddExpressions(BatchAddExpressionDto batchAddExpressionDto, String globalId, String customerCode, Integer type) {
        if (StringUtils.isEmpty(batchAddExpressionDto.getCategoryCode())) {
            throw new IllegalArgumentException("工程分类编码不能为空");
        }
        if (CollUtil.isEmpty(batchAddExpressionDto.getExpressions())) {
            throw new IllegalArgumentException("无有效的口径信息");
        }

        Map<String, List<ExpressionItem>> featureOfTradeMap = new HashMap<>();
        Set<String> unitSet = new HashSet<>();
        for (ExpressionItem item : batchAddExpressionDto.getExpressions()) {
            if (StringUtils.isEmpty(item.getName()) || StringUtils.isEmpty(item.getUnit()) || CollUtil.isEmpty(item.getTradeCodeVos())) {
                throw new IllegalArgumentException("计算口径关联的名称、单位、专业不能为空");
            }
            for (String tradeName : item.getTradeCodeVos()) {
                if (!StringUtils.isEmpty(tradeName)) {
                    featureOfTradeMap.computeIfAbsent(tradeName, k -> new ArrayList<>()).add(item);
                }
            }

            unitSet.add(item.getUnit());
        }

        if (featureOfTradeMap.size() == 0) {
            throw new IllegalArgumentException("请检查是否包含有效的专业信息");
        }

        // 1.收集所有特征单位,添加到字典中
        zbUnitService.batchAddUnitIfNotExist(new ArrayList<>(unitSet), globalId, customerCode);

        // 2.批量添加计算口径
        Map<String, ZbStandardsExpression> expressionMap = addExpressions(customerCode, batchAddExpressionDto.getExpressions(), type);

        // 3.特征添加到专业视图
        List<ProjectFeature> insertCategoryFeatureList = addExpressionToTradeView(
                batchAddExpressionDto.getCategoryCode(), globalId, customerCode, featureOfTradeMap, type, expressionMap);

        // 4.统一处理分类视图
        syncFeatureToCategoryView(batchAddExpressionDto.getCategoryCode(), customerCode, type, insertCategoryFeatureList);

        // 5.更新发布版本
        publishInfoServiceImpl.updateVersion(customerCode, globalId, OperateConstants.FEATURE);
    }

    /**
     * 添加特征到专业视图
     * @param categoryCode1
     * @param globalId
     * @param customerCode
     * @param featureOfTradeMap
     * @param type
     * @param expressionMap
     * @return 返回添加或修改的特征项
     */
    private List<ProjectFeature> addExpressionToTradeView(String categoryCode1, String globalId, String customerCode, Map<String, List<ExpressionItem>> featureOfTradeMap, Integer type, Map<String, ZbStandardsExpression> expressionMap) {
        // 1.查询企业下的专业
        List<ZbStandardsTrade> dbTradeList = tradeMapper.selectAllListByCustomerCode(customerCode);
        Map<String, Long>  dbTradeCodeAndIdMap = dbTradeList.stream().collect(Collectors.toMap(ZbStandardsTrade::getTradeCode, ZbStandardsTrade::getId, (v1, v2) -> v2));

        List<ProjectFeature> insertFeatureList = new ArrayList();
        List<ProjectFeature> updateFeatureList = new ArrayList();
        List<ProjectFeature> insertCategoryFeatureList = new ArrayList<>();
        // 2.依次处理每个专业
        for (Map.Entry<String, List<ExpressionItem>> entry : featureOfTradeMap.entrySet()) {
            String tradeCode = entry.getKey();
            Long tradeId = dbTradeCodeAndIdMap.get(tradeCode);
            if (tradeId == null) {
                log.warn("专业编码{}不存在,不添加特征信息", tradeCode);
                continue;
            }

            // 3. 收集专业下需要添加的特征
            List<ExpressionItem> expressionItems = entry.getValue();
            List<String> expressionNameList = expressionItems.stream().map(ExpressionItem::getName).collect(Collectors.toList());
            Map<String, ExpressionItem> expressionItemMap = expressionItems.stream().collect(Collectors.toMap(ExpressionItem::getName, Function.identity(), (v1, v2) -> v2));
            // 4. 查询专业下的最大序号
            Integer maxOrd = Optional.ofNullable(projectFeatureMapper.selectTradeFeatureMaxOrd(customerCode, type, tradeId)).orElse(0);
            // 5. 查询专业下已经存在的特征
            List<ProjectFeature> dbTradeFeatureList = projectFeatureMapper.selectTradeFeatureByCondition(customerCode, type, Arrays.asList(tradeId), expressionNameList);
            Set<Long> dbTradeFeatureIds = dbTradeFeatureList.stream().map(x->x.getId()).collect(Collectors.toSet());
            // 5.1 专业下有同名特征,只处理第一个
            Map<String, ProjectFeature> existFeatureNameSet = dbTradeFeatureList.stream().collect(Collectors.toMap(ProjectFeature::getName, Function.identity(), (v1, v2) -> v1));
            // 6. 查询分类视图下已经存在的特征id
            Set<Long> dbCategoryFeatureIds = Collections.emptySet();
            if (CollUtil.isNotEmpty(dbTradeFeatureList)) {
                List<ProjectFeatureCategoryView> dbCategoryFeatureList = projectFeatureCategoryViewMapper.selectByCategoryAndFeatureIds(customerCode, categoryCode1, dbTradeFeatureIds.stream().toList());
                dbCategoryFeatureIds = dbCategoryFeatureList.stream().map(x->x.getFeatureId()).collect(Collectors.toSet());
            }

            // 7.添加专业视图特征
            for (Map.Entry<String, ExpressionItem> item : expressionItemMap.entrySet()) {
                String featureName = item.getKey();
                ZbStandardsExpression dbExpression = expressionMap.get(featureName);
                if (existFeatureNameSet.containsKey(featureName)) {
                    ProjectFeature dbFeature = existFeatureNameSet.get(featureName);
                    // 修改计算口径类型
                    dbFeature.setIsExpression(WHETHER_TRUE);
                    dbFeature.setTypeCode(dbExpression.getTypeCode());
                    dbFeature.setExpressionId(dbExpression.getId());
                    dbFeature.setExpressionCode(dbExpression.getExpressionCode());
                    dbFeature.setUnit(dbExpression.getUnit());
                    dbFeature.setIsExpressionDefault(Constants.ZbFeatureConstants.WHETHER_FALSE);

                    // 7.1 如果分类视图下不存在当前特征id,专业视图添加工程分类
                    if (!dbCategoryFeatureIds.contains(dbFeature.getId())) {
                        // 分类视图下不存在该特征id,需要添加工程分类
                        dbFeature.setProjectType(featureCommonHandler.addNewCategoryCode(dbFeature.getProjectType(), categoryCode1));
                        insertCategoryFeatureList.add(dbFeature);
                    }

                    updateFeatureList.add(dbFeature);
                } else {
                    log.info("补充新的计算口径,专业:{},特征:{}", tradeCode, featureName);
                    ProjectFeature newFeature = new ProjectFeature();
                    newFeature.setId(SnowflakeIdUtils.getNextId());
                    newFeature.setOrdTrade(++maxOrd);
                    newFeature.setName(dbExpression.getName());
                    newFeature.setUnit(dbExpression.getUnit());
                    newFeature.setTypeCode(dbExpression.getTypeCode());
                    newFeature.setCustomerCode(customerCode);
                    newFeature.setType(type);
                    newFeature.setCreateGlobalId(Long.parseLong(globalId));

                    // 专业信息
                    newFeature.setTradeId(tradeId);
                    newFeature.setTradeName(tradeCode);

                    // 计算口径字段
                    newFeature.setIsUsing(dbExpression.getExpressionIsUsing());
                    newFeature.setExpressionId(dbExpression.getId());
                    newFeature.setIsExpression(WHETHER_TRUE);
                    newFeature.setExpressionCode(dbExpression.getExpressionCode());
                    newFeature.setIsExpressionDefault(Constants.ZbFeatureConstants.WHETHER_FALSE);
                    newFeature.setIsDefault(Constants.ZbFeatureConstants.WHETHER_FALSE);
                    newFeature.setIsDeleted(Constants.ZbFeatureConstants.WHETHER_FALSE);

                    // 工程分类字段
                    newFeature.setProjectType(featureCommonHandler.addNewCategoryCode(null, categoryCode1));

                    insertFeatureList.add(newFeature);
                    insertCategoryFeatureList.add(newFeature);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(updateFeatureList)) {
            projectFeatureMapper.batchUpdateSelective(updateFeatureList);
        }
        if(CollectionUtils.isNotEmpty(insertFeatureList)){
            projectFeatureMapper.batchInsert(insertFeatureList);
        }
        return insertCategoryFeatureList;
    }

    /**
     * 同步特征到分类视图
     * @param categoryCode1: 一级工程分类编码
     * @param customerCode: 企业编码
     * @param type: 企业工程分类类型
     * @param featureList: 专业视图下的特征
     */
    private void syncFeatureToCategoryView(String categoryCode1, String customerCode, Integer type, List<ProjectFeature> featureList) {
        Integer maxCategoryOrd = projectFeatureCategoryViewMapper.selectMaxOrdByCondition(categoryCode1, customerCode, type);
        List<ProjectFeatureCategoryView> insertFeatureCategoryViewList = new ArrayList<>();
        for (ProjectFeature feature : featureList) {
            ProjectFeatureCategoryView categoryView = new ProjectFeatureCategoryView();
            categoryView.setId(SnowflakeIdUtils.getNextId());
            categoryView.setTradeId(feature.getTradeId());

            categoryView.setCategoryCode(categoryCode1);
            categoryView.setType(type);
            categoryView.setFeatureId(feature.getId());
            categoryView.setOrdCategory(++maxCategoryOrd);
            categoryView.setQyCodeOld(feature.getQyCodeOld());
            categoryView.setTradeName(feature.getTradeName());

            categoryView.setInvalid(feature.getInvalid());
            categoryView.setCustomerCode(customerCode);
            categoryView.setQyFlag(feature.getQyFlag());

            insertFeatureCategoryViewList.add(categoryView);
        }

        if (!CollUtil.isEmpty(insertFeatureCategoryViewList)) {
            projectFeatureCategoryViewMapper.saveBatch(insertFeatureCategoryViewList);
        }
    }
}
