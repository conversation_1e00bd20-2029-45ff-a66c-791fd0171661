package com.glodon.qydata.service.standard.unit.impl;

import cn.hutool.core.collection.CollUtil;
import com.glodon.qydata.common.annotation.BusinessCache;
import com.glodon.qydata.common.constant.BusinessConstants;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.entity.standard.unit.ZbUnit;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.standard.unit.ZbUnitMapper;
import com.glodon.qydata.service.standard.unit.IZbUnitService;
import com.glodon.qydata.service.system.IGlodonUserService;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.standard.unit.SearchUnitVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 单位相关服务
 * @author: wangq
 * @create: 2022-01-17 14:53
 **/
@Slf4j
@Service
public class ZbUnitServiceImpl implements IZbUnitService{

    @Autowired
    private ZbUnitMapper zbUnitMapper;
    @Autowired
    private IGlodonUserService glodonUserService;

    /**
     * @Description 根据企业编码获取单位列表
     * <AUTHOR>
     * @Date 2022/1/17 14:56
     * @param searchUnitVo
     * @return java.util.List<com.glodon.qydata.entity.standard.unit.ZbUnit>
     **/
    @Override
    @BusinessCache(customerCode = "${searchUnitVo.getCustomerCode()}")
    public List<ZbUnit> getUnitList(SearchUnitVo searchUnitVo) throws BusinessException{
        try {
            String customerCode = searchUnitVo.getCustomerCode();
            String searchText = searchUnitVo.getSearchText();
            if (StringUtils.isEmpty(customerCode)){
                throw new BusinessException("查询单位列表失败,企业编码不能为空");
            }

            Map queryMap = new HashMap();
            List<String> customerCodeList = new ArrayList<>();
            customerCodeList.add(customerCode);
            customerCodeList.add(Constants.StandardsProjectOrContractInfoConstants.BUILT_IN_DATA_CUSTOMER_CODE); //添加内置单位
            queryMap.put(BusinessConstants.CUSTOMER_CODE_LIST,customerCodeList);
            if (StringUtils.isNotEmpty(searchText)){
                queryMap.put("searchText",searchText);
            }
            return zbUnitMapper.selectByCond(queryMap);
        }catch (Exception e){
            log.error("ZbUnitServiceImpl.getUnitList error", e);
            throw new BusinessException("查询单位列表失败，请联系管理员");
        }
    }

    /**
     * @Description 根据单位id获取单位详细信息
     * <AUTHOR>
     * @Date 2022/1/17 15:13
     * @param customerCode
     * @param unitId
     * @return com.glodon.qydata.entity.standard.unit.ZbUnit
     **/
    @Override
    public ZbUnit getDetail(String customerCode, String unitId) {
        try {
            Long id = Long.valueOf(unitId);
            Map queryMap = new HashMap();
            List<String> customerCodeList = new ArrayList<>();
            customerCodeList.add(customerCode);
            customerCodeList.add(Constants.StandardsProjectOrContractInfoConstants.BUILT_IN_DATA_CUSTOMER_CODE); //添加内置单位
            queryMap.put(BusinessConstants.CUSTOMER_CODE_LIST,customerCodeList);
            queryMap.put("id",id);
            queryMap.put("limit",1);
            List<ZbUnit> zbUnits = zbUnitMapper.selectByCond(queryMap);
            if (CollectionUtils.isNotEmpty(zbUnits)){
                return zbUnits.get(0);
            }else {
                return null;
            }
        }catch (Exception e){
            log.error("ZbUnitServiceImpl.getDetail error", e);
            throw new BusinessException("查询单位信息失败，请联系管理员");
        }
    }

    /**
     * @Description 新增单位
     * <AUTHOR>
     * @Date 2022/1/17 15:26
     * @param unitName
     * @param globalId
     * @return void
     **/
    @Override
    public List<ZbUnit> addUnit(String unitName, String globalId) throws BusinessException {
        try {
            String customerCode = glodonUserService.getCustomerCode(globalId);
            Map queryMap = new HashMap();
            List<String> customerCodeList = new ArrayList<>();
            customerCodeList.add(customerCode);
            queryMap.put(BusinessConstants.CUSTOMER_CODE_LIST,customerCodeList);
            customerCodeList.add(Constants.StandardsProjectOrContractInfoConstants.BUILT_IN_DATA_CUSTOMER_CODE); //添加内置单位
            queryMap.put("name",unitName);
            List<ZbUnit> zbUnits = zbUnitMapper.selectByCond(queryMap);
            if (CollectionUtils.isNotEmpty(zbUnits)){
                throw new BusinessException("该单位已经存在");
            }
            //获取该企业最大sort
            Integer maxSort = zbUnitMapper.selectMaxSort(customerCode);
            ZbUnit zbUnit = createZbUnit(unitName, globalId, customerCode, ++maxSort);
            zbUnitMapper.insertSelective(zbUnit);

            return this.getUnitList(new SearchUnitVo(customerCode, null));
        }catch (BusinessException e){
            e.printStackTrace();
            throw e;
        } catch (Exception e){
            log.error("ZbUnitServiceImpl.addUnit error", e);
            throw new BusinessException("新增单位失败，请联系管理员");
        }
    }

    /**
     * @Description 更新单位
     * <AUTHOR>
     * @Date 2022/1/27 8:55
     * @param id
     * @param name
     * @param globalId
     * @return ResponseVo
     **/
    @Override
    public ResponseVo updateUnit(String id, String name, String globalId) {
        try {
            String customerCode = glodonUserService.getCustomerCode(globalId);

            List<String> customerCodeList = new ArrayList<>();
            customerCodeList.add(customerCode);
            customerCodeList.add(Constants.StandardsProjectOrContractInfoConstants.BUILT_IN_DATA_CUSTOMER_CODE);
            ZbUnit unit = zbUnitMapper.selectByPrimaryKey(Long.valueOf(id));
            if (unit==null){
                return ResponseVo.error("未查询到该单位");
            }

            if (Constants.StandardsProjectOrContractInfoConstants.BUILT_IN_DATA_CUSTOMER_CODE.equals(unit.getCustomerCode())){
                return ResponseVo.error("该单位为系统内置，不允许编辑");
            }

            if( !customerCode.equals(unit.getCustomerCode())){
                return ResponseVo.error("该单位不属于您所属的企业，不能修改");
            }

            Map queryMap = new HashMap();
            queryMap.put("name",name);
            queryMap.put(BusinessConstants.CUSTOMER_CODE_LIST,customerCodeList);
            List<ZbUnit> zbUnits = zbUnitMapper.selectByCond(queryMap);
            if (CollectionUtils.isNotEmpty(zbUnits)){
                return ResponseVo.error("该单位已存在");
            }

            unit.setName(name);
            unit.setUpdateId(globalId);
            zbUnitMapper.updateByPrimaryKey(unit);

            return ResponseVo.success();
        }catch (Exception e){
            log.error("ZbUnitServiceImpl.updateUnit error", e);
            return ResponseVo.error("更新单位失败，请联系管理员");
        }
    }

    @Override
    @BusinessCache(customerCode = "${customerCode}", isInvalidateCache = true)
    public void batchAddUnitIfNotExist(List<String> unitList, String globalId, String customerCode) {
        if (CollectionUtils.isEmpty(unitList) || StringUtils.isBlank(globalId) || StringUtils.isBlank(customerCode)) {
            return;
        }

        List<ZbUnit> existingUnits = this.getUnitList(new SearchUnitVo(customerCode, null));

        // 获取现有单位的最大排序值
        Integer maxOrd = existingUnits.stream().filter(unit -> customerCode.equals(unit.getCustomerCode())).map(ZbUnit::getSort).max(Integer::compareTo).orElse(0);

        // 提取现有单位名
        List<String> existingUnitNames = existingUnits.stream().map(ZbUnit::getName).collect(Collectors.toList());

        // 从 unitList 中移除所有与 existingUnitNames 的匹配元素
        unitList.removeAll(existingUnitNames);

        // 创建新单位列表
        List<ZbUnit> newUnits = new ArrayList<>();
        for (String unitName : unitList) {
            if (StringUtils.isBlank(unitName)){
                continue;
            }
            maxOrd++; // 每次都增加 maxOrd 确保排序正确
            ZbUnit zbUnit = createZbUnit(unitName, globalId, customerCode, maxOrd);
            newUnits.add(zbUnit);
        }

        // 批量插入新单位
        if (CollUtil.isNotEmpty(newUnits)) {
            zbUnitMapper.batchInsert(newUnits);
        }
    }

    private ZbUnit createZbUnit(String unitName, String globalId, String customerCode, Integer sort) {
        ZbUnit zbUnit = new ZbUnit();
        zbUnit.setId(SnowflakeIdUtils.getNextId());
        zbUnit.setName(unitName);
        zbUnit.setIsEditable(Constants.WHETHER_TRUE);
        zbUnit.setIsDeleted(Constants.DEL_STATUS_NO_DEL);
        zbUnit.setCustomerCode(customerCode);
        zbUnit.setCreatorId(globalId);
        zbUnit.setCreateTime(new Date());
        zbUnit.setSort(sort);
        return zbUnit;
    }

}
