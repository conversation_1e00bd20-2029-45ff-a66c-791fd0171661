package com.glodon.qydata.service.standard.projectOrContractInfo;

import com.glodon.qydata.dto.ProjectInfoPromotionOrDemotionDTO;
import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.dto.ProjectInfoMoveToDTO;
import com.glodon.qydata.vo.standard.projectOrContractInfo.ProjectInfoShowVo;

import java.util.List;

/**
 * <p>
 * zb_standards_project_info - 主键 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
public interface IStandardsProjectInfoService {

    /**
     * 转换数据结构
     * @throws
     * @param list
     * @param type 信息类型（1：项目信息；2：合同信息。）
     * @param isOpenApi 是否为对外接口调用
     * <AUTHOR>
     * @return {@link List< ProjectInfoShowVo>}
     * @date 2021/10/15 14:35
     */
    List<ProjectInfoShowVo> convertData(List<StandardsProjectInfoEntity> list,
                                        Integer type,
                                        Boolean isOpenApi,
                                        Integer isSkipUserName,
                                        Integer queryType,
                                        Integer treeType,
                                        String customerCode);

    /**
     * 根据id删除项目/合同信息
     * @throws
     * @param ids 主键
     * <AUTHOR>
     * @return
     * @date 2021/10/15 15:19
     */
    void deleteInfoData(String ids, String customerCode, String operateType);

    /**
     * 初始化内置数据
     * @throws
     * @param isShowDelete 是否展示已删除数据（0：展示不包含已删除的；1：展示包含已删除的 ；不传值，默认为0。）
     * @param type 信息类型（1：项目信息；2：合同信息。）
     * <AUTHOR>
     * @return {@link List< StandardsProjectInfoEntity>}
     * @date 2021/10/15 17:10
     */
    List<StandardsProjectInfoEntity> initBuiltInData(String customerCode, Integer isShowDelete, Integer type) throws BusinessException;

    /**
     * 新增项目/合同信息
     * @throws
     * @param entity
     * <AUTHOR>
     * @return
     * @date 2021/10/19 10:09
     */
    StandardsProjectInfoEntity addInfoData(StandardsProjectInfoEntity entity);

    /***
     * @description: excel导入
     * @param entityList 1
     * @throws
     * <AUTHOR>
     * @date 2022/7/7 10:17
     */
    List<StandardsProjectInfoEntity> addInfoDataList(List<StandardsProjectInfoEntity> entityList, Integer standardDataType, String globalId, String customerCode);

    /**
     * 修改工程/合同信息
     * @throws
     * @param vo
     * <AUTHOR>
     * @return {@link StandardsProjectInfoEntity}
     * @date 2021/10/19 14:11
     */
    ProjectInfoShowVo updateInfoData(ProjectInfoShowVo vo) throws BusinessException;

    /**
     * 上下移动
     * @throws
     * @param flag 1：上移，2：下移
     * @param entity
     * <AUTHOR>
     * @return
     * @date 2021/10/19 17:23
     */
    void moveUpDown(Integer flag, StandardsProjectInfoEntity entity);

    /**
     　　* @description: 根据企业编码删除所有项目/合同标准
     　　* @param  customerCode 企业编码
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/9 19:41
     　　*/
    void deleteByCustomerCode(String customerCode);

    List<StandardsProjectInfoEntity> getSelfProjectInfoList(Integer isShowDelete, Integer type) throws BusinessException;

    /**
     * 发布数据
     */
    void publish(String customerCode, String globalId, Integer type) throws BusinessException;

    /**
     * @description: 获取【产品定位】的未删除的枚举值
     * @param customerCode
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2022/8/2 10:41
     */
    List<String> getProductPositioning(String customerCode);

    /**
     * @description: 查询企业是否有产品定位
     * @author: luoml-b
     * @date: 2024/4/16 14:11
     * @param: customerCode
     * @return: boolean
     **/
    boolean withoutPosition(String customerCode);

    /**
     * @description: 移动到
     * @author: luoml-b
     * @date: 2024/8/30 15:07
     * @param: moveToVo
     **/
    void moveTo(ProjectInfoMoveToDTO projectInfoMoveToDTO);

    /**
     * @description: 升降级
     * @author: luoml-b
     * @date: 2024/9/2 10:03
     * @param: projectInfoPromotionOrDemotionDTO
     **/
    void promotionOrDemotion(ProjectInfoPromotionOrDemotionDTO projectInfoPromotionOrDemotionDTO);
}
