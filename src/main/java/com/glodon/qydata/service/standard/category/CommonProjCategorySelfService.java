package com.glodon.qydata.service.standard.category;

import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.standard.category.CommonProjCategoryAddVo;
import com.glodon.qydata.vo.standard.category.CommonProjCategoryUpdateVo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/15 17:18
 */
public interface CommonProjCategorySelfService {
    

    /**
     * 新增工程分类
     * @param categoryVo
     * @param customerCode
     * @param globalId
     * @return com.glodon.zbw.dataManager.standardData.category.domain.CommonProjCategory
     * <AUTHOR>
     * @date 2021/10/19 14:59 
     */
    CommonProjCategory saveCommonProjCategory(CommonProjCategoryAddVo categoryVo, String customerCode, String globalId, Integer type);
    /***
     * @description: 批量新增分类
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2022/7/7 14:44
     */
    void saveCommonProjCategoryList(List<CommonProjCategory> excelCategoryList);
    /**
     * 同父级重名校验
     * @param commonprojcategoryid
     * @param type
     * @param categoryName
     * @param customerCode
     * @return int
     * <AUTHOR>
     * @date 2021/10/19 14:59 
     */
    int nameRepeatCheck(String commonprojcategoryid, Integer addType, String categoryName, String customerCode, String globalId, Integer type);
    
    /**
     * 上移下移
     * @return com.glodon.zbw.dataManager.standardData.category.domain.CommonProjCategory
     * <AUTHOR>
     * @date 2021/10/19 14:59 
     */
    CommonProjCategory downOrUp(String categoryCode, String operate, String customerCode, Integer type);
    
    /**
     * 删除工程分类
     * @return boolean
     * <AUTHOR>
     * @date 2021/10/19 14:59 
     */
    void deleteCategory(String categoryCode, String customerCode, Integer type);
    
    /**
     * 修改工程分类
     * @param updateVo
     * @param customerCode
     * @param globalId
     * @return com.glodon.zbw.dataManager.common.R<com.glodon.zbw.dataManager.standardData.category.domain.CommonProjCategory>
     * <AUTHOR>
     * @date 2021/10/19 14:59 
     */
    ResponseVo<CommonProjCategory> updateCategory(List<CommonProjCategoryUpdateVo> updateVo, String customerCode, String globalId, Integer type);

    /**
     * 发布暂存数据
     * @param customerCode 企业编码
     */
    void publish(String customerCode, String globalId, Integer type);

    /**
     * 获取所用户暂存有工程类别信息
     * @param customerCode 企业编码
     * @return
     */
    List<CommonProjCategory> getSelfCategoryListTree(String customerCode, Integer type) throws BusinessException;

}
