package com.glodon.qydata.service.standard.expression;

import com.glodon.qydata.entity.standard.expression.ZbStandardsExpression;
import com.glodon.qydata.vo.standard.expression.ExpressionResultVo;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 工程特征名称类型表及计算口径
 * @date 2021/11/2 17:13
 */
public interface IExpressionSelfService {

    /**
     *  存入 名称+类型+枚举值； 返回 主键id 。 如果不存在就新增，存在就只返回id
     * @param name
     * @param typeCode
     * @param option
     * @param customCode
     * @param globalId
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2021/11/5 8:53
     */
    Long addData(String name, String typeCode, String option, String customCode, Long globalId, Integer type);
    /***
     * @description: 批量处理字典,拷贝字addData
     * @param name 1
     * @param typeCode 2
     * @param option 3
     * @param customCode 4
     * @param globalId 5
     * @param type 6
     * @param zbStandardsExpressionList 7
     * @param list 8
     * @return java.lang.Long
     * @throws
     * <AUTHOR>
     * @date 2022/7/15 20:50
     */
    Long addDataByCache(String name, String typeCode, String option, String customCode, Long globalId, Integer type, List<ZbStandardsExpression> zbStandardsExpressionList, List<ZbStandardsExpression> list);

    /**
     *  根据主键查询
     * @param id
     * @return com.gcj.zblib.standardData.expression.entity.ZbStandardsExpression
     * <AUTHOR>
     * @date 2021/11/5 8:51
     */
    ZbStandardsExpression selectByPrimaryKey(Long id);

    /**
     * 删除计算口径
     * @param id
     * @return void
     * <AUTHOR>
     * @date 2021/11/5 16:16
     */
    void deleteByPrimaryKey(Long id);

    /**
     * 特征页面勾选是否计算口径，加入计算口径字典表
     * @param expressionId
     * @param globalId
     * @return void
     * <AUTHOR>
     * @date 2021/11/8 14:14
     */
    void addExpressionDict(Long expressionId, long globalId);

    /**
     * 是否启用，启用前需先输入单位
     * @param expressionId
     * @param operate
     * @param unit
     * @return void
     * <AUTHOR>
     * @date 2021/11/8 14:39
     */
    void enable(Long expressionId, Integer operate, String unit);

    /**
     *  按企业查询
     * @param customerCode
     * @return java.util.List<com.gcj.zblib.standardData.expression.entity.ZbStandardsExpression>
     * <AUTHOR>
     * @date 2022/2/10 15:04
     */
    List<ZbStandardsExpression> selectListBySelfCustomCode(String customerCode, Long selfGlobalId, Integer type);

    /**
     * 获取暂存表的计算口径数据
     * @throws
     * @param customerCode
     * <AUTHOR>
     * @return {@link List< ExpressionResultVo>}
     * @date 2022/3/1 17:37
     */
    List<ExpressionResultVo> selectSelfExpression(String customerCode, Integer type);
}
