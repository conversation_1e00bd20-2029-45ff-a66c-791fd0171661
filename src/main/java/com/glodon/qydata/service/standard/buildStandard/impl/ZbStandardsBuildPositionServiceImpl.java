package com.glodon.qydata.service.standard.buildStandard.impl;

import cn.hutool.core.collection.CollUtil;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.dto.StandardPositionDto;
import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard;
import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildCategory;
import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildPosition;
import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildPositionDetail;
import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.standard.buildStandard.ZbStandardsBuildPositionMapper;
import com.glodon.qydata.service.standard.buildStandard.ZbStandardsBuildCategoryService;
import com.glodon.qydata.service.standard.buildStandard.ZbStandardsBuildPositionDetailService;
import com.glodon.qydata.service.standard.buildStandard.ZbStandardsBuildPositionService;
import com.glodon.qydata.service.standard.category.CommonProjCategoryService;
import com.glodon.qydata.service.standard.projectOrContractInfo.IStandardsProjectInfoService;
import com.glodon.qydata.util.EmptyUtil;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【zb_standards_build_position(企业标准数据-建造标准定位)】的数据库操作Service实现
 * @createDate 2022-08-01 10:57:17
 */
@Service
public class ZbStandardsBuildPositionServiceImpl implements ZbStandardsBuildPositionService {
    @Autowired
    private ZbStandardsBuildPositionMapper standardsBuildPositionMapper;
    @Autowired
    private CommonProjCategoryService commonProjCategoryService;
    @Autowired
    private IStandardsProjectInfoService standardsProjectInfoService;
    @Autowired
    private ZbStandardsBuildCategoryService standardsBuildCategoryService;
    @Autowired
    private ZbStandardsBuildPositionDetailService standardsBuildPositionDetailService;

    @Override
    public void setCategoryAndPositions(List<ZbProjectStandard> returnList, String customerCode, boolean isSetCategoryAndPosition) {
        if (CollectionUtils.isEmpty(returnList)) {
            return;
        }
        List<Long> standardIds = returnList.stream().map(x -> x.getId()).collect(Collectors.toList());
        //根据建造标准查询所有的定位
        List<ZbStandardsBuildPosition> positionList = standardsBuildPositionMapper.selectByStandardIds(standardIds);
        if (CollectionUtils.isEmpty(positionList)) {
            return;
        }
        Set<String> categoryCodeSet = positionList.parallelStream().map(ZbStandardsBuildPosition::getCategoryCode).collect(Collectors.toSet());
        List<CommonProjCategory> categoryList = commonProjCategoryService.getCategoryByCodes(categoryCodeSet, customerCode);
        if (CollectionUtils.isEmpty(categoryList)) {
            return;
        }
        categoryList = categoryList.stream().filter(x -> !x.getIsDeleted()).collect(Collectors.toList());
        Map<String, CommonProjCategory> commonProjCategoryMap = categoryList.stream().collect(Collectors.toMap(x -> x.getCommonprojcategoryid(), x -> x, (k1, k2) -> k1));
        Map<Long, List<ZbStandardsBuildPosition>> positionMap = positionList.stream().collect(Collectors.groupingBy(x -> x.getStandardId()));
        // 查询项目信息【产品定位】
        List<String> selectList = standardsProjectInfoService.getProductPositioning(customerCode);
        returnList.stream().forEach(item -> {
            List<ZbStandardsBuildPosition> buildPositions = positionMap.get(item.getId());
            if (CollectionUtils.isEmpty(buildPositions)) {
                return;
            }
            Map<String, List<ZbStandardsBuildPosition>> categoryMap = buildPositions.stream().collect(Collectors.groupingBy(x -> x.getCategoryCode()));
            filterCategoryAndPosition(item, categoryMap, selectList, commonProjCategoryMap);
        });

        if (isSetCategoryAndPosition) {
            this.listSetCategoryAndPosition(returnList, positionList, categoryList, selectList);
        }
    }

    /**
     * 建造标准列表放入分类和定位的信息
     *
     * @param standardList
     * @param positionList
     * @param categoryList
     * @param positioningNameList
     */
    private void listSetCategoryAndPosition(List<ZbProjectStandard> standardList,
                                            List<ZbStandardsBuildPosition> positionList,
                                            List<CommonProjCategory> categoryList,
                                            List<String> positioningNameList) {

        if (CollectionUtils.isEmpty(standardList) || CollectionUtils.isEmpty(positionList)
                || CollectionUtils.isEmpty(categoryList) || CollectionUtils.isEmpty(positioningNameList)) {
            return;
        }

        // 分类
        List<Long> standardIds = standardList.stream().map(ZbProjectStandard::getId).collect(Collectors.toList());
        List<ZbStandardsBuildCategory> buildCategoryList = standardsBuildCategoryService.selectByStandardIds(standardIds);
        Map<Long, List<ZbStandardsBuildCategory>> standardIdMap = buildCategoryList.stream()
                .collect(Collectors.groupingBy(ZbStandardsBuildCategory::getStandardId));

        // 定位
        Map<Long, List<ZbStandardsBuildPosition>> positionMap = positionList.stream().collect(Collectors.groupingBy(ZbStandardsBuildPosition::getStandardId));

        standardList.forEach(item -> {
            Long standardId = item.getId();
            if (!standardIdMap.containsKey(standardId) || !positionMap.containsKey(standardId)) {
                return;
            }
            List<StandardPositionDto> positionDtoList =
                    convertStandardPositionDto(standardIdMap.get(standardId), positionMap.get(standardId), categoryList, positioningNameList);
            item.setCategoryAndPosition(positionDtoList);
        });
    }

    @Override
    public List<StandardPositionDto> getPositionList(Long standardId, String customerCode, boolean isSelf) {
        //根据建造标准查询列表
        List<ZbStandardsBuildCategory> categories;
        //根据建造标准查询所有的定位
        List<ZbStandardsBuildPosition> positionList;

        if (isSelf) {
            categories = standardsBuildCategoryService.selectSelfByStandardId(standardId);
            positionList = standardsBuildPositionMapper.selectSelfByStandardId(standardId);
        } else {
            categories = standardsBuildCategoryService.selectByStandardId(standardId);
            positionList = standardsBuildPositionMapper.selectByStandardId(standardId);
        }

        if (CollectionUtils.isEmpty(categories)) {
            return Lists.newArrayList();
        }

        // 工程分类
        Set<String> categoryCodeSet = categories.parallelStream().map(ZbStandardsBuildCategory::getCategoryCode).collect(Collectors.toSet());
        List<CommonProjCategory> categoryList = commonProjCategoryService.getCategoryByCodes(categoryCodeSet, customerCode);
        if (CollectionUtils.isEmpty(categoryList)) {
            return Lists.newArrayList();
        }

        // 查询项目信息【产品定位】
        List<String> selectList = standardsProjectInfoService.getProductPositioning(customerCode);

        return convertStandardPositionDto(categories, positionList, categoryList, selectList);
    }

    @Override
    @Transactional
    public void savePosition(String customerCode, String globalId, Long standardId, List<StandardPositionDto> standardPositionDtoList) {
        //删除所有数据
        boolean isDel = delAll(standardId, standardPositionDtoList);
        if (isDel) {
            return;
        }
        //校验工程分类是否重复
        checkCategory(standardPositionDtoList);
        //根据建造标准查询列表
        List<ZbStandardsBuildCategory> categories = standardsBuildCategoryService.selectSelfByStandardId(standardId);
        //根据建造标准查询所有的定位
        List<ZbStandardsBuildPosition> positionList = standardsBuildPositionMapper.selectSelfByStandardId(standardId);
        //将dto 转为 备注表可保存的实体对象
        List<ZbStandardsBuildCategory> insertCategoryList = convertDtoToBuildCategory(standardId, standardPositionDtoList, categories);
        //需要删除产品定位value表的数据
        List<Long> delPositionIds = new ArrayList<>();
        //获取插入定位信息列表
        List<ZbStandardsBuildPosition> positionInsertList = getInsertPositionList(standardId, customerCode, standardPositionDtoList, positionList, delPositionIds);
        //复制产品定位value表数据
        List<ZbStandardsBuildPositionDetail> positionDetails = copyPositionDetailData(positionInsertList);
        //保存备注表数据
        if (CollectionUtils.isNotEmpty(insertCategoryList)) {
            //删除备注表
            standardsBuildCategoryService.delSelfByStandardId(standardId);
            //插入备注表
            standardsBuildCategoryService.batchSaveSelf(insertCategoryList);
        }
        //保存定位信息
        if (CollectionUtils.isNotEmpty(positionInsertList)) {
            //删除定位表
            standardsBuildPositionMapper.deleteSelfByStandardId(standardId);
            //保存定位表
            standardsBuildPositionMapper.batchSaveSelf(positionInsertList);
        }
        if (CollectionUtils.isNotEmpty(positionDetails)) {
            standardsBuildPositionDetailService.batchSaveSelf(positionDetails);
        }
        //删除产品定位value表的数据
        if (CollectionUtils.isNotEmpty(delPositionIds)) {
            standardsBuildPositionDetailService.batchSelfByPositionIds(delPositionIds);
        }

    }

    /**
     * 删除所有数据
     *
     * @param standardId
     * @param standardPositionDtoList
     */
    private boolean delAll(Long standardId, List<StandardPositionDto> standardPositionDtoList) {
        if (CollectionUtils.isNotEmpty(standardPositionDtoList)) {
            return false;
        }
        standardsBuildCategoryService.delSelfByStandardId(standardId);
        standardsBuildPositionMapper.deleteSelfByStandardId(standardId);
        standardsBuildPositionDetailService.delSelfByStandardIds(Lists.newArrayList(standardId));
        return true;
    }

    /**
     * 检查工程分类是否重复
     *
     * @param standardPositionDtoList
     */
    private void checkCategory(List<StandardPositionDto> standardPositionDtoList) {
        if (CollectionUtils.isEmpty(standardPositionDtoList)) {
            return;
        }
        Set<String> sets = standardPositionDtoList.stream().map(StandardPositionDto::getCategoryCode).collect(Collectors.toSet());
        if (sets.size() < standardPositionDtoList.size()) {
            throw new BusinessException("工程分类不能重复");
        }
    }

    @Override
    public List<ZbStandardsBuildPosition> selectSelfByStandardIds(List<Long> standIds) {
        return standardsBuildPositionMapper.selectSelfByStandardIds(standIds);
    }

    @Override
    public void delByStandardIds(List<Long> standardIds) {
        if (CollectionUtils.isEmpty(standardIds)) {
            return;
        }
        standardsBuildPositionMapper.deleteByStandardIds(standardIds);
    }

    @Override
    public void batchSave(List<ZbStandardsBuildPosition> buildPositions) {
        if (CollectionUtils.isEmpty(buildPositions)) {
            return;
        }
        standardsBuildPositionMapper.batchSave(buildPositions);
    }

    @Override
    public void delSelfByStandardIds(List<Long> standardIds) {
        if (CollectionUtils.isEmpty(standardIds)) {
            return;
        }
        standardsBuildPositionMapper.deleteSelfByStandardIds(standardIds);
    }

    /**
     * 复制产品定位value表数据
     *
     * @param positionInsertList
     * @return
     */
    private List<ZbStandardsBuildPositionDetail> copyPositionDetailData(List<ZbStandardsBuildPosition> positionInsertList) {
        List<ZbStandardsBuildPositionDetail> list = Lists.newArrayList();
        if (CollectionUtils.isEmpty(positionInsertList)) {
            return list;
        }
        List<Long> ids = positionInsertList.stream().filter(item -> item.getCopyDataFromId() != null).map(ZbStandardsBuildPosition::getCopyDataFromId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return list;
        }
        List<ZbStandardsBuildPositionDetail> positionDetails = standardsBuildPositionDetailService.selectSelfByPositionIds(ids);
        if (CollectionUtils.isEmpty(positionDetails)) {
            return list;
        }
        Map<Long, List<ZbStandardsBuildPosition>> positionMap = positionInsertList.stream().filter(item -> item.getCopyDataFromId() != null)
                .collect(Collectors.groupingBy(ZbStandardsBuildPosition::getCopyDataFromId));
        for (ZbStandardsBuildPositionDetail positionDetail : positionDetails) {
            Long positionId = positionDetail.getPositionId();
            List<ZbStandardsBuildPosition> oldBuildPositions = positionMap.get(positionId);
            if (CollectionUtils.isEmpty(oldBuildPositions)) {
                continue;
            }
            oldBuildPositions.forEach(oldBuildPosition -> {
                ZbStandardsBuildPositionDetail detail = new ZbStandardsBuildPositionDetail();
                BeanUtils.copyProperties(positionDetail, detail);
                detail.setId(SnowflakeIdUtils.getNextId());
                detail.setPositionId(oldBuildPosition.getId());
                list.add(detail);
            });

        }

        return list;
    }

    /**
     * 获取产品定位数据列表
     *
     * @param standardId
     * @param standardPositionDtoList
     * @param positionList
     * @return
     */
    private List<ZbStandardsBuildPosition> getInsertPositionList(Long standardId,
                                                                 String customerCode,
                                                                 List<StandardPositionDto> standardPositionDtoList,
                                                                 List<ZbStandardsBuildPosition> positionList,
                                                                 List<Long> delPositionIds
    ) {
        List<ZbStandardsBuildPosition> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(standardPositionDtoList)) {
            return list;
        }
        standardPositionDtoList = standardPositionDtoList.stream().sorted(Comparator.comparingInt(StandardPositionDto::getOrd)).collect(Collectors.toList());
        List<String> selectList = standardsProjectInfoService.getProductPositioning(customerCode);
        List<String> categoryCodes = standardPositionDtoList.stream().map(StandardPositionDto::getCategoryCode).collect(Collectors.toList());
        Map<String, List<ZbStandardsBuildPosition>> positionMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(positionList)) {
            // 不在产品定位数据字典中的数据
            if (CollUtil.isNotEmpty(selectList)) {
                List<ZbStandardsBuildPosition> filterList = positionList.stream().filter(x -> StringUtils.isNotEmpty(x.getName()) && !selectList.contains(x.getName()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(filterList)) {
                    delPositionIds.addAll(filterList.stream().map(ZbStandardsBuildPosition::getId).collect(Collectors.toList()));
                    positionList.removeAll(filterList);
                }
            }
            //删除不存在的业态信息
            positionMap = positionList.stream().collect(Collectors.groupingBy(ZbStandardsBuildPosition::getCategoryCode));
            List<Long> delIds = positionList.stream().filter(x -> !categoryCodes.contains(x.getCategoryCode()))
                    .map(ZbStandardsBuildPosition::getId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(delIds)) {
                delPositionIds.addAll(delIds);
            }
        }
//        if (CollUtil.isEmpty(selectList)) {
//            return list;
//        }
        int selectDictSize = CollUtil.isNotEmpty(selectList) ? selectList.size() : 1;
        int index =0;
        for(StandardPositionDto standardPositionDto : standardPositionDtoList){
            int ord = selectDictSize*index;
            String categoryCode = standardPositionDto.getCategoryCode();
            //原表产品定位
            List<ZbStandardsBuildPosition> positions = positionMap.get(categoryCode);
            //原表没有数据则保存
            dealNoPosition(positions, standardPositionDto, standardId, list, ord);
            //如果原表只有工程分类没有产品定位，新数据有产品定位或只有业态
            dealOldOnlyCategory(list, delPositionIds, positions, standardPositionDto, standardId, ord);
            //原表有产品定位，新数据没有产品定位
            dealOldHavePositionNewNoPosition(list, delPositionIds, positions, standardPositionDto, standardId, ord);
            //原表有产品定位，新数据也有产品定位
            dealOldHavePositionNewHavePosition(list, delPositionIds, positions, standardPositionDto, standardId, ord);
            index++;
        }

        return list;

    }

    /**
     * 如果没有产品定位，则全部保存
     *
     * @param standardPositionDto
     * @param list
     */
    private void dealNoPosition(List<ZbStandardsBuildPosition> positions,
                                StandardPositionDto standardPositionDto,
                                Long standardId,
                                List<ZbStandardsBuildPosition> list,
                                Integer ord) {
        if (CollectionUtils.isNotEmpty(positions)) {
            return;
        }
        List<String> newPositions = standardPositionDto.getPositions();
        if (CollectionUtils.isEmpty(newPositions)) {
            ZbStandardsBuildPosition zbStandardsBuildPosition = getZbStandardsBuildPosition(standardPositionDto, standardId);
            zbStandardsBuildPosition.setType(Constants.CATEGORY);
            zbStandardsBuildPosition.setOrd(ord + 1);
            list.add(zbStandardsBuildPosition);
        } else {
            int i = ord;
            for (String item : newPositions) {
                ZbStandardsBuildPosition zbStandardsBuildPosition = getZbStandardsBuildPosition(standardPositionDto, standardId);
                zbStandardsBuildPosition.setName(item);
                zbStandardsBuildPosition.setOrd(i);
                zbStandardsBuildPosition.setType(Constants.POSITION);
                i++;
                list.add(zbStandardsBuildPosition);
            }
        }
    }

    private ZbStandardsBuildPosition getZbStandardsBuildPosition(StandardPositionDto standardPositionDto, Long standardId) {
        ZbStandardsBuildPosition zbStandardsBuildPosition = new ZbStandardsBuildPosition();
        BeanUtils.copyProperties(standardPositionDto, zbStandardsBuildPosition);
        zbStandardsBuildPosition.setStandardId(standardId);
        zbStandardsBuildPosition.setBuildCategoryId(standardPositionDto.getId());
        zbStandardsBuildPosition.setId(SnowflakeIdUtils.getNextId());
        zbStandardsBuildPosition.setOrd(0);
        return zbStandardsBuildPosition;
    }

    /**
     * 原表有产品定位，新数据也有产品定位
     *
     * @param list
     * @param delPositionIds
     * @param positions
     * @param standardPositionDto
     * @param standardId
     */
    private void dealOldHavePositionNewHavePosition(List<ZbStandardsBuildPosition> list,
                                                    List<Long> delPositionIds,
                                                    List<ZbStandardsBuildPosition> positions,
                                                    StandardPositionDto standardPositionDto,
                                                    Long standardId,
                                                    int ord) {
        List<String> newPositions = standardPositionDto.getPositions();
        String categoryCode = standardPositionDto.getCategoryCode();
        if (CollectionUtils.isNotEmpty(positions) && CollectionUtils.isNotEmpty(newPositions)) {
            Optional optional = positions.stream().filter(x -> 1 == x.getType()).findFirst();
            if (optional.isPresent()) {
                return;
            }
            Map<String, ZbStandardsBuildPosition> buildPositionMap = positions.stream().collect(Collectors.toMap(ZbStandardsBuildPosition::getName, x -> x, (k1, k2) -> k1));
            int index = ord;
            for (String position : newPositions) {
                ZbStandardsBuildPosition buildPosition = buildPositionMap.get(position);
                if (buildPosition == null) {
                    buildPosition = ZbStandardsBuildPosition.builder()
                            .id(SnowflakeIdUtils.getNextId())
                            .buildCategoryId(standardPositionDto.getId())
                            .categoryCode(categoryCode)
                            .standardId(standardId)
                            .type(Constants.POSITION)
                            .copyDataFromId(positions.get(0).getId())
                            .name(position)
                            .build();

                }
                buildPosition.setOrd(index);
                index++;
                list.add(buildPosition);
            }
            //过滤需要删除的定位信息
            List<Long> delIds = positions.stream().filter(x -> !newPositions.contains(x.getName())).map(ZbStandardsBuildPosition::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(delIds)) {
                delPositionIds.addAll(delIds);
            }
        }
    }

    /**
     * 原表有产品定位，新数据没有产品定位
     *
     * @param list
     * @param delPositionIds
     * @param positions
     * @param standardPositionDto
     * @param standardId
     */
    private void dealOldHavePositionNewNoPosition(List<ZbStandardsBuildPosition> list,
                                                  List<Long> delPositionIds,
                                                  List<ZbStandardsBuildPosition> positions,
                                                  StandardPositionDto standardPositionDto,
                                                  Long standardId,
                                                  int ord) {
        String categoryCode = standardPositionDto.getCategoryCode();
        if (CollectionUtils.isNotEmpty(positions)) {
            Optional optional = positions.stream().filter(x -> 1 == x.getType()).findFirst();
            List<String> newPositions = standardPositionDto.getPositions();
            if (!optional.isPresent() && CollectionUtils.isEmpty(newPositions)) {
                //需要将原表产品定位的值按顺序取第一个复制到业态上
                ZbStandardsBuildPosition newBuildPosition = ZbStandardsBuildPosition
                        .builder()
                        .categoryCode(categoryCode)
                        .standardId(standardId)
                        .id(SnowflakeIdUtils.getNextId())
                        .buildCategoryId(standardPositionDto.getId())
                        .ord(ord + 1)
                        .copyDataFromId(positions.get(0).getId())
                        .type(Constants.CATEGORY)
                        .build();
                list.add(newBuildPosition);
                //将原产品定位放到要删除的列表中
                delPositionIds.addAll(positions.stream().map(ZbStandardsBuildPosition::getId).collect(Collectors.toList()));
            }
        }
    }

    /**
     * 如果原表只有工程分类没有产品定位
     *
     * @param list
     * @param delPositionIds
     * @param positions
     * @param standardPositionDto
     */
    private void dealOldOnlyCategory(List<ZbStandardsBuildPosition> list,
                                     List<Long> delPositionIds,
                                     List<ZbStandardsBuildPosition> positions,
                                     StandardPositionDto standardPositionDto,
                                     Long standardId,
                                     int ord) {
        String categoryCode = standardPositionDto.getCategoryCode();
        if (CollectionUtils.isNotEmpty(positions) && positions.size() == 1 && StringUtils.isBlank(positions.get(0).getName())) {
            List<String> newPositions = standardPositionDto.getPositions();
            ZbStandardsBuildPosition buildPosition = positions.get(0);
            if (CollectionUtils.isEmpty(newPositions)) {
                buildPosition.setOrd(ord + 1);
                //如果只有业态则将原数据加到列表
                list.add(buildPosition);
            } else {
                //如果增加了产品定位则将产品定位加到列表，并记录原业态的id，需要将业态的值复制到定位上，然后删除原业态上的值
                int index = ord;
                for (String position : newPositions) {
                    ZbStandardsBuildPosition newBuildPosition = ZbStandardsBuildPosition.builder()
                            .id(SnowflakeIdUtils.getNextId())
                            .buildCategoryId(standardPositionDto.getId())
                            .categoryCode(categoryCode)
                            .copyDataFromId(buildPosition.getId())
                            .standardId(standardId)
                            .type(Constants.POSITION)
                            .ord(index)
                            .name(position)
                            .build();
                    list.add(newBuildPosition);
                    index++;
                }
                delPositionIds.add(buildPosition.getId());
            }

        }
    }

    /**
     * 将dto 转为实体对象
     *
     * @param standardId
     * @param standardPositionDtoList
     * @param categories
     * @return
     */
    private List<ZbStandardsBuildCategory> convertDtoToBuildCategory(Long standardId,
                                                                     List<StandardPositionDto> standardPositionDtoList,
                                                                     List<ZbStandardsBuildCategory> categories) {
        List<ZbStandardsBuildCategory> insertList = new ArrayList<>();
        if (CollectionUtils.isEmpty(standardPositionDtoList)) {
            return insertList;
        }
        Map<String, ZbStandardsBuildCategory> categoryMap = categories.stream().collect(Collectors.toMap(ZbStandardsBuildCategory::getCategoryCode, x -> x, (k1, k2) -> k1));
        for (StandardPositionDto item : standardPositionDtoList) {
            ZbStandardsBuildCategory zbStandardsBuildCategory = new ZbStandardsBuildCategory();
            ZbStandardsBuildCategory oldStandardsBuildCategory = categoryMap.get(item.getCategoryCode());
            if (oldStandardsBuildCategory != null) {
                BeanUtils.copyProperties(oldStandardsBuildCategory, zbStandardsBuildCategory);
            } else {
                zbStandardsBuildCategory.setCategoryCode(item.getCategoryCode());
                Long id = SnowflakeIdUtils.getNextId();
                zbStandardsBuildCategory.setId(id);
                item.setId(id);
            }
            zbStandardsBuildCategory.setRemark(item.getRemark());
            zbStandardsBuildCategory.setStandardId(standardId);
            zbStandardsBuildCategory.setOrd(item.getOrd());
            insertList.add(zbStandardsBuildCategory);
        }
        return insertList;
    }

    private List<StandardPositionDto> convertStandardPositionDto(List<ZbStandardsBuildCategory> categories,
                                                                 List<ZbStandardsBuildPosition> positionList,
                                                                 List<CommonProjCategory> categoryList,
                                                                 List<String> selectList) {
        List<StandardPositionDto> standardPositionList = new ArrayList<>();
        Map<String, CommonProjCategory> commonProjCategoryMap = categoryList.stream().collect(Collectors.toMap(CommonProjCategory::getCommonprojcategoryid, x -> x, (k1, k2) -> k1));
        Map<Long, List<ZbStandardsBuildPosition>> positionMap = positionList.stream().collect(Collectors.groupingBy(ZbStandardsBuildPosition::getBuildCategoryId));
        categories.forEach(item -> {
            StandardPositionDto standardPositionDto = new StandardPositionDto();
            //判断次业态是否存在
            CommonProjCategory commonProjCategory = commonProjCategoryMap.get(item.getCategoryCode());
            if (commonProjCategory == null) {
                return;
            }
            standardPositionDto.setCategoryName(commonProjCategory.getCategoryname());
            List<ZbStandardsBuildPosition> positions = positionMap.get(item.getId());
            if (CollectionUtils.isNotEmpty(positions) && positions.size() > 0) {
                if (CollUtil.isNotEmpty(selectList)) {
                    positions = positions.stream().filter(y -> selectList.contains(y.getName())).collect(Collectors.toList());
                    List<String> positionNames = positions.stream().map(ZbStandardsBuildPosition::getName).collect(Collectors.toList());
                    standardPositionDto.setPositions(positionNames);
                } else {
                    standardPositionDto.setPositions(Collections.emptyList());
                }

            }
            standardPositionDto.setOrd(item.getOrd());
            standardPositionDto.setRemark(item.getRemark());
            standardPositionDto.setId(item.getId());
            standardPositionDto.setCategoryCode(item.getCategoryCode());
            standardPositionList.add(standardPositionDto);
        });
        return standardPositionList;
    }

    private void filterCategoryAndPosition(ZbProjectStandard item,
                                           Map<String, List<ZbStandardsBuildPosition>> categoryMap,
                                           List<String> selectList,
                                           Map<String, CommonProjCategory> commonProjCategoryMap) {
        StringBuilder categoryCodes = new StringBuilder();
        for (Map.Entry<String, List<ZbStandardsBuildPosition>> entry : categoryMap.entrySet()) {
            String categoryCode = entry.getKey();
            List<ZbStandardsBuildPosition> list = entry.getValue();
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            CommonProjCategory category = commonProjCategoryMap.get(categoryCode);
            if (category == null) {
                continue;
            }
            categoryCodes.append(list.get(0).getCategoryCode()).append(",");
            StringBuilder sb = new StringBuilder();
            String categoryName = category.getCategoryname();
            sb.append(categoryName);
            String position = item.getPositions() == null ? "" : item.getPositions();
            if (list.size() == 1 && StringUtils.isBlank(list.get(0).getName())) {
                position = position + "," + sb;
            } else {
                if (CollectionUtils.isEmpty(list)) {
                    return;
                }
                if (CollUtil.isEmpty(selectList)) {
                    return;
                }
                list = list.stream().filter(y -> selectList.contains(y.getName())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(list)) {
                    return;
                }
                sb.append("(");
                list.stream().forEach(x -> sb.append(x.getName()).append(","));
                String name = sb.substring(0, sb.lastIndexOf(",")) + ")";
                if (StringUtils.isBlank(position)) {
                    position = name;
                } else {
                    position = position + "、" + name;
                }
            }
            if (StringUtils.isNotBlank(position) && position.indexOf(",") == 0) {
                position = position.replace(",", "");
            }
            item.setPositions(position);
        }
        String categoryCodeStr = categoryCodes.toString();
        if (StringUtils.isNotBlank(categoryCodeStr)) {
            categoryCodeStr = categoryCodeStr.substring(0, categoryCodeStr.lastIndexOf(","));
        }
        item.setPositionCategoryCodes(categoryCodeStr);

    }

    /**
     * 　　* @description: 根据工程分类编码与标准id集合查询对应position数据,取创建时间最新的一条
     * 　　* @param [categoryCode, standardIds]
     * 　　* @return void
     * 　　* @throws
     * 　　* <AUTHOR>
     * 　　* @date 2022/8/5 16:55
     */
    @Override
    public ZbStandardsBuildPosition getPositionByCategoryAndStandardsIds(String categoryCode, String positionName, List<Long> standardIds) {
        // 校验参数不能为空，避免调用不当导致的sql错误
        if (EmptyUtil.isEmpty(categoryCode) || CollectionUtils.isEmpty(standardIds)) {
            return null;
        }
        // 根据工程分类编码、产品定位名称、建造标注id集合查询对应记录
        List<ZbStandardsBuildPosition> positionList = standardsBuildPositionMapper.selectListByPositionInfoAndStandardIds(positionName, categoryCode, standardIds);
        if (CollectionUtils.isEmpty(positionList)) {
            return null;
        }
        return positionList.get(0);
    }
}




