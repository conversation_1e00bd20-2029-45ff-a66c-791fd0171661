package com.glodon.qydata.service.standard.mainQuantity;


import com.glodon.qydata.dto.ZbStandardsMainQuantityDto;
import com.glodon.qydata.entity.standard.mainQuantity.ZbStandardsMainQuantity;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.vo.standard.mainQuantity.StandardsMainQuantityBO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
　　* @description: 标准打通--主要量指标标准服务类
　　* <AUTHOR>
　　* @date 2021/10/19 9:52
　　*/
public interface IStandardsMainQuantityService {

    /**
     　　* @description: 专业新增时，初始化一份主要量指标
     　　* @param customerCode 企业编码 globalId 用户id tradeIdSys 系统专业id  newTradeId 新专业id
     　　* @return List<ZbStandardsTrade 专业列表
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/22 20:25
     　　*/
    void initMainQuantityList(String customerCode,Long globalId,Long tradeIdSys,Long newTradeId,String tradeCode,String tradeName);

    void executeInit(String customerCode);

    /**
    　　* @description: 查询指定专业下的主要量指标
    　　* @param customerCode 企业编码 tradeId 专业id
    　　* @return List<ZbStandardsMainQuantity> 主要量指标列表
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/10/27 10:48
    　　*/
    List<ZbStandardsMainQuantity> getListByTradeId(String customerCode, Long tradeId) throws BusinessException;

    /**
     * 解析工程特征库文件
     */
    void insertFeatureDataFile(String customerCode,Long globalId,MultipartFile file);

    /**
    　　* @description: 单个删除主要量指标
    　　* @param  customerCode 企业编码  id 主要量指标id
    　　* @return
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/10/28 8:44
    　　*/
    void deleteById(String customerCode, String id) throws BusinessException;

    /**
     　　* @description: 根据专业批量删除主要量指标
     　　* @param    tradeId 专业id
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/28 8:44
     　　*/
    void deleteByTradeId(Long tradeId);

    /**
     　　* @description: 增加一条记录
     　　* @param  customerCode 企业编码 globalId 用户id StandardsMainQuantityBO 待插入记录
     　　* @return ZbStandardsMainQuantity 插库之后的返回值
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/28 10:03
     　　*/
    ZbStandardsMainQuantity insertRecord(String customerCode, Long globalId, StandardsMainQuantityBO bo) throws BusinessException;

    /**
     　　* @description: 修改一条记录
     　　* @param  customerCode 企业编码 globalId 用户id StandardsMainQuantityBO 待更新记录
     　　* @return ZbStandardsMainQuantity 插库之后的返回值
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/28 10:03
     　　*/
    ZbStandardsMainQuantity updateRecord(String customerCode, Long globalId, StandardsMainQuantityBO bo) throws BusinessException;

    /**
     　　* @description: 查询指定专业编码下的主要量指标
     　　* @param customerCode 企业编码 tradeCode 专业编码 isShowDelete:是否展示已删除数据（0：展示不包含已删除的；1：展示包含已删除的 ；不传值，默认为0。）
     　　* @return List<ZbStandardsMainQuantityDto> 主要量指标列表
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/27 10:48
     　　*/
    List<ZbStandardsMainQuantityDto> getListByTradeCode(String customerCode, String tradeCode, Integer isShowDelete) throws BusinessException;

    /**
     　　* @description: 查询企业下所有的主要量指标
     　　* @param customerCode 企业编码 isShowDelete:是否展示已删除数据（0：展示不包含已删除的；1：展示包含已删除的 ；不传值，默认为0。）
     　　* @return List<ZbStandardsMainQuantity> 主要量指标列表
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/11/19 10:48
     　　*/
    List<ZbStandardsMainQuantityDto> selectAllMainQuantityList(String customerCode, Integer isShowDelete,Long globalId) throws BusinessException;

    /**
     * 上下移
     * @throws
     * @param flag 1：上移，2：下移
     * @param bo 主要量指标前端数据载体
     * <AUTHOR>
     * @return 
     * @date 2021/11/22 10:53
     */ 
    void moveUpDown(Integer flag, StandardsMainQuantityBO bo) throws BusinessException;

    /**
    　　* @description: 历史数据同步---主要工程量
    　　* @param
    　　* @return
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/12/3 0:05
    　　*/
    void insertHistoryData(Map<String, List<ZbStandardsMainQuantity>> map);

    /**
    　　* @description: 获取系统的主要量指标数据
    　　* @param
    　　* @return ZbStandardsMainQuantity
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/12/6 10:48
    　　*/
    List<ZbStandardsMainQuantity> getSystemData();

    /**
     　　* @description: 将旧企业主要量标准数据复制到新企业主要量标准
     　　* @param  Map<Long,Long> tradeIdRelationMap 新旧专业id的对应关系集合，key:oldId  value:newId  调用前控制不为空
     oldCustomerCode:原企业编码
     newCustomerCode 新企业编码
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/9 9:55
     　　*/
    void initFeatureData(Map<Long,Long> tradeIdRelationMap,String oldCustomerCode,String newCustomerCode);

    /**
     　　* @description: 根据企业编码删除所有主要量指标标准
     　　* @param  customerCode 企业编码
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/9 19:41
     　　*/
    void deleteByCustomerCode(String customerCode);


    /**
     * 查询指定专业下的暂存主要量指标
     * @param customerCode
     * @param tradeId
     * @return
     */
    List<ZbStandardsMainQuantity> getSelfListByTradeId(String customerCode, Long tradeId) throws BusinessException;

    /**
     * 发布
     */
    void publish(String customerCode, String globalId) throws BusinessException;

}
