package com.glodon.qydata.service.standard.projectOrContractInfo.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.glodon.qydata.common.RequestContent;
import com.glodon.qydata.common.annotation.BusinessCache;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.constant.OperateConstants;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.dto.ProjectInfoMoveToDTO;
import com.glodon.qydata.dto.ProjectInfoPromotionOrDemotionDTO;
import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity;
import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsUnitEntity;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.standard.projectOrContractInfo.StandardsProjectInfoMapper;
import com.glodon.qydata.service.init.enterprise.InitProjectService;
import com.glodon.qydata.service.init.self.InitProjectSelfService;
import com.glodon.qydata.service.standard.category.CommonProjCategoryService;
import com.glodon.qydata.service.standard.category.impl.CommonProjectCategoryUsedService;
import com.glodon.qydata.service.standard.expression.IExpressionService;
import com.glodon.qydata.service.standard.projectOrContractInfo.IStandardsProjectInfoService;
import com.glodon.qydata.service.standard.projectOrContractInfo.IStandardsUnitService;
import com.glodon.qydata.service.system.PublishInfoService;
import com.glodon.qydata.util.mover.ElementMover;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import com.glodon.qydata.vo.standard.expression.ExpressionResultVo;
import com.glodon.qydata.vo.standard.projectOrContractInfo.ProjectInfoShowVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.glodon.qydata.common.constant.Constants.StandardsProjectOrContractInfoConstants.*;
import static com.glodon.qydata.common.constant.Constants.WHETHER_FALSE;
import static com.glodon.qydata.common.constant.Constants.WHETHER_TRUE;
import static com.glodon.qydata.common.constant.Constants.ZbStandardsTradeConstants.DeletedStatus.NO_DELETED;

/**
 * <p>
 * zb_standards_project_info - 主键 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
@Service
@Slf4j
public class StandardsProjectInfoServiceImpl extends ServiceImpl<StandardsProjectInfoMapper, StandardsProjectInfoEntity> implements IStandardsProjectInfoService {
    @Autowired
    private StandardsProjectInfoMapper standardsProjectInfoMapper;
    @Autowired
    private IStandardsUnitService standardsUnitService;
    @Autowired
    private InitProjectService initProjectService;
    @Autowired
    private InitProjectSelfService initProjectSelfService;
    @Autowired
    private IExpressionService expressionService;

    @Autowired
    private CommonProjCategoryService commonProjCategoryService;

    @Autowired
    private CommonProjectCategoryUsedService projectCategoryUsedService;
    @Resource
    private PublishInfoService publishInfoServiceImpl;

    /**
     * 转换数据结构
     *
     * @param list
     * @param type 信息类型（1：项目信息；2：合同信息。）
     * @return {@link List< ProjectInfoShowVo>}
     * @throws
     * <AUTHOR>
     * @date 2021/10/15 14:35
     */
    @Override
    public List<ProjectInfoShowVo> convertData(List<StandardsProjectInfoEntity> list,
                                               Integer type,
                                               Boolean isOpenApi,
                                               Integer isSkipUserName,
                                               Integer queryType,
                                               Integer treeType,
                                               String customerCode) {
        //转换数据结构
        List<ProjectInfoShowVo> result = new ArrayList<>(list.size());
        for (StandardsProjectInfoEntity entity : list) {
            ProjectInfoShowVo vo = new ProjectInfoShowVo();
            BeanUtils.copyProperties(entity, vo);
            //是否可编辑、是否可删除字段赋值
            boolean contains = false;
            String name = vo.getName();
            if (name != null) {
                if (PROJECT_INFO.equals(type)) {
                    contains = PROJECT_INFO_BUILT_IN_NON_EDITABLE_NAME_LIST.contains(name);
                } else {
                    contains = CONTRACT_INFO_BUILT_IN_NON_EDITABLE_NAME_LIST.contains(name);
                }
            }
            vo.setIsDeletable(!contains);
            vo.setIsEditable(!contains);
            result.add(vo);
        }
        log.debug("查询真实名称开始");
        setRealName(result, isSkipUserName);
        log.debug("查询真实名称结束");
        if (CONTRACT_INFO.equals(type) && !isOpenApi) {
            // 合同信息中的内置数据【工程分类】不在前端展示
            result = result.stream().filter(vo -> !INVISIBLE_INFO_NAME.equals(vo.getName()))
                    .collect(Collectors.toList());
        }

        //如果是项目信息，返回值需要带上组
        if (PROJECT_INFO.equals(type)) {
            //如果是项目信息，需要组装下工程分类
            assembleProjectCategory(result, customerCode);
            result = assembleProjectInfo(queryType, treeType, result);
        }
        return result;
    }

    /**
     * @description: 组装工程分类
     * @author: luoml-b
     * @date: 2024/9/29 14:27
     * @param: result
     * @param: customerCode
     **/
    private void assembleProjectCategory(List<ProjectInfoShowVo> result, String customerCode) {
        Integer type = projectCategoryUsedService.getUsedCategoryType(customerCode);
        List<CommonProjCategory> categoryListTree = commonProjCategoryService.getCategoryListTree(customerCode, type, Constants.CategoryConstants.WHETHER_FALSE);
        List<String> categoryCodeList = categoryListTree.stream()
                .filter(x -> x.getLevel() == 1L)
                .map(CommonProjCategory::getCommonprojcategoryid).collect(Collectors.toList());
        if (CollUtil.isEmpty(categoryCodeList)) {
            return;
        }
        String categoryCode = String.join(",", categoryCodeList);
        result.forEach(x -> {
            if (Objects.equals(GROUP, x.getType())) {
                return;
            }
            if (Objects.isNull(x.getIsCommon()) || x.getIsCommon() == 1) {
                x.setCategoryCode(categoryCode);
            } else {
                String currentCategoryCode = x.getCategoryCode();
                if (StringUtils.isBlank(currentCategoryCode)) {
                    return;
                }
                List<String> currentCategoryCodeList = Stream.of(currentCategoryCode.split(",")).collect(Collectors.toList());
                currentCategoryCodeList = currentCategoryCodeList.stream().filter(categoryCodeList::contains).collect(Collectors.toList());
                currentCategoryCode = String.join(",", currentCategoryCodeList);
                x.setCategoryCode(currentCategoryCode);
            }
        });
    }


    private List<ProjectInfoShowVo> assembleProjectInfo(Integer queryType, Integer treeType, List<ProjectInfoShowVo> result) {
        result = buildTree(result);
        //如果不展示树，则平铺展开，重新构建ord
        if (SHOW_ORIGINAL.equals(treeType)) {
            result = flatList(result, false);
            result.forEach(x -> x.setChildrenList(null));
            if (SHOW_ITEM.equals(queryType)) {
                //不需要分组
                result = result.stream()
                        .filter(x -> x.getType() == null || ITEM.equals(x.getType())).collect(Collectors.toList());
            }
            //重新构建ord
            rebuildOrd(result);
        }
        return result;
    }

    public void setRealName(List<ProjectInfoShowVo> result, Integer isSkipUserName) {
        if (!WHETHER_FALSE.equals(isSkipUserName)) {
            return;
        }
        //globalId与真名的映射
        List<String> globalIds = result.stream().map(r -> String.valueOf(r.getCreatorId())).distinct().collect(Collectors.toList());
        List<String> updaters = result.stream().map(r -> String.valueOf(r.getUpdaterId())).distinct().collect(Collectors.toList());
        globalIds.addAll(updaters);
        Map<String, String> realNameMap = RequestContent.getGlobalIdNameMap(globalIds);
        for (ProjectInfoShowVo vo : result) {
            if (vo.getCreatorId() != null && realNameMap.containsKey(vo.getCreatorId().toString())) {
                vo.setCreatorRealName(realNameMap.get(vo.getCreatorId().toString()));
            }
            if (vo.getUpdaterId() != null && realNameMap.containsKey(vo.getUpdaterId().toString())) {
                vo.setUpdaterRealName(realNameMap.get(vo.getUpdaterId().toString()));
            }
        }
    }

    /**
     * 根据id删除项目/合同信息
     *
     * @param ids 主键
     * @return
     * @throws
     * <AUTHOR>
     * @date 2021/10/15 15:19
     */
    @Override
    public void deleteInfoData(String ids, String customerCode, String operateType) {
        String[] idArr = ids.split(",");
        if (OperateConstants.CONTRACT.equals(operateType)) {
            //删除合同信息走原逻辑
            this.update(new LambdaUpdateWrapper<StandardsProjectInfoEntity>()
                    .in(StandardsProjectInfoEntity::getId, (Object[]) idArr)
                    .set(StandardsProjectInfoEntity::getIsDeleted, IS_DELETED));
            //更新受影响的ord字段
            StandardsProjectInfoEntity entity = this.getById(idArr[idArr.length - 1]);
            this.update(new LambdaUpdateWrapper<StandardsProjectInfoEntity>()
                    .gt(StandardsProjectInfoEntity::getOrd, entity.getOrd())
                    .eq(StandardsProjectInfoEntity::getCustomerCode, entity.getCustomerCode())
                    .eq(StandardsProjectInfoEntity::getStandardDataType, entity.getStandardDataType())
                    .setSql("ord = ord - 1"));
        } else if (OperateConstants.PROJECT.equals(operateType)) {
            List<Long> idList = Arrays.stream(idArr).mapToLong(Long::parseLong).boxed().collect(Collectors.toList());
            standardsProjectInfoMapper.batchDelete(idList);
        } else {
            throw new BusinessException("不支持的删除操作");
        }
    }

    private void updateTreeSort(List<StandardsProjectInfoEntity> selfList, List<StandardsProjectInfoEntity> updateInfoList) {
        List<Long> deleteIds = updateInfoList.stream()
                .map(StandardsProjectInfoEntity::getId).collect(Collectors.toList());
        List<StandardsProjectInfoEntity> notDeleteInfoList = selfList.stream()
                .filter(x -> !deleteIds.contains(x.getId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(notDeleteInfoList)) {
            //构建树
            notDeleteInfoList = buildTree(notDeleteInfoList);
            //按照树结构排序然后平铺
            notDeleteInfoList = flatList(notDeleteInfoList, true);
            notDeleteInfoList.forEach(x -> x.setChildrenList(null));
            //放到一个list
            updateInfoList.addAll(notDeleteInfoList);
        }
    }

    /**
     * 初始化内置数据
     *
     * @param isShowDelete 是否展示已删除数据（0：展示不包含已删除的；1：展示包含已删除的 ；不传值，默认为0。）
     * @param type         信息类型（1：项目信息；2：合同信息。）
     * @return {@link List< StandardsProjectInfoEntity>}
     * @throws
     * <AUTHOR>
     * @date 2021/10/15 17:13
     */
    @Override
    @BusinessCache(customerCode = "${customerCode}")
    public List<StandardsProjectInfoEntity> initBuiltInData(String customerCode, Integer isShowDelete, Integer type) throws BusinessException {
        List<StandardsProjectInfoEntity> list = standardsProjectInfoMapper.selectAllPublishData(customerCode, isShowDelete, type);

        if (CollectionUtils.isEmpty(list)) {
            // 初始化内置数据
            initProjectService.initData(customerCode, type);
            list = standardsProjectInfoMapper.selectAllPublishData(customerCode, isShowDelete, type);
        }
        fillExpressionCode(list, customerCode, type);

        return list;
    }

    /**
     * 放入计算口径编码
     *
     * @param projectInfoList
     * @param customerCode
     * @param type            信息类型（1：项目信息；2：合同信息。）
     */
    public void fillExpressionCode(List<StandardsProjectInfoEntity> projectInfoList, String customerCode, Integer type) {
        if (CollectionUtils.isEmpty(projectInfoList) || !PROJECT_INFO.equals(type)) {
            return;
        }

        List<ExpressionResultVo> expressionResultVos = expressionService.selectExpression(customerCode, type, WHETHER_TRUE);

        if (CollectionUtils.isEmpty(expressionResultVos)) {
            return;
        }

        Map<String, String> nameAndCodeMap = expressionResultVos.parallelStream().filter(x -> x.getExpressionCode() != null)
                .collect(Collectors.toMap(ExpressionResultVo::getName, ExpressionResultVo::getExpressionCode, (v1, v2) -> v2));

        for (StandardsProjectInfoEntity projectInfo : projectInfoList) {
            if (WHETHER_TRUE.equals(projectInfo.getIsExpression()) && nameAndCodeMap.containsKey(projectInfo.getName())) {
                projectInfo.setExpressionCode(nameAndCodeMap.get(projectInfo.getName()));
            }
        }
    }

    /**
     * 新增项目/合同信息
     *
     * @param entity
     * @return
     * @throws
     * <AUTHOR>
     * @date 2021/10/19 10:10
     */
    @Override
    public StandardsProjectInfoEntity addInfoData(StandardsProjectInfoEntity entity) {
        String globalId = RequestContent.getGlobalId();
        String customerCode = RequestContent.getCustomerCode();

        buildInsertEntity(entity, globalId, customerCode);
        //更新排序字段
        this.update(new UpdateWrapper<StandardsProjectInfoEntity>().eq("customer_code", customerCode)
                .eq("standard_data_type", entity.getStandardDataType())
                .eq("pid", entity.getPid())
                .ge("ord", entity.getOrd())
                .setSql("ord = ord + 1"));

        this.save(entity);

        return getProjectInfoShowVo(entity.getId(), globalId);
    }

    private void buildInsertEntity(StandardsProjectInfoEntity entity, String globalId, String customerCode) {
        if (!Objects.isNull(entity.getIsCommon()) && entity.getIsCommon() == 1) {
            entity.setCategoryCode(StringUtils.EMPTY);
        }
        entity.setId(SnowflakeIdUtils.getNextId());
        //重名校验
        StandardsProjectInfoEntity deletedDupNameEntity = checkDupName(entity, customerCode);

        entity.setCustomerCode(customerCode);
        entity.setCreatorId(Long.parseLong(globalId));
        entity.setCreateTime(LocalDateTime.now());

        if (deletedDupNameEntity != null) {
            //同名已删除记录同id处理
            entity.setId(deletedDupNameEntity.getId());
            //物理删除已逻辑删除的同名记录
            this.removeById(entity.getId());
        }
    }

    private ProjectInfoShowVo getProjectInfoShowVo(Long id, String globalId) {
        StandardsProjectInfoEntity infoEntity = this.getById(id);
        ProjectInfoShowVo showVo = new ProjectInfoShowVo();
        BeanUtils.copyProperties(infoEntity, showVo);
        showVo.setIsDeletable(true);
        showVo.setIsEditable(true);
        if (showVo.getCreatorId() != null) {
            Map<String, String> realNameMap = RequestContent.getGlobalIdNameMap(Collections.singletonList(globalId));
            showVo.setCreatorRealName(realNameMap.get(showVo.getCreatorId().toString()));
        }
        return showVo;
    }

    /***
     * @description: excel导入
     * @param entityList 1
     * @throws
     * <AUTHOR>
     * @date 2022/7/7 10:17
     */
    @Override
    public List<StandardsProjectInfoEntity> addInfoDataList(List<StandardsProjectInfoEntity> entityList, Integer standardDataType, String globalId, String customerCode) {
        List<String> messages = new ArrayList<>();
        List<StandardsProjectInfoEntity> insertList = new ArrayList<>();

        List<StandardsProjectInfoEntity> dbDataList = getSelfProjectInfoList(NO_DELETED, standardDataType);
        int ord = dbDataList != null && !dbDataList.isEmpty() ?
                dbDataList.stream().max(Comparator.comparingInt(StandardsProjectInfoEntity::getOrd)).get().getOrd() + 1 : 1;
        for (StandardsProjectInfoEntity entity : entityList) {
            try {
                entity.setOrd(ord++);
                buildInsertEntity(entity, globalId, customerCode);
                insertList.add(entity);
            } catch (BusinessException e) {
                messages.add(e.getMessage());
            }
        }

        if (CollUtil.isNotEmpty(insertList)) {
            this.saveBatch(insertList);
        }

        return insertList;
    }

    /**
     * 修改项目/合同信息
     *
     * @param entity
     * @return {@link StandardsProjectInfoEntity}
     * @throws
     * <AUTHOR>
     * @date 2021/10/19 14:18
     */
    @Override
    public ProjectInfoShowVo updateInfoData(ProjectInfoShowVo entity) throws BusinessException {
        String globalId = RequestContent.getGlobalId();
        String customerCode = RequestContent.getCustomerCode();
        if (!Objects.isNull(entity.getIsCommon()) && entity.getIsCommon() == 1) {
            entity.setCategoryCode(StringUtils.EMPTY);
        }

        ((StandardsProjectInfoServiceImpl) AopContext.currentProxy()).updateProjectInfoData(entity, customerCode, globalId);

        StandardsProjectInfoEntity byId = this.getById(entity.getId());

        ProjectInfoShowVo vo = new ProjectInfoShowVo();
        BeanUtils.copyProperties(byId, vo);
        Long creatorId = byId.getCreatorId();
        Map<String, String> realNameMap = RequestContent.getGlobalIdNameMap(Arrays.asList(globalId, String.valueOf(creatorId)));
        if (realNameMap.containsKey(globalId)) {
            vo.setUpdaterRealName(realNameMap.get(globalId));
        }
        if (creatorId != null && realNameMap.containsKey(creatorId.toString())) {
            vo.setCreatorRealName(realNameMap.get(creatorId.toString()));
        }

        boolean isBuiltInProjectInfo = PROJECT_INFO_BUILT_IN_NON_EDITABLE_NAME_LIST.contains(vo.getName())
                && PROJECT_INFO.equals(vo.getStandardDataType());
        boolean isBuiltInContractInfo = CONTRACT_INFO_BUILT_IN_NON_EDITABLE_NAME_LIST.contains(vo.getName())
                && CONTRACT_INFO.equals(vo.getStandardDataType());

        if (isBuiltInContractInfo || isBuiltInProjectInfo) {
            vo.setIsDeletable(false);
            vo.setIsEditable(false);
        }
        //vo.setIsCommon(entity.getIsCommon());
        return vo;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateProjectInfoData(ProjectInfoShowVo entity, String customerCode, String globalId) {
        //重名校验
        StandardsProjectInfoEntity deletedDupNameEntity = checkDupName(entity, customerCode);
        //原id
        Long sourceId = entity.getId();
        entity.setUpdaterId(Long.parseLong(globalId));
        entity.setUpdateTime(LocalDateTime.now());
        if (deletedDupNameEntity != null) {
            //同名已删除记录同id处理
            entity.setId(deletedDupNameEntity.getId());
            //物理删除已逻辑删除的记录
            this.removeById(entity.getId());
            //替换被引用的pid值
            this.update(new UpdateWrapper<StandardsProjectInfoEntity>()
                    .eq("pid", sourceId)
                    .eq("customer_code", customerCode)
                    .set("pid", entity.getId()));
        }
        //更新整个实体，后面的set应该没有用，但是不敢删除，怕有其他问题，后续再观察
        this.update(entity, new UpdateWrapper<StandardsProjectInfoEntity>()
                .eq("id", sourceId)
                .eq("is_deleted", IS_NOT_DELETED));
    }

    /**
     * 上下移
     *
     * @param flag   1：上移，2：下移
     * @param entity
     * @return
     * @throws
     * <AUTHOR>
     * @date 2021/10/19 17:24
     */
    @Override
    public void moveUpDown(Integer flag, StandardsProjectInfoEntity entity) {
        String customerCode = RequestContent.getCustomerCode();
        Integer standardDataType = entity.getStandardDataType();

        List<StandardsProjectInfoEntity> infoSelfList = standardsProjectInfoMapper.selectSelfList(customerCode, 0, standardDataType);
        if (CONTRACT_INFO.equals(standardDataType)) {
            // 合同信息中的内置数据【工程分类】不在前端展示
            infoSelfList = infoSelfList.stream()
                    .filter(vo -> !INVISIBLE_INFO_NAME.equals(vo.getName()))
                    .collect(Collectors.toList());
            // 项目信息/合同信息的第一行是固定行
            moveUpDownValidityCheck(flag, entity.getOrd());
        }
        if (PROJECT_INFO.equals(standardDataType)) {
            //项目信息移动，只需要在同级中判断
            Long pid = entity.getPid();
            infoSelfList = infoSelfList.stream()
                    .filter(x -> Objects.equals(x.getPid(), pid))
                    .sorted(Comparator.comparing(StandardsProjectInfoEntity::getOrd)).collect(Collectors.toList());
        }

        String moveType = MOVE_UP.equals(flag) ? Constants.MOVE_TYPE_UP : Constants.MOVE_TYPE_DOWN;
        ElementMover.move(infoSelfList, entity.getId(), moveType, standardsProjectInfoMapper);
    }

    /**
     * 上下移参数合法性校验
     *
     * @param flag 1：上移，2：下移
     * @param ord
     * @return
     * <AUTHOR>
     * @date 2021/10/20 11:30
     */
    public void moveUpDownValidityCheck(Integer flag, Integer ord) {
        if (ord == 1) {
            //第一个不允许上下移
            throw new BusinessException(ResponseCode.PARAMETER_ERROR.getCode(), "第一行数据不允许上下移");
        }
        if (ord == 2 && MOVE_UP.equals(flag)) {
            //第二行不允许上移
            throw new BusinessException(ResponseCode.PARAMETER_ERROR.getCode(), "第二行数据不允许上移");
        }
    }

    /**
     * 重名校验,返回已删除记录中的同名记录
     *
     * @param entity
     * @param customerCode
     * @return
     * @throws
     * <AUTHOR>
     * @date 2021/10/19 10:14
     */
    private StandardsProjectInfoEntity checkDupName(StandardsProjectInfoEntity entity, String customerCode) {
        String name = entity.getName();
        if (INVISIBLE_INFO_NAME.equals(entity.getName())
                && CONTRACT_INFO.equals(entity.getStandardDataType())) {
            throw new BusinessException(ResponseCode.PARAMETER_ERROR.getCode(), "合同信息内置有工程分类，无需添加");
        }

        if (StringUtils.isNotBlank(name) && name.length() > Constants.MAX_NAME_LENGTH) {
            throw new BusinessException(ResponseCode.PARAMETER_ERROR.getCode(), "名称长度不能大于30");
        }

        if (StringUtils.isNotBlank(entity.getRemark()) && entity.getRemark().length() > Constants.MAX_REMARK_LENGTH) {
            throw new BusinessException(ResponseCode.PARAMETER_ERROR.getCode(), "备注长度不能大于200");
        }

        if (StringUtils.isBlank(name)) {
            return null;
        }

        //按名称查询
        List<StandardsProjectInfoEntity> dupNameEntities = this.list(new QueryWrapper<StandardsProjectInfoEntity>()
                .eq("name", name)
                .eq("customer_code", customerCode)
                .eq("standard_data_type", entity.getStandardDataType()));
        //未删除的记录
        List<StandardsProjectInfoEntity> notDeletedEntities = dupNameEntities.parallelStream()
                .filter(obj -> IS_NOT_DELETED.equals(obj.getIsDeleted()))
                .collect(Collectors.toList());

        if (notDeletedEntities.size() > 0 && !notDeletedEntities.get(0).getId().equals(entity.getId())) {
            throw new BusinessException(ResponseCode.PARAMETER_ERROR.getCode(), "该名称已存在");
        }

        //已删除的记录
        List<StandardsProjectInfoEntity> deletedEntities = dupNameEntities.parallelStream()
                .filter(obj -> IS_DELETED.equals(obj.getIsDeleted()) && obj.getOriginId() == null)
                .collect(Collectors.toList());

        if (deletedEntities.size() > 0) {
            return deletedEntities.get(0);
        }

        return null;
    }

    /**
     * 　　* @description: 根据企业编码删除所有专业标准
     * 　　* @param  customerCode 企业编码
     * 　　* @return
     * 　　* @throws
     * 　　* <AUTHOR>
     * 　　* @date 2021/12/9 19:41
     *
     */
    @Override
    public void deleteByCustomerCode(String customerCode) {
        standardsProjectInfoMapper.deleteByCustomerCode(customerCode);
    }

    /**
     * 获取暂存用户数据
     *
     * @param isShowDelete
     * @param type
     * @return
     */
    @Override
    public List<StandardsProjectInfoEntity> getSelfProjectInfoList(Integer isShowDelete, Integer type) throws BusinessException {
        String customerCode = RequestContent.getCustomerCode();

        isShowDelete = isShowDelete == null ? 0 : isShowDelete;
        //从暂存表中查询数据
        List<StandardsProjectInfoEntity> selfList = standardsProjectInfoMapper.selectSelfList(customerCode, isShowDelete, type);

        if (CollectionUtils.isEmpty(selfList)) {
            initProjectSelfService.initData(customerCode, type);
            selfList = standardsProjectInfoMapper.selectSelfList(customerCode, isShowDelete, type);
        }

        return selfList;
    }

    /**
     * 发布暂存数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @BusinessCache(customerCode = "${customerCode}", isInvalidateCache = true)
    public void publish(String customerCode, String globalId, Integer type) throws BusinessException {
        //从暂存表中查询数据
        Wrapper<StandardsProjectInfoEntity> wrapper = new LambdaQueryWrapper<StandardsProjectInfoEntity>()
                .eq(StandardsProjectInfoEntity::getCustomerCode, customerCode)
                .eq(StandardsProjectInfoEntity::getStandardDataType, type)
                .eq(StandardsProjectInfoEntity::getInvalid, Constants.DataInvalid.VALID);

        List<StandardsProjectInfoEntity> selfList = this.list(wrapper);
        // 校验是否有其他账号更新发布
        if (CollUtil.isEmpty(selfList)) {
            return;
        }

        List<StandardsProjectInfoEntity> insertList = new ArrayList<>();
        List<StandardsProjectInfoEntity> updateList = new ArrayList<>();
        Map<Long, Long> id2OriginalIdMap = selfList.stream()
                .filter(entity -> entity.getOriginId() != null)
                .collect(Collectors.toMap(StandardsProjectInfoEntity::getId, StandardsProjectInfoEntity::getOriginId));
        for (StandardsProjectInfoEntity entity : selfList) {
            if (entity.getPid() != null && !FIRST_LEVEL_PID.equals(entity.getPid())) {
                //生产主表id pid关系，有originalId 的直接从id2OriginalIdMap拿，没有的等于新增的数据，直接复制，因为id也是复制的self表的
                entity.setPid(id2OriginalIdMap.getOrDefault(entity.getPid(), entity.getPid()));
            }
            if (entity.getOriginId() == null) {
                insertList.add(entity);
            } else {
                updateList.add(entity);
            }

            // 名称中英文冒号转换，用于兼容element-ui的英文冒号bug
            entity.setName(convertEnglishColonToChineseColon(entity.getName()));
        }
        if (!CollectionUtils.isEmpty(insertList)) {
            standardsProjectInfoMapper.insertBatch(insertList);
        }
        if (!CollectionUtils.isEmpty(updateList)) {
            standardsProjectInfoMapper.batchUpdatePublish(updateList);
        }

        Optional<StandardsProjectInfoEntity> entityWithUnit = selfList.parallelStream().filter(entity -> entity.getName().contains("规模") && entity.getCreatorId() == null).findFirst();
        if (entityWithUnit.isPresent()) {
            StandardsProjectInfoEntity entity = entityWithUnit.get();
            Long id = entity.getId();
            Long originId = entity.getOriginId();
            // 删除已发布的规模单位数据
            standardsUnitService.remove(new LambdaUpdateWrapper<StandardsUnitEntity>().eq(StandardsUnitEntity::getCustomerCode, customerCode)
                    .eq(StandardsUnitEntity::getUnitSourceType, type)
                    .eq(StandardsUnitEntity::getZbStandardsProjectInfoId, originId));
            // 查询当前发布的规模单位数据
            List<StandardsUnitEntity> units = standardsUnitService.list(new LambdaQueryWrapper<StandardsUnitEntity>()
                    .eq(StandardsUnitEntity::getZbStandardsProjectInfoId, id));

            // 当前发布后删除暂存的规模单位数据
            standardsUnitService.remove(new LambdaUpdateWrapper<StandardsUnitEntity>()
                    .eq(StandardsUnitEntity::getZbStandardsProjectInfoId, id));

            for (StandardsUnitEntity unit : units) {
                unit.setZbStandardsProjectInfoId(originId);
            }

            standardsUnitService.saveBatch(units);
        }

        standardsProjectInfoMapper.deleteSelfByCustomerCode(customerCode, type);

        // 类型是项目信息时，同步计算口径
        if (Objects.equals(type, PROJECT_INFO)) {
            expressionService.expressionSyn(customerCode, type);
        }

        publishInfoServiceImpl.updateVersion(customerCode, globalId, Objects.equals(type, PROJECT_INFO) ? OperateConstants.PROJECT : OperateConstants.CONTRACT);
    }

    public static String convertEnglishColonToChineseColon(String input) {
        if (input == null) {
            return null; // 处理空输入
        }
        return input.replace(":", "：");
    }

    /**
     * @param customerCode
     * @return void
     * @description: 获取企业项目信息中的【产品定位】的枚举值
     * <AUTHOR>
     * @date 2022/8/2 10:07
     */
    @Override
    public List<String> getProductPositioning(String customerCode) {
        // 【产品定位】
        StandardsProjectInfoEntity standardsProjectInfoEntity = standardsProjectInfoMapper.selectProductPositioning(customerCode);

        if (standardsProjectInfoEntity == null) {
            return null;
        }

        String selectList = standardsProjectInfoEntity.getSelectList();

        if (StringUtils.isEmpty(selectList)) {
            return null;
        }

        List<JSONObject> jsonObjects = JSONArray.parseArray(selectList, JSONObject.class);

        return jsonObjects.stream().filter(Objects::nonNull)
                .filter(x -> IS_NOT_DELETED.equals(x.getInteger("isDeleted")))
                .map(x -> x.getString("name")).collect(Collectors.toList());
    }

    /**
     * @description: 查询企业是否有产品定位
     * @author: luoml-b
     * @date: 2024/4/16 14:12
     * @param: customerCode
     * @return: boolean
     **/
    @Override
    public boolean withoutPosition(String customerCode) {
        // 【产品定位】
        StandardsProjectInfoEntity standardsProjectInfoEntity = standardsProjectInfoMapper.selectProductPositioning(customerCode);
        return ObjectUtil.isNull(standardsProjectInfoEntity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void moveTo(ProjectInfoMoveToDTO projectInfoMoveToDTO) {
        List<StandardsProjectInfoEntity> items = projectInfoMoveToDTO.getItems();
        if (CollUtil.isEmpty(items)) {
            return;
        }
//       //重新排序
//        items = items.stream()
//                .sorted(Comparator.comparing(StandardsProjectInfoEntity::getOrd)).collect(Collectors.toList());
        StandardsProjectInfoEntity group = projectInfoMoveToDTO.getGroup();

        List<StandardsProjectInfoEntity> selfList
                = standardsProjectInfoMapper.selectSelfList(projectInfoMoveToDTO.getCustomerCode(), IS_NOT_DELETED, PROJECT_INFO);
        StandardsProjectInfoEntity maxProjectInfo = selfList.stream()
                .filter(x -> Objects.equals(x.getPid(), group.getId()))
                .max(Comparator.comparingInt(StandardsProjectInfoEntity::getOrd)).orElse(new StandardsProjectInfoEntity());
        int maxOrd = ObjectUtil.isNotNull(maxProjectInfo.getOrd()) ? maxProjectInfo.getOrd() : 0;

        //将子项重新排序
        int ord = 1;
        for (StandardsProjectInfoEntity item : items) {
            item.setPid(group.getId());
            item.setOrd(maxOrd + ord++);
        }
        //updateTreeSort(selfList, items);
        standardsProjectInfoMapper.batchUpdateOrdAndPid(items);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void promotionOrDemotion(ProjectInfoPromotionOrDemotionDTO projectInfoPromotionOrDemotionDTO) {
        String customerCode = projectInfoPromotionOrDemotionDTO.getCustomerCode();
        StandardsProjectInfoEntity group = projectInfoPromotionOrDemotionDTO.getGroup();
        if (!GROUP.equals(group.getType())) {
            throw new BusinessException("子项不支持该操作");
        }
        Integer type = projectInfoPromotionOrDemotionDTO.getType();
        List<StandardsProjectInfoEntity> selfList
                = standardsProjectInfoMapper.selectSelfList(customerCode, IS_NOT_DELETED, PROJECT_INFO);
        if (CollUtil.isEmpty(selfList)) {
            return;
        }
        List<StandardsProjectInfoEntity> updateInfo = new ArrayList<>();
        if (DEMOTION.equals(type)) {
            //降级分组（顺序排在上一个一级的子的最后面）
            if (selfList.stream().anyMatch(x -> Objects.equals(x.getPid(), group.getId()) && GROUP.equals(x.getType()))) {
                throw new BusinessException("当前分组下有子分组，请调整后再试");
            }
            //拿到需要降级分组的上面的一级分组
            StandardsProjectInfoEntity previousGroup = selfList.stream()
                    .filter(x -> FIRST_LEVEL_PID.equals(x.getPid()) && GROUP.equals(x.getType()))
                    .filter(x -> x.getOrd() < group.getOrd())
                    .max(Comparator.comparingInt(StandardsProjectInfoEntity::getOrd))
                    .orElse(null);
            if (previousGroup == null || Objects.equals(previousGroup.getId(), group.getId())) {
                throw new BusinessException("请设置父级分组后再试");
            }
            StandardsProjectInfoEntity maxChild = selfList.stream()
                    .filter(x -> Objects.equals(x.getPid(), previousGroup.getId()))
                    .max(Comparator.comparingInt(StandardsProjectInfoEntity::getOrd)).orElse(null);
            group.setPid(previousGroup.getId());
            group.setOrd(maxChild != null ? maxChild.getOrd() + 1 : 1);
            updateInfo = Collections.singletonList(group);
        } else if (PROMOTION.equals(type)) {
            //升级分组（顺序排在原始父级的下一个）
            StandardsProjectInfoEntity originalParent = selfList.stream()
                    .filter(x -> Objects.equals(x.getId(), group.getPid()))
                    .findFirst().orElseThrow(() -> new BusinessException("没有父级，不支持升级操作"));
            group.setPid(FIRST_LEVEL_PID);
            group.setOrd(originalParent.getOrd() + 1);
            updateInfo.add(group);
            //拿到所有一级，包括分组与子项
            List<StandardsProjectInfoEntity> allFirstLevel = selfList.stream()
                    .filter(x -> FIRST_LEVEL_PID.equals(x.getPid()))
                    .sorted(Comparator.comparingInt(StandardsProjectInfoEntity::getOrd)).collect(Collectors.toList());
            //拿到第一层所有数据ord大于originalParent的
            List<StandardsProjectInfoEntity> moreThanOrd = allFirstLevel.stream()
                    .filter(x -> x.getOrd() > originalParent.getOrd()).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(moreThanOrd)) {
                //将一层所有数据ord大于originalParent的ord设置为新升级数据的ord+1
                int ord = 1;
                for (StandardsProjectInfoEntity entity : moreThanOrd) {
                    entity.setOrd(group.getOrd() + ord++);
                }
                updateInfo.addAll(moreThanOrd);
            }
        } else {
            throw new BusinessException("升降级类型错误");
        }
        //更新group
        standardsProjectInfoMapper.batchUpdateOrdAndPid(updateInfo);
    }

    private <T extends StandardsProjectInfoEntity> List<T> buildTree(List<T> list) {
        if (CollUtil.isNotEmpty(list) && list.stream()
                .allMatch(entity -> FIRST_LEVEL_PID.equals(entity.getPid()) || entity.getPid() == null)) {
            return list.stream()
                    .sorted(Comparator.comparingInt(T::getOrd))
                    .collect(Collectors.toList());
        }
        Map<Long, T> map = list.stream()
                .collect(Collectors.toMap(T::getId, Function.identity()));
        return list.stream()
                .filter(entity -> FIRST_LEVEL_PID.equals(entity.getPid()))
                .sorted(Comparator.comparingInt(T::getOrd))
                .map(entity -> buildSubTree(entity, map))
                .collect(Collectors.toList());
    }

    private <T extends StandardsProjectInfoEntity> T buildSubTree(T entity, Map<Long, T> map) {
        if (entity.getChildrenList() == null) {
            entity.setChildrenList(new ArrayList<>());
        }

        map.values().stream()
                .filter(child -> child.getPid().equals(entity.getId()))
                .sorted(Comparator.comparingInt(T::getOrd))
                .forEach(child -> entity.getChildrenList().add(buildSubTree(child, map)));

        return entity;
    }

    private <T extends StandardsProjectInfoEntity> List<T> flatList(List<T> tree, boolean setOrdFlag) {
        List<T> result = new ArrayList<>();
        int ord = 1;
        for (T node : tree) {
            if (setOrdFlag) {
                node.setOrd(ord++);
            }
            result.add(node);
            if (CollUtil.isNotEmpty(node.getChildrenList())) {
                List<T> children = node.getChildrenList().stream()
                        .map(child -> (T) child)
                        .collect(Collectors.toList());
                result.addAll(flatList(children, setOrdFlag));
            }
        }
        return result;
    }

    private void rebuildTreeOrd(List<StandardsProjectInfoEntity> tree) {
        int ord = 1;
        for (StandardsProjectInfoEntity node : tree) {
            node.setOrd(ord++);
            if (CollUtil.isNotEmpty(node.getChildrenList())) {
                rebuildTreeOrd(node.getChildrenList());
            }
        }
    }

    private void rebuildOrd(List<ProjectInfoShowVo> result) {
        //重新构建ord
        int ord = 1;
        for (ProjectInfoShowVo projectInfoShowVo : result) {
            projectInfoShowVo.setOrd(ord++);
        }
    }

    private List<StandardsProjectInfoEntity> getAllChildren(StandardsProjectInfoEntity node) {
        List<StandardsProjectInfoEntity> children = new ArrayList<>();
        if (CollUtil.isNotEmpty(node.getChildrenList())) {
            children.addAll(node.getChildrenList());
            for (StandardsProjectInfoEntity child : node.getChildrenList()) {
                children.addAll(getAllChildren(child));
            }
        }
        return children;
    }

}