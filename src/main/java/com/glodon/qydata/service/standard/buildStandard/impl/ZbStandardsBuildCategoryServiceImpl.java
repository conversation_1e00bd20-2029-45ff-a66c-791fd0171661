package com.glodon.qydata.service.standard.buildStandard.impl;

import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildCategory;
import com.glodon.qydata.mapper.standard.buildStandard.ZbStandardsBuildCategoryMapper;
import com.glodon.qydata.service.standard.buildStandard.ZbStandardsBuildCategoryService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【zb_standards_build_category】的数据库操作Service实现
* @createDate 2022-08-01 10:57:17
*/
@Service
public class ZbStandardsBuildCategoryServiceImpl implements ZbStandardsBuildCategoryService {
    @Autowired
    private ZbStandardsBuildCategoryMapper standardsBuildCategoryMapper;

    @Override
    public List<ZbStandardsBuildCategory> selectByStandardId(Long standardId) {
        return standardsBuildCategoryMapper.selectByStandardId(standardId);
    }

    @Override
    public List<ZbStandardsBuildCategory> selectSelfByStandardId(Long standardId) {
        return standardsBuildCategoryMapper.selectSelfByStandardId(standardId);
    }

    @Override
    public void batchSaveSelf(List<ZbStandardsBuildCategory> insertCategoryList) {
        standardsBuildCategoryMapper.batchSaveSelf(insertCategoryList);
    }

    @Override
    public void delSelfByStandardId(Long standardId) {
        standardsBuildCategoryMapper.delSelfByStandardId(standardId);
    }

    @Override
    public List<ZbStandardsBuildCategory> selectSelfByStandardIds(List<Long> standIds) {
        return standardsBuildCategoryMapper.selectSelfByStandardIds(standIds);
    }

    @Override
    public void delByStandardIds(List<Long> allOriginIds) {
        if(CollectionUtils.isEmpty(allOriginIds)){
            return;
        }
        standardsBuildCategoryMapper.delByStandardIds(allOriginIds);
    }

    @Override
    public void batchSave(List<ZbStandardsBuildCategory> buildCategories) {
        if(CollectionUtils.isEmpty(buildCategories)){
            return;
        }
        standardsBuildCategoryMapper.batchSave(buildCategories);
    }

    @Override
    public void delSelfByStandardIds(List<Long> allSelfStandardIds) {
        if(CollectionUtils.isEmpty(allSelfStandardIds)){
            return;
        }
        standardsBuildCategoryMapper.delSelfByStandardIds(allSelfStandardIds);
    }

    @Override
    public List<ZbStandardsBuildCategory> selectByStandardIds(List<Long> standardIds) {
        return standardsBuildCategoryMapper.selectByStandardIds(standardIds);
    }
}




