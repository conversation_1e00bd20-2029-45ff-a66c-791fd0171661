package com.glodon.qydata.service.standard.buildStandard;

import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildPositionDetail;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【zb_standards_build_position_detail(企业标准数据-建造标准定位-细则)】的数据库操作Service
* @createDate 2022-08-01 10:57:17
*/
public interface ZbStandardsBuildPositionDetailService  {
    /**
     * 根据业态或产品定位id查询 详情列表
     * @param ids
     * @return
     */
    List<ZbStandardsBuildPositionDetail> selectSelfByPositionIds(List<Long> ids);

    /**
     * 批量保存结果数据
     * @param positionDetails
     */
    void batchSaveSelf(List<ZbStandardsBuildPositionDetail> positionDetails);

    /**
     * 删除产品定位的值
     * @param delPositionIds
     */
    void batchSelfByPositionIds(List<Long> delPositionIds);

    /**
     *
     * @param standIds
     * @return
     */
    List<ZbStandardsBuildPositionDetail> selectSelfByStandardIds(List<Long> standIds);

    /**
     * 根据建造标准删除企业数据
     * @param standardIds
     */
    void delByStandardIds(List<Long> standardIds);

    /**
     * 批量保存数据
     * @param positionDetails
     */
    void batchSave(List<ZbStandardsBuildPositionDetail> positionDetails);

    /**
     * 批量删除暂存表数据
     * @param standardIds
     */
    void delSelfByStandardIds(List<Long> standardIds);

    /**
     * @description: 插入或修改
     * @param standardId
     * @param standardDetailId
     * @param standardDetailDescId
     * @param positionDetails
     * @return void
     * <AUTHOR>
     * @date 2022/8/7 15:47
     */
    void insertOrUpdateSelf(Long standardId, Long standardDetailId, Long standardDetailDescId, List<ZbStandardsBuildPositionDetail> positionDetails);

    /**
     * 根据标准说明id查询
     * @param descId
     */
    List<ZbStandardsBuildPositionDetail> selectSelfByDescId(Long descId);
}
