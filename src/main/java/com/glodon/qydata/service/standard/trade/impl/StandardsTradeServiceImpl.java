package com.glodon.qydata.service.standard.trade.impl;

import cn.hutool.core.collection.CollUtil;
import com.glodon.qydata.common.CacheDataCommon;
import com.glodon.qydata.common.annotation.BusinessCache;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.constant.OperateConstants;
import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.common.enums.TradeNodeTypeEnum;
import com.glodon.qydata.common.enums.TradeReferEnum;
import com.glodon.qydata.dto.StandardTradeDto;
import com.glodon.qydata.dto.StandardTradeHistoryDataDto;
import com.glodon.qydata.dto.TradeDownOrUpDTO;
import com.glodon.qydata.dto.TradeEnableDTO;
import com.glodon.qydata.entity.standard.trade.TradeTagEnum;
import com.glodon.qydata.entity.standard.trade.ZbStandardsTrade;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.standard.trade.TradeTagEnumMapper;
import com.glodon.qydata.mapper.standard.trade.ZbStandardsTradeMapper;
import com.glodon.qydata.service.init.enterprise.InitTradeService;
import com.glodon.qydata.service.init.enterprise.InitTradeTagService;
import com.glodon.qydata.service.standard.feature.IProjectFeatureService;
import com.glodon.qydata.service.standard.historyDS.CustomerCodeConvertService;
import com.glodon.qydata.service.standard.mainQuantity.IStandardsMainQuantityService;
import com.glodon.qydata.service.standard.trade.IStandardsTradeService;
import com.glodon.qydata.service.system.PublishInfoService;
import com.glodon.qydata.util.EmptyUtil;
import com.glodon.qydata.util.RedisUtil;
import com.glodon.qydata.util.mover.ElementMover;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import com.glodon.qydata.vo.standard.trade.StandardReferTradeVO;
import com.glodon.qydata.vo.standard.trade.StandardTradeVO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 　　* @description: 标准打通--权限控制服务类
 　　* <AUTHOR>
 　　* @date 2021/10/19 9:52
 　　*/
@Slf4j
@Service
public class StandardsTradeServiceImpl implements IStandardsTradeService {
    @Autowired
    private ZbStandardsTradeMapper tradeMapper;
    @Autowired
    private TradeTagEnumMapper tradeTagEnumMapper;
    @Autowired
    private IProjectFeatureService featureService;
    @Autowired
    private IStandardsMainQuantityService mainQuantityService;
    @Autowired
    private CustomerCodeConvertService customerCodeConvertService;
    @Autowired
    private InitTradeService initTradeService;
    /**
     * 专业分类初始化
     */
    @Autowired
    private InitTradeTagService initTradeTagService;
    @Autowired
    private RedisUtil redisUtil;
    @Resource
    private PublishInfoService publishInfoServiceImpl;

    /**
     　　* @description: 根据企业编码查询所有待引用专业列表
     　　* @param customerCode 企业编码
     　　* @return List<StandardReferTradeVO> 内置专业引用列表返回前端数据载体列表
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/21 17:33
     　　*/
    @Override
    @BusinessCache(customerCode = "${customerCode}")
    public List<StandardReferTradeVO> getReferListByCustomerCode(String customerCode) throws BusinessException {

        List<StandardReferTradeVO> returnList = new ArrayList<>();
        TradeReferEnum[] referEnums = TradeReferEnum.values();
        for (TradeReferEnum tradeReferEnum : referEnums) {
            StandardReferTradeVO vo = new StandardReferTradeVO();
            vo.setDescription(tradeReferEnum.getName());
            vo.setTradeCode(tradeReferEnum.getCode());
            vo.setIfReferTrade(true);
            returnList.add(vo);
        }

        List<ZbStandardsTrade> tradeList = tradeMapper.selectListByCustomerCodeAndType(customerCode, Constants.ZbStandardsTradeConstants.TradeType.COMPANY_TRADE);
        if(CollectionUtils.isEmpty(tradeList)){
            return returnList;
        }
        Map<String,List<ZbStandardsTrade>> tradeMap = tradeList.parallelStream().collect(Collectors.groupingBy(ZbStandardsTrade::getReferTrade));
        for (StandardReferTradeVO StandardReferTradeVO : returnList) {
            String tradeCode = StandardReferTradeVO.getTradeCode();
            if(tradeMap.containsKey(tradeCode)){
                int size = tradeMap.get(tradeCode).size();
                if(size >= Constants.ZbStandardsTradeConstants.TRADE_REFER_MAX_COUNT){
                    StandardReferTradeVO.setIfReferTrade(false);
                }
            }
        }
        return returnList;
    }

    /**
     　　* @description: 新增专业
     　　* @param StandardTradeBo 新增专业数据载体 customerCode 企业编码 globalId 用户id
     　　* @return ZbStandardsTrade 新增入库的专业信息
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/21 17:55
     　　*/
    @Override
    @Transactional(rollbackFor = Exception.class)
    @BusinessCache(customerCode = "${customerCode}", isInvalidateCache = true)
    public ZbStandardsTrade insertTrade(StandardTradeDto standardTradeDto, String customerCode, Long globalId) throws BusinessException {
        String lockValue = lockValue();

        if (!redisUtil.tryLockRetry(lockValue, RedisKeyEnum.LOCK_INSERT_TRADE, customerCode)) {
            throw new BusinessException("请稍后重试...");
        }

        try {
            String referTradeCode = standardTradeDto.getTradeCode();
            String description = standardTradeDto.getDescription();

            // 专业编码与专业名称的正确性验证
            ZbStandardsTrade zbStandardsTrade = new ZbStandardsTrade();
            this.paramValidationByName(customerCode,description,null);
            this.paramValidationByTradeCode(referTradeCode, Constants.ZbStandardsTradeConstants.TradeType.SYSTEM_TRADE);
            zbStandardsTrade.setEnabled(Constants.ZbStandardsTradeConstants.EnableStatus.ENABLED);
            zbStandardsTrade.setCreatorId(globalId);
            zbStandardsTrade.setUpdaterId(globalId);
            zbStandardsTrade.setCustomerCode(customerCode);
            zbStandardsTrade.setDescription(standardTradeDto.getDescription());
            zbStandardsTrade.setId(SnowflakeIdUtils.getNextId());
            zbStandardsTrade.setIsDeleted(Constants.ZbStandardsTradeConstants.DeletedStatus.NO_DELETED);
            zbStandardsTrade.setReferTrade(referTradeCode);
            zbStandardsTrade.setCreateTime(new Date());
            zbStandardsTrade.setType(Constants.ZbStandardsTradeConstants.TradeType.COMPANY_TRADE);


            /**
             * 企业下所有专业信息，找到最大ord
             */
            List<ZbStandardsTrade> tradeListDb = this.getTradeList(customerCode);
            int ordMax = tradeListDb.get(tradeListDb.size() - 1).getOrd();
            zbStandardsTrade.setOrd(ordMax + 1);

            List<ZbStandardsTrade> tradeList = tradeMapper.selectListByCusAndReferTradeCode(customerCode,referTradeCode);
            /**
             * tradeList为null，用户第一次引用此专业新建专业,编码取第一个，顺序取 0 ，直接返回
             */
            TradeReferEnum anEnum = TradeReferEnum.getEnumByReferTradeCode(referTradeCode);
            List<String> tradeCodeList = anEnum.getReferCode();
            if(CollectionUtils.isEmpty(tradeList)){
                String tradeCode = tradeCodeList.get(0);
                zbStandardsTrade.setTradeCode(tradeCode);
                initTradeService.insertTradeData(zbStandardsTrade);
                return zbStandardsTrade;
            }

            /**
             * 过滤出已经删除的自定义专业信息，不为空，则直接使用此编码
             */
            List<ZbStandardsTrade> deletedTradeList = tradeList.stream().filter(vo->vo.getIsDeleted().equals(Constants.ZbStandardsTradeConstants.DeletedStatus.DELETED)).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(deletedTradeList)){
                ZbStandardsTrade trade = deletedTradeList.get(0);
                String tradeCode = trade.getTradeCode();
                zbStandardsTrade.setTradeCode(tradeCode);
                tradeMapper.deleteById(trade.getId());
                initTradeService.insertTradeData(zbStandardsTrade);
                return zbStandardsTrade;
            }

            /**
             * 专业列表中无已经删除的自定义专业信息，则对比字典表，拿出新编码，新增入库
             */
            tradeList.removeAll(deletedTradeList);
            List<String> tradeReferCodeList = tradeList.stream().map(ZbStandardsTrade ::getTradeCode).collect(Collectors.toList());
            for (String enumTradeCode : tradeCodeList) {
                if(!tradeReferCodeList.contains(enumTradeCode)){
                    zbStandardsTrade.setTradeCode(enumTradeCode);
                    initTradeService.insertTradeData(zbStandardsTrade);
                    return zbStandardsTrade;
                }
            }
        } finally {
            publishInfoServiceImpl.updateVersion(customerCode, String.valueOf(globalId), OperateConstants.TRADE);
            redisUtil.unlock(lockValue, RedisKeyEnum.LOCK_INSERT_TRADE, customerCode);
        }

        throw new BusinessException("新增专业失败，请稍后重试...");
    }

    protected String lockValue(){
        String id = SnowflakeIdUtils.getSystemUuid();
        long threadId = Thread.currentThread().getId();
        return id + ":" + threadId;
    }

    /**
    　　* @description: 专业名称的正确性验证
    　　* @param customerCode 企业编码 description 专业名称 id 专业id
    　　* @return
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/10/22 9:27
    　　*/
    private void paramValidationByName(String customerCode, String description, Long id){
        Boolean checkNameStatus = this.checkedName(customerCode,description,id);
        if(Boolean.TRUE.equals(checkNameStatus)){
            throw new BusinessException(ResponseCode.PARAMETER_ERROR,"名称重复，请核对");
        }
    }

    /**
     　　* @description: 专业编码的正确性验证
     　　* @param tradeCode 专业编码 type 专业类型
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/22 9:27
     　　*/
    private void paramValidationByTradeCode(String tradeCode, Byte type){
        /**
         * type = 0 表示验证被引用的专业，是否正确，TradeReferEnum中应该存在此枚举，否则报错
         */
        if(type.equals(Constants.ZbStandardsTradeConstants.TradeType.SYSTEM_TRADE)){
            TradeReferEnum anEnum = TradeReferEnum.getEnumByReferTradeCode(tradeCode);
            if(anEnum == null){
                throw new BusinessException(ResponseCode.PARAMETER_ERROR,"引用专业编码不存在，请核对");
            }
            return;
        }

        /**
         * type = 1 表示需要验证传入专业是否是系统默认，不是则正确，是则报错
         */
        if(type.equals(Constants.ZbStandardsTradeConstants.TradeType.COMPANY_TRADE)){
            List<ZbStandardsTrade> tradeList = CacheDataCommon.TRADE_LIST;
            for (ZbStandardsTrade zbStandardsTrade : tradeList) {
                String tradeCodeCache = zbStandardsTrade.getTradeCode();
                if(tradeCode.equals(tradeCodeCache)){
                    throw new BusinessException(ResponseCode.PARAMETER_ERROR,"默认专业不能编辑或删除，请核对");
                }
            }
        }

    }

    /**
     　　* @description: 编辑专业名称
     　　* @param StandardTradeBo 编辑专业数据载体 customerCode 企业编码 globalId 用户id
     　　* @return ZbStandardsTrade 更新后的入库的专业信息
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/21 17:55
     　　*/
    @Override
    @Transactional(rollbackFor = Exception.class)
    @BusinessCache(customerCode = "${customerCode}", isInvalidateCache = true)
    public ZbStandardsTrade updateTrade(StandardTradeDto standardTradeDto, String customerCode, Long globalId) throws BusinessException {

        String description = standardTradeDto.getDescription();
        String tradeCode = standardTradeDto.getTradeCode();
        Long id = standardTradeDto.getId();

        // 专业编码与专业名称的正确性验证
        this.paramValidationByName(customerCode,description,id);
        this.paramValidationByTradeCode(tradeCode, Constants.ZbStandardsTradeConstants.TradeType.COMPANY_TRADE);

        ZbStandardsTrade trade = tradeMapper.selectById(id);
        // 验证专业存在性
        if(trade == null){
            throw new BusinessException(ResponseCode.PARAMETER_ERROR,"编辑专业不存在，请核对");
        }

        trade.setDescription(description);
        trade.setUpdaterId(globalId);
        tradeMapper.updateTrade(trade);
        publishInfoServiceImpl.updateVersion(customerCode, String.valueOf(globalId), OperateConstants.TRADE);
        return trade;
    }

    /**
     　　* @description: 同一个企业下，标准名称唯一
     id为空，企业下同名称记录存在  则重复名称
     不存在  则正常
     id有值 企业下同名称记录存在  id相同  则正常
     id不相同  则重复名称
     企业下同名称记录不存在  则正常
     　　* @param  name:要判断的标准名称 id：修改接口下的编辑标准id
     　　* @return 是否存在，true存在，false不存在
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/21 20:37
     　　*/
    @Override
    public Boolean checkedName(String customerCode,String name, Long id) {
        ZbStandardsTrade zbStandardsTrade = tradeMapper.searchRecordsByCusCodeAndName(name,customerCode);
        if(EmptyUtil.isEmpty(zbStandardsTrade)){
            return false;
        }
        return EmptyUtil.isEmpty(id) || zbStandardsTrade.getId().compareTo(id) != 0;
    }

    /**
     　　* @description: 删除专业
     　　* @param customerCode 所在企业 id 专业id  globalId 用户标识id
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/21 17:55
     　　*/
    @Override
    @BusinessCache(customerCode = "${customerCode}", isInvalidateCache = true)
    @Transactional(rollbackFor = Exception.class)
    public void deleteTrade(String customerCode,Long id,Long globalId) throws BusinessException{

        ZbStandardsTrade zbStandardsTrade = tradeMapper.selectById(id);
        /**
         * 企业与专业id做匹配验证
         */
        if(!zbStandardsTrade.getCustomerCode().equals(customerCode)){
            throw new BusinessException(ResponseCode.PARAMETER_ERROR,"需删除专业不属于本企业，请核对");
        }

        /**
         * 默认专业不能删除，使用专业码做验证
         */
       this.paramValidationByTradeCode(zbStandardsTrade.getTradeCode(), Constants.ZbStandardsTradeConstants.TradeType.COMPANY_TRADE);

        // 逻辑删除此记录
        tradeMapper.updateTradeIsDeleted(id, Constants.ZbStandardsTradeConstants.DeletedStatus.DELETED,globalId);

        // 同步逻辑删除专业下工程特征、工程特征分类视图、含量指标等携带信息
        featureService.deleteByTradeId(id);
        mainQuantityService.deleteByTradeId(id);
        publishInfoServiceImpl.updateVersion(customerCode, String.valueOf(globalId), OperateConstants.TRADE);
    }

    /**
     　　* @description: 根据企业编码查询专业列表
     　　* @param customerCode 企业编码 globalId 用户id
     　　* @return List<StandardTradeVO> 专业列表返回前端数据载体列表
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/21 17:33

     　　*/
    @Override
    @BusinessCache(customerCode = "${customerCode}")
    public List<StandardTradeVO> getTradeVoList(String customerCode) throws BusinessException{
        List<StandardTradeVO> returnList = new ArrayList<>();
        List<ZbStandardsTrade> tradeList = this.getTradeList(customerCode);
        for (ZbStandardsTrade zbStandardsTrade : tradeList) {
            StandardTradeVO vo = new StandardTradeVO();
            BeanUtils.copyProperties(zbStandardsTrade,vo);
            vo.setReferTradeCode(zbStandardsTrade.getReferTrade());
            returnList.add(vo);
        }
        return returnList;
    }

    /**
     * 生成分类节点
     * @param tradeList
     * @return
     * @throws BusinessException
     */
    @Override
    public List<StandardTradeVO> convertToTradeTree(String customCode, List<StandardTradeVO> tradeList, Boolean needTag) throws BusinessException{
        if (CollUtil.isEmpty(tradeList)) {
            return tradeList;
        }

        // 查询企业下的专业标签分类
        List<TradeTagEnum> tradeTagEnums = tradeTagEnumMapper.selectAllListByCustomerCode(customCode);

        // 当前企业的专业使用的专业标签
        Set<Long> tagIds = tradeList.stream().map(StandardTradeVO::getTradeTagId).collect(Collectors.toSet());
        tradeTagEnums = tradeTagEnums.stream().filter(item->(tagIds.contains(item.getId()))).collect(Collectors.toList());
        Map<Long, StandardTradeVO> tagEnumMap = tradeTagEnums.stream().collect(Collectors.toMap(TradeTagEnum::getId, v->new StandardTradeVO()));
        List<StandardTradeVO> resultList = new ArrayList<>();
        // 转换标签节点
        for (TradeTagEnum tradeTagEnum : tradeTagEnums) {
            StandardTradeVO vo = new StandardTradeVO();
            vo.setId(tradeTagEnum.getId());
            vo.setNodeType(TradeNodeTypeEnum.TRADE_TAG_NODE.getNodeType());
            vo.setDescription(tradeTagEnum.getName());
            vo.setChildren(new ArrayList<>());
            tagEnumMap.put(tradeTagEnum.getId(), vo);
            resultList.add(vo);
        }

        // 将专业插入到对应的标签下
        for (StandardTradeVO zbStandardsTrade : tradeList) {
            Long tradeTagId = zbStandardsTrade.getTradeTagId();
            zbStandardsTrade.setNodeType(TradeNodeTypeEnum.TRADE_NODE.getNodeType());
            if (tagEnumMap.containsKey(tradeTagId)) {
                tagEnumMap.get(tradeTagId).getChildren().add(zbStandardsTrade);
            } else {
                resultList.add(zbStandardsTrade);
            }
        }

        // 如果需要标签返回标签
        if (needTag) {
            return resultList;
        } else {
            List<StandardTradeVO> sortedList = new ArrayList<>();
            resultList.forEach(item->{
                if (Objects.equals(item.getNodeType(), TradeNodeTypeEnum.TRADE_TAG_NODE.getNodeType())){
                    if (!CollUtil.isEmpty(item.getChildren())){
                        sortedList.addAll(item.getChildren());
                    }
                } else {
                    sortedList.add(item);
                }
            });

            return sortedList;
        }
    }

    /**
     * 生成分类节点
     * @param tradeList
     * @return
     * @throws BusinessException
     */
    @Override
    public List<ZbStandardsTrade> convertToStandardTradeTree(String customCode, List<ZbStandardsTrade> tradeList) throws BusinessException{
        if (CollUtil.isEmpty(tradeList)) {
            return tradeList;
        }

        // 检查是否设置了标签
        Set<Long> tagIds = tradeList.stream().map(ZbStandardsTrade::getTradeTagId).collect(Collectors.toSet());
        if (CollUtil.isEmpty(tagIds)){
            return tradeList;
        }

        // 查询企业下的专业标签分类
        List<TradeTagEnum> tradeTagEnums = tradeTagEnumMapper.selectAllListByCustomerCode(customCode);
        tradeTagEnums = tradeTagEnums.stream().filter(item->(tagIds.contains(item.getId()))).collect(Collectors.toList());
        Map<Long, ZbStandardsTrade> tagEnumMap = tradeTagEnums.stream().collect(Collectors.toMap(TradeTagEnum::getId, v->new ZbStandardsTrade()));
        List<ZbStandardsTrade> resultList = new ArrayList<>();
        // 转换标签节点
        for (TradeTagEnum tradeTagEnum : tradeTagEnums) {
            ZbStandardsTrade vo = new ZbStandardsTrade();
            vo.setId(tradeTagEnum.getId());
            vo.setNodeType(TradeNodeTypeEnum.TRADE_TAG_NODE.getNodeType());
            vo.setDescription(tradeTagEnum.getName());
            vo.setChildren(new ArrayList<>());
            tagEnumMap.put(tradeTagEnum.getId(), vo);
            resultList.add(vo);
        }

        // 将专业放在标签下
        for (ZbStandardsTrade zbStandardsTrade : tradeList) {
            Long tradeTagId = zbStandardsTrade.getTradeTagId();
            zbStandardsTrade.setNodeType(TradeNodeTypeEnum.TRADE_NODE.getNodeType());
            if (tagEnumMap.containsKey(tradeTagId)) {
                tagEnumMap.get(tradeTagId).getChildren().add(zbStandardsTrade);
            } else {
                resultList.add(zbStandardsTrade);
            }
        }
        return resultList;
    }

    /**
    　　* @description: 获取企业下未删除专业数据，若不存在，会复制一份系统内置返回
    　　* @param customerCode 企业编码 globalId 用户id
    　　* @return List<ZbStandardsTrade 专业列表
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/10/22 20:25
    　　*/
    @Override
    public List<ZbStandardsTrade> getTradeList(String customerCode){
        try{
            List<ZbStandardsTrade> tradeList = tradeMapper.selectListByCustomerCode(customerCode);

             // 根据企业编码查询到的专业，列表有值，表明企业下已经存在专业列表,直接返回
            if(CollectionUtils.isEmpty(tradeList)){
                initTradeService.initData(customerCode);
                tradeList = tradeMapper.selectListByCustomerCode(customerCode);
            }

            return tradeList;
        }catch (Exception e){
            log.error("根据企业编码查询专业列表出错", e);
            throw new BusinessException(500, "根据企业编码查询专业列表出错");
        }
    }

    /**
     　　* @description: 历史数据同步
     　　* @param
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/2 23:17
     　　*/
    @Override
    public void insertHistoryData(Map<String,List<StandardTradeHistoryDataDto>> map){
        List<ZbStandardsTrade> needInsertList = new ArrayList<>();
        for (String customerCode : map.keySet()) {
            List<StandardTradeHistoryDataDto> list =  map.get(customerCode);
            if(CollectionUtils.isEmpty(list)){
                continue;
            }

            // 为了支持多次同步同一企业，首先删除企业下所有专业信息
            String newCustomerCode = customerCodeConvertService.getCustomerCode(customerCode);
            tradeMapper.deleteByCustomerCode(newCustomerCode);

            //复制属性，放置主键与企业编码
            int size = list.size();
            List<Long> idList = SnowflakeIdUtils.getNextId(size);
            for (int i = 0; i < list.size(); i++) {
                ZbStandardsTrade trade = new ZbStandardsTrade();
                StandardTradeHistoryDataDto dto = list.get(i);
                BeanUtils.copyProperties(dto,trade);
                trade.setId(idList.get(i));
                trade.setCustomerCode(newCustomerCode);
                needInsertList.add(trade);
            }
        }

        // 入库保存
        if(CollectionUtils.isNotEmpty(needInsertList)){
            tradeMapper.batchInsert(needInsertList);
        }
    }

    /**
     　　* @description: 查询系统内置的专业列表
     　　* @param
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/6 10:41
     　　*/
    @Override
    public List<ZbStandardsTrade> getSystemData(){
        return tradeMapper.selectListByCustomerCode(Constants.ZbStandardsTradeConstants.SYSTEM_CUSTOMER_CODE);
    }

    /**
     　　* @description: 对外服务--根据企业编码获取所有专业数据 包括内置和自定义,包括删除与未删除
     　　* @param customerCode 查询的企业编码
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/6 10:41
     　　*/
    @Override
    public List<ZbStandardsTrade> getTradeListByCustomerCode(String customerCode) throws BusinessException{
        String newCustomerCode = customerCodeConvertService.getCustomerCode(customerCode);
        return tradeMapper.selectAllListByCustomerCode(newCustomerCode);
    }

    /**
     　　* @description: 对外接口，根据企业编码查询专业列表，包括删除与未删除
     　　* @param customerCode 企业编码
     　　* @return List<StandardTradeVO> 专业列表返回前端数据载体列表
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/21 17:33
     　　*/
    @Override
    @BusinessCache(customerCode = "${customerCode}")
    public List<ZbStandardsTrade> initAllListByCustomerCode(String customerCode) throws BusinessException {

        // 查询企业下删除与未删除的专业列表，若为空，则初始化一份
        List<ZbStandardsTrade> tradeList = tradeMapper.selectAllListByCustomerCode(customerCode);
        if (CollectionUtils.isEmpty(tradeList)) {
            initTradeService.initData(customerCode);
            tradeList = tradeMapper.selectAllListByCustomerCode(customerCode);
        }
        return tradeList;
    }

    /**
     　　* @description: 将旧企业专业标准数据复制到新业态专业标准
     　　* @param  oldCustomerCode:原企业编码  newCustomerCode 新企业编码
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/9 9:55
     　　*/
    @Override
    public Map<Long,Long> initTradeDate(String oldCustomerCode,String newCustomerCode){
        // 查找原企业下的专业数据，若为空，不处理
        List<ZbStandardsTrade> tradeList = tradeMapper.selectAllListByCustomerCode(oldCustomerCode);
        if(CollectionUtils.isEmpty(tradeList)){
            return null;
        }
        // 后续特征与主要量标准的复制需要使用到新旧专业的对应关系
        Map<Long,Long> tradeIdRelationMap = new HashMap<>();

        // 复制一份新数据入库
        int size = tradeList.size();
        List<Long> ids = SnowflakeIdUtils.getNextId(size);
        for (int i = 0; i < size; i++) {
            ZbStandardsTrade trade = tradeList.get(i);
            tradeIdRelationMap.put(trade.getId(),ids.get(i));
            trade.setId(ids.get(i));
            trade.setCustomerCode(newCustomerCode);
            trade.setCreatorId(-100L);
            trade.setCreateTime(new Date());
            trade.setUpdaterId(null);
            trade.setUpdateTime(null);
        }

        // 入库保存
        tradeMapper.batchInsert(tradeList);
        return tradeIdRelationMap;
    }

    /**
     　　* @description: 根据企业编码删除所有专业标准
     　　* @param  customerCode 企业编码
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/9 19:41
     　　*/
    @Override
    public void deleteByCustomerCode(String customerCode){
         tradeMapper.deleteByCustomerCode(customerCode);
    }

    @Override
    public List<ZbStandardsTrade> selectDescriptionListByIds(String customerCode, List<Long> tradeIds) {
        return tradeMapper.selectDescriptionListByIds(customerCode, tradeIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @BusinessCache(customerCode = "${customerCode}", isInvalidateCache = true)
    public void downOrUpTrade(String customerCode, String globalId, TradeDownOrUpDTO tradeDownOrUpDTO) {
        List<ZbStandardsTrade> zbStandardsTrades = tradeMapper.selectListByCustomerCode(customerCode);
        String moveType = Constants.ZbStandardsTradeConstants.UpOrDownOperation.UP.equals(tradeDownOrUpDTO.getOperation())
                ? Constants.MOVE_TYPE_UP : Constants.MOVE_TYPE_DOWN;

        Long tradeTagId = null;
        Map<Long, List<ZbStandardsTrade>> groupTagMap = new HashMap<>();
        for (int i = 0; i < zbStandardsTrades.size(); ++i) {
            ZbStandardsTrade trade = zbStandardsTrades.get(i);
            if (Objects.equals(trade.getId(), tradeDownOrUpDTO.getTradeId())) {
                tradeTagId = trade.getTradeTagId();
            }
            groupTagMap.computeIfAbsent(trade.getTradeTagId(), k->{return new ArrayList<>();}).add(trade);
        }

        List<ZbStandardsTrade> siblingNodes = groupTagMap.get(tradeTagId);

        ElementMover.move(siblingNodes, tradeDownOrUpDTO.getTradeId(), moveType, tradeMapper);
        publishInfoServiceImpl.updateVersion(customerCode, globalId, OperateConstants.TRADE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @BusinessCache(customerCode = "${customerCode}", isInvalidateCache = true)
    public void changeEnable(String customerCode, String globalId, List<TradeEnableDTO> tradeEnableDTOS) {
        if (CollectionUtils.isEmpty(tradeEnableDTOS)) {
            log.info("当前输入的更新启用状态的输入参数为空");
            return;
        }
        List<ZbStandardsTrade> zbStandardsTrades = tradeMapper.selectListByCustomerCode(customerCode);
        if (CollectionUtils.isEmpty(zbStandardsTrades)) {
            throw new BusinessException(ResponseCode.PARAMETER_ERROR, "当前企业下的专业工程为空!");
        }

        Map<Long, TradeEnableDTO> tradeEnableMap = tradeEnableDTOS.stream()
                .collect(Collectors.toMap(TradeEnableDTO::getId, Function.identity()));
        List<ZbStandardsTrade> updateList = Lists.newArrayList();
        for (ZbStandardsTrade zbStandardsTrade : zbStandardsTrades) {
            TradeEnableDTO tradeItem = tradeEnableMap.get(zbStandardsTrade.getId());
            if (tradeItem == null || (Objects.equals(tradeItem.getTradeTagId(), zbStandardsTrade.getTradeTagId()) && Objects.equals(tradeItem.getEnabled(), zbStandardsTrade.getEnabled()))) {
                log.info("[tradeId:{}]对应的专业未传入对应更新参数或未进行更新", zbStandardsTrade.getId());
                continue;
            }

            zbStandardsTrade.setEnabled(tradeItem.getEnabled());
            zbStandardsTrade.setTradeTagId(tradeItem.getTradeTagId());
            updateList.add(zbStandardsTrade);
        }

        if (CollectionUtils.isNotEmpty(updateList)){
            tradeMapper.batchUpdateEnabled(updateList);
            publishInfoServiceImpl.updateVersion(customerCode, globalId, OperateConstants.TRADE);
        }
    }

    private int findIndexById(List<ZbStandardsTrade> list, Long id) {
        for (int i = 0; i < list.size(); i++) {
            if (Objects.equals(list.get(i).getId(), id)) {
                return i;
            }
        }
        throw new BusinessException(ResponseCode.PARAMETER_ERROR, "操作的记录不存在，请刷新重试");
    }

    /**
     * 更新专业标签选项表
     */
    @Override
    public void saveTradeTag(String customerCode, List<TradeTagEnum> tradeTagEnums) {
        // 加锁,防止与初始化数据冲突
        if (StringUtils.isEmpty(customerCode)){
            return;
        }

        // 删除当前企业的专业标签
        tradeTagEnumMapper.removeAllByCustomCode(customerCode);
        if (CollUtil.isEmpty(tradeTagEnums)) {
            return;
        }

        // 将专业标签插入
        for (int index = 0; index < tradeTagEnums.size(); ++index) {
            TradeTagEnum item = tradeTagEnums.get(index);
            item.setEnterpriseId(customerCode);
            item.setModifyTime(new Date());
            item.setOrd(index + 1);
            if (Objects.isNull(item.getId())) {
                item.setId(SnowflakeIdUtils.getNextId());
            }
        }

        tradeTagEnumMapper.saveBatch(tradeTagEnums);
    }

    /**
     * 获取专业标签选项
     */
    @Override
    public List<TradeTagEnum> getTradeOptions(String customerCode) {
        // 检查专业分类有没有初始化
        List<TradeTagEnum> tradeTagEnumList = tradeTagEnumMapper.selectAllListByCustomerCode(customerCode);
        if (CollUtil.isEmpty(tradeTagEnumList)){
            initTradeTagService.initData(customerCode);
        }

        return tradeTagEnumMapper.selectAllListByCustomerCode(customerCode);
    }
}
