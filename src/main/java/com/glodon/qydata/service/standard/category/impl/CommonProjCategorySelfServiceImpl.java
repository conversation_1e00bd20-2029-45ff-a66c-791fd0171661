package com.glodon.qydata.service.standard.category.impl;

import cn.hutool.core.collection.CollUtil;
import com.glodon.qydata.common.RequestContent;
import com.glodon.qydata.common.annotation.BusinessCache;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.constant.OperateConstants;
import com.glodon.qydata.common.constant.TableNameConstants;
import com.glodon.qydata.config.idsequence.IDGenerator;
import com.glodon.qydata.entity.standard.category.*;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.standard.category.*;
import com.glodon.qydata.service.init.self.InitCategorySelfService;
import com.glodon.qydata.service.standard.category.CommonProjCategorySelfService;
import com.glodon.qydata.service.system.PublishInfoService;
import com.glodon.qydata.util.SpringUtil;
import com.glodon.qydata.util.mover.ElementMover;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.standard.category.CommonProjCategoryAddVo;
import com.glodon.qydata.vo.standard.category.CommonProjCategoryUpdateVo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.glodon.qydata.common.constant.Constants.CategoryConstants.*;

/**
 * <AUTHOR>
 * @date 2019/12/17 16:49
 */
@Service
@Slf4j
public class CommonProjCategorySelfServiceImpl implements CommonProjCategorySelfService {

    @Autowired
    private CommonProjCategoryMapper commonProjCategoryMapper;
    @Autowired
    private CommonProjCategorySelfMapper commonProjCategorySelfMapper;
    @Autowired
    private InitCategorySelfService initCategorySelfService;
    @Autowired
    private CategoryTagSelfMapper categoryTagSelfMapper;
    @Autowired
    private CategoryTagEnumSelfMapper categoryTagEnumSelfMapper;
    @Autowired
    private CategoryTagEnumRelSelfMapper categoryTagEnumRelSelfMapper;

    @Autowired
    private CategoryTagMapper categoryTagMapper;
    @Autowired
    private CategoryTagEnumMapper categoryTagEnumMapper;
    @Autowired
    private CategoryTagEnumRelMapper categoryTagEnumRelMapper;
    @Autowired
    private CategoryTagSelfServiceImpl categoryTagSelfServiceImpl;
    @Autowired
    private CommonProjectCategoryUsedService commonProjectCategoryUsedService;
    @Resource
    private PublishInfoService publishInfoServiceImpl;
    private final Pattern pattern = Pattern.compile("\\d{3}$");

    /**
     * 新增分类/子分类
     */
    @Override
    public CommonProjCategory saveCommonProjCategory(CommonProjCategoryAddVo categoryVo, String customerCode, String globalId, Integer type) {
        CommonProjCategory category = new CommonProjCategory();
        category.setAccountname(categoryVo.getAccountName());
        category.setCategoryTypeCode(categoryVo.getCategoryTypeCode());
        category.setCategoryTypeName(categoryVo.getCategoryTypeName());
        category.setCategoryname(categoryVo.getCategoryName());
        category.setGlobalId(globalId);
        category.setQyCode(customerCode);
        category.setType(type);

        AddCategoryParams addCategoryParams = new AddCategoryParams();

        if (categoryVo.getInsertFlag() == 0 && StringUtils.isEmpty(categoryVo.getPreCategoryCode())) {
            // 普通插入
            setOrd(categoryVo, category, customerCode, type, addCategoryParams);
        } else {
            // 标签下插入，需要给标签绑定关系
            setOrdInTag(categoryVo, category, addCategoryParams);
        }
        // 新增的分类名称与删除的分类名称完全一致
        CommonProjCategory oldCommonProjCategory = commonProjCategorySelfMapper.queryRepeatName(customerCode, categoryVo.getCategoryName(), addCategoryParams.getLevel(), addCategoryParams.getParentId(), type);
        if (Objects.isNull(oldCommonProjCategory)){
            // 获取新的categoryCode
            String newCode = addCategoryParams.getParentId() + getEffectiveNewCode(addCategoryParams.getBrotherCategory(), 0);
            category.setCommonprojcategoryid(newCode);
            category.setCategorycode1(newCode.substring(0, 3));
            category.setType(type);
            if(newCode.length() > Constants.CategoryConstants.LEVEL_1_LEN){
                category.setCategorycode2(newCode.substring(0, 6));
            }
            if(newCode.length() > Constants.CategoryConstants.LEVEL_2_LEN){
                category.setCategorycode3(newCode.substring(0, 9));
            }
            if(newCode.length() > Constants.CategoryConstants.LEVEL_3_LEN){
                category.setCategorycode4(newCode.substring(0, 12));
            }
            category.setLevel((long) (newCode.length()/Constants.CategoryConstants.SINGLE_LEVEL_LEN));
            commonProjCategorySelfMapper.saveCommonProjCategory(category);
        } else {
            // 删除掉的分类，再次新增时，只要新增的分类名称与删除时的分类名称完全一致，则ID相同。
            category.setCommonprojcategoryid(oldCommonProjCategory.getCommonprojcategoryid());
            category.setId(oldCommonProjCategory.getId());
            category.setIsUsing(Constants.CategoryConstants.WHETHER_TRUE);
            category.setIsDeleted(false);
            commonProjCategorySelfMapper.updateByPrimaryKey(category);
        }

        if (CollectionUtils.isNotEmpty(addCategoryParams.getOtherOrdChangeList())){
            commonProjCategorySelfMapper.batchUpdateOrd(addCategoryParams.getOtherOrdChangeList());
        }

        // 标签上插入需要绑关系
        if (categoryVo.getInsertFlag() == 1 || (categoryVo.getInsertFlag() == 0 && StringUtils.isNotEmpty(categoryVo.getPreCategoryCode()))) {
            saveCategoryTagRels(categoryVo, category);
            if (category.getOrd() == null) {
                categoryTagSelfServiceImpl.resetCategoryOrd(category.getQyCode());
            }
        }

        return commonProjCategorySelfMapper.getSelfCategoryByPrimaryKey(category.getId());
    }

    private void setOrdInTag(CommonProjCategoryAddVo categoryVo, CommonProjCategory category, AddCategoryParams addCategoryParams) {
        if (!StringUtils.isEmpty(categoryVo.getPreCategoryCode())) {
            categoryVo.setLastCategoryCode(categoryVo.getPreCategoryCode());
        }

        category.setCategoryTypeCode(Constants.CategoryConstants.DEFAULT_CATEGORY_TYPE_CODE);
        category.setCategoryTypeName(Constants.CategoryConstants.DEFAULT_CATEGORY_TYPE_NAME);

        List<CommonProjCategory> topLevelOneCategory = commonProjCategorySelfMapper.queryTopCategoryList(category.getQyCode(), category.getType());
        addCategoryParams.setBrotherCategory(topLevelOneCategory);
        // 在标签上插入分类（插入与标签同级别的分类，位置在同级标签最下，同级分类最上）
        if (categoryVo.getInsertFlag() == 1 && Constants.CategoryConstants.ADD_TYPE_THIS.equals(categoryVo.getAddType())) {
            Optional<CommonProjCategory> firstCategory = topLevelOneCategory.stream().filter(cat -> cat.getCommonprojcategoryid().equals(categoryVo.getFirstCategoryCode())).findFirst();
            if (firstCategory.isPresent()) {
                CommonProjCategory baseCategory = firstCategory.get();
                category.setOrd(baseCategory.getOrd());
                topLevelOneCategory.stream().filter(cat -> cat.getOrd() > baseCategory.getOrd()).forEach(cat -> cat.setOrd(cat.getOrd() + 1));
                baseCategory.setOrd(baseCategory.getOrd() + 1);
                ElementMover.saveToDatabase(topLevelOneCategory, commonProjCategorySelfMapper);
            }
        }
        // 在标签上插入子分类（即相当于给分类打标签，位置在同级标签或分类的最下）
        if ((categoryVo.getInsertFlag() == 1 && Constants.CategoryConstants.ADD_TYPE_CHILD.equals(categoryVo.getAddType()))
                || (categoryVo.getInsertFlag() == 0)) {
            Optional<CommonProjCategory> lastCategory = topLevelOneCategory.stream().filter(cat -> cat.getCommonprojcategoryid().equals(categoryVo.getLastCategoryCode())).findFirst();
            if (lastCategory.isPresent()) {
                CommonProjCategory baseCategory = lastCategory.get();
                category.setOrd(baseCategory.getOrd() + 1);
                topLevelOneCategory.stream().filter(cat -> cat.getOrd() > baseCategory.getOrd()).forEach(cat -> cat.setOrd(cat.getOrd() + 1));
                ElementMover.saveToDatabase(topLevelOneCategory, commonProjCategorySelfMapper);
            }
        }

    }

    private void setOrd(CommonProjCategoryAddVo categoryVo,
                         CommonProjCategory category,
                         String customerCode,
                         Integer type,
                         AddCategoryParams addCategoryParams) {
        String baseCategoryCode = categoryVo.getBaseCommonprojcategoryid();
        Integer addType = categoryVo.getAddType();
        String parentId = "";
        int level;
        List<CommonProjCategory> brotherCategory;
        List<CommonProjCategory> otherOrdChangeList = null;
        if (Constants.CategoryConstants.ADD_TYPE_THIS.equals(addType)){
            if (StringUtils.isEmpty(baseCategoryCode)){
                level = LEVEL_1;
            } else {
                level = baseCategoryCode.length() / Constants.CategoryConstants.SINGLE_LEVEL_LEN;
            }
            if(level == LEVEL_1){
                // 顶级
                brotherCategory = commonProjCategorySelfMapper.queryTopCategoryList(customerCode, type);
                // 新增一级分类默认归属房建模板
                category.setCategoryTypeCode(Constants.CategoryConstants.DEFAULT_CATEGORY_TYPE_CODE);
                category.setCategoryTypeName(Constants.CategoryConstants.DEFAULT_CATEGORY_TYPE_NAME);
            }else{
                // 2/3/4级
                parentId = baseCategoryCode.substring(0, baseCategoryCode.length() - Constants.CategoryConstants.SINGLE_LEVEL_LEN);
                brotherCategory = getCommonProjCategories(parentId, customerCode, type);
            }

            if (StringUtils.isEmpty(baseCategoryCode)){
                // 没有选中的分类就是给1级的末位加
                int maxOrd = getMaxOrd(brotherCategory);
                category.setOrd(maxOrd + 1);
            }else {
                // ord处理，新增的分类ord=选中ord+1，选中之后的分类ord全+1
                Integer baseOrd = gerBaseOrd(baseCategoryCode, brotherCategory);
                category.setOrd(baseOrd + 1);
                otherOrdChangeList = otherOrdChange(baseOrd, brotherCategory);
            }
        }else {
            level = baseCategoryCode.length() / Constants.CategoryConstants.SINGLE_LEVEL_LEN + 1;
            // 选中的父级分类
            parentId = baseCategoryCode;
            brotherCategory = getCommonProjCategories(parentId, customerCode, type);
            // ord处理，插入到子分类的最后
            int maxOrd = getMaxOrd(brotherCategory);
            category.setOrd(maxOrd + 1);
        }
        addCategoryParams.setLevel(level);
        addCategoryParams.setParentId(parentId);
        addCategoryParams.setBrotherCategory(brotherCategory);
        addCategoryParams.setOtherOrdChangeList(otherOrdChangeList);
    }

    private void saveCategoryTagRels(CommonProjCategoryAddVo categoryVo, CommonProjCategory category) {
        String relIdPath = categoryVo.getRelIdPath();
        if (StringUtils.isEmpty(relIdPath)) {
            return;
        }
        List<String> relIds = Arrays.asList(relIdPath.split("/"));
        List<CategoryTagEnumRel> allCategoryTagEnumRels = categoryTagEnumRelSelfMapper.selectByEnterpriseId(category.getQyCode());

        List<CategoryTagEnumRel> thisCategoryTagEnumRelList = allCategoryTagEnumRels.stream().filter(categoryTagEnumRel -> relIds.contains(categoryTagEnumRel.getId().toString())).collect(Collectors.toList());

        List<CategoryTagEnumRel> categoryTagEnumRelList = new ArrayList<>();

        thisCategoryTagEnumRelList.forEach(categoryTagEnumRel -> {
            CategoryTagEnumRel newCategoryTagEnumRel = new CategoryTagEnumRel();
            BeanUtils.copyProperties(categoryTagEnumRel, newCategoryTagEnumRel);
            newCategoryTagEnumRel.setId(SnowflakeIdUtils.getNextId());
            newCategoryTagEnumRel.setCategoryCode(category.getCommonprojcategoryid());
            newCategoryTagEnumRel.setCreateTime(new Date());
            categoryTagEnumRelList.add(newCategoryTagEnumRel);
        });
        if (CollectionUtils.isNotEmpty(categoryTagEnumRelList)) {
            categoryTagEnumRelSelfMapper.saveBatch(categoryTagEnumRelList);
        }
    }

    /***
     * @description: 批量新增分类
     * @param excelCommonProjCategoryList 1
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2022/7/7 14:44
     */
    @Override
    public void saveCommonProjCategoryList(List<CommonProjCategory> excelCommonProjCategoryList) {
        String globalId = RequestContent.getGlobalId();
        String customerCode = RequestContent.getCustomerCode();
        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        //获取分类，用于去重
        List<CommonProjCategory> categoryList = commonProjCategorySelfMapper.selectSelfAll(customerCode, type, null);
        //数据库用户分类分组
        Map<Long, List<CommonProjCategory>> groupCommonProjCategory = categoryList.stream().collect(Collectors.groupingBy(CommonProjCategory::getLevel));
        Map<String, CommonProjCategory>categoryNameMap = categoryList.stream().collect(Collectors.toMap(CommonProjCategory::getCommonprojcategoryid, t->t, (v1, v2) -> v2));

        Map<String, CommonProjCategory>categoryMap1 = new HashMap<>();
        Map<String, CommonProjCategory>categoryMap2 = new HashMap<>();
        Map<String, CommonProjCategory>categoryMap3 = new HashMap<>();
        Map<String, CommonProjCategory>categoryMap4 = new HashMap<>();
        categoryList.forEach(item -> {
            Long level = item.getLevel();
            StringBuilder namePath = new StringBuilder(item.getCategoryname());

            if (level.equals(2L) && categoryNameMap.containsKey(item.getCategorycode1())) {
                namePath.append(categoryNameMap.get(item.getCategorycode1()).getCategoryname());
                categoryMap2.put(namePath.toString(), item);
            } else if (level.equals(3L) && categoryNameMap.containsKey(item.getCategorycode1()) && categoryNameMap.containsKey(item.getCategorycode2())) {
                namePath.append(categoryNameMap.get(item.getCategorycode2()).getCategoryname());
                namePath.append(categoryNameMap.get(item.getCategorycode1()).getCategoryname());
                categoryMap3.put(namePath.toString(), item);
            } else if (level.equals(4L) && categoryNameMap.containsKey(item.getCategorycode1()) && categoryNameMap.containsKey(item.getCategorycode2()) && categoryNameMap.containsKey(item.getCategorycode3())) {
                namePath.append(categoryNameMap.get(item.getCategorycode3()).getCategoryname());
                namePath.append(categoryNameMap.get(item.getCategorycode2()).getCategoryname());
                namePath.append(categoryNameMap.get(item.getCategorycode1()).getCategoryname());
                categoryMap4.put(namePath.toString(), item);
            } else if (level.equals(1L)) {
                categoryMap1.put(item.getCategoryname(), item);
            }
        });

        //按级别分组
        Map<Long, List<CommonProjCategory>> excelGroupCommonProjCategory = excelCommonProjCategoryList.stream().collect(Collectors.groupingBy(CommonProjCategory::getLevel));
        if (excelGroupCommonProjCategory.get(1L) != null) {

            excelGroupCommonProjCategory.get(1L).forEach(item -> {
                if (categoryMap1.containsKey(item.getCategoryname())) {
                    item.setCommonprojcategoryid(categoryMap1.get(item.getCategoryname()).getCommonprojcategoryid());
                    item.setIsDeleted(true);
                } else {
                    item.setIsDeleted(false);
                }
            });
            setSaveData(1L, excelGroupCommonProjCategory, groupCommonProjCategory, null, globalId, customerCode, type);
        }
        //处理二级
        Map<String, CommonProjCategory>categoryCodeExcelMap = excelCommonProjCategoryList.stream().collect(Collectors.toMap(CommonProjCategory::getParentId, t->t));
        if (excelGroupCommonProjCategory.get(2L) != null) {
            excelGroupCommonProjCategory.get(2L).forEach(item -> {
                String pcode = item.getParentId().substring(0, item.getParentId().lastIndexOf("."));
                if(!categoryCodeExcelMap.containsKey(pcode)) {
                    item.setIsDeleted(true);
                    categoryCodeExcelMap.remove(item.getParentId());
                    return;
                }
                String name = item.getCategoryname() + categoryCodeExcelMap.get(item.getParentId().split("\\.")[0]).getCategoryname();
                if (categoryMap2.containsKey(name)) {
                    item.setCommonprojcategoryid(categoryMap2.get(name).getCommonprojcategoryid());
                    item.setIsDeleted(true);
                } else {
                    item.setCommonprojcategoryid(categoryCodeExcelMap.get(item.getParentId().split("\\.")[0]).getCommonprojcategoryid());
                    item.setIsDeleted(false);
                }
            });
            setSaveData(2L, excelGroupCommonProjCategory, groupCommonProjCategory, categoryCodeExcelMap, globalId, customerCode, type);
        }

        //处理三级
        if (excelGroupCommonProjCategory.get(3L) != null) {
            excelGroupCommonProjCategory.get(3L).forEach(item -> {
                String[] pCode = item.getParentId().split("\\.");
                String pCode1 = pCode[0];
                String pCode2 = pCode[0] + "." + pCode[1];
                if(!categoryCodeExcelMap.containsKey(pCode2)) {
                    item.setIsDeleted(true);
                    categoryCodeExcelMap.remove(item.getParentId());
                    return;
                }
                String name = item.getCategoryname() + categoryCodeExcelMap.get(pCode2).getCategoryname()  + categoryCodeExcelMap.get(pCode1).getCategoryname();
            if (categoryMap3.containsKey(name)) {
                item.setCommonprojcategoryid(categoryMap3.get(name).getCommonprojcategoryid());
                item.setIsDeleted(true);
            } else {
                item.setCommonprojcategoryid(categoryCodeExcelMap.get(pCode2).getCommonprojcategoryid());
                item.setIsDeleted(false);
            }
            });
            //        临时存查询ord结果
            setSaveData(3L, excelGroupCommonProjCategory, groupCommonProjCategory, categoryCodeExcelMap, globalId, customerCode, type);
        }
        if (excelGroupCommonProjCategory.get(4L) != null) {
            excelGroupCommonProjCategory.get(4L).forEach(item -> {
                String[] pCode = item.getParentId().split("\\.");
                String pCode1 = pCode[0];
                String pCode2 = pCode[0] + "." + pCode[1];
                String pCode3 = pCode[0] + "." + pCode[1] + "." + pCode[2];
                if(!categoryCodeExcelMap.containsKey(pCode3)) {
                    item.setIsDeleted(true);
                    return;
                }
                String name = item.getCategoryname() + categoryCodeExcelMap.get(pCode3).getCategoryname() + categoryCodeExcelMap.get(pCode2).getCategoryname() + categoryCodeExcelMap.get(pCode1).getCategoryname();
            if (categoryMap4.containsKey(name)) {
                item.setCommonprojcategoryid(categoryMap4.get(name).getCommonprojcategoryid());
                item.setIsDeleted(true);
            } else {
                item.setCommonprojcategoryid(categoryCodeExcelMap.get(pCode3).getCommonprojcategoryid());
                item.setIsDeleted(false);
            }
            });
            setSaveData(4L, excelGroupCommonProjCategory, groupCommonProjCategory, categoryCodeExcelMap, globalId, customerCode, type);
        }

        this.repair(excelCommonProjCategoryList, globalId, customerCode, type);
    }

    /**
     * 修复：需要和已删除的工程分类进行比对，如果相同，继续使用旧编码。
     * @param excelCategoryList
     * @param globalId
     * @param customerCode
     * @param type
     */
    private void repair(List<CommonProjCategory> excelCategoryList, String globalId, String customerCode, Integer type) {
        if (CollectionUtils.isEmpty(excelCategoryList)){
            return;
        }

        // 查询用户所有分类（包含删除）
        List<CommonProjCategory> allDbCategoryList = commonProjCategorySelfMapper.selectSelfAll(customerCode, type, Constants.CategoryConstants.WHETHER_TRUE);

        if (CollectionUtils.isEmpty(allDbCategoryList)){
            return;
        }

        // 更新的集合
        List<CommonProjCategory> updateList = new ArrayList<>();
        // 新增的集合
        List<CommonProjCategory> insertList = new ArrayList<>();

        // 分等级处理匹配
        this.levelMatch(allDbCategoryList, excelCategoryList, updateList, insertList);

        // 入库
        CommonProjCategorySelfServiceImpl commonProjCategorySelfServiceImpl = SpringUtil.getBean("commonProjCategorySelfServiceImpl");
        commonProjCategorySelfServiceImpl.dbTransactional(insertList, updateList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void dbTransactional(List<CommonProjCategory> insertList, List<CommonProjCategory> updateList){
        if (CollectionUtils.isNotEmpty(insertList)){
            commonProjCategorySelfMapper.saveCommonProjCategoryList(insertList);
        }
        if (CollectionUtils.isNotEmpty(updateList)){
            commonProjCategorySelfMapper.updateBatch(updateList);
        }
    }

    private void levelMatch(List<CommonProjCategory> allDbCategoryList,
                            List<CommonProjCategory> excelCategoryList,
                            List<CommonProjCategory> updateList,
                            List<CommonProjCategory> insertList) {
        // 全路径名称 如：居住建筑/中高层/高层
        this.setNamePath(excelCategoryList, true);
        this.setNamePath(allDbCategoryList, false);
        Map<String, CommonProjCategory> dbNamePathMap = allDbCategoryList.parallelStream()
                .collect(Collectors.toMap(CommonProjCategory::getNamePath, Function.identity(), (v1, v2) -> v2));

        // 根据等级分组
        Map<Long, List<CommonProjCategory>> excelLevelGroup = excelCategoryList.stream().collect(Collectors.groupingBy(CommonProjCategory::getLevel));
        Map<Long, List<CommonProjCategory>> dbLevelGroup = allDbCategoryList.parallelStream().collect(Collectors.groupingBy(CommonProjCategory::getLevel));
        // excel编码和修正后编码的关系
        Map<String, String> codeRelation = new HashMap<>();

        // 一级分类处理
        if (excelLevelGroup.containsKey(LEVEL_1.longValue())){
            // 1级分类，并按ord字段排序
            List<CommonProjCategory> levelOneExcelCategoryOrdList = excelLevelGroup.get(LEVEL_1.longValue());

            // 库里的所有1级数据
            List<CommonProjCategory> allLevelOneDbCategoryList = dbLevelGroup.get(LEVEL_1.longValue());

            AtomicInteger index = new AtomicInteger(-1);
            for (CommonProjCategory excelCategory : levelOneExcelCategoryOrdList) {
                // 单个匹配
                this.singleMatch(excelCategory, allLevelOneDbCategoryList, index, null, dbNamePathMap, codeRelation, updateList, insertList);
            }
        }

        // 处理2级编码
        if (excelLevelGroup.containsKey(LEVEL_2.longValue())){
            // 本级分类，按父级分类名称分组
            Map<String, List<CommonProjCategory>> excelGroupByParentName = excelLevelGroup.get(LEVEL_2.longValue())
                    .parallelStream().collect(Collectors.groupingBy(CommonProjCategory::getCategoryName1));

            // 库里的所有2级数据
            List<CommonProjCategory> level2List = dbLevelGroup.getOrDefault(LEVEL_2.longValue(), Collections.emptyList());
            Map<String, List<CommonProjCategory>> dbGroupByParentName = level2List.parallelStream()
                    .collect(Collectors.groupingBy(CommonProjCategory::getCategoryName1));

            // 分组匹配
            this.groupMatch(dbNamePathMap, updateList, insertList, codeRelation, excelGroupByParentName, dbGroupByParentName);
        }

        // 处理3级编码
        if (excelLevelGroup.containsKey(LEVEL_3.longValue())){
            // 本级分类，按父级分类名称分组
            Map<String, List<CommonProjCategory>> excelGroupByParentName = excelLevelGroup.get(LEVEL_3.longValue())
                    .parallelStream().collect(Collectors.groupingBy(CommonProjCategory::getCategoryName2));

            // 库里的所有3级数据
            List<CommonProjCategory> level3List = dbLevelGroup.getOrDefault(LEVEL_3.longValue(), Collections.emptyList());
            Map<String, List<CommonProjCategory>> dbGroupByParentName = level3List.parallelStream()
                    .collect(Collectors.groupingBy(CommonProjCategory::getCategoryName2));

            this.groupMatch(dbNamePathMap, updateList, insertList, codeRelation, excelGroupByParentName, dbGroupByParentName);
        }

        // 处理4级编码
        if (excelLevelGroup.containsKey(LEVEL_4.longValue())){
            // 本级分类，按父级分类名称分组
            Map<String, List<CommonProjCategory>> excelGroupByParentName = excelLevelGroup.get(LEVEL_4.longValue())
                    .parallelStream().collect(Collectors.groupingBy(CommonProjCategory::getCategoryName3));

            // 库里的所有4级数据
            List<CommonProjCategory> level4List = dbLevelGroup.getOrDefault(LEVEL_4.longValue(), Collections.emptyList());
            Map<String, List<CommonProjCategory>> dbGroupByParentName = level4List.parallelStream()
                    .collect(Collectors.groupingBy(CommonProjCategory::getCategoryName3));

            this.groupMatch(dbNamePathMap, updateList, insertList, codeRelation, excelGroupByParentName, dbGroupByParentName);
        }
    }

    private void groupMatch(Map<String, CommonProjCategory> dbNamePathMap,
                            List<CommonProjCategory> updateList,
                            List<CommonProjCategory> insertList,
                            Map<String, String> codeRelation,
                            Map<String, List<CommonProjCategory>> excelGroupByParentName,
                            Map<String, List<CommonProjCategory>> dbGroupByParentName) {
        for (Map.Entry<String, List<CommonProjCategory>> entry : excelGroupByParentName.entrySet()) {
            String parentName = entry.getKey();
            List<CommonProjCategory> excelBrotherCategory = entry.getValue();

            if (!codeRelation.containsKey(parentName)){
                continue;
            }

            List<CommonProjCategory> dbBrotherCategory = Optional.ofNullable(dbGroupByParentName)
                    .map(map -> map.getOrDefault(parentName, Collections.emptyList()))
                    .orElse(Collections.emptyList());

            AtomicInteger index = new AtomicInteger(-1);
            for (CommonProjCategory excelCategory : excelBrotherCategory) {
                this.singleMatch(excelCategory, dbBrotherCategory, index, codeRelation.get(parentName), dbNamePathMap, codeRelation, updateList, insertList);
            }
        }
    }

    private void singleMatch(CommonProjCategory excelCategory,
                             List<CommonProjCategory> dbBrotherCategory,
                             AtomicInteger index,
                             String parentCode,
                             Map<String, CommonProjCategory> dbNamePathMap,
                             Map<String, String> codeRelation,
                             List<CommonProjCategory> updateList,
                             List<CommonProjCategory> insertList){

        // 和库里的能匹配到的，使用原先的编码，如果原先的是已删除状态，需要更新它
        if (dbNamePathMap.containsKey(excelCategory.getNamePath())){
            CommonProjCategory dbCategory = dbNamePathMap.get(excelCategory.getNamePath());

            if (dbCategory.getIsDeleted()){
                // 已删除，使用原先的编码
                excelCategory.setId(dbCategory.getId());
                excelCategory.setIsUsing(Constants.CategoryConstants.WHETHER_TRUE);
                excelCategory.setIsDeleted(false);
                updateList.add(excelCategory);
            }
            codeRelation.put(excelCategory.getNamePath(), dbCategory.getCommonprojcategoryid());
        }else {
            // 和库里的匹配不到的，重新生成编码
            String newCode = StringUtils.isEmpty(parentCode) ? this.getEffectiveNewCode(dbBrotherCategory, index.addAndGet(1)) : parentCode + this.getEffectiveNewCode(dbBrotherCategory, index.addAndGet(1));

            excelCategory.setCommonprojcategoryid(newCode);
            codeRelation.put(excelCategory.getNamePath(), newCode);

            if (StringUtils.isNotEmpty(excelCategory.getCategoryName1())) {
                excelCategory.setCategorycode1(codeRelation.get(excelCategory.getCategoryName1()));
            }
            if (StringUtils.isNotEmpty(excelCategory.getCategoryName2())) {
                excelCategory.setCategorycode2(codeRelation.get(excelCategory.getCategoryName2()));
            }
            if (StringUtils.isNotEmpty(excelCategory.getCategoryName3())) {
                excelCategory.setCategorycode3(codeRelation.get(excelCategory.getCategoryName3()));
            }
            if (StringUtils.isNotEmpty(excelCategory.getCategoryName4())) {
                excelCategory.setCategorycode4(codeRelation.get(excelCategory.getCategoryName4()));
            }

            insertList.add(excelCategory);
        }
    }

    public void setNamePath(List<CommonProjCategory> categoryList, boolean isClearCode){
        String sep = "/";

        // 获取nameMap   key => 居住建筑/中高层/高层
        Map<String, String> levelAndNameMap = categoryList.parallelStream().collect(Collectors.toMap(CommonProjCategory::getCommonprojcategoryid, CommonProjCategory::getCategoryname, (x, y) -> x));

        for (CommonProjCategory category : categoryList) {
            int level = category.getCommonprojcategoryid().length();
            StringJoiner joiner = new StringJoiner(sep);

            for (int i = 1; i <= level/3; i++) {
                String currLevelCode = category.getCommonprojcategoryid().substring(0, i * 3);
                String name = levelAndNameMap.get(currLevelCode);
                joiner.add(name);
                switch (i){
                    case 1:
                        category.setCategoryName1(joiner.toString());
                        break;
                    case 2:
                        category.setCategoryName2(joiner.toString());
                        break;
                    case 3:
                        category.setCategoryName3(joiner.toString());
                        break;
                    case 4:
                        category.setCategoryName4(joiner.toString());
                        break;
                    default:
                }
            }
            category.setNamePath(joiner.toString());

            if (isClearCode){
                category.setCommonprojcategoryid(null);
                category.setCategorycode1(null);
                category.setCategorycode2(null);
                category.setCategorycode3(null);
                category.setCategorycode4(null);
            }
        }
    }

    private void setSaveData(Long level, Map<Long, List<CommonProjCategory>> excelGroupCommonProjCategory,
                             Map<Long, List<CommonProjCategory>>groupCommonProjCategory,
                             Map<String, CommonProjCategory>categoryCodeExcelMap,
                             String globalId, String customerCode, Integer type) {
        Map<String, List<CommonProjCategory>> tempList = new HashMap<>();
        Map<String, Integer> tempInt = new HashMap<>();
        AtomicInteger indexOrd = new AtomicInteger(1);
        AtomicInteger codeOrd = new AtomicInteger(-1);
        excelGroupCommonProjCategory.get(level).forEach(item->{
            if (Boolean.TRUE.equals(item.getIsDeleted())) {
                return ;
            }
            item.setCategoryTypeCode(Constants.CategoryConstants.DEFAULT_CATEGORY_TYPE_CODE);
            item.setCategoryTypeName(Constants.CategoryConstants.DEFAULT_CATEGORY_TYPE_NAME);
            item.setGlobalId(globalId);
            item.setQyCode(customerCode);
            item.setType(type);
            if (level.equals(1L)) {
                String newCode = getEffectiveNewCode(groupCommonProjCategory.get(1L), 0);
                item.setCategorycode1(newCode);
                item.setCommonprojcategoryid(newCode);
                int maxOrd = getMaxOrd(groupCommonProjCategory.get(1L));
                item.setOrd(maxOrd + 1);
                groupCommonProjCategory.computeIfAbsent(1L, k -> new ArrayList<CommonProjCategory>());
                groupCommonProjCategory.get(1L).add(item);
            } else {
                List<CommonProjCategory> listDirectChildrenByCode = new ArrayList<>();
                String pCode = item.getCommonprojcategoryid().substring(0, (level.intValue() - 1) * 3);
                if (tempList.containsKey(pCode)) {
                    listDirectChildrenByCode = tempList.get(pCode);
                } else {
                    listDirectChildrenByCode = commonProjCategorySelfMapper.listDirectChildrenByCode(pCode, customerCode, type);
                    tempList.put(pCode, listDirectChildrenByCode);
                }
                int maxOrd = 0;
                if (tempInt.containsKey(pCode)) {
                    maxOrd = tempInt.get(pCode) + 1;
                } else {
                    maxOrd = getMaxOrd(listDirectChildrenByCode) + 1;
                    tempInt.put(pCode, maxOrd);
                }
                item.setOrd(maxOrd);
                String newCode = pCode + getEffectiveNewCode(tempList.get(pCode), 0);
                tempList.get(pCode).add(item);
                if (level.equals(2L)) {
                    item.setCategorycode1(pCode);
                    item.setCategorycode2(newCode);
                    item.setCommonprojcategoryid(newCode);
                    categoryCodeExcelMap.get(item.getParentId()).setCommonprojcategoryid(newCode);
                } else if (level.equals(3L)) {
                    item.setCategorycode1(pCode.substring(0, 3));
                    item.setCategorycode2(pCode);
                    item.setCategorycode3(newCode);
                    item.setCommonprojcategoryid(newCode);
                    categoryCodeExcelMap.get(item.getParentId()).setCommonprojcategoryid(newCode);
                } else if (level.equals(4L)) {
                    item.setCategorycode1(pCode.substring(0, 3));
                    item.setCategorycode2(pCode.substring(0, 6));
                    item.setCategorycode3(pCode);
                    item.setCategorycode4(newCode);
                    item.setCommonprojcategoryid(newCode);
                }
            }
        });
    }
    /**
     * 获取选中的分类ord
     */
    private Integer gerBaseOrd(String baseCommonprojcategoryid, List<CommonProjCategory> commonProjCategories){
        if (CollectionUtils.isEmpty(commonProjCategories)) {
            return 0;
        }
        CommonProjCategory baseCommonprojcategory = commonProjCategories.stream().filter(
                x -> x.getCommonprojcategoryid().equals(baseCommonprojcategoryid)).findFirst().orElse(null);
        if (Objects.isNull(baseCommonprojcategory)){
            throw new BusinessException("选中的工程分类不能为空");
        }
        return Optional.ofNullable(baseCommonprojcategory.getOrd()).orElse(0);
    }

    /**
     * 获取选中的分类id
     */
    private Integer gerBaseId(String baseCommonprojcategoryid, List<CommonProjCategory> commonProjCategories){
        if (CollectionUtils.isEmpty(commonProjCategories)) {
            return 0;
        }
        CommonProjCategory baseCommonprojcategory = commonProjCategories.stream().filter(
                x -> x.getCommonprojcategoryid().equals(baseCommonprojcategoryid)).findFirst().orElse(null);
        if (Objects.isNull(baseCommonprojcategory)){
            throw new BusinessException("选中的工程分类不能为空");
        }
        return baseCommonprojcategory.getId();
    }

    /**
     * @description: 根据所有兄弟，获取新插入分类的commonprojcategoryid
     * @param commonProjCategories
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/10/18 19:13
     */
    private String getNewCode(List<CommonProjCategory> commonProjCategories, int index){
        String code = Constants.CategoryConstants.DEFAULT_CODE;
        if (CollectionUtils.isEmpty(commonProjCategories)) {
            return String.format("%03d", index + 1);
        }

        int max = commonProjCategories.parallelStream().map(CommonProjCategory::getCommonprojcategoryid).filter(Objects::nonNull).map(s -> {
            Matcher matcher = pattern.matcher(s);

            if (matcher.find()) {
                return Integer.parseInt(matcher.group());
            }
            return -1;
        }).max(Comparator.comparingInt(o -> o)).orElse(-1);
        if (max > 0) {
            code = String.format("%03d", max + 1 + index);
        }
        return code;
    }

    private String getEffectiveNewCode(List<CommonProjCategory> commonProjCategories, int index) {
        String newCode = getNewCode(commonProjCategories, index);

        if (newCode.length() % 3 != 0 || newCode.length() > 12) {
            throw new BusinessException("编码长度不符合要求：【" + newCode + "】");
        }

        return newCode;
    }

    /**
     * @description: 通过父id查询所有兄弟节点
     * @param parentId
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/10/18 19:13
     */
    private List<CommonProjCategory> getCommonProjCategories(String parentId, String customerCode, Integer type){
        CommonProjCategory parent = commonProjCategorySelfMapper.getByCategoryCode(parentId, customerCode, type);
        if (Objects.isNull(parent)) {
            throw new BusinessException("父级分类不存在，请确认");
        }
        // 新增分类的所有兄弟节点
        return commonProjCategorySelfMapper.listDirectChildrenByCode(parentId, customerCode, type);
    }

    /**
     * @description: 工程分类-同父级重名校验
     * @param categoryCode  选中的categoryCode
     * @param type  类型： 0 新增分类，1 新增子分类，2 修改
     * @param categoryName  名称
     * @return com.glodon.zbw.dataManager.common.R<java.lang.String>
     * <AUTHOR>
     * @date 2021/10/19 10:12
     */
    @Override
    public int nameRepeatCheck(String categoryCode, Integer addType, String categoryName, String customerCode, String globalId, Integer type) {
        String parentCategoryCode = "";
        String divideSelf = null;
        int level;
        if (StringUtils.isEmpty(categoryCode)){
            level = LEVEL_1;
        } else {
            level = categoryCode.length() / SINGLE_LEVEL_LEN;
        }
        // 类型： 0 新增分类，1新增子分类，2修改
        if(ADD_TYPE_THIS.equals(addType)){
            if (level != LEVEL_1){
                parentCategoryCode = categoryCode.substring(0, categoryCode.length() - SINGLE_LEVEL_LEN);
            }
        }else if (ADD_TYPE_CHILD.equals(addType)){
            level = level + 1;
            parentCategoryCode = categoryCode;
        }else {
            parentCategoryCode = categoryCode.substring(0, categoryCode.length() - SINGLE_LEVEL_LEN);
            divideSelf = categoryCode;
        }

        // 企业id、分类名称、this等级、父节点id
        return commonProjCategorySelfMapper.queryNameCount(customerCode, categoryName, level, parentCategoryCode, divideSelf, type);
    }

    /**
     * @description: 获取某级最大ord
     * @param commonProjCategories
     * @return void
     * <AUTHOR>
     * @date 2021/10/19 11:32
     */
    private int getMaxOrd(List<CommonProjCategory> commonProjCategories){
        if (CollectionUtils.isEmpty(commonProjCategories)) {
            return 0;
        }
        return commonProjCategories.parallelStream().map(CommonProjCategory::getOrd).filter(Objects::nonNull).max(Comparator.comparingInt(o -> o)).orElse(0);
    }

    /**
     * @description: 后续ord+1
     * @param baseOrd
     * @param commonProjCategories
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/10/18 19:13
     */
    private List<CommonProjCategory> otherOrdChange(Integer baseOrd, List<CommonProjCategory> commonProjCategories){
        if (CollectionUtils.isEmpty(commonProjCategories)) {
            return null;
        }
        List<CommonProjCategory> collect = commonProjCategories.stream().filter(x -> !x.getIsDeleted())
                .filter(x -> x.getOrd() > baseOrd).collect(Collectors.toList());
        collect.forEach(x -> x.setOrd(x.getOrd() + 1));
        return collect;
    }

    @Override
    public CommonProjCategory downOrUp(String categoryCode, String operate, String customerCode, Integer type) {

        CommonProjCategory getByCategoryCode = commonProjCategorySelfMapper.getByCategoryCode(categoryCode, customerCode, type);

        if (getByCategoryCode == null){
            throw new BusinessException("操作的记录不存在，请刷新重试");
        }

        List<CommonProjCategory> brotherList;
        if (getByCategoryCode.getLevel().compareTo(LEVEL_1.longValue()) == 0){
            // 所有一级节点
            brotherList = commonProjCategorySelfMapper.queryTopCategoryList(customerCode, type);
        }else {
            // 所有兄弟节点
            String parentCategoryCode = categoryCode.substring(0, categoryCode.length() - Constants.CategoryConstants.SINGLE_LEVEL_LEN);
            brotherList = getCommonProjCategories(parentCategoryCode, customerCode, type);
        }
        brotherList = brotherList.stream().filter(x -> x.getIsDeleted() == null || !x.getIsDeleted()).collect(Collectors.toList());

        String moveType = Constants.CategoryConstants.OPERATE_UP.equals(operate) ? Constants.MOVE_TYPE_UP : Constants.MOVE_TYPE_DOWN;
        ElementMover.move(brotherList, getByCategoryCode.getUniqueIdentity(), moveType, commonProjCategorySelfMapper);

        return commonProjCategorySelfMapper.getByCategoryCode(categoryCode, customerCode, type);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCategory(String categoryCode, String customerCode, Integer type) {
        Map<String, List<String>> param = new HashMap<>();
        for (String s : categoryCode.split(",")) {
            int level = s.length() / Constants.CategoryConstants.SINGLE_LEVEL_LEN;
            param.computeIfAbsent("level" + level, k -> new ArrayList<>());
            param.get("level" + level).add(s);
        }
        commonProjCategorySelfMapper.deleteChildCategory(customerCode, type, param.get("level1"), param.get("level2"), param.get("level3"), param.get("level4"));
        if (param.containsKey("level1") && CollUtil.isNotEmpty(param.get("level1"))) {
            categoryTagEnumRelSelfMapper.deleteByCategoryCode(customerCode, param.get("level1"));
        }
    }

    @Override
    public ResponseVo updateCategory(List<CommonProjCategoryUpdateVo> updateVoList, String customerCode, String globalId, Integer type) {
        if (updateVoList.get(0).getOperate() == Constants.CategoryConstants.UPDATE_ENABLE) {
            List<String> ids = new ArrayList<>();
            updateVoList.forEach(item-> {
                Integer isEnable = item.getIsUsing();
                if (Objects.isNull(isEnable)){
                    return;
                }
                ids.add(item.getId().toString());
            });
            commonProjCategorySelfMapper.updateByPrimaryKeys(ids, globalId, updateVoList.get(0).getIsUsing());
            return ResponseVo.success(this.commonProjCategorySelfMapper.getSelfCategoryByIds(updateVoList));
        } else {
            CommonProjCategoryUpdateVo updateVo = updateVoList.get(0);
            CommonProjCategory category = new CommonProjCategory();
            category.setId(updateVo.getId());
            // 更新操作类型
            Integer operate = updateVo.getOperate();
            switch (operate){
                case Constants.CategoryConstants.UPDATE_NAME:
                    String categoryName = updateVo.getCategoryName();
                    String categoryCode = updateVo.getCommonprojcategoryid();
                    // 重名校验
                    if (StringUtils.isEmpty(categoryName)){
                        return ResponseVo.error("工程分类名称不能为空！");
                    }
                    if(categoryName.length() > Constants.MAX_NAME_LENGTH){
                        return ResponseVo.error("工程分类名称不能超过30字！");
                    }
                    int count = nameRepeatCheck(categoryCode, Constants.CategoryConstants.ADD_TYPE_UPDATE, categoryName, customerCode, globalId, type);
                    if (count > 0){
                        return ResponseVo.error("工程分类在当前父级分类下名称已存在！");
                    }
                    // 与已删除的分类名称一致
                    int level = categoryCode.length() / Constants.CategoryConstants.SINGLE_LEVEL_LEN;
                    String parentId = categoryCode.substring(0, categoryCode.length()-Constants.CategoryConstants.SINGLE_LEVEL_LEN);
                    CommonProjCategory oldCommonProjCategory = commonProjCategorySelfMapper.queryRepeatName(customerCode, categoryName, level, parentId, type);
                    if (Objects.isNull(oldCommonProjCategory)){
                        category.setCategoryname(categoryName);
                    }else {
                        // 针对删除后的信息-编辑现有项与删除项同名时
                        // 隐藏现有项（逻辑删除）
                        CommonProjCategory categoryByPrimaryKey = commonProjCategorySelfMapper.getSelfCategoryByPrimaryKey(updateVo.getId());
                        category.setIsDeleted(true);
                        category.setUpdateGlobalId(globalId);
                        commonProjCategorySelfMapper.updateByPrimaryKey(category);
                        // 显示删除项
                        oldCommonProjCategory.setIsUsing(categoryByPrimaryKey.getIsUsing());
                        oldCommonProjCategory.setIsDeleted(false);
                        oldCommonProjCategory.setOrd(categoryByPrimaryKey.getOrd());
                        oldCommonProjCategory.setUpdateGlobalId(globalId);
                        oldCommonProjCategory.setCategoryTypeName(categoryName);
                        commonProjCategorySelfMapper.updateByPrimaryKey(oldCommonProjCategory);
                        //重新绑定标签的关系
                        if (Objects.equals(categoryByPrimaryKey.getLevel(), Constants.CategoryConstants.LEVEL_1.longValue())) {
                            List<CategoryTagEnumRel> categoryTagEnumRels = categoryTagEnumRelSelfMapper.selectByEnterpriseId(customerCode);
                            categoryTagEnumRels = categoryTagEnumRels.stream()
                                    .filter(x -> categoryByPrimaryKey.getCommonprojcategoryid().equals(x.getCategoryCode())).collect(Collectors.toList());
                            if (CollUtil.isNotEmpty(categoryTagEnumRels)) {
                                List<Long> ids = categoryTagEnumRels.stream().map(CategoryTagEnumRel::getId).collect(Collectors.toList());
                                categoryTagEnumRelSelfMapper.updateById(oldCommonProjCategory.getCommonprojcategoryid(), ids);
                            }
                        }
                        return ResponseVo.success(oldCommonProjCategory);
                    }
                    break;
                case Constants.CategoryConstants.UPDATE_REMARK:
                    String remark = updateVo.getRemark();
                    if(StringUtils.isNotEmpty(remark) && remark.length() > Constants.MAX_REMARK_LENGTH){
                        return ResponseVo.error("备注最多允许输入200个字！");
                    }
                    category.setRemark(remark);
                    break;
                default:
                    return ResponseVo.error("工程分类修改操作非法！");
            }
            category.setUpdateGlobalId(globalId);
            commonProjCategorySelfMapper.updateByPrimaryKey(category);
            return ResponseVo.success(this.commonProjCategorySelfMapper.getSelfCategoryByPrimaryKey(updateVo.getId()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @BusinessCache(customerCode = "${customerCode}", isInvalidateCache = true)
    public void publish(String customerCode, String globalId, Integer type) {
        List<CommonProjCategory> commonProjCategories = commonProjCategorySelfMapper.selectSelfAll(customerCode, type, Constants.CategoryConstants.WHETHER_TRUE);
        // 校验是否有其他账号更新发布
        if (CollUtil.isEmpty(commonProjCategories)) {
            return;
        }
        List<CommonProjCategory> insertProjCategories = new ArrayList<>();
        for (CommonProjCategory commonProjCategory : commonProjCategories) {
            Integer originId = commonProjCategory.getOriginId();
            commonProjCategory.setId(originId);
            if (originId == null) {
                commonProjCategory.setId(IDGenerator.getNextIntId(TableNameConstants.TB_COMMONPROJCATEGORY_STANDARDS));
                if (Boolean.FALSE.equals(commonProjCategory.getIsDeleted())){
                    insertProjCategories.add(commonProjCategory);
                }
            } else {
                commonProjCategoryMapper.updatePublish(commonProjCategory);
            }
        }
        // 插入新增数据
        if(!CollectionUtils.isEmpty(insertProjCategories)) {
            commonProjCategoryMapper.saveBatchCommonProjCategory(insertProjCategories);
        }
        commonProjCategorySelfMapper.deleteSelfByCustomerCode(customerCode, type);

        categoryTagPublish(customerCode);

        publishInfoServiceImpl.updateVersion(customerCode, globalId, OperateConstants.CATEGORY);
    }

    @Override
    public List<CommonProjCategory> getSelfCategoryListTree(String customerCode, Integer type) throws BusinessException {
        // 有暂存数据
        List<CommonProjCategory> commonProjCategories = commonProjCategorySelfMapper.selectSelfAll(customerCode, type, Constants.CategoryConstants.WHETHER_FALSE);
        if(CollectionUtils.isEmpty(commonProjCategories)) {
            initCategorySelfService.initData(customerCode, type);
            commonProjCategories = commonProjCategorySelfMapper.selectSelfAll(customerCode, type, Constants.CategoryConstants.WHETHER_FALSE);
        }
        commonProjCategories.forEach(x -> x.setDataType(Constants.CategoryTagConstants.DATA_TYPE_CATEGORY));
        return commonProjCategories;
    }

    public void categoryTagPublish(String customerCode) {
        List<CategoryTag> categoryTags = categoryTagSelfMapper.selectByEnterpriseId(customerCode);
        List<CategoryTagEnum> categoryTagEnums = categoryTagEnumSelfMapper.selectByTagId(customerCode, null);
        List<CategoryTagEnumRel> categoryTagEnumRelList = categoryTagEnumRelSelfMapper.selectByEnterpriseId(customerCode);

        Map<Long, Long> tagIdMap = new HashMap<>();
        Map<Long, Long> tagEnumIdMap = new HashMap<>();

        categoryTags.forEach(x -> {
            Long tagId = SnowflakeIdUtils.getNextId();
            tagIdMap.put(x.getId(), tagId);
            x.setId(tagId);
            x.setCreateTime(new Date());
        });

        categoryTagEnums.forEach(x -> {
            Long tagEnumId = SnowflakeIdUtils.getNextId();
            tagEnumIdMap.put(x.getId(), tagEnumId);
            x.setId(tagEnumId);
            x.setCreateTime(new Date());
            x.setTagId(tagIdMap.get(x.getTagId()));
        });

        categoryTagEnumRelList.forEach(x -> {
            x.setId(SnowflakeIdUtils.getNextId());
            x.setCreateTime(new Date());
            x.setTagId(tagIdMap.get(x.getTagId()));
            x.setTagEnumId(tagEnumIdMap.get(x.getTagEnumId()));
        });

        categoryTagMapper.deleteByEnterpriseId(customerCode);
        categoryTagEnumMapper.deleteByTagId(customerCode, null);
        categoryTagEnumRelMapper.deleteByEnterpriseId(customerCode);
        if (CollectionUtils.isNotEmpty(categoryTags)) {
            categoryTagMapper.saveBatch(categoryTags);
        }
        if (CollectionUtils.isNotEmpty(categoryTagEnums)) {
            categoryTagEnumMapper.saveBatch(categoryTagEnums);
        }
        if (CollectionUtils.isNotEmpty(categoryTagEnumRelList)) {
            categoryTagEnumRelMapper.saveBatch(categoryTagEnumRelList);
        }
    }

}
