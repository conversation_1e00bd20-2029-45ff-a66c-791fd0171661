package com.glodon.qydata.service.standard.feature;

import com.glodon.qydata.dto.BatchAddExpressionDto;
import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.entity.standard.feature.ProjectFeature;
import com.glodon.qydata.entity.standard.feature.ProjectFeatureCategoryView;
import com.glodon.qydata.vo.standard.feature.ProjectFeatureCategoryViewVO;
import com.glodon.qydata.vo.standard.feature.ProjectFeatureDsVO;
import com.glodon.qydata.vo.standard.feature.ProjectFeatureFilterVO;
import com.glodon.qydata.vo.standard.feature.ProjectFeatureResultVO;
import com.glodon.qydata.vo.standard.trade.StandardTradeVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 工程特征相关接口
 * <AUTHOR>
 * @date 2021/10/21 20:15
 */
public interface IProjectFeatureService {

    /**
     * 根据项目特征id查询项目特征信息
     * @param featureId
     * @param viewType
     * @param categoryCode
     * @return com.gcj.zblib.standardData.feature.entity.ProjectFeature
     * <AUTHOR>
     * @date 2021/10/21 20:16
     */
    ProjectFeatureResultVO getCompleteFeatureByPrimaryKey(Long featureId, Integer viewType, String categoryCode, List<CommonProjCategory> categoryListTree);

    /**
    　　* 根据专业ID物理删除专业下携带的工程特征,工程特征的分类视图(企业+个人)
     　　* @param  tradeId 专业id
    　　* @return
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/10/25 10:36
    　　*/
    void deleteByTradeId(Long tradeId);

    /**
     * 查询工程特征列表（专业视图）
     * @param filterVO
     * @return PageInfo<ProjectFeatureVO>
     * <AUTHOR>
     * @date 2021/10/25 20:11
     */
    List<ProjectFeatureResultVO> featureTradeView(String customerCode, ProjectFeatureFilterVO filterVO);

    /**
     * 查询工程特征列表（分类视图）
     * @param filterVO
     * @return java.util.List<com.gcj.zblib.standardData.feature.vo.ProjectFeatureResultVO>
     * <AUTHOR>
     * @date 2021/11/3 15:03
     */
    List<ProjectFeatureCategoryViewVO> featureCategoryView(String customerCode, ProjectFeatureFilterVO filterVO);

    /**
     * 给企业 创建特征+口径 系统内置的副本
     * @param customerCode
     * @return void
     * <AUTHOR>
     * @date 2021/11/12 8:40
     */
    void initFeatureData(String customerCode, Integer type);

    /**
     * @description: 获取工程特征内置数据
     * @param
     * @return java.util.List<com.gcj.zblib.standardData.feature.entity.ProjectFeature>
     * <AUTHOR>
     * @date 2021/11/19 9:47
     */
    List<ProjectFeatureDsVO> getSystemFeature();

    /**
     * @description: 工程特征数据同步
     * @param featureDsVOList
     * @return void
     * <AUTHOR>
     * @date 2021/11/19 11:50
     */
    void featureHistoryDs(List<ProjectFeatureDsVO> featureDsVOList, String customerCode);

    /**
     * @description: 根据工程分类获取-特征的工程专业列表
     * @param categoryCode
     * @param needTag: 是否需要标签
     * @return com.glodon.qydata.vo.standard.trade.StandardTradeVO
     * <AUTHOR>
     * @date 2022/1/17 19:27
     */
    List<StandardTradeVO> getTradeByCategoryFeature(String categoryCode);

    /**
     * @description: 根据一级工程分类 和 工程专业 获取工程特征
     * @return java.util.List<com.glodon.qydata.vo.standard.feature.ProjectFeatureResultVO>
     * <AUTHOR>
     * @date 2022/1/17 19:57
     */
    List<ProjectFeatureResultVO> getFeatureByCategoryAndTrade(String categoryCode, Long tradeId);

    /**
     * @description: 通过Excel导入内置工程特征数据
     * @param file
     * @return void
     * <AUTHOR>
     * @date 2022/2/28 10:09
     */
    void importTz(MultipartFile file);

    /**
     　　* 根据系统内置专业编码，查询所有专业对应的内置工程特征，并复制到最新专业id下
     　　* @param  tradeIdSys 引用的专业id; tradeId 专业id, tradeCode 引用的专业编码
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/25 16:35
     　　*/
    void copyFeatureToNewTrade(String customerCode, Long globalId, Long tradeIdSys, Long tradeId, String tradeCode, String tradeName);

    /**
     * 专业视图数据正常，分类视图数据为空时，同步分类视图数据
     * @throws
     * @param customerCodeTypeMap 企业编码和工程分类类型的映射
     * <AUTHOR>
     * @return
     * @date 2022/4/14 14:30
     */
    void syncCategoryViewFeature(Map<String, Integer> customerCodeTypeMap);
    
    /**
     * 通过专业id获取工程特征
     * @param customerCode
     * @param tradeIds  专业idList
     * @return
     */
    Map<String, List<ProjectFeatureResultVO>> filterFeatureByTrades(String customerCode, List<Long> tradeIds);

    /**
     * @Description: 通过专业id获取工程特征
     * @param customerCode
     * @param tradeIds
     * @return java.util.Map<java.lang.String,java.util.List<com.glodon.qydata.vo.standard.feature.ProjectFeatureResultVO>>
     * @Author: zhangj-cl
     * @Date: 2022/12/7 17:13
     */
    Map<String, List<ProjectFeatureResultVO>> getByTradesV2(String customerCode, List<Long> tradeIds, Integer isNeedCategoryInfo);

    /**
     * 根据企业编码和类型，查询ProjectType
     * @param customerCode
     * @param type
     * @return
     */
    String categoryAllCode(String customerCode, int type, List<String> topCategoryNameList);

    /**
     * projectType 转成一级的工程分类Code
     * @param projectType
     * @return
     * <AUTHOR>
     */
    List<String> projectTypeConvertToFirstCategoryCode(String projectType);

    List<ProjectFeatureCategoryView> initCategoryView(ProjectFeature projectFeature, Map<String, List<ProjectFeatureCategoryView>> groupMap);

    void tempRefreshOrd(String customerCode);
}
