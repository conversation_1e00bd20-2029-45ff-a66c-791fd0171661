package com.glodon.qydata.service.deleteself;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.constant.OperateConstants;
import com.glodon.qydata.entity.deleteself.SelfEntity;
import com.glodon.qydata.mapper.deleteself.DeleteSelfMapper;
import com.glodon.qydata.util.PublishLockerUtil;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

/**
 * @author: luoml-b
 * @date: 2024/4/23 10:45
 * @description: 删除暂存数据
 */
@Service
@Slf4j
public class DeleteSelfService {

    @Autowired
    @Qualifier("delSelfExecutor")
    private ThreadPoolTaskExecutor delSelfExecutor;

    @Autowired
    private DeleteSelfMapper deleteSelfMapper;

    @Autowired
    private PublishLockerUtil publishLockerUtil;

    private final Map<String, DelSelfOperation> delTypeMap = new HashMap<>();

    @PostConstruct
    public void init() {
        delTypeMap.put("category", this::delCategory);
        delTypeMap.put("project", this::delProject);
        delTypeMap.put("contract", this::delContract);
        delTypeMap.put("feature", this::delFeature);
        delTypeMap.put("buildStandard", this::delBuildStandard);
        delTypeMap.put("mainQuantity", this::delMainQuantity);
    }

    public void singleExecute(String type, String customerCode) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        delTypeMap.get(type).operate(customerCode);
        stopWatch.stop();
        long elapsedTimeInMillis = stopWatch.getTotalTimeMillis();
        String costStr = String.format("single模式删除%s的self数据完成， 耗时：%d小时 %d分钟 %d秒",
                type, elapsedTimeInMillis / 1000 / 60 / 60, elapsedTimeInMillis / 1000 / 60 % 60, elapsedTimeInMillis / 1000 % 60);
        log.info(costStr);
    }

    public void batchExecute(String customerCode) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();

        CountDownLatch latch = new CountDownLatch(delTypeMap.size());
        delTypeMap.values().forEach(method -> delSelfExecutor.execute(() -> {
            method.operate(customerCode);
            latch.countDown();
        }));

        try {
            latch.await();
            log.info("All methods executed successfully");
        } catch (InterruptedException e) {
            log.error("Failed to execute all methods", e);
        }

        stopWatch.stop();
        long elapsedTimeInMillis = stopWatch.getTotalTimeMillis();
        String costStr = String.format("batch模式删除self数据完成， 耗时：%d小时 %d分钟 %d秒",
                elapsedTimeInMillis / 1000 / 60 / 60, elapsedTimeInMillis / 1000 / 60 % 60, elapsedTimeInMillis / 1000 % 60);
        log.info(costStr);
    }


    public void delCategory(String customerCode) {
        List<SelfEntity> selfEntityList = deleteSelfMapper.selectCategoryAll(customerCode);
        List<String> globalIds = getSelfGlobalIds(selfEntityList, OperateConstants.CATEGORY);
        log.info("delete category globalId size:{}", globalIds.size());
        if (CollUtil.isEmpty(globalIds)) {
            return;
        }
        deleteSelfMapper.deleteCategory(globalIds);
    }


    public void delProject(String customerCode) {
        List<SelfEntity> selfEntityList =
                deleteSelfMapper.selectProjectOrContractAll(Constants.StandardsProjectOrContractInfoConstants.PROJECT_INFO, customerCode);
        List<String> globalIds = getSelfGlobalIds(selfEntityList, OperateConstants.PROJECT);
        log.info("delete project globalId size:{}", globalIds.size());
        if (CollUtil.isEmpty(globalIds)) {
            return;
        }
        deleteSelfMapper.deleteProjectOrContract(globalIds, Constants.StandardsProjectOrContractInfoConstants.PROJECT_INFO);
    }

    public void delContract(String customerCode) {
        List<SelfEntity> selfEntityList =
                deleteSelfMapper.selectProjectOrContractAll(Constants.StandardsProjectOrContractInfoConstants.CONTRACT_INFO, customerCode);
        List<String> globalIds = getSelfGlobalIds(selfEntityList, OperateConstants.CONTRACT);
        log.info("delete contract globalId size:{}", globalIds.size());
        if (CollUtil.isEmpty(globalIds)) {
            return;
        }
        deleteSelfMapper.deleteProjectOrContract(globalIds, Constants.StandardsProjectOrContractInfoConstants.CONTRACT_INFO);
    }

    public void delFeature(String customerCode) {
        List<SelfEntity> selfEntityList = deleteSelfMapper.selectFeatureAll(customerCode);
        List<String> globalIds = getSelfGlobalIds(selfEntityList, OperateConstants.FEATURE);
        log.info("delete feature globalId size:{}", globalIds.size());
        if (CollUtil.isEmpty(globalIds)) {
            return;
        }
        deleteSelfMapper.deleteFeature(globalIds);
        deleteSelfMapper.deleteFeatureView(globalIds);
    }

    public void delBuildStandard(String customerCode) {
        List<SelfEntity> selfEntityList = deleteSelfMapper.selectBuildStandardAll(customerCode);
        List<String> globalIds = getSelfGlobalIds(selfEntityList, OperateConstants.BUILDING);
        log.info("delete buildStandard globalId size:{}", globalIds.size());
        if (CollUtil.isEmpty(globalIds)) {
            return;
        }
        List<Long> ids = deleteSelfMapper.selectBuildStandardId(globalIds);
        deleteSelfMapper.deleteBuildStandardDetailDesc(ids);
        deleteSelfMapper.deleteBuildStandardDetail(ids);
        deleteSelfMapper.deleteBuildStandardCategory(ids);
        deleteSelfMapper.deleteBuildStandardPositionDetail(ids);
        deleteSelfMapper.deleteBuildStandardPosition(ids);
        deleteSelfMapper.deleteBuildStandard(globalIds);
    }

    public void delMainQuantity(String customerCode) {
        List<SelfEntity> selfEntityList = deleteSelfMapper.selectMainQuantityAll(customerCode);
        List<String> globalIds = getSelfGlobalIds(selfEntityList, OperateConstants.MAIN_QUANTITY);
        log.info("delete mainQuantity globalId size:{}", globalIds.size());
        if (CollUtil.isEmpty(globalIds)) {
            return;
        }
        deleteSelfMapper.deleteMainQuantity(globalIds);
    }


    private List<String> getSelfGlobalIds(List<SelfEntity> selfEntityList, String type) {
        if (CollUtil.isEmpty(selfEntityList)) {
            return Collections.emptyList();
        }
        selfEntityList = selfEntityList.stream().filter(c -> StringUtils.isNotBlank(c.getSelfGlobalId())).collect(Collectors.toList());
        List<String> allGlobalIds = selfEntityList.stream().map(SelfEntity::getSelfGlobalId).distinct().collect(Collectors.toList());
        Map<String, List<SelfEntity>> selfEntityListMap = selfEntityList.stream()
                .filter(c -> StringUtils.isNotBlank(c.getCustomerCode())).collect(Collectors.groupingBy(SelfEntity::getCustomerCode));

        //需要保留的globalId
        List<String> reserveGlobalIds = new ArrayList<>();
        selfEntityListMap.forEach((k, v) -> {
            // 当前企业有用户编辑锁，则保留所有globalId，没有则保留最新globalId
            if (publishLockerUtil.verifyLockEnt(k, type)) {
                reserveGlobalIds.addAll(v.stream().map(SelfEntity::getSelfGlobalId).collect(Collectors.toList()));
            } else {
                SelfEntity createSelfEntity = v.stream().filter(c -> ObjectUtil.isNotNull(c.getCreateTime()))
                        .max(Comparator.comparing(SelfEntity::getCreateTime)).orElse(v.get(0));
                SelfEntity updateSelfEntity = v.stream().filter(c -> ObjectUtil.isNotNull(c.getUpdateTime()))
                        .max(Comparator.comparing(SelfEntity::getUpdateTime)).orElse(v.get(0));

                if (ObjectUtil.isNull(createSelfEntity.getCreateTime()) && ObjectUtil.isNull(updateSelfEntity.getUpdateTime())) {
                    reserveGlobalIds.add(createSelfEntity.getSelfGlobalId());
                } else if (ObjectUtil.isNotNull(createSelfEntity.getCreateTime()) && ObjectUtil.isNotNull(updateSelfEntity.getUpdateTime())) {
                    if (createSelfEntity.getCreateTime().compareTo(updateSelfEntity.getUpdateTime()) > 0) {
                        reserveGlobalIds.add(createSelfEntity.getSelfGlobalId());
                    } else {
                        reserveGlobalIds.add(updateSelfEntity.getSelfGlobalId());
                    }
                } else {
                    if (ObjectUtil.isNotNull(createSelfEntity.getCreateTime())) {
                        reserveGlobalIds.add(createSelfEntity.getSelfGlobalId());
                    } else {
                        reserveGlobalIds.add(updateSelfEntity.getSelfGlobalId());
                    }
                }
            }
        });
        allGlobalIds.removeAll(reserveGlobalIds);
        return allGlobalIds;
    }
}
