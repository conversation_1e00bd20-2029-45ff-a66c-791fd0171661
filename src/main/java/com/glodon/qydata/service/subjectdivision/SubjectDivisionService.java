package com.glodon.qydata.service.subjectdivision;

import com.glodon.qydata.entity.zbsq.Item;
import com.glodon.qydata.entity.zbsq.ZbsqCategory;

import java.util.List;

/**
 * @author: luoml-b
 * @date: 2024/5/9 14:47
 * @description: 项目划分service
 */
public interface SubjectDivisionService {

    List<Item> getTemplateByCategory(String categoryCode);

    String getFullItemName(String getItemDivisionSubjectId, List<Item> templateByCategory);
    List<ZbsqCategory> getAllCategory();
    List<Item> getTemplateByCategoryWithInit(String categoryCode);
}
