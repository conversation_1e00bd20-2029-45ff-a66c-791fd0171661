package com.glodon.qydata.service.system.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.glodon.qydata.common.RequestContent;
import com.glodon.qydata.common.constant.DigitalCostConstants;
import com.glodon.qydata.common.constant.QyRightInfoConstants;
import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.config.CommonConfig;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.service.cloud.GlodonService;
import com.glodon.qydata.service.depend.ApiAuthService;
import com.glodon.qydata.service.depend.DigitalCostService;
import com.glodon.qydata.service.system.IGlodonUserService;
import com.glodon.qydata.service.system.ISysRightInfoService;
import com.glodon.qydata.util.RedisUtil;
import com.glodon.qydata.vo.system.RightInfoDXJXKVo;
import com.glodon.qydata.vo.system.RightInfoVo;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * 系统授权、权限相关
 * Created by weijf on 2022/1/26.
 */
@Slf4j
@Service
public class SysRightInfoServiceImpl implements ISysRightInfoService {

    @Autowired
    private IGlodonUserService glodonUserService;
    @Autowired
    private DigitalCostService digitalCostService;
    @Autowired
    private GlodonService glodonService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ApiAuthService apiAuthService;
    @Autowired
    private CommonConfig commonConfig;
    /**
     * 查询用户授权：rightType、remainingDays
     * 查询用户权限：canEdit
     * @param globalId 用户广联云gldUserId
     * @param sgToken 施工平台登录Token
     * @param selectCache 是否查询缓存，true：是，false：否
     * @return
     * @throws BusinessException
     * <AUTHOR>
     */
    @Override
    public RightInfoVo  getRightInfo(String globalId, String sgToken, boolean selectCache) throws BusinessException {
        try{

            //查缓存
            if(selectCache){
                RightInfoVo rightInfo = redisUtil.getObject(RedisKeyEnum.USER_RIGHT_INFO,RightInfoVo.class,DigitalCostConstants.REFER_CODE,sgToken,globalId);
                if(rightInfo!=null){
                    return rightInfo;
                }
            }
            RightInfoVo rightInfoVo = new RightInfoVo();
            CompletableFuture<RightInfoVo> accessRightRightCompletableFuture = CompletableFuture.supplyAsync(() -> getAccessRight(sgToken));
            CompletableFuture<RightInfoVo> apiAuthRightRightCompletableFuture = CompletableFuture.supplyAsync(() -> getApiAuthRight(globalId, sgToken));
            CompletableFuture<Void> completableFuture = CompletableFuture.allOf(accessRightRightCompletableFuture, apiAuthRightRightCompletableFuture);
            completableFuture.thenAccept((Void) -> {
                RightInfoVo accessRight = accessRightRightCompletableFuture.join();
                RightInfoVo apiAuthRight = apiAuthRightRightCompletableFuture.join();
                if (accessRight.getRightType().equals(QyRightInfoConstants.RightType_1) || apiAuthRight.getRightType().equals(QyRightInfoConstants.RightType_1)) {
                    rightInfoVo.setRightType(QyRightInfoConstants.RightType_1);
                } else {
                    rightInfoVo.setRightType(accessRight.getRightType());
                }
            });

            completableFuture.join();

            //查询权限，拼装canEdit
            setEditRight(rightInfoVo, sgToken, DigitalCostConstants.EDIT_RIGHT_MANAGE);
            //放到缓存中
            redisUtil.setObject(RedisKeyEnum.USER_RIGHT_INFO,rightInfoVo,DigitalCostConstants.REFER_CODE,sgToken,globalId);
            return rightInfoVo;
        }catch (BusinessException be){
            throw be;
        }catch (Exception e){
            log.error("查询用户权限失败, 查询参数gldId = {}, sgToken = {}", globalId, sgToken, e);
            throw new BusinessException("查询用户权限失败");
        }

    }

    private RightInfoVo getApiAuthRight(String globalId, String sgToken) {
        RightInfoVo rightInfoVo = new RightInfoVo();
        if (commonConfig.isPrivateStat()) {
            rightInfoVo.setRightType(QyRightInfoConstants.RightType_1);
            return rightInfoVo;
        }
        //先从缓存查，同一次登录（gldToken）,只查一次
        RightInfoVo rightInfoVoFromCache = redisUtil.getObject(RedisKeyEnum.USER_API_AUTH_RIGHT_INFO, RightInfoVo.class, globalId, sgToken);
        if (Objects.nonNull(rightInfoVoFromCache)) {
            return rightInfoVoFromCache;
        }

        //查该帐号的企业ID
        String enterpriseGlobalId = glodonUserService.getEnterpriseId(globalId,null);
        JSONObject rightsByEnterpriseId = apiAuthService.getRightsByEnterpriseId(enterpriseGlobalId,
                Joiner.on(StrUtil.C_COMMA).join(DigitalCostConstants.hgGmsPids), globalId);
        if (CollectionUtils.isEmpty(rightsByEnterpriseId.getJSONArray("assetNumList"))) {
            rightInfoVo.setRightType(QyRightInfoConstants.RightType_0);
        } else {
            rightInfoVo.setRightType(QyRightInfoConstants.RightType_1);
        }
        //放到缓存中
        redisUtil.setObject(RedisKeyEnum.USER_API_AUTH_RIGHT_INFO, rightInfoVo, globalId, sgToken);
        return rightInfoVo;
    }

    /**
     * 查询用户授权：rightType、remainingDays
     * 查询用户权限：canEdit
     * @param globalId 用户广联云gldUserId
     * @param sgToken 施工平台登录Token
     * @param selectCache 是否查询缓存，true：是，false：否
     * @param subSystemCodes 资产编码
     * @return
     * @throws BusinessException
     * <AUTHOR>
     */
    @Override
    public RightInfoVo getRightInfoBySubSystemCodes(String globalId, String sgToken, boolean selectCache, List<String> subSystemCodes) throws BusinessException {
        try{
            //查缓存
            if(selectCache){
                RightInfoVo rightInfo = redisUtil.getObject(RedisKeyEnum.USER_RIGHT_INFO,RightInfoVo.class,DigitalCostConstants.LARGEMACHINERY_REFER_CODE,sgToken,globalId);
                if(rightInfo!=null){
                    return rightInfo;
                }
            }
            //查询授权，拼装rightType
            RightInfoVo rightInfo = getAccessRight(sgToken, subSystemCodes);
            //查看是否有大型机械库的菜单
            setRight(rightInfo,sgToken,DigitalCostConstants.LARGEMACHINERY_EDIT_RIGHT);
            //查询权限，拼装canEdit
            setEditRight(rightInfo, sgToken, DigitalCostConstants.LARGEMACHINERY_EDIT_RIGHT_MANAGER);
            setExportRight((RightInfoDXJXKVo) rightInfo, sgToken, DigitalCostConstants.LARGEMACHINERY_EDIT_RIGHT_DOWNLOAD);
            //放到缓存中
            redisUtil.setObject(RedisKeyEnum.USER_RIGHT_INFO,rightInfo,DigitalCostConstants.LARGEMACHINERY_REFER_CODE,sgToken,globalId);
            return rightInfo;
        }catch (BusinessException be){
            throw be;
        }catch (Exception e){
            e.printStackTrace();
            throw new BusinessException("查询用户权限失败");
        }
    }

    /**
     * 无菜单的情况下不展示大型机械库页签
     * @param rightInfo
     * @param sgToken
     * @param rightCode
     */
    private void setRight(RightInfoVo rightInfo, String sgToken, String rightCode) {
        //判空
        if (rightInfo == null || rightInfo.getRightType() == null) {
            return;
        }

        //无授权时、授权已过期，直接返回
        if (QyRightInfoConstants.RightType_0.intValue() == rightInfo.getRightType().intValue() ||
                QyRightInfoConstants.RightType_2.intValue() == rightInfo.getRightType().intValue()) {
            return;
        }

        //正常有授权时，从接口获取权限
        try {
            JSONArray rights = digitalCostService.getEditRight(sgToken, null);
            if (rights != null && !rights.isEmpty() && !rights.contains(rightCode)) {
                //编辑权限
                rightInfo.setRightType(QyRightInfoConstants.RightType_0);
            }
        } catch (BusinessException e) {
            log.error("setRight获取权限失败");
        }
    }

    /**
     * 查询授权，拼装rightType、remainingDays
     * @param sgToken
     * @return
     * @throws BusinessException
     * @throws BusinessException
     * <AUTHOR>
     */
    private RightInfoVo getAccessRight(String sgToken, List<String> subSystemCodes) throws BusinessException {
        RightInfoVo rightInfo = new RightInfoDXJXKVo();
        //先查授权
        JSONArray right = digitalCostService.getAccessRight(sgToken,subSystemCodes);
        //返回为空时：未分配资产，直接无权限
        if(right == null){
            rightInfo.setRightType(QyRightInfoConstants.RightType_0);
            return rightInfo;
        }
        //有效权益
        int haveEffectiveRight = 0;
        //过期权益
        int haveOverdueRight = 0;
        for (Object o : right) {
            JSONObject obj = (JSONObject) JSONObject.toJSON(o);
            String subSystemCode = obj.getString("subSystemCode");
            Integer status = obj.getInteger("status");
            //若返回的权益与查询的权益不一致，则不处理
            if(!subSystemCodes.contains(subSystemCode)){
                continue;
            }
            if(DigitalCostConstants.RIGHT_STATUS_0.intValue() == status.intValue()){
//               多个资产判断，每多一个有效权益，加1
                haveEffectiveRight ++;
            }
            if(DigitalCostConstants.RIGHT_STATUS_1.intValue() == status.intValue()){
//                多个资产判断，每多一个过期权益，加1
                haveOverdueRight ++;
            }
        }
        //默认无权益
        Integer rightType = QyRightInfoConstants.RightType_0;
        if (haveEffectiveRight > 0){
            // 模块都有权益，就是有权益
            rightType = QyRightInfoConstants.RightType_1;
        }else if(haveOverdueRight > 0){
            //不存在有效权益的情况下，只要有一个过期权益就算过期，比如某账号有三个资产：过期、禁用、未生效，则整体算作过期
            rightType = QyRightInfoConstants.RightType_2;
        }
        rightInfo.setRightType(rightType);

        return rightInfo;
    }
    /**
     * 查询授权，拼装rightType、remainingDays
     * @param sgToken
     * @return
     * @throws BusinessException
     * <AUTHOR>
     */
    private RightInfoVo getAccessRight(String sgToken) throws BusinessException {
        RightInfoVo rightInfo = new RightInfoVo();
        //先查授权
        List<String> subSystemCodes = DigitalCostConstants.subSystemCodes;

        JSONArray right = digitalCostService.getAccessRight(sgToken,subSystemCodes);
        //返回为空时：未分配资产，直接无权限
        if(right == null){
            rightInfo.setRightType(QyRightInfoConstants.RightType_0);
            return rightInfo;
        }

        //存在有效权益
        boolean haveEffectiveRight = false;
        //存在过期权益
        boolean haveOverdueRight = false;
        for (Object o : right) {
            JSONObject obj = (JSONObject) JSONObject.toJSON(o);
            String subSystemCode = obj.getString("subSystemCode");
            Integer status = obj.getInteger("status");
            //若返回的权益与查询的权益不一致，则不处理
            if(!subSystemCodes.contains(subSystemCode)){
                continue;
            }
            if(DigitalCostConstants.RIGHT_STATUS_0.intValue() == status.intValue()){
                haveEffectiveRight = true;
            }
            if(DigitalCostConstants.RIGHT_STATUS_1.intValue() == status.intValue()){
                haveOverdueRight = true;
            }
        }
        //默认无权益
        Integer rightType = QyRightInfoConstants.RightType_0;
        if (haveEffectiveRight){
            //存在一个有效权益就算有权益
            rightType = QyRightInfoConstants.RightType_1;
        }else if(haveOverdueRight){
            //不存在有效权益的情况下，只要有一个过期权益就算过期，比如某账号有三个资产：过期、禁用、未生效，则整体算作过期
            rightType = QyRightInfoConstants.RightType_2;
        }

        rightInfo.setRightType(rightType);

        return rightInfo;
    }

    /**
     * 设置用户权限，拼装canEdit、canDownload、canAudit
     * @param rightInfo
     * @param sgToken
     * @param editRight
     */
    private void setEditRight(RightInfoVo rightInfo, String sgToken, String editRight) {
        //先默认全部无权限
        rightInfo.setCanEdit(false);

        //判空
        if (rightInfo.getRightType() == null){
            return;
        }

        //无授权时、授权已过期，直接返回
        if(QyRightInfoConstants.RightType_0.intValue() == rightInfo.getRightType().intValue() ||
                QyRightInfoConstants.RightType_2.intValue() == rightInfo.getRightType().intValue()){
            return;
        }

        //正常有授权时，从接口获取权限
        try {
            JSONArray editRights = digitalCostService.getEditRight(sgToken,null);
            if(editRights!= null && !editRights.isEmpty() && editRights.contains(editRight)){
                //编辑权限
                rightInfo.setCanEdit(true);
            }
        } catch (BusinessException e) {
            log.error("setEditRight获取权限失败");
        }

    }
    /**
     * 设置用户权限，拼装canEdit、canDownload、canAudit
     * @param rightInfo
     * @param sgToken
     * @param editRight
     */
    private void setExportRight(RightInfoDXJXKVo rightInfo, String sgToken, String editRight) {
        //先默认全部无权限
        rightInfo.setCanExport(false);

        //判空
        if (rightInfo.getRightType()==null){
            return;
        }

        //无授权时、授权已过期，直接返回
        if(QyRightInfoConstants.RightType_0.intValue() == rightInfo.getRightType().intValue() ||
                QyRightInfoConstants.RightType_2.intValue() == rightInfo.getRightType().intValue()){
            return;
        }

        //正常有授权时，从接口获取权限
        try {
            JSONArray editRights = digitalCostService.getEditRight(sgToken,null);
            if(editRights!= null && !editRights.isEmpty() && editRights.contains(editRight)){
                //编辑权限
                rightInfo.setCanExport(true);
            }
        } catch (BusinessException e) {
            log.error("setExportRight获取权限失败");
        }

    }
    /**
     * @Description 鉴权（有权限使用产品、无权限使用产品、权限过期）
     * <AUTHOR>
     * @Date 2022/1/28 11:09
     * @param
     * @return void
     **/
    @Override
    public void authLimit() throws BusinessException {
        try {
            String globalId = RequestContent.getGlobalId();
            String sgToken = RequestContent.getSgToken();
            RightInfoVo rightInfo = getRightInfo(globalId, sgToken, true);
            Integer rightType = rightInfo.getRightType();

            if (rightType==null || rightType.intValue()==QyRightInfoConstants.RightType_0){
                throw new BusinessException("您无权限使用该功能，请联系客服后使用");
            }
            if (rightType.intValue()==QyRightInfoConstants.RightType_2){
                throw new BusinessException("权限已过期请续费后使用");
            }
        }catch (BusinessException be){
            throw be;
        }catch (Exception e){
            e.printStackTrace();
            throw new BusinessException("查询权限异常，请稍后尝试");
        }
    }

    /**
     * @Description 编辑权限校验
     * <AUTHOR>
     * @Date 2022/1/28 13:57
     * @param
     * @return void
     **/
    @Override
    public void checkEditAuth() throws BusinessException {
        String globalId = RequestContent.getGlobalId();
        String sgToken = RequestContent.getSgToken();
        RightInfoVo rightInfo = getRightInfo(globalId, sgToken, true);
        Boolean canEdit = rightInfo.getCanEdit();

        if (canEdit==null || !canEdit){
            throw new BusinessException("您还没有维护权限，不可使用该功能");
        }
    }

    /**
     * 根据当前登录用户查询资产编码
     * @param globalId
     * @param gldToken
     * @return
     * <AUTHOR>
     */
    public String getAssetNum(String globalId,String gldToken,String pcode){
        try {
            //先从缓存查，同一次登录（gldToken）,只查一次
            String assetNum = redisUtil.getString(RedisKeyEnum.USER_INFO_ASSET_NUM,globalId,pcode,gldToken);
            if(StringUtils.isNotBlank(assetNum)){
                return assetNum;
            }
            //查该帐号的企业ID
            String enterpriseGlobalId = glodonUserService.getEnterpriseId(globalId,null);
            JSONObject data = glodonService.getProductRights(enterpriseGlobalId,String.join(",",DigitalCostConstants.subGmsPids),globalId);
            if(data==null || data.isEmpty() || !data.containsKey("assetNumList")){
                return null;
            }

            JSONArray assetNumList = data.getJSONArray("assetNumList");

            long lastLimitEndTime = Long.MIN_VALUE;

            for (Object o : assetNumList) {
                JSONObject obj = (JSONObject) JSONObject.toJSON(o);
                for (String tmpAssetNum : obj.keySet()) {
                    JSONArray assetArr = obj.getJSONArray(tmpAssetNum);
                    if(assetArr==null || assetArr.isEmpty()) continue;

                    if(StringUtils.isBlank(assetNum)){ //第一次，资产编码为空时，直接取，无需判断
                        assetNum = tmpAssetNum;
                        continue;
                    }
                    for (Object assetO : assetArr) {
                        JSONObject assetObj = (JSONObject) JSONObject.toJSON(assetO);
                        Long tmpLimitEndTime = assetObj.getLong("limitEndTime");

                        //按最后生效时间来判断
                        if( tmpLimitEndTime!= null && tmpLimitEndTime > lastLimitEndTime ){
                            assetNum = tmpAssetNum;
                            lastLimitEndTime = tmpLimitEndTime;
                        }
                    }

                }

            }

            //存缓存
            if(StringUtils.isNotBlank(assetNum)){
                redisUtil.setString(RedisKeyEnum.USER_INFO_ASSET_NUM,assetNum,globalId,pcode,gldToken);
            }
            return assetNum;
        }catch (Exception e){
            log.error("根据当前登录用户查询资产编码出错，e={}", e.getMessage(), e);
            return null;
        }
    }
}
