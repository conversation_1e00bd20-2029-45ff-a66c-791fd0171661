package com.glodon.qydata.service.system.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.config.CommonConfig;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.service.system.IGlodonUserService;
import com.glodon.qydata.service.system.ISysRightInfoService;
import com.glodon.qydata.service.system.IoDataService;
import com.glodon.qydata.util.DateUtils;
import com.glodon.qydata.util.IPUtils;
import com.glodon.qydata.vo.system.UserBehaviorVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * IO埋点记录Service
 *  埋点规范<a href="https://doc.weixin.qq.com/sheet/e3_AcwAmwayAKQsVaneFKYQemF0ZNcM0?scode=ALgASQcQABIVF5LyJFAcwAmwayAKQ&tab=BB08J2"/>
 */
@Service
@Slf4j
public class IoDataServiceImpl implements IoDataService {

    @Autowired
    private CommonConfig commonConfig;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private ISysRightInfoService sysRightInfoService;
    @Autowired
    private IGlodonUserService glodonUserService;
    @Value("${sendLogDebug}")
    private boolean sendLogDebug;

    private static final String URL = "https://tj.glodon.com/applog2/LogBodyAccept";
    private static final String P_CODE = "120766";

    /**
     * 用户埋点行为发送至io系统服务
     * @param userBehavior 用户埋点行为参数vo
     * @param ip ip
     * @return
     */
    @Override
    public void sendLog(UserBehaviorVO userBehavior, String ip) throws Exception{
        log.debug("埋点入参：{}", JSONObject.toJSONString(userBehavior));
        // 构建埋点对象
        JSONArray logBody = buildLogBody(userBehavior, ip);
        // 向统计系统发统计请求
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<JSONArray> req = new HttpEntity<>(logBody, headers);
        ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(URL, req, JSONObject.class);
        if (responseEntity.getStatusCodeValue() != ResponseCode.SUCCESS.getCode()) {
            throw new BusinessException("发送行为日志到io系统错误，" + JSONObject.toJSONString(responseEntity));
        }
    }

    /**
     * 构建埋点对象
     * @param userBehavior
     * @param ip
     */
    private JSONArray buildLogBody(UserBehaviorVO userBehavior, String ip) {
        Map<String, Object> logBody = new HashMap<>();

        logBody.put("pcode", P_CODE);
        logBody.put("vername", ""); //版本说明
        logBody.put("fncode", userBehavior.getFncode());
        logBody.put("fnname", userBehavior.getFnname());
        logBody.put("fngroup", userBehavior.getFngroup());
        logBody.put("trigertime", DateUtils.DateToString(new Date(), "yyyy/MM/dd HH:mm:ss"));
        logBody.put("device_id", ""); //用户设备号
        logBody.put("gid", userBehavior.getGlobalId());
        logBody.put("debug", sendLogDebug); //是否开发模式：为false的数据将进入生产仓库；为true的数据将进入测试仓库
        logBody.put("ip", ip);
        logBody.put("sessionid", userBehavior.getSessionid());
        logBody.put("query", getQueryJson(userBehavior, ip)); // 其他业务内容

        // 统计时长
        if (isNumber(userBehavior.getDuration())) {
            logBody.put("duration", userBehavior.getDuration());
        }

        JSONArray jsonArray = new JSONArray();
        jsonArray.add(logBody);
        return jsonArray;
    }

    /**
     * 其他业务内容，根据业务情况按json格式带入，自行编写key，带入后统一进入数据仓库query字段
     * 示例：{query:{customer_solution:jsf|zxf|sfg}}
     * @param userBehavior userBehavior
     * @param ip ip
     */
    private String getQueryJson(UserBehaviorVO userBehavior, String ip) {
        String queryStr = userBehavior.getQueryStr();
        JSONObject jsonObj = new JSONObject();
        if (StringUtils.isNotEmpty(queryStr) && !queryStr.equals("null")) {
            try {
                jsonObj = JSONObject.parseObject(queryStr);
            } catch (Exception e) {
                log.error("sendLog parseObject error, queryStr={}", queryStr);
            }
        }

        jsonObj.put("area", getCityInfo(ip));
        jsonObj.put("asset_no", sysRightInfoService.getAssetNum(userBehavior.getGlobalId(), userBehavior.getGldToken(), P_CODE)); //获取资产编码

        return jsonObj.toJSONString();
    }

    private String getCityInfo(String ip) {
        if (ip == null || ip.equals("127.0.0.1")) {
            ip = "************";
        }
        return IPUtils.getCityInfo(ip, commonConfig.ip2regionPath);
    }

    private static boolean isNumber(String str) {
        String pattern ="^\\d+(.\\d+)?$";
        return StringUtils.isNotBlank(str) && Pattern.matches(pattern, str);
    }
}
