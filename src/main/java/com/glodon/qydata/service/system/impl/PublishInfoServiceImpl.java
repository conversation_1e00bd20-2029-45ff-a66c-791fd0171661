package com.glodon.qydata.service.system.impl;

import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.dto.PublishInfoDto;
import com.glodon.qydata.entity.system.PublishInfo;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.system.PublishInfoMapper;
import com.glodon.qydata.service.system.PublishInfoService;
import com.glodon.qydata.util.RedisUtil;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PublishInfoServiceImpl implements PublishInfoService {
    @Resource
    private PublishInfoMapper publishInfoMapper;
    @Resource
    private RedisUtil redisUtil;

    @Override
    public void updateVersion(String customerCode, String globalId, String type) {
        String lockValue = redisUtil.generateLockValue();

        if (!redisUtil.tryLockRetry(lockValue, RedisKeyEnum.PUBLISH_VERSION_LOCK, customerCode)) {
            throw new BusinessException("版本发布失败，请稍后重试...");
        }

        try {
            // 查询最大版本并创建新的发布信息
            Integer maxVersion = publishInfoMapper.findMaxVersion(customerCode);
            PublishInfo publishInfo = createPublishInfo(customerCode, globalId, type, maxVersion);

            publishInfoMapper.insert(publishInfo);
        } finally {
            redisUtil.unlock(lockValue, RedisKeyEnum.PUBLISH_VERSION_LOCK, customerCode);
        }
    }

    private PublishInfo createPublishInfo(String customerCode, String globalId, String type, Integer maxVersion) {
        PublishInfo publishInfo = new PublishInfo();
        publishInfo.setId(SnowflakeIdUtils.getNextId());
        publishInfo.setCustomerCode(customerCode);
        publishInfo.setGlobalId(globalId);
        publishInfo.setType(type);
        publishInfo.setVersion(maxVersion != null ? maxVersion + 1 : 1); // 确保版本号从1开始
        return publishInfo;
    }

    @Override
    public PublishInfoDto getPublishInfo(String customerCode) {
        PublishInfo maxVersionInfo = publishInfoMapper.findMaxVersionInfo(customerCode);
        return mapToDto(maxVersionInfo);
    }

    private PublishInfoDto mapToDto(PublishInfo maxVersionInfo) {
        PublishInfoDto publishInfoDto = new PublishInfoDto();
        if (maxVersionInfo != null) {
            publishInfoDto.setVersion(maxVersionInfo.getVersion());
            publishInfoDto.setUpdateTime(maxVersionInfo.getModifyTime());
        }
        return publishInfoDto;
    }
}
