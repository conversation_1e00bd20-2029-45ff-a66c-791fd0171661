package com.glodon.qydata.service.system;

import com.alibaba.fastjson.JSONObject;
import com.glodon.qydata.common.AccountTypeService;
import com.glodon.qydata.common.enums.AccountTypeEnum;
import com.glodon.qydata.mapper.system.DataCleanupMapper;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据清理服务
 * 用于清理个人账号相关的数据
 *
 * @author: yanyh <EMAIL>
 * @date: 2025年7月31日15:44:22
 */
@SuppressWarnings({"DuplicatedCode", "ExtractMethodRecommender", "LoggingSimilarMessage"})
@Slf4j
@Service
public class DataCleanupService {

    @Autowired
    private DataCleanupMapper dataCleanupMapper;
    @Autowired
    private AccountTypeService accountTypeService;
    @Autowired
    private DataCleanupService dataCleanupService;
    // 记录ID缓存：qy_code -> 记录ID列表
    private final Map<String, List<Long>> recordIdCache = new HashMap<>();
    // customer_code记录ID缓存：customer_code -> 记录ID列表
    private final Map<String, List<Long>> customerCodeRecordIdCache = new HashMap<>();

    /**
     * 数据清理结果类
     */
    @Setter
    @Getter
    public static class CleanupResult {
        // 基础统计信息
        private long totalRecords;
        private Map<String, Long> qyCodeGroupStats;
        private Map<String, AccountTypeEnum> qyCodeToAccountType;
        private List<PersonalAccountData> personalAccountDataList;
        private boolean cleanupExecuted;
        private long deletedRecords;
        private String message;

        // 空间统计信息
        private List<TableSpaceInfo> tableSpaceInfoList;
        private Map<String, QyCodeSpaceInfo> qyCodeSpaceStats;
        private SpaceSummary spaceSummary;
        private SpaceSummary beforeCleanupSpace;
        private SpaceSummary afterCleanupSpace;
    }

    /**
     * 个人账号数据类
     */
    @Setter
    @Getter
    public static class PersonalAccountData {
        private String qyCode;
        private String globalId;
        private long recordCount;
        private List<Long> recordIds;
        private QyCodeSpaceInfo spaceInfo;

        public PersonalAccountData(String qyCode, String globalId, long recordCount, List<Long> recordIds) {
            this.qyCode = qyCode;
            this.globalId = globalId;
            this.recordCount = recordCount;
            this.recordIds = recordIds;
        }
    }

    /**
     * 表空间信息类
     */
    @Setter
    @Getter
    public static class TableSpaceInfo {
        private String tableName;
        private long tableRows;
        private long dataLength;
        private long indexLength;
        private long totalLength;
        private String dataLengthFormatted;
        private String indexLengthFormatted;
        private String totalLengthFormatted;

        public TableSpaceInfo() {
            // MyBatis需要无参构造函数
        }

        public TableSpaceInfo(String tableName, long tableRows, long dataLength, long indexLength) {
            this.tableName = tableName;
            this.tableRows = tableRows;
            this.dataLength = dataLength;
            this.indexLength = indexLength;
            this.totalLength = dataLength + indexLength;
            this.dataLengthFormatted = formatBytes(dataLength);
            this.indexLengthFormatted = formatBytes(indexLength);
            this.totalLengthFormatted = formatBytes(totalLength);
        }

        // 用于MyBatis结果映射后的格式化处理
        public void formatSizes() {
            this.totalLength = this.dataLength + this.indexLength;
            this.dataLengthFormatted = formatBytes(this.dataLength);
            this.indexLengthFormatted = formatBytes(this.indexLength);
            this.totalLengthFormatted = formatBytes(this.totalLength);
        }
    }

    /**
     * qy_code分组统计信息类
     */
    @Setter
    @Getter
    public static class QyCodeGroupStats {
        private String qyCode;
        private long recordCount;
        private List<Long> recordIds; // 新增：记录ID列表

        public QyCodeGroupStats() {
            // MyBatis需要无参构造函数
            this.recordIds = new ArrayList<>();
        }

        public QyCodeGroupStats(String qyCode, long recordCount) {
            this.qyCode = qyCode;
            this.recordCount = recordCount;
            this.recordIds = new ArrayList<>();
        }

        public QyCodeGroupStats(String qyCode, long recordCount, List<Long> recordIds) {
            this.qyCode = qyCode;
            this.recordCount = recordCount;
            this.recordIds = recordIds != null ? recordIds : new ArrayList<>();
        }
    }

    /**
     * qy_code按表分布统计信息类
     */
    @Setter
    @Getter
    public static class QyCodeTableDistribution {
        private String qyCode;
        private Map<String, Long> tableRecordCounts; // 表名 -> 记录数
        private long totalRecordCount;
        private Map<String, Long> tableSpaceBytes; // 表名 -> 空间占用字节数
        private long totalSpaceBytes;

        public QyCodeTableDistribution() {
            this.tableRecordCounts = new HashMap<>();
            this.tableSpaceBytes = new HashMap<>();
        }

        public QyCodeTableDistribution(String qyCode) {
            this.qyCode = qyCode;
            this.tableRecordCounts = new HashMap<>();
            this.tableSpaceBytes = new HashMap<>();
        }

        public void addTableRecords(String tableName, long recordCount) {
            this.tableRecordCounts.put(tableName, recordCount);
            this.totalRecordCount += recordCount;
        }

        public void addTableSpace(String tableName, long spaceBytes) {
            this.tableSpaceBytes.put(tableName, spaceBytes);
            this.totalSpaceBytes += spaceBytes;
        }

        public long getRecordCountForTable(String tableName) {
            return this.tableRecordCounts.getOrDefault(tableName, 0L);
        }

        public long getSpaceBytesForTable(String tableName) {
            return this.tableSpaceBytes.getOrDefault(tableName, 0L);
        }
    }

    /**
     * 记录ID信息类
     */
    @Setter
    @Getter
    public static class RecordIdInfo {
        private Long id;
        private String qyCode;

        public RecordIdInfo() {
            // MyBatis需要无参构造函数
        }

        public RecordIdInfo(Long id, String qyCode) {
            this.id = id;
            this.qyCode = qyCode;
        }
    }

    // ==================== 新增：支持 customer_code 字段的内部类 ====================

    /**
     * customer_code分组统计信息类
     */
    @Setter
    @Getter
    public static class CustomerCodeGroupStats {
        private String customerCode;
        private long recordCount;
        private List<Long> recordIds; // 记录ID列表

        public CustomerCodeGroupStats() {
            // MyBatis需要无参构造函数
            this.recordIds = new ArrayList<>();
        }

        public CustomerCodeGroupStats(String customerCode, long recordCount) {
            this.customerCode = customerCode;
            this.recordCount = recordCount;
            this.recordIds = new ArrayList<>();
        }

        public CustomerCodeGroupStats(String customerCode, long recordCount, List<Long> recordIds) {
            this.customerCode = customerCode;
            this.recordCount = recordCount;
            this.recordIds = recordIds != null ? recordIds : new ArrayList<>();
        }
    }

    /**
     * customer_code记录ID信息类
     */
    @Setter
    @Getter
    public static class CustomerCodeRecordIdInfo {
        private Long id;
        private String customerCode;

        public CustomerCodeRecordIdInfo() {
            // MyBatis需要无参构造函数
        }

        public CustomerCodeRecordIdInfo(Long id, String customerCode) {
            this.id = id;
            this.customerCode = customerCode;
        }
    }

    /**
     * customer_code个人账号数据类
     */
    @Setter
    @Getter
    public static class PersonalAccountDataByCustomerCode {
        private String customerCode;
        private String globalId;
        private long recordCount;
        private List<Long> recordIds;
        private CustomerCodeSpaceInfo spaceInfo;

        public PersonalAccountDataByCustomerCode(String customerCode, String globalId, long recordCount, List<Long> recordIds) {
            this.customerCode = customerCode;
            this.globalId = globalId;
            this.recordCount = recordCount;
            this.recordIds = recordIds;
        }
    }

    /**
     * customer_code空间信息类
     */
    @Setter
    @Getter
    public static class CustomerCodeSpaceInfo {
        private String customerCode;
        private long recordCount;
        private long estimatedSpaceBytes;
        private String estimatedSpaceFormatted;
        private AccountTypeEnum accountType;
        private double spacePercentage;

        public CustomerCodeSpaceInfo(String customerCode, long recordCount, long estimatedSpaceBytes, AccountTypeEnum accountType) {
            this.customerCode = customerCode;
            this.recordCount = recordCount;
            this.estimatedSpaceBytes = estimatedSpaceBytes;
            this.estimatedSpaceFormatted = formatBytes(estimatedSpaceBytes);
            this.accountType = accountType;
        }
    }

    /**
     * 企业编码空间信息类
     */
    @Setter
    @Getter
    public static class QyCodeSpaceInfo {
        private String qyCode;
        private long recordCount;
        private long estimatedSpaceBytes;
        private String estimatedSpaceFormatted;
        private AccountTypeEnum accountType;
        private double spacePercentage;

        public QyCodeSpaceInfo(String qyCode, long recordCount, long estimatedSpaceBytes, AccountTypeEnum accountType) {
            this.qyCode = qyCode;
            this.recordCount = recordCount;
            this.estimatedSpaceBytes = estimatedSpaceBytes;
            this.estimatedSpaceFormatted = formatBytes(estimatedSpaceBytes);
            this.accountType = accountType;
        }
    }

    /**
     * 空间汇总信息类
     */
    @Setter
    @Getter
    public static class SpaceSummary {
        private long totalDataBytes;
        private long totalIndexBytes;
        private long totalSpaceBytes;
        private String totalDataFormatted;
        private String totalIndexFormatted;
        private String totalSpaceFormatted;
        private long personalAccountSpaceBytes;
        private long enterpriseAccountSpaceBytes;
        private String personalAccountSpaceFormatted;
        private String enterpriseAccountSpaceFormatted;
        private double personalAccountPercentage;
        private double enterpriseAccountPercentage;

        public SpaceSummary(long totalDataBytes, long totalIndexBytes, long personalAccountSpaceBytes, long enterpriseAccountSpaceBytes) {
            this.totalDataBytes = totalDataBytes;
            this.totalIndexBytes = totalIndexBytes;
            this.totalSpaceBytes = totalDataBytes + totalIndexBytes;
            this.totalDataFormatted = formatBytes(totalDataBytes);
            this.totalIndexFormatted = formatBytes(totalIndexBytes);
            this.totalSpaceFormatted = formatBytes(totalSpaceBytes);
            this.personalAccountSpaceBytes = personalAccountSpaceBytes;
            this.enterpriseAccountSpaceBytes = enterpriseAccountSpaceBytes;
            this.personalAccountSpaceFormatted = formatBytes(personalAccountSpaceBytes);
            this.enterpriseAccountSpaceFormatted = formatBytes(enterpriseAccountSpaceBytes);

            if (totalSpaceBytes > 0) {
                this.personalAccountPercentage = (double) personalAccountSpaceBytes * 100 / totalSpaceBytes;
                this.enterpriseAccountPercentage = (double) enterpriseAccountSpaceBytes * 100 / totalSpaceBytes;
            }
        }
    }

    /**
     * customer_code数据清理结果类
     */
    @Setter
    @Getter
    public static class CustomerCodeCleanupResult {
        // 基础统计信息
        private long totalRecords;
        private Map<String, Long> customerCodeGroupStats;
        private Map<String, AccountTypeEnum> customerCodeToAccountType;
        private List<PersonalAccountDataByCustomerCode> personalAccountDataList;
        private boolean cleanupExecuted;
        private long deletedRecords;
        private String message;

        // 空间统计信息
        private List<TableSpaceInfo> tableSpaceInfoList;
        private Map<String, CustomerCodeSpaceInfo> customerCodeSpaceStats;
        private SpaceSummary spaceSummary;
        private SpaceSummary beforeCleanupSpace;
        private SpaceSummary afterCleanupSpace;
    }

    /**
     * 执行数据清理操作
     *
     * @param executeCleanup 是否执行清理操作，true-执行删除，false-仅分析展示
     * @return 清理结果
     */
    public CleanupResult performCommonProjectCategoryDataCleanup(boolean executeCleanup) {
        log.info("开始执行数据清理操作，executeCleanup: {}", executeCleanup);
        CleanupResult result = new CleanupResult();
        try {
            // 1. 统计数据量和表空间
            statisticsData(result);
            // 2. 分组分析和空间估算
            analyzeQyCodeGroups(result);
            // 3. 判断账号类型
            determineAccountTypes(result);
            // 4. 收集个人账号数据和空间统计
            collectPersonalAccountData(result);
            // 5. 生成空间汇总信息
            generateSpaceSummary(result);
            // 6. 执行清理操作
            if (executeCleanup) {
                dataCleanupService.executeDataCleanup(result);
            } else {
                result.setCleanupExecuted(false);
                result.setMessage("数据分析完成，未执行清理操作");
            }
            log.info("数据清理操作完成，结果: {}", JSONObject.toJSONString(result));
        } catch (Exception e) {
            log.error("数据清理操作失败", e);
            result.setMessage("数据清理操作失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 执行 zb_standards_trade 表的数据清理操作
     *
     * @param executeCleanup 是否执行清理操作，true-执行删除，false-仅分析展示
     * @return 清理结果
     */
    public CustomerCodeCleanupResult performTradeDataCleanup(boolean executeCleanup) {
        log.info("开始执行 zb_standards_trade 表数据清理操作，executeCleanup: {}", executeCleanup);
        CustomerCodeCleanupResult result = new CustomerCodeCleanupResult();
        try {
            // 1. 统计数据量和表空间
            statisticsTradeData(result);
            // 2. 分组分析和空间估算
            analyzeCustomerCodeGroups(result);
            // 3. 判断账号类型
            determineAccountTypesByCustomerCode(result);
            // 4. 收集个人账号数据和空间统计
            collectPersonalAccountDataByCustomerCode(result);
            // 5. 生成空间汇总信息
            generateSpaceSummaryForTrade(result);
            // 6. 执行清理操作
            if (executeCleanup) {
                // 记录清理前的空间信息
                result.setBeforeCleanupSpace(result.getSpaceSummary());
                dataCleanupService.executeTradeDataCleanup(result);
            } else {
                result.setCleanupExecuted(false);
                result.setMessage("zb_standards_trade 表数据分析完成，未执行清理操作");
            }
            log.info("zb_standards_trade 表数据清理操作完成，结果: {}", JSONObject.toJSONString(result));
        } catch (Exception e) {
            log.error("zb_standards_trade 表数据清理操作失败", e);
            result.setMessage("zb_standards_trade 表数据清理操作失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 执行 zb_standards_main_quantity 表的数据清理操作
     *
     * @param executeCleanup 是否执行清理操作，true-执行删除，false-仅分析展示
     * @return 清理结果
     */
    public CustomerCodeCleanupResult performMainQuantityDataCleanup(boolean executeCleanup) {
        log.info("开始执行 zb_standards_main_quantity 表数据清理操作，executeCleanup: {}", executeCleanup);
        CustomerCodeCleanupResult result = new CustomerCodeCleanupResult();
        try {
            // 1. 统计数据量和表空间
            statisticsMainQuantityData(result);
            // 2. 分组分析和空间估算
            analyzeMainQuantityCustomerCodeGroups(result);
            // 3. 判断账号类型
            determineAccountTypesByCustomerCodeForMainQuantity(result);
            // 4. 收集个人账号数据和空间统计
            collectPersonalAccountDataByCustomerCodeForMainQuantity(result);
            // 5. 生成空间汇总信息
            generateSpaceSummaryForMainQuantity(result);
            // 6. 执行清理操作
            if (executeCleanup) {
                // 记录清理前的空间信息
                result.setBeforeCleanupSpace(result.getSpaceSummary());
                dataCleanupService.executeMainQuantityDataCleanup(result);
            } else {
                result.setCleanupExecuted(false);
                result.setMessage("zb_standards_main_quantity 表数据分析完成，未执行清理操作");
            }
            log.info("zb_standards_main_quantity 表数据清理操作完成，结果: {}", JSONObject.toJSONString(result));
        } catch (Exception e) {
            log.error("zb_standards_main_quantity 表数据清理操作失败", e);
            result.setMessage("zb_standards_main_quantity 表数据清理操作失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 执行 zb_project_feature_standards 表的数据清理操作
     *
     * @param executeCleanup 是否执行清理操作，true-执行删除，false-仅分析展示
     * @return 清理结果
     */
    public CustomerCodeCleanupResult performProjectFeatureDataCleanup(boolean executeCleanup) {
        log.info("开始执行 zb_project_feature_standards 表数据清理操作，executeCleanup: {}", executeCleanup);
        CustomerCodeCleanupResult result = new CustomerCodeCleanupResult();
        try {
            // 1. 统计数据量和表空间
            statisticsProjectFeatureData(result);
            // 2. 分组分析和空间估算
            analyzeProjectFeatureCustomerCodeGroups(result);
            // 3. 判断账号类型
            determineAccountTypesByCustomerCodeForProjectFeature(result);
            // 4. 收集个人账号数据和空间统计
            collectPersonalAccountDataByCustomerCodeForProjectFeature(result);
            // 5. 生成空间汇总信息
            generateSpaceSummaryForProjectFeature(result);
            // 6. 执行清理操作
            if (executeCleanup) {
                // 记录清理前的空间信息
                result.setBeforeCleanupSpace(result.getSpaceSummary());
                dataCleanupService.executeProjectFeatureDataCleanup(result);
            } else {
                result.setCleanupExecuted(false);
                result.setMessage("zb_project_feature_standards 表数据分析完成，未执行清理操作");
            }
            log.info("zb_project_feature_standards 表数据清理操作完成，结果: {}", JSONObject.toJSONString(result));
        } catch (Exception e) {
            log.error("zb_project_feature_standards 表数据清理操作失败", e);
            result.setMessage("zb_project_feature_standards 表数据清理操作失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 执行 zb_project_feature_category_view_standards 表的数据清理操作
     *
     * @param executeCleanup 是否执行清理操作，true-执行删除，false-仅分析展示
     * @return 清理结果
     */
    public CustomerCodeCleanupResult performCategoryViewDataCleanup(boolean executeCleanup) {
        log.info("开始执行 zb_project_feature_category_view_standards 表数据清理操作，executeCleanup: {}", executeCleanup);
        CustomerCodeCleanupResult result = new CustomerCodeCleanupResult();
        try {
            // 1. 统计数据量和表空间
            statisticsCategoryViewData(result);
            // 2. 分组分析和空间估算
            analyzeCategoryViewCustomerCodeGroups(result);
            // 3. 判断账号类型
            determineAccountTypesByCustomerCodeForCategoryView(result);
            // 4. 收集个人账号数据和空间统计
            collectPersonalAccountDataByCustomerCodeForCategoryView(result);
            // 5. 生成空间汇总信息
            generateSpaceSummaryForCategoryView(result);
            // 6. 执行清理操作
            if (executeCleanup) {
                // 记录清理前的空间信息
                result.setBeforeCleanupSpace(result.getSpaceSummary());
                dataCleanupService.executeCategoryViewDataCleanup(result);
            } else {
                result.setCleanupExecuted(false);
                result.setMessage("zb_project_feature_category_view_standards 表数据分析完成，未执行清理操作");
            }
            log.info("zb_project_feature_category_view_standards 表数据清理操作完成，结果: {}", JSONObject.toJSONString(result));
        } catch (Exception e) {
            log.error("zb_project_feature_category_view_standards 表数据清理操作失败", e);
            result.setMessage("zb_project_feature_category_view_standards 表数据清理操作失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 执行 zb_standards_expression 表的数据清理操作
     *
     * @param executeCleanup 是否执行清理操作，true-执行删除，false-仅分析展示
     * @return 清理结果
     */
    public CleanupResult performExpressionDataCleanup(boolean executeCleanup) {
        log.info("开始执行 zb_standards_expression 表数据清理操作，executeCleanup: {}", executeCleanup);
        CleanupResult result = new CleanupResult();
        try {
            // 1. 统计数据量和表空间
            statisticsExpressionData(result);
            // 2. 分组分析和空间估算
            analyzeExpressionQyCodeGroups(result);
            // 3. 判断账号类型
            determineAccountTypesByQyCodeForExpression(result);
            // 4. 收集个人账号数据和空间统计
            collectPersonalAccountDataByQyCodeForExpression(result);
            // 5. 生成空间汇总信息
            generateSpaceSummaryForExpression(result);
            // 6. 执行清理操作
            if (executeCleanup) {
                // 记录清理前的空间信息
                result.setBeforeCleanupSpace(result.getSpaceSummary());
                dataCleanupService.executeExpressionDataCleanup(result);
            } else {
                result.setCleanupExecuted(false);
                result.setMessage("zb_standards_expression 表数据分析完成，未执行清理操作");
            }
            log.info("zb_standards_expression 表数据清理操作完成，结果: {}", JSONObject.toJSONString(result));
        } catch (Exception e) {
            log.error("zb_standards_expression 表数据清理操作失败", e);
            result.setMessage("zb_standards_expression 表数据清理操作失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 格式化字节大小
     */
    private static String formatBytes(long bytes) {
        if (bytes < 0) return "0 B";
        if (bytes < 1024) return bytes + " B";

        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = bytes;

        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }

        return String.format("%.2f %s", size, units[unitIndex]);
    }

    /**
     * 判断字符串是否为纯数字
     *
     * @param str 待判断的字符串
     * @return true-纯数字，false-非纯数字
     */
    private static boolean isNumeric(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }

        // 使用正则表达式判断是否为纯数字
        return str.matches("\\d+");
    }

    /**
     * 统计两张表的数据量和空间信息
     */
    private void statisticsData(CleanupResult result) {
        log.info("开始统计数据量和表空间信息");
        // 统计记录数量
        Long count1 = dataCleanupMapper.countTableRecords("tb_commonprojcategory_standards_0");
        Long count2 = dataCleanupMapper.countTableRecords("tb_commonprojcategory_standards");
        long totalRecords = (count1 != null ? count1 : 0) + (count2 != null ? count2 : 0);
        result.setTotalRecords(totalRecords);
        // 统计表空间信息
        result.setTableSpaceInfoList(getTableSpaceInfo());
        // 输出统计日志
        log.info("数据量统计完成 - tb_commonprojcategory_standards_0: {}, tb_commonprojcategory_standards: {}, 总计: {}", count1, count2, totalRecords);
    }

    /**
     * 获取表空间信息（使用预设静态数据）
     */
    private List<TableSpaceInfo> getTableSpaceInfo() {
        log.info("使用预设数据获取表空间信息");
        List<TableSpaceInfo> tableSpaceInfoList = new ArrayList<>();
        // 基于提供的实际数据创建静态表空间信息
        // tb_commonprojcategory_standards: 97,690,155行, 数据18GB, 索引20GB
        TableSpaceInfo info1 = createTableSpaceInfo(
                "tb_commonprojcategory_standards", 97690155L,
                18076401664L, // 18GB转换为字节
                20777369600L  // 20GB转换为字节
        );

        // tb_commonprojcategory_standards_0: 106,107,802行, 数据17GB, 索引32GB
        TableSpaceInfo info2 = createTableSpaceInfo(
                "tb_commonprojcategory_standards_0",
                106107802L,
                17325588480L, // 17GB转换为字节
                32570490880L  // 32GB转换为字节
        );
        tableSpaceInfoList.add(info1);
        tableSpaceInfoList.add(info2);
        // 输出表空间统计日志
        for (TableSpaceInfo spaceInfo : tableSpaceInfoList) {
            log.info("表空间信息 - 表名: {}, 记录数: {}, 数据大小: {}, 索引大小: {}, 总大小: {}", spaceInfo.getTableName(), spaceInfo.getTableRows(), spaceInfo.getDataLengthFormatted(), spaceInfo.getIndexLengthFormatted(), spaceInfo.getTotalLengthFormatted());
        }
        return tableSpaceInfoList;
    }

    /**
     * 创建表空间信息对象
     */
    private TableSpaceInfo createTableSpaceInfo(String tableName, long tableRows, long dataLength, long indexLength) {
        TableSpaceInfo spaceInfo = new TableSpaceInfo();
        spaceInfo.setTableName(tableName);
        spaceInfo.setTableRows(tableRows);
        spaceInfo.setDataLength(dataLength);
        spaceInfo.setIndexLength(indexLength);
        spaceInfo.formatSizes(); // 格式化大小信息
        return spaceInfo;
    }

    /**
     * 按 qy_code 分组分析和空间估算（预先收集记录ID）
     */
    private void analyzeQyCodeGroups(CleanupResult result) {
        log.info("开始按 qy_code 分组分析和空间估算（包含记录ID预收集）");
        Map<String, Long> qyCodeStats = new HashMap<>();
        // 清空之前的缓存
        recordIdCache.clear();
        // 预先收集所有记录ID，避免后续重复查询
        log.debug("预先收集记录ID，避免后续重复查询");
        collectAndCacheAllRecordIds();

        // 分别收集每张表的数据，保持表级别的分布信息
        Map<String, QyCodeTableDistribution> qyCodeDistributions = new HashMap<>();
        // 分析 tb_commonprojcategory_standards_0 表
        String table1Name = "tb_commonprojcategory_standards_0";
        List<QyCodeGroupStats> result1 = dataCleanupMapper.getQyCodeGroupStats(table1Name);
        for (QyCodeGroupStats stats : result1) {
            QyCodeTableDistribution distribution = qyCodeDistributions.computeIfAbsent(stats.getQyCode(), QyCodeTableDistribution::new);
            distribution.addTableRecords(table1Name, stats.getRecordCount());
            qyCodeStats.put(stats.getQyCode(), qyCodeStats.getOrDefault(stats.getQyCode(), 0L) + stats.getRecordCount());
        }

        // 分析 tb_commonprojcategory_standards 表
        String table2Name = "tb_commonprojcategory_standards";
        List<QyCodeGroupStats> result2 = dataCleanupMapper.getQyCodeGroupStats(table2Name);
        for (QyCodeGroupStats stats : result2) {
            QyCodeTableDistribution distribution = qyCodeDistributions.computeIfAbsent(stats.getQyCode(), QyCodeTableDistribution::new);
            distribution.addTableRecords(table2Name, stats.getRecordCount());
            qyCodeStats.put(stats.getQyCode(), qyCodeStats.getOrDefault(stats.getQyCode(), 0L) + stats.getRecordCount());
        }

        result.setQyCodeGroupStats(qyCodeStats);
        // 计算企业级空间估算（使用新的按表分别计算的方法）
        Map<String, QyCodeSpaceInfo> qyCodeSpaceStats = calculateQyCodeSpaceEstimationByTable(qyCodeDistributions, result.getTableSpaceInfoList());
        result.setQyCodeSpaceStats(qyCodeSpaceStats);
        log.info("qy_code 分组分析完成，共 {} 个不同的企业编码，已缓存 {} 个企业的记录ID", qyCodeStats.size(), recordIdCache.size());
        // 输出空间占用最多的前50个企业编码
        List<QyCodeSpaceInfo> topSpaceConsumers = qyCodeSpaceStats.values().stream()
                .sorted((a, b) -> Long.compare(b.getEstimatedSpaceBytes(), a.getEstimatedSpaceBytes()))
                .limit(50)
                .toList();
        log.info("空间占用最多的前50个企业编码:");
        for (int i = 0; i < topSpaceConsumers.size(); i++) {
            QyCodeSpaceInfo spaceInfo = topSpaceConsumers.get(i);
            log.info("{}. qy_code: {}, 记录数: {}, 估算空间: {}", i + 1, spaceInfo.getQyCode(), spaceInfo.getRecordCount(), spaceInfo.getEstimatedSpaceFormatted());
        }
    }

    /**
     * 预先收集并缓存所有记录ID，避免后续重复查询
     */
    private void collectAndCacheAllRecordIds() {
        long startTime = System.currentTimeMillis();
        // 收集 tb_commonprojcategory_standards_0 表的记录ID
        List<RecordIdInfo> recordIds1 = dataCleanupMapper.collectAllRecordIds("tb_commonprojcategory_standards_0");
        for (RecordIdInfo recordInfo : recordIds1) {
            recordIdCache.computeIfAbsent(recordInfo.getQyCode(), k -> new ArrayList<>()).add(recordInfo.getId());
        }
        // 收集 tb_commonprojcategory_standards 表的记录ID
        List<RecordIdInfo> recordIds2 = dataCleanupMapper.collectAllRecordIds("tb_commonprojcategory_standards");
        for (RecordIdInfo recordInfo : recordIds2) {
            recordIdCache.computeIfAbsent(recordInfo.getQyCode(), k -> new ArrayList<>()).add(recordInfo.getId());
        }
        long endTime = System.currentTimeMillis();
        long totalRecords = recordIds1.size() + recordIds2.size();
        log.info("记录ID预收集完成，耗时: {}ms, 总记录数: {}, 缓存企业数: {}", endTime - startTime, totalRecords, recordIdCache.size());
    }

    /**
     * 计算企业编码的空间估算（重构版：按表分别计算空间占用）
     */
    private Map<String, QyCodeSpaceInfo> calculateQyCodeSpaceEstimationByTable(Map<String, QyCodeTableDistribution> qyCodeDistributions, List<TableSpaceInfo> tableSpaceInfoList) {
        Map<String, QyCodeSpaceInfo> qyCodeSpaceStats = new HashMap<>();
        // 计算每张表的平均行大小（基于预设数据）
        Map<String, Double> tableAvgRowSizes = new HashMap<>();
        for (TableSpaceInfo tableInfo : tableSpaceInfoList) {
            long tableRows = tableInfo.getTableRows();
            long tableSpaceBytes = tableInfo.getDataLength() + tableInfo.getIndexLength();
            if (tableRows > 0) {
                double avgRowSize = (double) tableSpaceBytes / tableRows;
                tableAvgRowSizes.put(tableInfo.getTableName(), avgRowSize);
                log.debug("表 {} 平均行大小: {} bytes (基于预设数据)", tableInfo.getTableName(), Math.round(avgRowSize));
            }
        }
        // 计算每个企业编码在各表中的空间占用
        for (QyCodeTableDistribution distribution : qyCodeDistributions.values()) {
            String qyCode = distribution.getQyCode();
            long totalSpaceBytes = 0;
            // 分别计算在每张表中的空间占用
            for (Map.Entry<String, Long> entry : distribution.getTableRecordCounts().entrySet()) {
                String tableName = entry.getKey();
                long recordCount = entry.getValue();
                Double avgRowSize = tableAvgRowSizes.get(tableName);
                if (avgRowSize != null && recordCount > 0) {
                    long tableSpaceBytes = Math.round(recordCount * avgRowSize);
                    distribution.addTableSpace(tableName, tableSpaceBytes);
                    totalSpaceBytes += tableSpaceBytes;
                    log.debug("企业 {} 在表 {} 中: {} 条记录, {} 空间", qyCode, tableName, recordCount, formatBytes(tableSpaceBytes));
                }
            }
            // 创建QyCodeSpaceInfo对象
            QyCodeSpaceInfo spaceInfo = new QyCodeSpaceInfo(qyCode, distribution.getTotalRecordCount(), totalSpaceBytes, null);
            qyCodeSpaceStats.put(qyCode, spaceInfo);
        }
        // 计算实际的总空间（基于当前查询的数据）
        long actualTotalSpaceBytes = qyCodeSpaceStats.values().stream().mapToLong(QyCodeSpaceInfo::getEstimatedSpaceBytes).sum();
        // 计算空间占用百分比（基于实际计算的总空间）
        for (QyCodeSpaceInfo spaceInfo : qyCodeSpaceStats.values()) {
            if (actualTotalSpaceBytes > 0) {
                double percentage = (double) spaceInfo.getEstimatedSpaceBytes() * 100 / actualTotalSpaceBytes;
                spaceInfo.setSpacePercentage(percentage);
            }
        }
        log.info("按表分别计算空间估算完成 - 企业编码数: {}, 实际总空间: {}", qyCodeSpaceStats.size(), formatBytes(actualTotalSpaceBytes));
        return qyCodeSpaceStats;
    }

    /**
     * 判断账号类型并更新空间统计信息
     */
    private void determineAccountTypes(CleanupResult result) {
        log.info("开始判断账号类型");
        Map<String, AccountTypeEnum> qyCodeToAccountType = new HashMap<>();
        Map<String, Long> qyCodeStats = result.getQyCodeGroupStats();
        Map<String, QyCodeSpaceInfo> qyCodeSpaceStats = result.getQyCodeSpaceStats();
        for (String qyCode : qyCodeStats.keySet()) {
            try {
                // 预过滤：只有纯数字的qyCode才可能是个人账号，需要进行账号类型判断
                if (!isNumeric(qyCode)) {
                    // 非纯数字的qyCode直接跳过，不进行账号类型判断
                    log.info("跳过非纯数字的qy_code: {} (企业编码格式)", qyCode);
                    qyCodeToAccountType.put(qyCode, AccountTypeEnum.ENTERPRISE_TYPE);
                    // 更新空间统计信息中的账号类型
                    QyCodeSpaceInfo spaceInfo = qyCodeSpaceStats.get(qyCode);
                    if (spaceInfo != null) {
                        spaceInfo.setAccountType(AccountTypeEnum.ENTERPRISE_TYPE);
                    }
                    continue;
                }
                // 判断账号类型（仅对纯数字的qyCode）
                AccountTypeEnum accountType = accountTypeService.getAccountType(qyCode);
                qyCodeToAccountType.put(qyCode, accountType);
                // 更新空间统计信息中的账号类型
                QyCodeSpaceInfo spaceInfo = qyCodeSpaceStats.get(qyCode);
                if (spaceInfo != null) {
                    spaceInfo.setAccountType(accountType);
                }
                log.info("qy_code: {}, globalId: {}, accountType: {}", qyCode, qyCode, accountType.getValue());
            } catch (Exception e) {
                qyCodeToAccountType.put(qyCode, null);
                log.error("判断 qy_code {} 的账号类型失败", qyCode, e);
            }
        }
        result.setQyCodeToAccountType(qyCodeToAccountType);
        // 统计各账号类型的数量和空间占用
        long personalAccountCount = 0;
        long enterpriseAccountCount = 0;
        long personalAccountSpaceBytes = 0;
        long enterpriseAccountSpaceBytes = 0;

        for (Map.Entry<String, AccountTypeEnum> entry : qyCodeToAccountType.entrySet()) {
            String qyCode = entry.getKey();
            AccountTypeEnum accountType = entry.getValue();
            QyCodeSpaceInfo spaceInfo = qyCodeSpaceStats.get(qyCode);

            if (AccountTypeEnum.PERSONAL_TYPE.equals(accountType)) {
                personalAccountCount++;
                if (spaceInfo != null) {
                    personalAccountSpaceBytes += spaceInfo.getEstimatedSpaceBytes();
                }
            } else if (AccountTypeEnum.ENTERPRISE_TYPE.equals(accountType)) {
                enterpriseAccountCount++;
                if (spaceInfo != null) {
                    enterpriseAccountSpaceBytes += spaceInfo.getEstimatedSpaceBytes();
                }
            }
        }
        log.info("账号类型判断完成 - 个人账号: {} 个 ({}), 企业账号: {} 个 ({})", personalAccountCount, formatBytes(personalAccountSpaceBytes), enterpriseAccountCount, formatBytes(enterpriseAccountSpaceBytes));
    }

    /**
     * 收集个人账号数据和空间统计
     */
    private void collectPersonalAccountData(CleanupResult result) {
        log.info("开始收集个人账号数据和空间统计");
        List<PersonalAccountData> personalAccountDataList = new ArrayList<>();
        Map<String, AccountTypeEnum> qyCodeToAccountType = result.getQyCodeToAccountType();
        Map<String, QyCodeSpaceInfo> qyCodeSpaceStats = result.getQyCodeSpaceStats();
        for (Map.Entry<String, AccountTypeEnum> entry : qyCodeToAccountType.entrySet()) {
            String qyCode = entry.getKey();
            AccountTypeEnum accountType = entry.getValue();
            // 只处理个人账号
            if (AccountTypeEnum.PERSONAL_TYPE.equals(accountType)) {
                try {
                    // 收集该 qy_code 的所有记录ID
                    List<Long> recordIds = collectRecordIdsByQyCode(qyCode);
                    long recordCount = recordIds.size();
                    PersonalAccountData personalData = new PersonalAccountData(qyCode, qyCode, recordCount, recordIds);
                    // 关联空间信息
                    QyCodeSpaceInfo spaceInfo = qyCodeSpaceStats.get(qyCode);
                    if (spaceInfo != null) {
                        personalData.setSpaceInfo(spaceInfo);
                    }
                    personalAccountDataList.add(personalData);
                    String spaceInfoStr = spaceInfo != null ? spaceInfo.getEstimatedSpaceFormatted() : "未知";
                    log.info("收集到个人账号数据 - qy_code: {}, globalId: {}, 记录数: {}, 估算空间: {}", qyCode, qyCode, recordCount, spaceInfoStr);
                } catch (Exception e) {
                    log.error("收集个人账号数据失败 - qy_code: {}", qyCode, e);
                }
            }
        }
        result.setPersonalAccountDataList(personalAccountDataList);
        // 计算个人账号总空间占用
        long totalPersonalSpaceBytes = personalAccountDataList.stream().filter(data -> data.getSpaceInfo() != null).mapToLong(data -> data.getSpaceInfo().getEstimatedSpaceBytes()).sum();
        log.info("个人账号数据收集完成，共收集到 {} 个个人账号的数据，总空间占用: {}", personalAccountDataList.size(), formatBytes(totalPersonalSpaceBytes));
    }

    /**
     * 生成空间汇总信息
     */
    private void generateSpaceSummary(CleanupResult result) {
        log.info("开始生成空间汇总信息");

        List<TableSpaceInfo> tableSpaceInfoList = result.getTableSpaceInfoList();
        Map<String, QyCodeSpaceInfo> qyCodeSpaceStats = result.getQyCodeSpaceStats();
        // 计算总空间
        long totalDataBytes = tableSpaceInfoList.stream().mapToLong(TableSpaceInfo::getDataLength).sum();
        long totalIndexBytes = tableSpaceInfoList.stream().mapToLong(TableSpaceInfo::getIndexLength).sum();
        // 计算个人账号和企业账号的空间占用
        long personalAccountSpaceBytes = 0;
        long enterpriseAccountSpaceBytes = 0;
        for (QyCodeSpaceInfo spaceInfo : qyCodeSpaceStats.values()) {
            if (AccountTypeEnum.PERSONAL_TYPE.equals(spaceInfo.getAccountType())) {
                personalAccountSpaceBytes += spaceInfo.getEstimatedSpaceBytes();
            } else if (AccountTypeEnum.ENTERPRISE_TYPE.equals(spaceInfo.getAccountType())) {
                enterpriseAccountSpaceBytes += spaceInfo.getEstimatedSpaceBytes();
            }
        }
        SpaceSummary spaceSummary = new SpaceSummary(totalDataBytes, totalIndexBytes, personalAccountSpaceBytes, enterpriseAccountSpaceBytes);
        result.setSpaceSummary(spaceSummary);
        log.info("空间汇总信息生成完成 - 总空间: {}, 个人账号: {} ({}%), 企业账号: {} ({}%)",
                spaceSummary.getTotalSpaceFormatted(),
                spaceSummary.getPersonalAccountSpaceFormatted(), spaceSummary.getPersonalAccountPercentage(),
                spaceSummary.getEnterpriseAccountSpaceFormatted(), spaceSummary.getEnterpriseAccountPercentage());
    }

    /**
     * 执行数据清理操作
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeDataCleanup(CleanupResult result) {
        log.info("开始执行数据清理操作");
        List<PersonalAccountData> personalAccountDataList = result.getPersonalAccountDataList();
        long totalDeletedRecords = 0;
        long totalReleasedSpaceBytes = 0;
        for (PersonalAccountData personalData : personalAccountDataList) {
            try {
                // 删除 tb_commonprojcategory_standards_0 表中的数据
                long deleted1 = deleteRecordsByQyCode("tb_commonprojcategory_standards_0", personalData.getQyCode());
                // 删除 tb_commonprojcategory_standards 表中的数据
                long deleted2 = deleteRecordsByQyCode("tb_commonprojcategory_standards", personalData.getQyCode());
                long deletedCount = deleted1 + deleted2;
                totalDeletedRecords += deletedCount;
                // 计算释放的空间
                if (personalData.getSpaceInfo() != null) {
                    totalReleasedSpaceBytes += personalData.getSpaceInfo().getEstimatedSpaceBytes();
                }
                String spaceInfo = personalData.getSpaceInfo() != null ? personalData.getSpaceInfo().getEstimatedSpaceFormatted() : "未知";
                log.info("删除个人账号数据完成 - qy_code: {}, 删除记录数: {} (表1: {}, 表2: {}), 释放空间: {}", personalData.getQyCode(), deletedCount, deleted1, deleted2, spaceInfo);
            } catch (Exception e) {
                log.error("删除个人账号数据失败 - qy_code: {}", personalData.getQyCode(), e);
                throw new RuntimeException("删除数据失败: " + e.getMessage(), e);
            }
        }
        result.setCleanupExecuted(true);
        result.setDeletedRecords(totalDeletedRecords);
        result.setMessage(String.format("数据清理完成，共删除 %d 条个人账号相关记录，释放空间约 %s", totalDeletedRecords, formatBytes(totalReleasedSpaceBytes)));
        log.info("数据清理操作完成，共删除 {} 条记录，释放空间约 {}", totalDeletedRecords, formatBytes(totalReleasedSpaceBytes));
    }

    /**
     * 收集指定 qy_code 的所有记录ID
     */
    private List<Long> collectRecordIdsByQyCode(String qyCode) {
        // 优化：直接从缓存中获取记录ID，避免重复数据库查询
        List<Long> cachedRecordIds = recordIdCache.get(qyCode);
        if (cachedRecordIds != null && !cachedRecordIds.isEmpty()) {
            log.debug("从缓存获取 qy_code {} 的记录ID，数量: {}", qyCode, cachedRecordIds.size());
            return new ArrayList<>(cachedRecordIds); // 返回副本，避免外部修改缓存
        }
        // 如果缓存中没有，则回退到数据库查询（兜底方案）
        log.warn("缓存中未找到 qy_code {} 的记录ID，回退到数据库查询", qyCode);
        return collectRecordIdsByQyCodeFromDatabase(qyCode);
    }

    /**
     * 从数据库查询指定 qy_code 的记录ID（兜底方案）
     */
    private List<Long> collectRecordIdsByQyCodeFromDatabase(String qyCode) {
        List<Long> recordIds = new ArrayList<>();
        // 收集 tb_commonprojcategory_standards_0 表的记录ID
        List<RecordIdInfo> result1 = dataCleanupMapper.collectRecordIdsByQyCode("tb_commonprojcategory_standards_0", qyCode);
        for (RecordIdInfo recordInfo : result1) {
            recordIds.add(recordInfo.getId());
        }
        // 收集 tb_commonprojcategory_standards 表的记录ID
        List<RecordIdInfo> result2 = dataCleanupMapper.collectRecordIdsByQyCode("tb_commonprojcategory_standards", qyCode);
        for (RecordIdInfo recordInfo : result2) {
            recordIds.add(recordInfo.getId());
        }
        log.debug("从数据库查询 qy_code {} 的记录ID，数量: {}", qyCode, recordIds.size());
        return recordIds;
    }

    /**
     * 删除指定表中指定 qy_code 的记录（软删除）
     */
    private long deleteRecordsByQyCode(String tableName, String qyCode) {
        int deletedCount = dataCleanupMapper.deleteRecordsByQyCode(tableName, qyCode);
        log.info("表 {} 中 qy_code {} 的记录已删除，影响行数: {}", tableName, qyCode, deletedCount);
        return deletedCount;
    }

    // ==================== 新增：支持 zb_standards_trade 表的方法 ====================

    /**
     * 统计 zb_standards_trade 表的数据量和空间信息
     */
    private void statisticsTradeData(CustomerCodeCleanupResult result) {
        log.info("开始统计 zb_standards_trade 表的数据量和表空间信息");
        // 统计记录数量
        Long count = dataCleanupMapper.countTableRecords("zb_standards_trade");
        long totalRecords = count != null ? count : 0;
        result.setTotalRecords(totalRecords);
        // 统计表空间信息（使用预设数据，实际项目中可以查询真实的表空间信息）
        result.setTableSpaceInfoList(getTradeTableSpaceInfo());
        // 输出统计日志
        log.info("zb_standards_trade 表数据量统计完成 - 总记录数: {}", totalRecords);
    }

    /**
     * 获取 zb_standards_trade 表空间信息（使用预设静态数据）
     */
    private List<TableSpaceInfo> getTradeTableSpaceInfo() {
        log.info("使用预设数据获取 zb_standards_trade 表空间信息");
        List<TableSpaceInfo> tableSpaceInfoList = new ArrayList<>();
        // 基于实际情况创建表空间信息，这里使用估算值
        // 实际项目中应该查询 information_schema.tables 获取真实数据
        TableSpaceInfo info = createTableSpaceInfo(
                "zb_standards_trade", 2826581L,
                422543360L,
                491290624L
        );
        tableSpaceInfoList.add(info);
        // 输出表空间统计日志
        for (TableSpaceInfo spaceInfo : tableSpaceInfoList) {
            log.info("表空间信息 - 表名: {}, 记录数: {}, 数据大小: {}, 索引大小: {}, 总大小: {}",
                    spaceInfo.getTableName(), spaceInfo.getTableRows(),
                    spaceInfo.getDataLengthFormatted(), spaceInfo.getIndexLengthFormatted(),
                    spaceInfo.getTotalLengthFormatted());
        }
        return tableSpaceInfoList;
    }

    /**
     * 按 customer_code 分组分析和空间估算（预先收集记录ID）
     */
    private void analyzeCustomerCodeGroups(CustomerCodeCleanupResult result) {
        log.info("开始按 customer_code 分组分析和空间估算（包含记录ID预收集）");
        Map<String, Long> customerCodeStats = new HashMap<>();
        // 清空之前的缓存
        customerCodeRecordIdCache.clear();
        // 预先收集所有记录ID，避免后续重复查询
        log.debug("预先收集记录ID，避免后续重复查询");
        collectAndCacheAllRecordIdsByCustomerCode();

        // 收集 zb_standards_trade 表的数据
        String tableName = "zb_standards_trade";
        List<CustomerCodeGroupStats> resultList = dataCleanupMapper.getCustomerCodeGroupStats(tableName);
        for (CustomerCodeGroupStats stats : resultList) {
            customerCodeStats.put(stats.getCustomerCode(), stats.getRecordCount());
        }
        result.setCustomerCodeGroupStats(customerCodeStats);
        // 计算企业级空间估算
        Map<String, CustomerCodeSpaceInfo> customerCodeSpaceStats = calculateCustomerCodeSpaceEstimation(customerCodeStats, result.getTableSpaceInfoList());
        result.setCustomerCodeSpaceStats(customerCodeSpaceStats);
        log.info("customer_code 分组分析完成，共 {} 个不同的企业编码，已缓存 {} 个企业的记录ID", customerCodeStats.size(), customerCodeRecordIdCache.size());
        // 输出空间占用最多的前50个企业编码
        List<CustomerCodeSpaceInfo> topSpaceConsumers = customerCodeSpaceStats.values().stream()
                .sorted((a, b) -> Long.compare(b.getEstimatedSpaceBytes(), a.getEstimatedSpaceBytes()))
                .limit(50)
                .toList();
        log.info("空间占用最多的前50个企业编码:");
        for (int i = 0; i < topSpaceConsumers.size(); i++) {
            CustomerCodeSpaceInfo spaceInfo = topSpaceConsumers.get(i);
            log.info("{}. customer_code: {}, 记录数: {}, 估算空间: {}",
                    i + 1, spaceInfo.getCustomerCode(), spaceInfo.getRecordCount(),
                    spaceInfo.getEstimatedSpaceFormatted());
        }
    }

    /**
     * 预先收集并缓存所有customer_code的记录ID，避免后续重复查询
     */
    private void collectAndCacheAllRecordIdsByCustomerCode() {
        long startTime = System.currentTimeMillis();
        // 收集 zb_standards_trade 表的记录ID
        List<CustomerCodeRecordIdInfo> recordIds = dataCleanupMapper.collectAllRecordIdsByCustomerCode("zb_standards_trade");
        for (CustomerCodeRecordIdInfo recordInfo : recordIds) {
            customerCodeRecordIdCache.computeIfAbsent(recordInfo.getCustomerCode(), k -> new ArrayList<>()).add(recordInfo.getId());
        }
        long endTime = System.currentTimeMillis();
        long totalRecords = recordIds.size();
        log.info("customer_code记录ID预收集完成，耗时: {}ms, 总记录数: {}, 缓存企业数: {}", endTime - startTime, totalRecords, customerCodeRecordIdCache.size());
    }

    /**
     * 计算企业编码的空间估算
     */
    private Map<String, CustomerCodeSpaceInfo> calculateCustomerCodeSpaceEstimation(Map<String, Long> customerCodeStats, List<TableSpaceInfo> tableSpaceInfoList) {
        Map<String, CustomerCodeSpaceInfo> customerCodeSpaceStats = new HashMap<>();
        // 计算表的平均行大小（基于预设数据）
        double avgRowSize = 0;
        for (TableSpaceInfo tableInfo : tableSpaceInfoList) {
            long tableRows = tableInfo.getTableRows();
            long tableSpaceBytes = tableInfo.getDataLength() + tableInfo.getIndexLength();
            if (tableRows > 0) {
                avgRowSize = (double) tableSpaceBytes / tableRows;
                log.debug("表 {} 平均行大小: {} bytes (基于预设数据)", tableInfo.getTableName(), Math.round(avgRowSize));
                break; // zb_standards_trade只有一张表
            }
        }

        // 计算每个企业编码的空间占用
        for (Map.Entry<String, Long> entry : customerCodeStats.entrySet()) {
            String customerCode = entry.getKey();
            long recordCount = entry.getValue();
            long estimatedSpaceBytes = Math.round(recordCount * avgRowSize);
            CustomerCodeSpaceInfo spaceInfo = new CustomerCodeSpaceInfo(customerCode, recordCount, estimatedSpaceBytes, null);
            customerCodeSpaceStats.put(customerCode, spaceInfo);
        }

        // 计算实际的总空间（基于当前查询的数据）
        long actualTotalSpaceBytes = customerCodeSpaceStats.values().stream().mapToLong(CustomerCodeSpaceInfo::getEstimatedSpaceBytes).sum();
        // 计算空间占用百分比（基于实际计算的总空间）
        for (CustomerCodeSpaceInfo spaceInfo : customerCodeSpaceStats.values()) {
            if (actualTotalSpaceBytes > 0) {
                double percentage = (double) spaceInfo.getEstimatedSpaceBytes() * 100 / actualTotalSpaceBytes;
                spaceInfo.setSpacePercentage(percentage);
            }
        }
        log.info("customer_code空间估算完成 - 企业编码数: {}, 实际总空间: {}",
                customerCodeSpaceStats.size(), formatBytes(actualTotalSpaceBytes));
        return customerCodeSpaceStats;
    }

    /**
     * 判断账号类型并更新空间统计信息（基于customer_code）
     */
    private void determineAccountTypesByCustomerCode(CustomerCodeCleanupResult result) {
        log.info("开始判断账号类型（基于customer_code）");
        Map<String, AccountTypeEnum> customerCodeToAccountType = new HashMap<>();
        Map<String, Long> customerCodeStats = result.getCustomerCodeGroupStats();
        Map<String, CustomerCodeSpaceInfo> customerCodeSpaceStats = result.getCustomerCodeSpaceStats();

        for (String customerCode : customerCodeStats.keySet()) {
            try {
                // 预过滤：只有纯数字的customerCode才可能是个人账号，需要进行账号类型判断
                if (!isNumeric(customerCode)) {
                    // 非纯数字的customerCode直接跳过，不进行账号类型判断
                    log.info("跳过非纯数字的customer_code: {} (企业编码格式)", customerCode);
                    customerCodeToAccountType.put(customerCode, AccountTypeEnum.ENTERPRISE_TYPE);
                    // 更新空间统计信息中的账号类型
                    CustomerCodeSpaceInfo spaceInfo = customerCodeSpaceStats.get(customerCode);
                    if (spaceInfo != null) {
                        spaceInfo.setAccountType(AccountTypeEnum.ENTERPRISE_TYPE);
                    }
                    continue;
                }
                // 判断账号类型（仅对纯数字的customerCode）
                AccountTypeEnum accountType = accountTypeService.getAccountType(customerCode);
                customerCodeToAccountType.put(customerCode, accountType);
                // 更新空间统计信息中的账号类型
                CustomerCodeSpaceInfo spaceInfo = customerCodeSpaceStats.get(customerCode);
                if (spaceInfo != null) {
                    spaceInfo.setAccountType(accountType);
                }
                log.info("customer_code: {}, globalId: {}, accountType: {}", customerCode, customerCode, accountType.getValue());
            } catch (Exception e) {
                customerCodeToAccountType.put(customerCode, null);
                log.error("判断 customer_code {} 的账号类型失败", customerCode, e);
            }
        }
        result.setCustomerCodeToAccountType(customerCodeToAccountType);

        // 统计各账号类型的数量和空间占用
        long personalAccountCount = 0;
        long enterpriseAccountCount = 0;
        long personalAccountSpaceBytes = 0;
        long enterpriseAccountSpaceBytes = 0;

        for (Map.Entry<String, AccountTypeEnum> entry : customerCodeToAccountType.entrySet()) {
            String customerCode = entry.getKey();
            AccountTypeEnum accountType = entry.getValue();
            CustomerCodeSpaceInfo spaceInfo = customerCodeSpaceStats.get(customerCode);

            if (AccountTypeEnum.PERSONAL_TYPE.equals(accountType)) {
                personalAccountCount++;
                if (spaceInfo != null) {
                    personalAccountSpaceBytes += spaceInfo.getEstimatedSpaceBytes();
                }
            } else if (AccountTypeEnum.ENTERPRISE_TYPE.equals(accountType)) {
                enterpriseAccountCount++;
                if (spaceInfo != null) {
                    enterpriseAccountSpaceBytes += spaceInfo.getEstimatedSpaceBytes();
                }
            }
        }
        log.info("账号类型判断完成 - 个人账号: {} 个 ({}), 企业账号: {} 个 ({})",
                personalAccountCount, formatBytes(personalAccountSpaceBytes),
                enterpriseAccountCount, formatBytes(enterpriseAccountSpaceBytes));
    }

    /**
     * 收集个人账号数据和空间统计（基于customer_code）
     */
    private void collectPersonalAccountDataByCustomerCode(CustomerCodeCleanupResult result) {
        log.info("开始收集个人账号数据和空间统计（基于customer_code）");
        List<PersonalAccountDataByCustomerCode> personalAccountDataList = new ArrayList<>();
        Map<String, AccountTypeEnum> customerCodeToAccountType = result.getCustomerCodeToAccountType();
        Map<String, CustomerCodeSpaceInfo> customerCodeSpaceStats = result.getCustomerCodeSpaceStats();
        for (Map.Entry<String, AccountTypeEnum> entry : customerCodeToAccountType.entrySet()) {
            String customerCode = entry.getKey();
            AccountTypeEnum accountType = entry.getValue();
            // 只处理个人账号
            if (AccountTypeEnum.PERSONAL_TYPE.equals(accountType)) {
                try {
                    // 收集该 customer_code 的所有记录ID
                    List<Long> recordIds = collectRecordIdsByCustomerCode(customerCode);
                    long recordCount = recordIds.size();
                    PersonalAccountDataByCustomerCode personalData = new PersonalAccountDataByCustomerCode(customerCode, customerCode, recordCount, recordIds);
                    // 关联空间信息
                    CustomerCodeSpaceInfo spaceInfo = customerCodeSpaceStats.get(customerCode);
                    if (spaceInfo != null) {
                        personalData.setSpaceInfo(spaceInfo);
                    }
                    personalAccountDataList.add(personalData);
                    String spaceInfoStr = spaceInfo != null ? spaceInfo.getEstimatedSpaceFormatted() : "未知";
                    log.info("收集到个人账号数据 - customer_code: {}, globalId: {}, 记录数: {}, 估算空间: {}", customerCode, customerCode, recordCount, spaceInfoStr);
                } catch (Exception e) {
                    log.error("收集个人账号数据失败 - customer_code: {}", customerCode, e);
                }
            }
        }
        result.setPersonalAccountDataList(personalAccountDataList);
        // 计算个人账号总空间占用
        long totalPersonalSpaceBytes = personalAccountDataList.stream()
                .filter(data -> data.getSpaceInfo() != null)
                .mapToLong(data -> data.getSpaceInfo().getEstimatedSpaceBytes())
                .sum();
        log.info("个人账号数据收集完成，共收集到 {} 个个人账号的数据，总空间占用: {}", personalAccountDataList.size(), formatBytes(totalPersonalSpaceBytes));
    }

    /**
     * 生成空间汇总信息（针对trade表）
     */
    private void generateSpaceSummaryForTrade(CustomerCodeCleanupResult result) {
        log.info("开始生成空间汇总信息（针对trade表）");

        List<TableSpaceInfo> tableSpaceInfoList = result.getTableSpaceInfoList();
        Map<String, CustomerCodeSpaceInfo> customerCodeSpaceStats = result.getCustomerCodeSpaceStats();
        // 计算总空间
        long totalDataBytes = tableSpaceInfoList.stream().mapToLong(TableSpaceInfo::getDataLength).sum();
        long totalIndexBytes = tableSpaceInfoList.stream().mapToLong(TableSpaceInfo::getIndexLength).sum();
        // 计算个人账号和企业账号的空间占用
        long personalAccountSpaceBytes = 0;
        long enterpriseAccountSpaceBytes = 0;
        for (CustomerCodeSpaceInfo spaceInfo : customerCodeSpaceStats.values()) {
            if (AccountTypeEnum.PERSONAL_TYPE.equals(spaceInfo.getAccountType())) {
                personalAccountSpaceBytes += spaceInfo.getEstimatedSpaceBytes();
            } else if (AccountTypeEnum.ENTERPRISE_TYPE.equals(spaceInfo.getAccountType())) {
                enterpriseAccountSpaceBytes += spaceInfo.getEstimatedSpaceBytes();
            }
        }
        SpaceSummary spaceSummary = new SpaceSummary(totalDataBytes, totalIndexBytes, personalAccountSpaceBytes, enterpriseAccountSpaceBytes);
        result.setSpaceSummary(spaceSummary);
        log.info("空间汇总信息生成完成 - 总空间: {}, 个人账号: {} ({}%), 企业账号: {} ({}%)",
                spaceSummary.getTotalSpaceFormatted(),
                spaceSummary.getPersonalAccountSpaceFormatted(), spaceSummary.getPersonalAccountPercentage(),
                spaceSummary.getEnterpriseAccountSpaceFormatted(), spaceSummary.getEnterpriseAccountPercentage());
    }

    /**
     * 执行trade表数据清理操作
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeTradeDataCleanup(CustomerCodeCleanupResult result) {
        log.info("开始执行trade表数据清理操作");
        List<PersonalAccountDataByCustomerCode> personalAccountDataList = result.getPersonalAccountDataList();
        long totalDeletedRecords = 0;
        long totalReleasedSpaceBytes = 0;
        for (PersonalAccountDataByCustomerCode personalData : personalAccountDataList) {
            try {
                // 删除 zb_standards_trade 表中的数据
                long deletedCount = deleteRecordsByCustomerCode("zb_standards_trade", personalData.getCustomerCode());
                totalDeletedRecords += deletedCount;
                // 计算释放的空间
                if (personalData.getSpaceInfo() != null) {
                    totalReleasedSpaceBytes += personalData.getSpaceInfo().getEstimatedSpaceBytes();
                }
                String spaceInfo = personalData.getSpaceInfo() != null ? personalData.getSpaceInfo().getEstimatedSpaceFormatted() : "未知";
                log.info("删除个人账号数据完成 - customer_code: {}, 删除记录数: {}, 释放空间: {}",
                        personalData.getCustomerCode(), deletedCount, spaceInfo);
            } catch (Exception e) {
                log.error("删除个人账号数据失败 - customer_code: {}", personalData.getCustomerCode(), e);
                throw new RuntimeException("删除数据失败: " + e.getMessage(), e);
            }
        }
        result.setCleanupExecuted(true);
        result.setDeletedRecords(totalDeletedRecords);
        result.setMessage(String.format("zb_standards_trade表数据清理完成，共删除 %d 条个人账号相关记录，释放空间约 %s",
                totalDeletedRecords, formatBytes(totalReleasedSpaceBytes)));
        log.info("trade表数据清理操作完成，共删除 {} 条记录，释放空间约 {}",
                totalDeletedRecords, formatBytes(totalReleasedSpaceBytes));
    }

    /**
     * 收集指定 customer_code 的所有记录ID
     */
    private List<Long> collectRecordIdsByCustomerCode(String customerCode) {
        // 优化：直接从缓存中获取记录ID，避免重复数据库查询
        List<Long> cachedRecordIds = customerCodeRecordIdCache.get(customerCode);
        if (cachedRecordIds != null && !cachedRecordIds.isEmpty()) {
            log.debug("从缓存获取 customer_code {} 的记录ID，数量: {}", customerCode, cachedRecordIds.size());
            return new ArrayList<>(cachedRecordIds); // 返回副本，避免外部修改缓存
        }
        // 如果缓存中没有，则回退到数据库查询（兜底方案）
        log.warn("缓存中未找到 customer_code {} 的记录ID，回退到数据库查询", customerCode);
        return collectRecordIdsByCustomerCodeFromDatabase(customerCode);
    }

    /**
     * 从数据库查询指定 customer_code 的记录ID（兜底方案）
     */
    private List<Long> collectRecordIdsByCustomerCodeFromDatabase(String customerCode) {
        List<Long> recordIds = new ArrayList<>();
        // 收集 zb_standards_trade 表的记录ID
        List<CustomerCodeRecordIdInfo> result = dataCleanupMapper.collectRecordIdsByCustomerCode("zb_standards_trade", customerCode);
        for (CustomerCodeRecordIdInfo recordInfo : result) {
            recordIds.add(recordInfo.getId());
        }
        log.debug("从数据库查询 customer_code {} 的记录ID，数量: {}", customerCode, recordIds.size());
        return recordIds;
    }

    /**
     * 删除指定表中指定 customer_code 的记录（软删除）
     */
    private long deleteRecordsByCustomerCode(String tableName, String customerCode) {
        int deletedCount = dataCleanupMapper.deleteRecordsByCustomerCode(tableName, customerCode);
        log.info("表 {} 中 customer_code {} 的记录已删除，影响行数: {}", tableName, customerCode, deletedCount);
        return deletedCount;
    }

    // ==================== 新增：支持 zb_standards_main_quantity 表的方法 ====================

    /**
     * 统计 zb_standards_main_quantity 表的数据量和空间信息
     */
    private void statisticsMainQuantityData(CustomerCodeCleanupResult result) {
        log.info("开始统计 zb_standards_main_quantity 表的数据量和表空间信息");
        // 统计记录数量
        Long count = dataCleanupMapper.countTableRecords("zb_standards_main_quantity");
        long totalRecords = count != null ? count : 0;
        result.setTotalRecords(totalRecords);
        // 统计表空间信息（使用预设数据，实际项目中可以查询真实的表空间信息）
        result.setTableSpaceInfoList(getMainQuantityTableSpaceInfo());
        // 输出统计日志
        log.info("zb_standards_main_quantity 表数据量统计完成 - 总记录数: {}", totalRecords);
    }

    /**
     * 获取 zb_standards_main_quantity 表空间信息（使用预设静态数据）
     */
    private List<TableSpaceInfo> getMainQuantityTableSpaceInfo() {
        log.info("使用预设数据获取 zb_standards_main_quantity 表空间信息");
        List<TableSpaceInfo> tableSpaceInfoList = new ArrayList<>();
        // 基于实际情况创建表空间信息，这里使用估算值
        // 实际项目中应该查询 information_schema.tables 获取真实数据
        TableSpaceInfo info = createTableSpaceInfo(
                "zb_standards_main_quantity", 37328258L,
                5955895296L,
                6120456192L
        );
        tableSpaceInfoList.add(info);
        // 输出表空间统计日志
        for (TableSpaceInfo spaceInfo : tableSpaceInfoList) {
            log.info("表空间信息 - 表名: {}, 记录数: {}, 数据大小: {}, 索引大小: {}, 总大小: {}",
                    spaceInfo.getTableName(), spaceInfo.getTableRows(),
                    spaceInfo.getDataLengthFormatted(), spaceInfo.getIndexLengthFormatted(),
                    spaceInfo.getTotalLengthFormatted());
        }
        return tableSpaceInfoList;
    }

    /**
     * 按 customer_code 分组分析和空间估算（针对 zb_standards_main_quantity 表）
     */
    private void analyzeMainQuantityCustomerCodeGroups(CustomerCodeCleanupResult result) {
        log.info("开始按 customer_code 分组分析和空间估算（zb_standards_main_quantity表，包含记录ID预收集）");
        Map<String, Long> customerCodeStats = new HashMap<>();
        // 清空之前的缓存
        customerCodeRecordIdCache.clear();
        // 预先收集所有记录ID，避免后续重复查询
        log.debug("预先收集记录ID，避免后续重复查询");
        collectAndCacheAllRecordIdsByCustomerCodeForMainQuantity();

        // 收集 zb_standards_main_quantity 表的数据
        String tableName = "zb_standards_main_quantity";
        List<CustomerCodeGroupStats> resultList = dataCleanupMapper.getCustomerCodeGroupStats(tableName);
        for (CustomerCodeGroupStats stats : resultList) {
            customerCodeStats.put(stats.getCustomerCode(), stats.getRecordCount());
        }

        result.setCustomerCodeGroupStats(customerCodeStats);
        // 计算企业级空间估算
        Map<String, CustomerCodeSpaceInfo> customerCodeSpaceStats = calculateCustomerCodeSpaceEstimationForMainQuantity(customerCodeStats, result.getTableSpaceInfoList());
        result.setCustomerCodeSpaceStats(customerCodeSpaceStats);
        log.info("customer_code 分组分析完成（zb_standards_main_quantity表），共 {} 个不同的企业编码，已缓存 {} 个企业的记录ID",
                customerCodeStats.size(), customerCodeRecordIdCache.size());
        // 输出空间占用最多的前50个企业编码
        List<CustomerCodeSpaceInfo> topSpaceConsumers = customerCodeSpaceStats.values().stream()
                .sorted((a, b) -> Long.compare(b.getEstimatedSpaceBytes(), a.getEstimatedSpaceBytes()))
                .limit(50)
                .toList();
        log.info("空间占用最多的前50个企业编码（zb_standards_main_quantity表）:");
        for (int i = 0; i < topSpaceConsumers.size(); i++) {
            CustomerCodeSpaceInfo spaceInfo = topSpaceConsumers.get(i);
            log.info("{}. customer_code: {}, 记录数: {}, 估算空间: {}",
                    i + 1, spaceInfo.getCustomerCode(), spaceInfo.getRecordCount(),
                    spaceInfo.getEstimatedSpaceFormatted());
        }
    }

    /**
     * 预先收集并缓存所有customer_code的记录ID（针对 zb_standards_main_quantity 表）
     */
    private void collectAndCacheAllRecordIdsByCustomerCodeForMainQuantity() {
        long startTime = System.currentTimeMillis();
        // 收集 zb_standards_main_quantity 表的记录ID
        List<CustomerCodeRecordIdInfo> recordIds = dataCleanupMapper.collectAllRecordIdsByCustomerCode("zb_standards_main_quantity");
        for (CustomerCodeRecordIdInfo recordInfo : recordIds) {
            customerCodeRecordIdCache.computeIfAbsent(recordInfo.getCustomerCode(), k -> new ArrayList<>()).add(recordInfo.getId());
        }
        long endTime = System.currentTimeMillis();
        long totalRecords = recordIds.size();
        log.info("customer_code记录ID预收集完成（zb_standards_main_quantity表），耗时: {}ms, 总记录数: {}, 缓存企业数: {}",
                endTime - startTime, totalRecords, customerCodeRecordIdCache.size());
    }

    /**
     * 计算企业编码的空间估算（针对 zb_standards_main_quantity 表）
     */
    private Map<String, CustomerCodeSpaceInfo> calculateCustomerCodeSpaceEstimationForMainQuantity(Map<String, Long> customerCodeStats, List<TableSpaceInfo> tableSpaceInfoList) {
        Map<String, CustomerCodeSpaceInfo> customerCodeSpaceStats = new HashMap<>();
        // 计算表的平均行大小（基于预设数据）
        double avgRowSize = 0;
        for (TableSpaceInfo tableInfo : tableSpaceInfoList) {
            long tableRows = tableInfo.getTableRows();
            long tableSpaceBytes = tableInfo.getDataLength() + tableInfo.getIndexLength();
            if (tableRows > 0) {
                avgRowSize = (double) tableSpaceBytes / tableRows;
                log.debug("表 {} 平均行大小: {} bytes (基于预设数据)", tableInfo.getTableName(), Math.round(avgRowSize));
                break; // zb_standards_main_quantity只有一张表
            }
        }

        // 计算每个企业编码的空间占用
        for (Map.Entry<String, Long> entry : customerCodeStats.entrySet()) {
            String customerCode = entry.getKey();
            long recordCount = entry.getValue();
            long estimatedSpaceBytes = Math.round(recordCount * avgRowSize);
            CustomerCodeSpaceInfo spaceInfo = new CustomerCodeSpaceInfo(customerCode, recordCount, estimatedSpaceBytes, null);
            customerCodeSpaceStats.put(customerCode, spaceInfo);
        }

        // 计算实际的总空间（基于当前查询的数据）
        long actualTotalSpaceBytes = customerCodeSpaceStats.values().stream().mapToLong(CustomerCodeSpaceInfo::getEstimatedSpaceBytes).sum();
        // 计算空间占用百分比（基于实际计算的总空间）
        for (CustomerCodeSpaceInfo spaceInfo : customerCodeSpaceStats.values()) {
            if (actualTotalSpaceBytes > 0) {
                double percentage = (double) spaceInfo.getEstimatedSpaceBytes() * 100 / actualTotalSpaceBytes;
                spaceInfo.setSpacePercentage(percentage);
            }
        }
        log.info("customer_code空间估算完成（zb_standards_main_quantity表） - 企业编码数: {}, 实际总空间: {}",
                customerCodeSpaceStats.size(), formatBytes(actualTotalSpaceBytes));
        return customerCodeSpaceStats;
    }

    /**
     * 判断账号类型并更新空间统计信息（针对 zb_standards_main_quantity 表）
     */
    private void determineAccountTypesByCustomerCodeForMainQuantity(CustomerCodeCleanupResult result) {
        log.info("开始判断账号类型（基于customer_code，zb_standards_main_quantity表）");
        Map<String, AccountTypeEnum> customerCodeToAccountType = new HashMap<>();
        Map<String, Long> customerCodeStats = result.getCustomerCodeGroupStats();
        Map<String, CustomerCodeSpaceInfo> customerCodeSpaceStats = result.getCustomerCodeSpaceStats();

        for (String customerCode : customerCodeStats.keySet()) {
            try {
                // 预过滤：只有纯数字的customerCode才可能是个人账号，需要进行账号类型判断
                if (!isNumeric(customerCode)) {
                    // 非纯数字的customerCode直接跳过，不进行账号类型判断
                    log.info("跳过非纯数字的customer_code: {} (企业编码格式)", customerCode);
                    customerCodeToAccountType.put(customerCode, AccountTypeEnum.ENTERPRISE_TYPE);
                    // 更新空间统计信息中的账号类型
                    CustomerCodeSpaceInfo spaceInfo = customerCodeSpaceStats.get(customerCode);
                    if (spaceInfo != null) {
                        spaceInfo.setAccountType(AccountTypeEnum.ENTERPRISE_TYPE);
                    }
                    continue;
                }
                // 判断账号类型（仅对纯数字的customerCode）
                AccountTypeEnum accountType = accountTypeService.getAccountType(customerCode);
                customerCodeToAccountType.put(customerCode, accountType);
                // 更新空间统计信息中的账号类型
                CustomerCodeSpaceInfo spaceInfo = customerCodeSpaceStats.get(customerCode);
                if (spaceInfo != null) {
                    spaceInfo.setAccountType(accountType);
                }
                log.info("customer_code: {}, globalId: {}, accountType: {}", customerCode, customerCode, accountType.getValue());
            } catch (Exception e) {
                customerCodeToAccountType.put(customerCode, null);
                log.error("判断 customer_code {} 的账号类型失败", customerCode, e);
            }
        }
        result.setCustomerCodeToAccountType(customerCodeToAccountType);

        // 统计各账号类型的数量和空间占用
        long personalAccountCount = 0;
        long enterpriseAccountCount = 0;
        long personalAccountSpaceBytes = 0;
        long enterpriseAccountSpaceBytes = 0;

        for (Map.Entry<String, AccountTypeEnum> entry : customerCodeToAccountType.entrySet()) {
            String customerCode = entry.getKey();
            AccountTypeEnum accountType = entry.getValue();
            CustomerCodeSpaceInfo spaceInfo = customerCodeSpaceStats.get(customerCode);

            if (AccountTypeEnum.PERSONAL_TYPE.equals(accountType)) {
                personalAccountCount++;
                if (spaceInfo != null) {
                    personalAccountSpaceBytes += spaceInfo.getEstimatedSpaceBytes();
                }
            } else if (AccountTypeEnum.ENTERPRISE_TYPE.equals(accountType)) {
                enterpriseAccountCount++;
                if (spaceInfo != null) {
                    enterpriseAccountSpaceBytes += spaceInfo.getEstimatedSpaceBytes();
                }
            }
        }
        log.info("账号类型判断完成（zb_standards_main_quantity表） - 个人账号: {} 个 ({}), 企业账号: {} 个 ({})",
                personalAccountCount, formatBytes(personalAccountSpaceBytes),
                enterpriseAccountCount, formatBytes(enterpriseAccountSpaceBytes));
    }

    /**
     * 收集个人账号数据和空间统计（针对 zb_standards_main_quantity 表）
     */
    private void collectPersonalAccountDataByCustomerCodeForMainQuantity(CustomerCodeCleanupResult result) {
        log.info("开始收集个人账号数据和空间统计（基于customer_code，zb_standards_main_quantity表）");
        List<PersonalAccountDataByCustomerCode> personalAccountDataList = new ArrayList<>();
        Map<String, AccountTypeEnum> customerCodeToAccountType = result.getCustomerCodeToAccountType();
        Map<String, CustomerCodeSpaceInfo> customerCodeSpaceStats = result.getCustomerCodeSpaceStats();

        for (Map.Entry<String, AccountTypeEnum> entry : customerCodeToAccountType.entrySet()) {
            String customerCode = entry.getKey();
            AccountTypeEnum accountType = entry.getValue();
            // 只处理个人账号
            if (AccountTypeEnum.PERSONAL_TYPE.equals(accountType)) {
                try {
                    // 收集该 customer_code 的所有记录ID
                    List<Long> recordIds = collectRecordIdsByCustomerCodeForMainQuantity(customerCode);
                    long recordCount = recordIds.size();
                    PersonalAccountDataByCustomerCode personalData = new PersonalAccountDataByCustomerCode(customerCode, customerCode, recordCount, recordIds);
                    // 关联空间信息
                    CustomerCodeSpaceInfo spaceInfo = customerCodeSpaceStats.get(customerCode);
                    if (spaceInfo != null) {
                        personalData.setSpaceInfo(spaceInfo);
                    }
                    personalAccountDataList.add(personalData);
                    String spaceInfoStr = spaceInfo != null ? spaceInfo.getEstimatedSpaceFormatted() : "未知";
                    log.info("收集到个人账号数据（zb_standards_main_quantity表） - customer_code: {}, globalId: {}, 记录数: {}, 估算空间: {}",
                            customerCode, customerCode, recordCount, spaceInfoStr);
                } catch (Exception e) {
                    log.error("收集个人账号数据失败（zb_standards_main_quantity表） - customer_code: {}", customerCode, e);
                }
            }
        }
        result.setPersonalAccountDataList(personalAccountDataList);
        // 计算个人账号总空间占用
        long totalPersonalSpaceBytes = personalAccountDataList.stream()
                .filter(data -> data.getSpaceInfo() != null)
                .mapToLong(data -> data.getSpaceInfo().getEstimatedSpaceBytes())
                .sum();
        log.info("个人账号数据收集完成（zb_standards_main_quantity表），共收集到 {} 个个人账号的数据，总空间占用: {}",
                personalAccountDataList.size(), formatBytes(totalPersonalSpaceBytes));
    }

    /**
     * 生成空间汇总信息（针对 zb_standards_main_quantity 表）
     */
    private void generateSpaceSummaryForMainQuantity(CustomerCodeCleanupResult result) {
        log.info("开始生成空间汇总信息（针对zb_standards_main_quantity表）");

        List<TableSpaceInfo> tableSpaceInfoList = result.getTableSpaceInfoList();
        Map<String, CustomerCodeSpaceInfo> customerCodeSpaceStats = result.getCustomerCodeSpaceStats();
        // 计算总空间
        long totalDataBytes = tableSpaceInfoList.stream().mapToLong(TableSpaceInfo::getDataLength).sum();
        long totalIndexBytes = tableSpaceInfoList.stream().mapToLong(TableSpaceInfo::getIndexLength).sum();
        // 计算个人账号和企业账号的空间占用
        long personalAccountSpaceBytes = 0;
        long enterpriseAccountSpaceBytes = 0;
        for (CustomerCodeSpaceInfo spaceInfo : customerCodeSpaceStats.values()) {
            if (AccountTypeEnum.PERSONAL_TYPE.equals(spaceInfo.getAccountType())) {
                personalAccountSpaceBytes += spaceInfo.getEstimatedSpaceBytes();
            } else if (AccountTypeEnum.ENTERPRISE_TYPE.equals(spaceInfo.getAccountType())) {
                enterpriseAccountSpaceBytes += spaceInfo.getEstimatedSpaceBytes();
            }
        }
        SpaceSummary spaceSummary = new SpaceSummary(totalDataBytes, totalIndexBytes, personalAccountSpaceBytes, enterpriseAccountSpaceBytes);
        result.setSpaceSummary(spaceSummary);
        log.info("空间汇总信息生成完成（zb_standards_main_quantity表） - 总空间: {}, 个人账号: {} ({}%), 企业账号: {} ({}%)",
                spaceSummary.getTotalSpaceFormatted(),
                spaceSummary.getPersonalAccountSpaceFormatted(), spaceSummary.getPersonalAccountPercentage(),
                spaceSummary.getEnterpriseAccountSpaceFormatted(), spaceSummary.getEnterpriseAccountPercentage());
    }

    /**
     * 执行 zb_standards_main_quantity 表数据清理操作
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeMainQuantityDataCleanup(CustomerCodeCleanupResult result) {
        log.info("开始执行zb_standards_main_quantity表数据清理操作");
        List<PersonalAccountDataByCustomerCode> personalAccountDataList = result.getPersonalAccountDataList();
        long totalDeletedRecords = 0;
        long totalReleasedSpaceBytes = 0;

        for (PersonalAccountDataByCustomerCode personalData : personalAccountDataList) {
            try {
                // 删除 zb_standards_main_quantity 表中的数据
                long deletedCount = deleteRecordsByCustomerCodeForMainQuantity("zb_standards_main_quantity", personalData.getCustomerCode());
                totalDeletedRecords += deletedCount;
                // 计算释放的空间
                if (personalData.getSpaceInfo() != null) {
                    totalReleasedSpaceBytes += personalData.getSpaceInfo().getEstimatedSpaceBytes();
                }
                String spaceInfo = personalData.getSpaceInfo() != null ? personalData.getSpaceInfo().getEstimatedSpaceFormatted() : "未知";
                log.info("删除个人账号数据完成（zb_standards_main_quantity表） - customer_code: {}, 删除记录数: {}, 释放空间: {}",
                        personalData.getCustomerCode(), deletedCount, spaceInfo);
            } catch (Exception e) {
                log.error("删除个人账号数据失败（zb_standards_main_quantity表） - customer_code: {}", personalData.getCustomerCode(), e);
                throw new RuntimeException("删除数据失败: " + e.getMessage(), e);
            }
        }
        result.setCleanupExecuted(true);
        result.setDeletedRecords(totalDeletedRecords);
        result.setMessage(String.format("zb_standards_main_quantity表数据清理完成，共删除 %d 条个人账号相关记录，释放空间约 %s",
                totalDeletedRecords, formatBytes(totalReleasedSpaceBytes)));
        log.info("zb_standards_main_quantity表数据清理操作完成，共删除 {} 条记录，释放空间约 {}",
                totalDeletedRecords, formatBytes(totalReleasedSpaceBytes));
    }

    /**
     * 收集指定 customer_code 的所有记录ID（针对 zb_standards_main_quantity 表）
     */
    private List<Long> collectRecordIdsByCustomerCodeForMainQuantity(String customerCode) {
        // 优化：直接从缓存中获取记录ID，避免重复数据库查询
        List<Long> cachedRecordIds = customerCodeRecordIdCache.get(customerCode);
        if (cachedRecordIds != null && !cachedRecordIds.isEmpty()) {
            log.debug("从缓存获取 customer_code {} 的记录ID（zb_standards_main_quantity表），数量: {}", customerCode, cachedRecordIds.size());
            return new ArrayList<>(cachedRecordIds); // 返回副本，避免外部修改缓存
        }
        // 如果缓存中没有，则回退到数据库查询（兜底方案）
        log.warn("缓存中未找到 customer_code {} 的记录ID（zb_standards_main_quantity表），回退到数据库查询", customerCode);
        return collectRecordIdsByCustomerCodeFromDatabaseForMainQuantity(customerCode);
    }

    /**
     * 从数据库查询指定 customer_code 的记录ID（针对 zb_standards_main_quantity 表，兜底方案）
     */
    private List<Long> collectRecordIdsByCustomerCodeFromDatabaseForMainQuantity(String customerCode) {
        List<Long> recordIds = new ArrayList<>();
        // 收集 zb_standards_main_quantity 表的记录ID
        List<CustomerCodeRecordIdInfo> result = dataCleanupMapper.collectRecordIdsByCustomerCode("zb_standards_main_quantity", customerCode);
        for (CustomerCodeRecordIdInfo recordInfo : result) {
            recordIds.add(recordInfo.getId());
        }
        log.debug("从数据库查询 customer_code {} 的记录ID（zb_standards_main_quantity表），数量: {}", customerCode, recordIds.size());
        return recordIds;
    }

    /**
     * 删除指定表中指定 customer_code 的记录（针对 zb_standards_main_quantity 表）
     */
    private long deleteRecordsByCustomerCodeForMainQuantity(String tableName, String customerCode) {
        int deletedCount = dataCleanupMapper.deleteRecordsByCustomerCode(tableName, customerCode);
        log.info("表 {} 中 customer_code {} 的记录已删除，影响行数: {}", tableName, customerCode, deletedCount);
        return deletedCount;
    }

    // ==================== 新增：支持 zb_project_feature_standards 表的方法 ====================

    /**
     * 统计 zb_project_feature_standards 表的数据量和空间信息
     */
    private void statisticsProjectFeatureData(CustomerCodeCleanupResult result) {
        log.info("开始统计 zb_project_feature_standards 表的数据量和表空间信息");
        // 统计记录数量
        Long count = dataCleanupMapper.countTableRecords("zb_project_feature_standards");
        long totalRecords = count != null ? count : 0;
        result.setTotalRecords(totalRecords);
        // 统计表空间信息（使用预设数据，实际项目中可以查询真实的表空间信息）
        result.setTableSpaceInfoList(getProjectFeatureTableSpaceInfo());
        // 输出统计日志
        log.info("zb_project_feature_standards 表数据量统计完成 - 总记录数: {}", totalRecords);
    }

    /**
     * 获取 zb_project_feature_standards 表空间信息（使用预设静态数据）
     */
    private List<TableSpaceInfo> getProjectFeatureTableSpaceInfo() {
        log.info("使用预设数据获取 zb_project_feature_standards 表空间信息");
        List<TableSpaceInfo> tableSpaceInfoList = new ArrayList<>();
        // 基于实际情况创建表空间信息，这里使用估算值
        // 实际项目中应该查询 information_schema.tables 获取真实数据
        TableSpaceInfo info = createTableSpaceInfo(
                "zb_project_feature_standards", 24939572L,
                97338458112L,
                5995724800L
        );
        //TableSpaceInfo info = createTableSpaceInfo(
        //        "zb_project_feature_standards", 252730L,
        //        861077504L,
        //        76152832L
        //);
        tableSpaceInfoList.add(info);
        // 输出表空间统计日志
        for (TableSpaceInfo spaceInfo : tableSpaceInfoList) {
            log.info("表空间信息 - 表名: {}, 记录数: {}, 数据大小: {}, 索引大小: {}, 总大小: {}",
                    spaceInfo.getTableName(), spaceInfo.getTableRows(),
                    spaceInfo.getDataLengthFormatted(), spaceInfo.getIndexLengthFormatted(),
                    spaceInfo.getTotalLengthFormatted());
        }
        return tableSpaceInfoList;
    }

    /**
     * 按 customer_code 分组分析和空间估算（针对 zb_project_feature_standards 表）
     */
    private void analyzeProjectFeatureCustomerCodeGroups(CustomerCodeCleanupResult result) {
        log.info("开始按 customer_code 分组分析和空间估算（zb_project_feature_standards表，包含记录ID预收集）");
        Map<String, Long> customerCodeStats = new HashMap<>();
        // 清空之前的缓存
        customerCodeRecordIdCache.clear();
        // 预先收集所有记录ID，避免后续重复查询
        log.debug("预先收集记录ID，避免后续重复查询");
        collectAndCacheAllRecordIdsByCustomerCodeForProjectFeature();

        // 收集 zb_project_feature_standards 表的数据
        String tableName = "zb_project_feature_standards";
        List<CustomerCodeGroupStats> resultList = dataCleanupMapper.getCustomerCodeGroupStats(tableName);
        for (CustomerCodeGroupStats stats : resultList) {
            customerCodeStats.put(stats.getCustomerCode(), stats.getRecordCount());
        }

        result.setCustomerCodeGroupStats(customerCodeStats);
        // 计算企业级空间估算
        Map<String, CustomerCodeSpaceInfo> customerCodeSpaceStats = calculateCustomerCodeSpaceEstimationForProjectFeature(customerCodeStats, result.getTableSpaceInfoList());
        result.setCustomerCodeSpaceStats(customerCodeSpaceStats);
        log.info("customer_code 分组分析完成（zb_project_feature_standards表），共 {} 个不同的企业编码，已缓存 {} 个企业的记录ID",
                customerCodeStats.size(), customerCodeRecordIdCache.size());
        // 输出空间占用最多的前50个企业编码
        List<CustomerCodeSpaceInfo> topSpaceConsumers = customerCodeSpaceStats.values().stream()
                .sorted((a, b) -> Long.compare(b.getEstimatedSpaceBytes(), a.getEstimatedSpaceBytes()))
                .limit(50)
                .toList();
        log.info("空间占用最多的前50个企业编码（zb_project_feature_standards表）:");
        for (int i = 0; i < topSpaceConsumers.size(); i++) {
            CustomerCodeSpaceInfo spaceInfo = topSpaceConsumers.get(i);
            log.info("{}. customer_code: {}, 记录数: {}, 估算空间: {}",
                    i + 1, spaceInfo.getCustomerCode(), spaceInfo.getRecordCount(),
                    spaceInfo.getEstimatedSpaceFormatted());
        }
    }

    /**
     * 预先收集并缓存所有customer_code的记录ID（针对 zb_project_feature_standards 表）
     */
    private void collectAndCacheAllRecordIdsByCustomerCodeForProjectFeature() {
        long startTime = System.currentTimeMillis();
        // 收集 zb_project_feature_standards 表的记录ID
        List<CustomerCodeRecordIdInfo> recordIds = dataCleanupMapper.collectAllRecordIdsByCustomerCode("zb_project_feature_standards");
        for (CustomerCodeRecordIdInfo recordInfo : recordIds) {
            customerCodeRecordIdCache.computeIfAbsent(recordInfo.getCustomerCode(), k -> new ArrayList<>()).add(recordInfo.getId());
        }
        long endTime = System.currentTimeMillis();
        long totalRecords = recordIds.size();
        log.info("customer_code记录ID预收集完成（zb_project_feature_standards表），耗时: {}ms, 总记录数: {}, 缓存企业数: {}",
                endTime - startTime, totalRecords, customerCodeRecordIdCache.size());
    }

    /**
     * 计算企业编码的空间估算（针对 zb_project_feature_standards 表）
     */
    private Map<String, CustomerCodeSpaceInfo> calculateCustomerCodeSpaceEstimationForProjectFeature(Map<String, Long> customerCodeStats, List<TableSpaceInfo> tableSpaceInfoList) {
        Map<String, CustomerCodeSpaceInfo> customerCodeSpaceStats = new HashMap<>();
        // 计算表的平均行大小（基于预设数据）
        double avgRowSize = 0;
        for (TableSpaceInfo tableInfo : tableSpaceInfoList) {
            long tableRows = tableInfo.getTableRows();
            long tableSpaceBytes = tableInfo.getDataLength() + tableInfo.getIndexLength();
            if (tableRows > 0) {
                avgRowSize = (double) tableSpaceBytes / tableRows;
                log.debug("表 {} 平均行大小: {} bytes (基于预设数据)", tableInfo.getTableName(), Math.round(avgRowSize));
                break; // zb_project_feature_standards只有一张表
            }
        }

        // 计算每个企业编码的空间占用
        for (Map.Entry<String, Long> entry : customerCodeStats.entrySet()) {
            String customerCode = entry.getKey();
            long recordCount = entry.getValue();
            long estimatedSpaceBytes = Math.round(recordCount * avgRowSize);
            CustomerCodeSpaceInfo spaceInfo = new CustomerCodeSpaceInfo(customerCode, recordCount, estimatedSpaceBytes, null);
            customerCodeSpaceStats.put(customerCode, spaceInfo);
        }

        // 计算实际的总空间（基于当前查询的数据）
        long actualTotalSpaceBytes = customerCodeSpaceStats.values().stream().mapToLong(CustomerCodeSpaceInfo::getEstimatedSpaceBytes).sum();
        // 计算空间占用百分比（基于实际计算的总空间）
        for (CustomerCodeSpaceInfo spaceInfo : customerCodeSpaceStats.values()) {
            if (actualTotalSpaceBytes > 0) {
                double percentage = (double) spaceInfo.getEstimatedSpaceBytes() * 100 / actualTotalSpaceBytes;
                spaceInfo.setSpacePercentage(percentage);
            }
        }
        log.info("customer_code空间估算完成（zb_project_feature_standards表） - 企业编码数: {}, 实际总空间: {}",
                customerCodeSpaceStats.size(), formatBytes(actualTotalSpaceBytes));
        return customerCodeSpaceStats;
    }

    /**
     * 判断账号类型并更新空间统计信息（针对 zb_project_feature_standards 表）
     */
    private void determineAccountTypesByCustomerCodeForProjectFeature(CustomerCodeCleanupResult result) {
        log.info("开始判断账号类型（基于customer_code，zb_project_feature_standards表）");
        Map<String, AccountTypeEnum> customerCodeToAccountType = new HashMap<>();
        Map<String, Long> customerCodeStats = result.getCustomerCodeGroupStats();
        Map<String, CustomerCodeSpaceInfo> customerCodeSpaceStats = result.getCustomerCodeSpaceStats();

        for (String customerCode : customerCodeStats.keySet()) {
            try {
                // 预过滤：只有纯数字的customerCode才可能是个人账号，需要进行账号类型判断
                if (!isNumeric(customerCode)) {
                    // 非纯数字的customerCode直接跳过，不进行账号类型判断
                    log.info("跳过非纯数字的customer_code: {} (企业编码格式)", customerCode);
                    customerCodeToAccountType.put(customerCode, AccountTypeEnum.ENTERPRISE_TYPE);
                    // 更新空间统计信息中的账号类型
                    CustomerCodeSpaceInfo spaceInfo = customerCodeSpaceStats.get(customerCode);
                    if (spaceInfo != null) {
                        spaceInfo.setAccountType(AccountTypeEnum.ENTERPRISE_TYPE);
                    }
                    continue;
                }
                // 判断账号类型（仅对纯数字的customerCode）
                AccountTypeEnum accountType = accountTypeService.getAccountType(customerCode);
                customerCodeToAccountType.put(customerCode, accountType);
                // 更新空间统计信息中的账号类型
                CustomerCodeSpaceInfo spaceInfo = customerCodeSpaceStats.get(customerCode);
                if (spaceInfo != null) {
                    spaceInfo.setAccountType(accountType);
                }
                log.info("customer_code: {}, globalId: {}, accountType: {}", customerCode, customerCode, accountType.getValue());
            } catch (Exception e) {
                customerCodeToAccountType.put(customerCode, null);
                log.error("判断 customer_code {} 的账号类型失败", customerCode, e);
            }
        }
        result.setCustomerCodeToAccountType(customerCodeToAccountType);

        // 统计各账号类型的数量和空间占用
        long personalAccountCount = 0;
        long enterpriseAccountCount = 0;
        long personalAccountSpaceBytes = 0;
        long enterpriseAccountSpaceBytes = 0;

        for (Map.Entry<String, AccountTypeEnum> entry : customerCodeToAccountType.entrySet()) {
            String customerCode = entry.getKey();
            AccountTypeEnum accountType = entry.getValue();
            CustomerCodeSpaceInfo spaceInfo = customerCodeSpaceStats.get(customerCode);

            if (AccountTypeEnum.PERSONAL_TYPE.equals(accountType)) {
                personalAccountCount++;
                if (spaceInfo != null) {
                    personalAccountSpaceBytes += spaceInfo.getEstimatedSpaceBytes();
                }
            } else if (AccountTypeEnum.ENTERPRISE_TYPE.equals(accountType)) {
                enterpriseAccountCount++;
                if (spaceInfo != null) {
                    enterpriseAccountSpaceBytes += spaceInfo.getEstimatedSpaceBytes();
                }
            }
        }
        log.info("账号类型判断完成（zb_project_feature_standards表） - 个人账号: {} 个 ({}), 企业账号: {} 个 ({})",
                personalAccountCount, formatBytes(personalAccountSpaceBytes),
                enterpriseAccountCount, formatBytes(enterpriseAccountSpaceBytes));
    }

    /**
     * 收集个人账号数据和空间统计（针对 zb_project_feature_standards 表）
     */
    private void collectPersonalAccountDataByCustomerCodeForProjectFeature(CustomerCodeCleanupResult result) {
        log.info("开始收集个人账号数据和空间统计（基于customer_code，zb_project_feature_standards表）");
        List<PersonalAccountDataByCustomerCode> personalAccountDataList = new ArrayList<>();
        Map<String, AccountTypeEnum> customerCodeToAccountType = result.getCustomerCodeToAccountType();
        Map<String, CustomerCodeSpaceInfo> customerCodeSpaceStats = result.getCustomerCodeSpaceStats();

        for (Map.Entry<String, AccountTypeEnum> entry : customerCodeToAccountType.entrySet()) {
            String customerCode = entry.getKey();
            AccountTypeEnum accountType = entry.getValue();
            // 只处理个人账号
            if (AccountTypeEnum.PERSONAL_TYPE.equals(accountType)) {
                try {
                    // 收集该 customer_code 的所有记录ID
                    List<Long> recordIds = collectRecordIdsByCustomerCodeForProjectFeature(customerCode);
                    long recordCount = recordIds.size();
                    PersonalAccountDataByCustomerCode personalData = new PersonalAccountDataByCustomerCode(customerCode, customerCode, recordCount, recordIds);
                    // 关联空间信息
                    CustomerCodeSpaceInfo spaceInfo = customerCodeSpaceStats.get(customerCode);
                    if (spaceInfo != null) {
                        personalData.setSpaceInfo(spaceInfo);
                    }
                    personalAccountDataList.add(personalData);
                    String spaceInfoStr = spaceInfo != null ? spaceInfo.getEstimatedSpaceFormatted() : "未知";
                    log.info("收集到个人账号数据（zb_project_feature_standards表） - customer_code: {}, globalId: {}, 记录数: {}, 估算空间: {}",
                            customerCode, customerCode, recordCount, spaceInfoStr);
                } catch (Exception e) {
                    log.error("收集个人账号数据失败（zb_project_feature_standards表） - customer_code: {}", customerCode, e);
                }
            }
        }
        result.setPersonalAccountDataList(personalAccountDataList);
        // 计算个人账号总空间占用
        long totalPersonalSpaceBytes = personalAccountDataList.stream()
                .filter(data -> data.getSpaceInfo() != null)
                .mapToLong(data -> data.getSpaceInfo().getEstimatedSpaceBytes())
                .sum();
        log.info("个人账号数据收集完成（zb_project_feature_standards表），共收集到 {} 个个人账号的数据，总空间占用: {}",
                personalAccountDataList.size(), formatBytes(totalPersonalSpaceBytes));
    }

    /**
     * 生成空间汇总信息（针对 zb_project_feature_standards 表）
     */
    private void generateSpaceSummaryForProjectFeature(CustomerCodeCleanupResult result) {
        log.info("开始生成空间汇总信息（针对zb_project_feature_standards表）");

        List<TableSpaceInfo> tableSpaceInfoList = result.getTableSpaceInfoList();
        Map<String, CustomerCodeSpaceInfo> customerCodeSpaceStats = result.getCustomerCodeSpaceStats();
        // 计算总空间
        long totalDataBytes = tableSpaceInfoList.stream().mapToLong(TableSpaceInfo::getDataLength).sum();
        long totalIndexBytes = tableSpaceInfoList.stream().mapToLong(TableSpaceInfo::getIndexLength).sum();
        // 计算个人账号和企业账号的空间占用
        long personalAccountSpaceBytes = 0;
        long enterpriseAccountSpaceBytes = 0;
        for (CustomerCodeSpaceInfo spaceInfo : customerCodeSpaceStats.values()) {
            if (AccountTypeEnum.PERSONAL_TYPE.equals(spaceInfo.getAccountType())) {
                personalAccountSpaceBytes += spaceInfo.getEstimatedSpaceBytes();
            } else if (AccountTypeEnum.ENTERPRISE_TYPE.equals(spaceInfo.getAccountType())) {
                enterpriseAccountSpaceBytes += spaceInfo.getEstimatedSpaceBytes();
            }
        }
        SpaceSummary spaceSummary = new SpaceSummary(totalDataBytes, totalIndexBytes, personalAccountSpaceBytes, enterpriseAccountSpaceBytes);
        result.setSpaceSummary(spaceSummary);
        log.info("空间汇总信息生成完成（zb_project_feature_standards表） - 总空间: {}, 个人账号: {} ({}%), 企业账号: {} ({}%)",
                spaceSummary.getTotalSpaceFormatted(),
                spaceSummary.getPersonalAccountSpaceFormatted(), spaceSummary.getPersonalAccountPercentage(),
                spaceSummary.getEnterpriseAccountSpaceFormatted(), spaceSummary.getEnterpriseAccountPercentage());
    }

    /**
     * 执行 zb_project_feature_standards 表数据清理操作
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeProjectFeatureDataCleanup(CustomerCodeCleanupResult result) {
        log.info("开始执行zb_project_feature_standards表数据清理操作");
        List<PersonalAccountDataByCustomerCode> personalAccountDataList = result.getPersonalAccountDataList();
        long totalDeletedRecords = 0;
        long totalReleasedSpaceBytes = 0;

        for (PersonalAccountDataByCustomerCode personalData : personalAccountDataList) {
            try {
                // 删除 zb_project_feature_standards 表中的数据
                long deletedCount = deleteRecordsByCustomerCodeForProjectFeature("zb_project_feature_standards", personalData.getCustomerCode());
                totalDeletedRecords += deletedCount;
                // 计算释放的空间
                if (personalData.getSpaceInfo() != null) {
                    totalReleasedSpaceBytes += personalData.getSpaceInfo().getEstimatedSpaceBytes();
                }
                String spaceInfo = personalData.getSpaceInfo() != null ? personalData.getSpaceInfo().getEstimatedSpaceFormatted() : "未知";
                log.info("删除个人账号数据完成（zb_project_feature_standards表） - customer_code: {}, 删除记录数: {}, 释放空间: {}",
                        personalData.getCustomerCode(), deletedCount, spaceInfo);
            } catch (Exception e) {
                log.error("删除个人账号数据失败（zb_project_feature_standards表） - customer_code: {}", personalData.getCustomerCode(), e);
                throw new RuntimeException("删除数据失败: " + e.getMessage(), e);
            }
        }
        result.setCleanupExecuted(true);
        result.setDeletedRecords(totalDeletedRecords);
        result.setMessage(String.format("zb_project_feature_standards表数据清理完成，共删除 %d 条个人账号相关记录，释放空间约 %s",
                totalDeletedRecords, formatBytes(totalReleasedSpaceBytes)));
        log.info("zb_project_feature_standards表数据清理操作完成，共删除 {} 条记录，释放空间约 {}",
                totalDeletedRecords, formatBytes(totalReleasedSpaceBytes));
    }

    /**
     * 收集指定 customer_code 的所有记录ID（针对 zb_project_feature_standards 表）
     */
    private List<Long> collectRecordIdsByCustomerCodeForProjectFeature(String customerCode) {
        // 优化：直接从缓存中获取记录ID，避免重复数据库查询
        List<Long> cachedRecordIds = customerCodeRecordIdCache.get(customerCode);
        if (cachedRecordIds != null && !cachedRecordIds.isEmpty()) {
            log.debug("从缓存获取 customer_code {} 的记录ID（zb_project_feature_standards表），数量: {}", customerCode, cachedRecordIds.size());
            return new ArrayList<>(cachedRecordIds); // 返回副本，避免外部修改缓存
        }
        // 如果缓存中没有，则回退到数据库查询（兜底方案）
        log.warn("缓存中未找到 customer_code {} 的记录ID（zb_project_feature_standards表），回退到数据库查询", customerCode);
        return collectRecordIdsByCustomerCodeFromDatabaseForProjectFeature(customerCode);
    }

    /**
     * 从数据库查询指定 customer_code 的记录ID（针对 zb_project_feature_standards 表，兜底方案）
     */
    private List<Long> collectRecordIdsByCustomerCodeFromDatabaseForProjectFeature(String customerCode) {
        List<Long> recordIds = new ArrayList<>();
        // 收集 zb_project_feature_standards 表的记录ID
        List<CustomerCodeRecordIdInfo> result = dataCleanupMapper.collectRecordIdsByCustomerCode("zb_project_feature_standards", customerCode);
        for (CustomerCodeRecordIdInfo recordInfo : result) {
            recordIds.add(recordInfo.getId());
        }
        log.debug("从数据库查询 customer_code {} 的记录ID（zb_project_feature_standards表），数量: {}", customerCode, recordIds.size());
        return recordIds;
    }

    /**
     * 删除指定表中指定 customer_code 的记录（针对 zb_project_feature_standards 表）
     */
    private long deleteRecordsByCustomerCodeForProjectFeature(String tableName, String customerCode) {
        int deletedCount = dataCleanupMapper.deleteRecordsByCustomerCode(tableName, customerCode);
        log.info("表 {} 中 customer_code {} 的记录已删除，影响行数: {}", tableName, customerCode, deletedCount);
        return deletedCount;
    }

    // ==================== 新增：支持 zb_project_feature_category_view_standards 双表的方法 ====================

    /**
     * 统计 zb_project_feature_category_view_standards 双表的数据量和空间信息
     */
    private void statisticsCategoryViewData(CustomerCodeCleanupResult result) {
        log.info("开始统计 zb_project_feature_category_view_standards 双表的数据量和表空间信息");
        // 统计两张表的记录数量
        Long count1 = dataCleanupMapper.countTableRecords("zb_project_feature_category_view_standards");
        Long count2 = dataCleanupMapper.countTableRecords("zb_project_feature_category_view_standards_0");
        long totalRecords = (count1 != null ? count1 : 0) + (count2 != null ? count2 : 0);
        result.setTotalRecords(totalRecords);
        // 统计表空间信息（使用预设数据，实际项目中可以查询真实的表空间信息）
        result.setTableSpaceInfoList(getCategoryViewTableSpaceInfo());
        // 输出统计日志
        log.info("zb_project_feature_category_view_standards 双表数据量统计完成 - 主表记录数: {}, 分表记录数: {}, 总记录数: {}",
                count1, count2, totalRecords);
    }

    /**
     * 获取 zb_project_feature_category_view_standards 双表空间信息（使用预设静态数据）
     */
    private List<TableSpaceInfo> getCategoryViewTableSpaceInfo() {
        log.info("使用预设数据获取 zb_project_feature_category_view_standards 双表空间信息");
        List<TableSpaceInfo> tableSpaceInfoList = new ArrayList<>();
        // 基于实际情况创建表空间信息，这里使用估算值
        // 实际项目中应该查询 information_schema.tables 获取真实数据

        //// 主表空间信息
        //TableSpaceInfo info1 = createTableSpaceInfo(
        //        "zb_project_feature_category_view_standards", 795468L,
        //        105529344L,  
        //        126681088L   
        //);
        //tableSpaceInfoList.add(info1);
        //
        //// 分表空间信息
        //TableSpaceInfo info2 = createTableSpaceInfo(
        //        "zb_project_feature_category_view_standards_0", 838703L,  
        //        106414080L,  
        //        160153600L   
        //);
        //tableSpaceInfoList.add(info2);
        //

        // 主表空间信息
        TableSpaceInfo info1 = createTableSpaceInfo(
                "zb_project_feature_category_view_standards", 184595273L,
                21920448512L,
                24638128128L
        );
        tableSpaceInfoList.add(info1);

        // 分表空间信息
        TableSpaceInfo info2 = createTableSpaceInfo(
                "zb_project_feature_category_view_standards_0", 44741626L,
                7510605824L,
                11325259776L
        );
        tableSpaceInfoList.add(info2);

        // 输出表空间统计日志
        for (TableSpaceInfo spaceInfo : tableSpaceInfoList) {
            log.info("表空间信息 - 表名: {}, 记录数: {}, 数据大小: {}, 索引大小: {}, 总大小: {}",
                    spaceInfo.getTableName(), spaceInfo.getTableRows(),
                    spaceInfo.getDataLengthFormatted(), spaceInfo.getIndexLengthFormatted(),
                    spaceInfo.getTotalLengthFormatted());
        }
        return tableSpaceInfoList;
    }

    /**
     * 按 customer_code 分组分析和空间估算（针对 zb_project_feature_category_view_standards 双表）
     */
    private void analyzeCategoryViewCustomerCodeGroups(CustomerCodeCleanupResult result) {
        log.info("开始按 customer_code 分组分析和空间估算（zb_project_feature_category_view_standards双表，包含记录ID预收集）");
        Map<String, Long> customerCodeStats = new HashMap<>();
        // 清空之前的缓存
        customerCodeRecordIdCache.clear();
        // 预先收集所有记录ID，避免后续重复查询
        log.debug("预先收集记录ID，避免后续重复查询");
        collectAndCacheAllRecordIdsByCustomerCodeForCategoryView();

        // 收集两张表的数据，保持表级别的分布信息
        String[] tableNames = {"zb_project_feature_category_view_standards", "zb_project_feature_category_view_standards_0"};
        for (String tableName : tableNames) {
            List<CustomerCodeGroupStats> resultList = dataCleanupMapper.getCustomerCodeGroupStats(tableName);
            for (CustomerCodeGroupStats stats : resultList) {
                customerCodeStats.merge(stats.getCustomerCode(), stats.getRecordCount(), Long::sum);
            }
        }

        result.setCustomerCodeGroupStats(customerCodeStats);
        // 计算企业级空间估算
        Map<String, CustomerCodeSpaceInfo> customerCodeSpaceStats = calculateCustomerCodeSpaceEstimationForCategoryView(customerCodeStats, result.getTableSpaceInfoList());
        result.setCustomerCodeSpaceStats(customerCodeSpaceStats);
        log.info("customer_code 分组分析完成（zb_project_feature_category_view_standards双表），共 {} 个不同的企业编码，已缓存 {} 个企业的记录ID",
                customerCodeStats.size(), customerCodeRecordIdCache.size());
        // 输出空间占用最多的前50个企业编码
        List<CustomerCodeSpaceInfo> topSpaceConsumers = customerCodeSpaceStats.values().stream()
                .sorted((a, b) -> Long.compare(b.getEstimatedSpaceBytes(), a.getEstimatedSpaceBytes()))
                .limit(50)
                .toList();
        log.info("空间占用最多的前50个企业编码（zb_project_feature_category_view_standards双表）:");
        for (int i = 0; i < topSpaceConsumers.size(); i++) {
            CustomerCodeSpaceInfo spaceInfo = topSpaceConsumers.get(i);
            log.info("{}. customer_code: {}, 记录数: {}, 估算空间: {}",
                    i + 1, spaceInfo.getCustomerCode(), spaceInfo.getRecordCount(),
                    spaceInfo.getEstimatedSpaceFormatted());
        }
    }

    /**
     * 预先收集并缓存所有customer_code的记录ID（针对 zb_project_feature_category_view_standards 双表）
     */
    private void collectAndCacheAllRecordIdsByCustomerCodeForCategoryView() {
        long startTime = System.currentTimeMillis();
        // 收集两张表的记录ID
        String[] tableNames = {"zb_project_feature_category_view_standards", "zb_project_feature_category_view_standards_0"};
        long totalRecords = 0;
        for (String tableName : tableNames) {
            List<CustomerCodeRecordIdInfo> recordIds = dataCleanupMapper.collectAllRecordIdsByCustomerCode(tableName);
            for (CustomerCodeRecordIdInfo recordInfo : recordIds) {
                customerCodeRecordIdCache.computeIfAbsent(recordInfo.getCustomerCode(), k -> new ArrayList<>()).add(recordInfo.getId());
            }
            totalRecords += recordIds.size();
        }
        long endTime = System.currentTimeMillis();
        log.info("customer_code记录ID预收集完成（zb_project_feature_category_view_standards双表），耗时: {}ms, 总记录数: {}, 缓存企业数: {}",
                endTime - startTime, totalRecords, customerCodeRecordIdCache.size());
    }

    /**
     * 计算企业编码的空间估算（针对 zb_project_feature_category_view_standards 双表）
     */
    private Map<String, CustomerCodeSpaceInfo> calculateCustomerCodeSpaceEstimationForCategoryView(Map<String, Long> customerCodeStats, List<TableSpaceInfo> tableSpaceInfoList) {
        Map<String, CustomerCodeSpaceInfo> customerCodeSpaceStats = new HashMap<>();
        // 计算双表的平均行大小（基于预设数据）
        double avgRowSize = 0;
        long totalTableRows = 0;
        long totalTableSpaceBytes = 0;
        for (TableSpaceInfo tableInfo : tableSpaceInfoList) {
            long tableRows = tableInfo.getTableRows();
            long tableSpaceBytes = tableInfo.getDataLength() + tableInfo.getIndexLength();
            totalTableRows += tableRows;
            totalTableSpaceBytes += tableSpaceBytes;
        }
        if (totalTableRows > 0) {
            avgRowSize = (double) totalTableSpaceBytes / totalTableRows;
            log.debug("zb_project_feature_category_view_standards双表平均行大小: {} bytes (基于预设数据)", Math.round(avgRowSize));
        }

        // 计算每个企业编码的空间占用
        for (Map.Entry<String, Long> entry : customerCodeStats.entrySet()) {
            String customerCode = entry.getKey();
            long recordCount = entry.getValue();
            long estimatedSpaceBytes = Math.round(recordCount * avgRowSize);
            CustomerCodeSpaceInfo spaceInfo = new CustomerCodeSpaceInfo(customerCode, recordCount, estimatedSpaceBytes, null);
            customerCodeSpaceStats.put(customerCode, spaceInfo);
        }

        // 计算实际的总空间（基于当前查询的数据）
        long actualTotalSpaceBytes = customerCodeSpaceStats.values().stream().mapToLong(CustomerCodeSpaceInfo::getEstimatedSpaceBytes).sum();
        // 计算空间占用百分比（基于实际计算的总空间）
        for (CustomerCodeSpaceInfo spaceInfo : customerCodeSpaceStats.values()) {
            if (actualTotalSpaceBytes > 0) {
                double percentage = (double) spaceInfo.getEstimatedSpaceBytes() * 100 / actualTotalSpaceBytes;
                spaceInfo.setSpacePercentage(percentage);
            }
        }
        log.info("customer_code空间估算完成（zb_project_feature_category_view_standards双表） - 企业编码数: {}, 实际总空间: {}",
                customerCodeSpaceStats.size(), formatBytes(actualTotalSpaceBytes));
        return customerCodeSpaceStats;
    }

    /**
     * 判断账号类型并更新空间统计信息（针对 zb_project_feature_category_view_standards 双表）
     */
    private void determineAccountTypesByCustomerCodeForCategoryView(CustomerCodeCleanupResult result) {
        log.info("开始判断账号类型（基于customer_code，zb_project_feature_category_view_standards双表）");
        Map<String, AccountTypeEnum> customerCodeToAccountType = new HashMap<>();
        Map<String, Long> customerCodeStats = result.getCustomerCodeGroupStats();
        Map<String, CustomerCodeSpaceInfo> customerCodeSpaceStats = result.getCustomerCodeSpaceStats();

        for (String customerCode : customerCodeStats.keySet()) {
            try {
                // 预过滤：只有纯数字的customerCode才可能是个人账号，需要进行账号类型判断
                if (!isNumeric(customerCode)) {
                    // 非纯数字的customerCode直接跳过，不进行账号类型判断
                    log.info("跳过非纯数字的customer_code: {} (企业编码格式)", customerCode);
                    customerCodeToAccountType.put(customerCode, AccountTypeEnum.ENTERPRISE_TYPE);
                    // 更新空间统计信息中的账号类型
                    CustomerCodeSpaceInfo spaceInfo = customerCodeSpaceStats.get(customerCode);
                    if (spaceInfo != null) {
                        spaceInfo.setAccountType(AccountTypeEnum.ENTERPRISE_TYPE);
                    }
                    continue;
                }
                // 判断账号类型（仅对纯数字的customerCode）
                AccountTypeEnum accountType = accountTypeService.getAccountType(customerCode);
                customerCodeToAccountType.put(customerCode, accountType);
                // 更新空间统计信息中的账号类型
                CustomerCodeSpaceInfo spaceInfo = customerCodeSpaceStats.get(customerCode);
                if (spaceInfo != null) {
                    spaceInfo.setAccountType(accountType);
                }
                log.info("customer_code: {}, globalId: {}, accountType: {}", customerCode, customerCode, accountType.getValue());
            } catch (Exception e) {
                customerCodeToAccountType.put(customerCode, null);
                log.error("判断 customer_code {} 的账号类型失败", customerCode, e);
            }
        }
        result.setCustomerCodeToAccountType(customerCodeToAccountType);

        // 统计各账号类型的数量和空间占用
        long personalAccountCount = 0;
        long enterpriseAccountCount = 0;
        long personalAccountSpaceBytes = 0;
        long enterpriseAccountSpaceBytes = 0;

        for (Map.Entry<String, AccountTypeEnum> entry : customerCodeToAccountType.entrySet()) {
            String customerCode = entry.getKey();
            AccountTypeEnum accountType = entry.getValue();
            CustomerCodeSpaceInfo spaceInfo = customerCodeSpaceStats.get(customerCode);

            if (AccountTypeEnum.PERSONAL_TYPE.equals(accountType)) {
                personalAccountCount++;
                if (spaceInfo != null) {
                    personalAccountSpaceBytes += spaceInfo.getEstimatedSpaceBytes();
                }
            } else if (AccountTypeEnum.ENTERPRISE_TYPE.equals(accountType)) {
                enterpriseAccountCount++;
                if (spaceInfo != null) {
                    enterpriseAccountSpaceBytes += spaceInfo.getEstimatedSpaceBytes();
                }
            }
        }
        log.info("账号类型判断完成（zb_project_feature_category_view_standards双表） - 个人账号: {} 个 ({}), 企业账号: {} 个 ({})",
                personalAccountCount, formatBytes(personalAccountSpaceBytes),
                enterpriseAccountCount, formatBytes(enterpriseAccountSpaceBytes));
    }

    /**
     * 收集个人账号数据和空间统计（针对 zb_project_feature_category_view_standards 双表）
     */
    private void collectPersonalAccountDataByCustomerCodeForCategoryView(CustomerCodeCleanupResult result) {
        log.info("开始收集个人账号数据和空间统计（基于customer_code，zb_project_feature_category_view_standards双表）");
        List<PersonalAccountDataByCustomerCode> personalAccountDataList = new ArrayList<>();
        Map<String, AccountTypeEnum> customerCodeToAccountType = result.getCustomerCodeToAccountType();
        Map<String, CustomerCodeSpaceInfo> customerCodeSpaceStats = result.getCustomerCodeSpaceStats();

        for (Map.Entry<String, AccountTypeEnum> entry : customerCodeToAccountType.entrySet()) {
            String customerCode = entry.getKey();
            AccountTypeEnum accountType = entry.getValue();
            // 只处理个人账号
            if (AccountTypeEnum.PERSONAL_TYPE.equals(accountType)) {
                try {
                    // 收集该 customer_code 的所有记录ID
                    List<Long> recordIds = collectRecordIdsByCustomerCodeForCategoryView(customerCode);
                    long recordCount = recordIds.size();
                    PersonalAccountDataByCustomerCode personalData = new PersonalAccountDataByCustomerCode(customerCode, customerCode, recordCount, recordIds);
                    // 关联空间信息
                    CustomerCodeSpaceInfo spaceInfo = customerCodeSpaceStats.get(customerCode);
                    if (spaceInfo != null) {
                        personalData.setSpaceInfo(spaceInfo);
                    }
                    personalAccountDataList.add(personalData);
                    String spaceInfoStr = spaceInfo != null ? spaceInfo.getEstimatedSpaceFormatted() : "未知";
                    log.info("收集到个人账号数据（zb_project_feature_category_view_standards双表） - customer_code: {}, globalId: {}, 记录数: {}, 估算空间: {}",
                            customerCode, customerCode, recordCount, spaceInfoStr);
                } catch (Exception e) {
                    log.error("收集个人账号数据失败（zb_project_feature_category_view_standards双表） - customer_code: {}", customerCode, e);
                }
            }
        }
        result.setPersonalAccountDataList(personalAccountDataList);
        // 计算个人账号总空间占用
        long totalPersonalSpaceBytes = personalAccountDataList.stream()
                .filter(data -> data.getSpaceInfo() != null)
                .mapToLong(data -> data.getSpaceInfo().getEstimatedSpaceBytes())
                .sum();
        log.info("个人账号数据收集完成（zb_project_feature_category_view_standards双表），共收集到 {} 个个人账号的数据，总空间占用: {}",
                personalAccountDataList.size(), formatBytes(totalPersonalSpaceBytes));
    }

    /**
     * 生成空间汇总信息（针对 zb_project_feature_category_view_standards 双表）
     */
    private void generateSpaceSummaryForCategoryView(CustomerCodeCleanupResult result) {
        log.info("开始生成空间汇总信息（针对zb_project_feature_category_view_standards双表）");

        List<TableSpaceInfo> tableSpaceInfoList = result.getTableSpaceInfoList();
        Map<String, CustomerCodeSpaceInfo> customerCodeSpaceStats = result.getCustomerCodeSpaceStats();
        // 计算总空间
        long totalDataBytes = tableSpaceInfoList.stream().mapToLong(TableSpaceInfo::getDataLength).sum();
        long totalIndexBytes = tableSpaceInfoList.stream().mapToLong(TableSpaceInfo::getIndexLength).sum();
        // 计算个人账号和企业账号的空间占用
        long personalAccountSpaceBytes = 0;
        long enterpriseAccountSpaceBytes = 0;
        for (CustomerCodeSpaceInfo spaceInfo : customerCodeSpaceStats.values()) {
            if (AccountTypeEnum.PERSONAL_TYPE.equals(spaceInfo.getAccountType())) {
                personalAccountSpaceBytes += spaceInfo.getEstimatedSpaceBytes();
            } else if (AccountTypeEnum.ENTERPRISE_TYPE.equals(spaceInfo.getAccountType())) {
                enterpriseAccountSpaceBytes += spaceInfo.getEstimatedSpaceBytes();
            }
        }
        SpaceSummary spaceSummary = new SpaceSummary(totalDataBytes, totalIndexBytes, personalAccountSpaceBytes, enterpriseAccountSpaceBytes);
        result.setSpaceSummary(spaceSummary);
        log.info("空间汇总信息生成完成（zb_project_feature_category_view_standards双表） - 总空间: {}, 个人账号: {} ({}%), 企业账号: {} ({}%)",
                spaceSummary.getTotalSpaceFormatted(),
                spaceSummary.getPersonalAccountSpaceFormatted(), spaceSummary.getPersonalAccountPercentage(),
                spaceSummary.getEnterpriseAccountSpaceFormatted(), spaceSummary.getEnterpriseAccountPercentage());
    }

    /**
     * 执行 zb_project_feature_category_view_standards 双表数据清理操作
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeCategoryViewDataCleanup(CustomerCodeCleanupResult result) {
        log.info("开始执行zb_project_feature_category_view_standards双表数据清理操作");
        List<PersonalAccountDataByCustomerCode> personalAccountDataList = result.getPersonalAccountDataList();
        long totalDeletedRecords = 0;
        long totalReleasedSpaceBytes = 0;

        for (PersonalAccountDataByCustomerCode personalData : personalAccountDataList) {
            try {
                // 删除两张表中的数据
                long deleted1 = deleteRecordsByCustomerCodeForCategoryView("zb_project_feature_category_view_standards", personalData.getCustomerCode());
                long deleted2 = deleteRecordsByCustomerCodeForCategoryView("zb_project_feature_category_view_standards_0", personalData.getCustomerCode());
                long deletedCount = deleted1 + deleted2;
                totalDeletedRecords += deletedCount;
                // 计算释放的空间
                if (personalData.getSpaceInfo() != null) {
                    totalReleasedSpaceBytes += personalData.getSpaceInfo().getEstimatedSpaceBytes();
                }
                String spaceInfo = personalData.getSpaceInfo() != null ? personalData.getSpaceInfo().getEstimatedSpaceFormatted() : "未知";
                log.info("删除个人账号数据完成（zb_project_feature_category_view_standards双表） - customer_code: {}, 主表删除: {}, 分表删除: {}, 总删除: {}, 释放空间: {}",
                        personalData.getCustomerCode(), deleted1, deleted2, deletedCount, spaceInfo);
            } catch (Exception e) {
                log.error("删除个人账号数据失败（zb_project_feature_category_view_standards双表） - customer_code: {}", personalData.getCustomerCode(), e);
                throw new RuntimeException("删除数据失败: " + e.getMessage(), e);
            }
        }
        result.setCleanupExecuted(true);
        result.setDeletedRecords(totalDeletedRecords);
        result.setMessage(String.format("zb_project_feature_category_view_standards双表数据清理完成，共删除 %d 条个人账号相关记录，释放空间约 %s",
                totalDeletedRecords, formatBytes(totalReleasedSpaceBytes)));
        log.info("zb_project_feature_category_view_standards双表数据清理操作完成，共删除 {} 条记录，释放空间约 {}",
                totalDeletedRecords, formatBytes(totalReleasedSpaceBytes));
    }

    /**
     * 收集指定 customer_code 的所有记录ID（针对 zb_project_feature_category_view_standards 双表）
     */
    private List<Long> collectRecordIdsByCustomerCodeForCategoryView(String customerCode) {
        // 优化：直接从缓存中获取记录ID，避免重复数据库查询
        List<Long> cachedRecordIds = customerCodeRecordIdCache.get(customerCode);
        if (cachedRecordIds != null && !cachedRecordIds.isEmpty()) {
            log.debug("从缓存获取 customer_code {} 的记录ID（zb_project_feature_category_view_standards双表），数量: {}", customerCode, cachedRecordIds.size());
            return new ArrayList<>(cachedRecordIds); // 返回副本，避免外部修改缓存
        }
        // 如果缓存中没有，则回退到数据库查询（兜底方案）
        log.warn("缓存中未找到 customer_code {} 的记录ID（zb_project_feature_category_view_standards双表），回退到数据库查询", customerCode);
        return collectRecordIdsByCustomerCodeFromDatabaseForCategoryView(customerCode);
    }

    /**
     * 从数据库查询指定 customer_code 的记录ID（针对 zb_project_feature_category_view_standards 双表，兜底方案）
     */
    private List<Long> collectRecordIdsByCustomerCodeFromDatabaseForCategoryView(String customerCode) {
        List<Long> recordIds = new ArrayList<>();
        // 收集两张表的记录ID
        String[] tableNames = {"zb_project_feature_category_view_standards", "zb_project_feature_category_view_standards_0"};
        for (String tableName : tableNames) {
            List<CustomerCodeRecordIdInfo> result = dataCleanupMapper.collectRecordIdsByCustomerCode(tableName, customerCode);
            for (CustomerCodeRecordIdInfo recordInfo : result) {
                recordIds.add(recordInfo.getId());
            }
        }
        log.debug("从数据库查询 customer_code {} 的记录ID（zb_project_feature_category_view_standards双表），数量: {}", customerCode, recordIds.size());
        return recordIds;
    }

    /**
     * 删除指定表中指定 customer_code 的记录（针对 zb_project_feature_category_view_standards 双表）
     */
    private long deleteRecordsByCustomerCodeForCategoryView(String tableName, String customerCode) {
        int deletedCount = dataCleanupMapper.deleteRecordsByCustomerCode(tableName, customerCode);
        log.info("表 {} 中 customer_code {} 的记录已删除，影响行数: {}", tableName, customerCode, deletedCount);
        return deletedCount;
    }

    // ==================== 新增：支持 zb_standards_expression 表的方法 ====================

    /**
     * 统计 zb_standards_expression 表的数据量和空间信息
     */
    private void statisticsExpressionData(CleanupResult result) {
        log.info("开始统计 zb_standards_expression 表的数据量和表空间信息");
        // 统计记录数量
        Long count = dataCleanupMapper.countTableRecords("zb_standards_expression");
        long totalRecords = count != null ? count : 0;
        result.setTotalRecords(totalRecords);
        // 统计表空间信息（使用预设数据，实际项目中可以查询真实的表空间信息）
        result.setTableSpaceInfoList(getExpressionTableSpaceInfo());
        // 输出统计日志
        log.info("zb_standards_expression 表数据量统计完成 - 总记录数: {}", totalRecords);
    }

    /**
     * 获取 zb_standards_expression 表空间信息（使用预设静态数据）
     */
    private List<TableSpaceInfo> getExpressionTableSpaceInfo() {
        log.info("使用预设数据获取 zb_standards_expression 表空间信息");
        List<TableSpaceInfo> tableSpaceInfoList = new ArrayList<>();
        // 基于实际情况创建表空间信息，这里使用估算值
        // 实际项目中应该查询 information_schema.tables 获取真实数据
        // 测试
        //TableSpaceInfo info = createTableSpaceInfo(
        //        "zb_standards_expression", 185118L,
        //        46743552L,
        //        50528256L
        //);
        // 生产
        TableSpaceInfo info = createTableSpaceInfo(
                "zb_standards_expression", 31200370L,
                7893680128L,
                6506299392L
        );
        tableSpaceInfoList.add(info);
        // 输出表空间统计日志
        for (TableSpaceInfo spaceInfo : tableSpaceInfoList) {
            log.info("表空间信息 - 表名: {}, 记录数: {}, 数据大小: {}, 索引大小: {}, 总大小: {}",
                    spaceInfo.getTableName(), spaceInfo.getTableRows(),
                    spaceInfo.getDataLengthFormatted(), spaceInfo.getIndexLengthFormatted(),
                    spaceInfo.getTotalLengthFormatted());
        }
        return tableSpaceInfoList;
    }

    /**
     * 按 qy_code 分组分析和空间估算（针对 zb_standards_expression 表）
     */
    private void analyzeExpressionQyCodeGroups(CleanupResult result) {
        log.info("开始按 qy_code 分组分析和空间估算（zb_standards_expression表，包含记录ID预收集）");
        Map<String, Long> qyCodeStats = new HashMap<>();
        // 清空之前的缓存
        recordIdCache.clear();
        // 预先收集所有记录ID，避免后续重复查询
        log.debug("预先收集记录ID，避免后续重复查询");
        collectAndCacheAllRecordIdsByQyCodeForExpression();

        // 收集 zb_standards_expression 表的数据
        String tableName = "zb_standards_expression";
        List<QyCodeGroupStats> resultList = dataCleanupMapper.getQyCodeGroupStats(tableName);
        for (QyCodeGroupStats stats : resultList) {
            qyCodeStats.put(stats.getQyCode(), stats.getRecordCount());
        }

        result.setQyCodeGroupStats(qyCodeStats);
        // 计算企业级空间估算
        Map<String, QyCodeSpaceInfo> qyCodeSpaceStats = calculateQyCodeSpaceEstimationForExpression(qyCodeStats, result.getTableSpaceInfoList());
        result.setQyCodeSpaceStats(qyCodeSpaceStats);
        log.info("qy_code 分组分析完成（zb_standards_expression表），共 {} 个不同的企业编码，已缓存 {} 个企业的记录ID",
                qyCodeStats.size(), recordIdCache.size());
        // 输出空间占用最多的前50个企业编码
        List<QyCodeSpaceInfo> topSpaceConsumers = qyCodeSpaceStats.values().stream()
                .sorted((a, b) -> Long.compare(b.getEstimatedSpaceBytes(), a.getEstimatedSpaceBytes()))
                .limit(50)
                .toList();
        log.info("空间占用最多的前50个企业编码（zb_standards_expression表）:");
        for (int i = 0; i < topSpaceConsumers.size(); i++) {
            QyCodeSpaceInfo spaceInfo = topSpaceConsumers.get(i);
            log.info("{}. qy_code: {}, 记录数: {}, 估算空间: {}",
                    i + 1, spaceInfo.getQyCode(), spaceInfo.getRecordCount(),
                    spaceInfo.getEstimatedSpaceFormatted());
        }
    }

    /**
     * 预先收集并缓存所有qy_code的记录ID（针对 zb_standards_expression 表）
     */
    private void collectAndCacheAllRecordIdsByQyCodeForExpression() {
        long startTime = System.currentTimeMillis();
        // 收集 zb_standards_expression 表的记录ID
        List<RecordIdInfo> recordIds = dataCleanupMapper.collectAllRecordIds("zb_standards_expression");
        for (RecordIdInfo recordInfo : recordIds) {
            recordIdCache.computeIfAbsent(recordInfo.getQyCode(), k -> new ArrayList<>()).add(recordInfo.getId());
        }
        long endTime = System.currentTimeMillis();
        long totalRecords = recordIds.size();
        log.info("qy_code记录ID预收集完成（zb_standards_expression表），耗时: {}ms, 总记录数: {}, 缓存企业数: {}",
                endTime - startTime, totalRecords, recordIdCache.size());
    }

    /**
     * 计算企业编码的空间估算（针对 zb_standards_expression 表）
     */
    private Map<String, QyCodeSpaceInfo> calculateQyCodeSpaceEstimationForExpression(Map<String, Long> qyCodeStats, List<TableSpaceInfo> tableSpaceInfoList) {
        Map<String, QyCodeSpaceInfo> qyCodeSpaceStats = new HashMap<>();
        // 计算表的平均行大小（基于预设数据）
        double avgRowSize = 0;
        for (TableSpaceInfo tableInfo : tableSpaceInfoList) {
            long tableRows = tableInfo.getTableRows();
            long tableSpaceBytes = tableInfo.getDataLength() + tableInfo.getIndexLength();
            if (tableRows > 0) {
                avgRowSize = (double) tableSpaceBytes / tableRows;
                log.debug("表 {} 平均行大小: {} bytes (基于预设数据)", tableInfo.getTableName(), Math.round(avgRowSize));
                break; // zb_standards_expression只有一张表
            }
        }

        // 计算每个企业编码的空间占用
        for (Map.Entry<String, Long> entry : qyCodeStats.entrySet()) {
            String qyCode = entry.getKey();
            long recordCount = entry.getValue();
            long estimatedSpaceBytes = Math.round(recordCount * avgRowSize);
            QyCodeSpaceInfo spaceInfo = new QyCodeSpaceInfo(qyCode, recordCount, estimatedSpaceBytes, null);
            qyCodeSpaceStats.put(qyCode, spaceInfo);
        }

        // 计算实际的总空间（基于当前查询的数据）
        long actualTotalSpaceBytes = qyCodeSpaceStats.values().stream().mapToLong(QyCodeSpaceInfo::getEstimatedSpaceBytes).sum();
        // 计算空间占用百分比（基于实际计算的总空间）
        for (QyCodeSpaceInfo spaceInfo : qyCodeSpaceStats.values()) {
            if (actualTotalSpaceBytes > 0) {
                double percentage = (double) spaceInfo.getEstimatedSpaceBytes() * 100 / actualTotalSpaceBytes;
                spaceInfo.setSpacePercentage(percentage);
            }
        }
        log.info("qy_code空间估算完成（zb_standards_expression表） - 企业编码数: {}, 实际总空间: {}",
                qyCodeSpaceStats.size(), formatBytes(actualTotalSpaceBytes));
        return qyCodeSpaceStats;
    }

    /**
     * 判断账号类型并更新空间统计信息（针对 zb_standards_expression 表）
     */
    private void determineAccountTypesByQyCodeForExpression(CleanupResult result) {
        log.info("开始判断账号类型（基于qy_code，zb_standards_expression表）");
        Map<String, AccountTypeEnum> qyCodeToAccountType = new HashMap<>();
        Map<String, Long> qyCodeStats = result.getQyCodeGroupStats();
        Map<String, QyCodeSpaceInfo> qyCodeSpaceStats = result.getQyCodeSpaceStats();

        for (String qyCode : qyCodeStats.keySet()) {
            try {
                // 预过滤：只有纯数字的qyCode才可能是个人账号，需要进行账号类型判断
                if (!isNumeric(qyCode)) {
                    // 非纯数字的qyCode直接跳过，不进行账号类型判断
                    log.info("跳过非纯数字的qy_code: {} (企业编码格式)", qyCode);
                    qyCodeToAccountType.put(qyCode, AccountTypeEnum.ENTERPRISE_TYPE);
                    // 更新空间统计信息中的账号类型
                    QyCodeSpaceInfo spaceInfo = qyCodeSpaceStats.get(qyCode);
                    if (spaceInfo != null) {
                        spaceInfo.setAccountType(AccountTypeEnum.ENTERPRISE_TYPE);
                    }
                    continue;
                }
                // 判断账号类型（仅对纯数字的qyCode）
                AccountTypeEnum accountType = accountTypeService.getAccountType(qyCode);
                qyCodeToAccountType.put(qyCode, accountType);
                // 更新空间统计信息中的账号类型
                QyCodeSpaceInfo spaceInfo = qyCodeSpaceStats.get(qyCode);
                if (spaceInfo != null) {
                    spaceInfo.setAccountType(accountType);
                }
                log.info("qy_code: {}, globalId: {}, accountType: {}", qyCode, qyCode, accountType.getValue());
            } catch (Exception e) {
                qyCodeToAccountType.put(qyCode, null);
                log.error("判断 qy_code {} 的账号类型失败", qyCode, e);
            }
        }
        result.setQyCodeToAccountType(qyCodeToAccountType);

        // 统计各账号类型的数量和空间占用
        long personalAccountCount = 0;
        long enterpriseAccountCount = 0;
        long personalAccountSpaceBytes = 0;
        long enterpriseAccountSpaceBytes = 0;

        for (Map.Entry<String, AccountTypeEnum> entry : qyCodeToAccountType.entrySet()) {
            String qyCode = entry.getKey();
            AccountTypeEnum accountType = entry.getValue();
            QyCodeSpaceInfo spaceInfo = qyCodeSpaceStats.get(qyCode);

            if (AccountTypeEnum.PERSONAL_TYPE.equals(accountType)) {
                personalAccountCount++;
                if (spaceInfo != null) {
                    personalAccountSpaceBytes += spaceInfo.getEstimatedSpaceBytes();
                }
            } else if (AccountTypeEnum.ENTERPRISE_TYPE.equals(accountType)) {
                enterpriseAccountCount++;
                if (spaceInfo != null) {
                    enterpriseAccountSpaceBytes += spaceInfo.getEstimatedSpaceBytes();
                }
            }
        }
        log.info("账号类型判断完成（zb_standards_expression表） - 个人账号: {} 个 ({}), 企业账号: {} 个 ({})",
                personalAccountCount, formatBytes(personalAccountSpaceBytes),
                enterpriseAccountCount, formatBytes(enterpriseAccountSpaceBytes));
    }

    /**
     * 收集个人账号数据和空间统计（针对 zb_standards_expression 表）
     */
    private void collectPersonalAccountDataByQyCodeForExpression(CleanupResult result) {
        log.info("开始收集个人账号数据和空间统计（基于qy_code，zb_standards_expression表）");
        List<PersonalAccountData> personalAccountDataList = new ArrayList<>();
        Map<String, AccountTypeEnum> qyCodeToAccountType = result.getQyCodeToAccountType();
        Map<String, QyCodeSpaceInfo> qyCodeSpaceStats = result.getQyCodeSpaceStats();

        for (Map.Entry<String, AccountTypeEnum> entry : qyCodeToAccountType.entrySet()) {
            String qyCode = entry.getKey();
            AccountTypeEnum accountType = entry.getValue();
            // 只处理个人账号
            if (AccountTypeEnum.PERSONAL_TYPE.equals(accountType)) {
                try {
                    // 收集该 qy_code 的所有记录ID
                    List<Long> recordIds = collectRecordIdsByQyCodeForExpression(qyCode);
                    long recordCount = recordIds.size();
                    PersonalAccountData personalData = new PersonalAccountData(qyCode, qyCode, recordCount, recordIds);
                    // 关联空间信息
                    QyCodeSpaceInfo spaceInfo = qyCodeSpaceStats.get(qyCode);
                    if (spaceInfo != null) {
                        personalData.setSpaceInfo(spaceInfo);
                    }
                    personalAccountDataList.add(personalData);
                    String spaceInfoStr = spaceInfo != null ? spaceInfo.getEstimatedSpaceFormatted() : "未知";
                    log.info("收集到个人账号数据（zb_standards_expression表） - qy_code: {}, globalId: {}, 记录数: {}, 估算空间: {}",
                            qyCode, qyCode, recordCount, spaceInfoStr);
                } catch (Exception e) {
                    log.error("收集个人账号数据失败（zb_standards_expression表） - qy_code: {}", qyCode, e);
                }
            }
        }
        result.setPersonalAccountDataList(personalAccountDataList);
        // 计算个人账号总空间占用
        long totalPersonalSpaceBytes = personalAccountDataList.stream()
                .filter(data -> data.getSpaceInfo() != null)
                .mapToLong(data -> data.getSpaceInfo().getEstimatedSpaceBytes())
                .sum();
        log.info("个人账号数据收集完成（zb_standards_expression表），共收集到 {} 个个人账号的数据，总空间占用: {}",
                personalAccountDataList.size(), formatBytes(totalPersonalSpaceBytes));
    }

    /**
     * 生成空间汇总信息（针对 zb_standards_expression 表）
     */
    private void generateSpaceSummaryForExpression(CleanupResult result) {
        log.info("开始生成空间汇总信息（针对zb_standards_expression表）");

        List<TableSpaceInfo> tableSpaceInfoList = result.getTableSpaceInfoList();
        Map<String, QyCodeSpaceInfo> qyCodeSpaceStats = result.getQyCodeSpaceStats();
        // 计算总空间
        long totalDataBytes = tableSpaceInfoList.stream().mapToLong(TableSpaceInfo::getDataLength).sum();
        long totalIndexBytes = tableSpaceInfoList.stream().mapToLong(TableSpaceInfo::getIndexLength).sum();
        // 计算个人账号和企业账号的空间占用
        long personalAccountSpaceBytes = 0;
        long enterpriseAccountSpaceBytes = 0;
        for (QyCodeSpaceInfo spaceInfo : qyCodeSpaceStats.values()) {
            if (AccountTypeEnum.PERSONAL_TYPE.equals(spaceInfo.getAccountType())) {
                personalAccountSpaceBytes += spaceInfo.getEstimatedSpaceBytes();
            } else if (AccountTypeEnum.ENTERPRISE_TYPE.equals(spaceInfo.getAccountType())) {
                enterpriseAccountSpaceBytes += spaceInfo.getEstimatedSpaceBytes();
            }
        }
        SpaceSummary spaceSummary = new SpaceSummary(totalDataBytes, totalIndexBytes, personalAccountSpaceBytes, enterpriseAccountSpaceBytes);
        result.setSpaceSummary(spaceSummary);
        log.info("空间汇总信息生成完成（zb_standards_expression表） - 总空间: {}, 个人账号: {} ({}%), 企业账号: {} ({}%)",
                spaceSummary.getTotalSpaceFormatted(),
                spaceSummary.getPersonalAccountSpaceFormatted(), spaceSummary.getPersonalAccountPercentage(),
                spaceSummary.getEnterpriseAccountSpaceFormatted(), spaceSummary.getEnterpriseAccountPercentage());
    }

    /**
     * 执行 zb_standards_expression 表数据清理操作
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeExpressionDataCleanup(CleanupResult result) {
        log.info("开始执行zb_standards_expression表数据清理操作");
        List<PersonalAccountData> personalAccountDataList = result.getPersonalAccountDataList();
        long totalDeletedRecords = 0;
        long totalReleasedSpaceBytes = 0;

        for (PersonalAccountData personalData : personalAccountDataList) {
            try {
                // 删除 zb_standards_expression 表中的数据
                long deletedCount = deleteRecordsByQyCodeForExpression("zb_standards_expression", personalData.getQyCode());
                totalDeletedRecords += deletedCount;
                // 计算释放的空间
                if (personalData.getSpaceInfo() != null) {
                    totalReleasedSpaceBytes += personalData.getSpaceInfo().getEstimatedSpaceBytes();
                }
                String spaceInfo = personalData.getSpaceInfo() != null ? personalData.getSpaceInfo().getEstimatedSpaceFormatted() : "未知";
                log.info("删除个人账号数据完成（zb_standards_expression表） - qy_code: {}, 删除记录数: {}, 释放空间: {}",
                        personalData.getQyCode(), deletedCount, spaceInfo);
            } catch (Exception e) {
                log.error("删除个人账号数据失败（zb_standards_expression表） - qy_code: {}", personalData.getQyCode(), e);
                throw new RuntimeException("删除数据失败: " + e.getMessage(), e);
            }
        }
        result.setCleanupExecuted(true);
        result.setDeletedRecords(totalDeletedRecords);
        result.setMessage(String.format("zb_standards_expression表数据清理完成，共删除 %d 条个人账号相关记录，释放空间约 %s",
                totalDeletedRecords, formatBytes(totalReleasedSpaceBytes)));
        log.info("zb_standards_expression表数据清理操作完成，共删除 {} 条记录，释放空间约 {}",
                totalDeletedRecords, formatBytes(totalReleasedSpaceBytes));
    }

    /**
     * 收集指定 qy_code 的所有记录ID（针对 zb_standards_expression 表）
     */
    private List<Long> collectRecordIdsByQyCodeForExpression(String qyCode) {
        // 优化：直接从缓存中获取记录ID，避免重复数据库查询
        List<Long> cachedRecordIds = recordIdCache.get(qyCode);
        if (cachedRecordIds != null && !cachedRecordIds.isEmpty()) {
            log.debug("从缓存获取 qy_code {} 的记录ID（zb_standards_expression表），数量: {}", qyCode, cachedRecordIds.size());
            return new ArrayList<>(cachedRecordIds); // 返回副本，避免外部修改缓存
        }
        // 如果缓存中没有，则回退到数据库查询（兜底方案）
        log.warn("缓存中未找到 qy_code {} 的记录ID（zb_standards_expression表），回退到数据库查询", qyCode);
        return collectRecordIdsByQyCodeFromDatabaseForExpression(qyCode);
    }

    /**
     * 从数据库查询指定 qy_code 的记录ID（针对 zb_standards_expression 表，兜底方案）
     */
    private List<Long> collectRecordIdsByQyCodeFromDatabaseForExpression(String qyCode) {
        List<Long> recordIds = new ArrayList<>();
        // 收集 zb_standards_expression 表的记录ID
        List<RecordIdInfo> result = dataCleanupMapper.collectRecordIdsByQyCode("zb_standards_expression", qyCode);
        for (RecordIdInfo recordInfo : result) {
            recordIds.add(recordInfo.getId());
        }
        log.debug("从数据库查询 qy_code {} 的记录ID（zb_standards_expression表），数量: {}", qyCode, recordIds.size());
        return recordIds;
    }

    /**
     * 删除指定表中指定 qy_code 的记录（针对 zb_standards_expression 表）
     */
    private long deleteRecordsByQyCodeForExpression(String tableName, String qyCode) {
        int deletedCount = dataCleanupMapper.deleteRecordsByQyCode(tableName, qyCode);
        log.info("表 {} 中 qy_code {} 的记录已删除，影响行数: {}", tableName, qyCode, deletedCount);
        return deletedCount;
    }
}
