package com.glodon.qydata.service.init.self;

import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard;
import com.glodon.qydata.mapper.standard.buildStandard.ZbProjectStandardMapper;
import com.glodon.qydata.service.standard.buildStandard.impl.ZbProjectStandardServiceImpl;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 建造标准暂存数据初始化
 * @date 2022/7/8 18:04
 */
@Service
public class InitBuildStandardSelfService extends InitSelfService {
    @Autowired
    private ZbProjectStandardMapper zbProjectStandardMapper;

    @Autowired
    private ZbProjectStandardServiceImpl zbProjectStandardServiceImpl;

    public void initData(String customerCode){
        super.initData(customerCode, null, null);
    }

    @Override
    protected Boolean isNeedInit(String customerCode, Integer type, Long id) {
        return CollectionUtils.isEmpty(zbProjectStandardMapper.selectSelfByCustomerCode(customerCode, Constants.CategoryConstants.WHETHER_TRUE));
    }

    @Override
    protected void executeInit(String customerCode, Integer type, Long id) {
        List<ZbProjectStandard> list = zbProjectStandardServiceImpl.standardList(customerCode, Constants.CategoryConstants.WHETHER_TRUE, Constants.ORDER_ASC, false);

        if (CollectionUtils.isEmpty(list)){
            return;
        }

        for (ZbProjectStandard entity : list) {
            entity.setOriginId(entity.getId());
            entity.setId(SnowflakeIdUtils.getNextId());
        }

        zbProjectStandardMapper.selfBatchInsert(list);
    }
}
