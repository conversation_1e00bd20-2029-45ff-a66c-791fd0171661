package com.glodon.qydata.service.init.self;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity;
import com.glodon.qydata.mapper.standard.projectOrContractInfo.StandardsProjectInfoMapper;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 项目信息/合同信息暂存
 * @date 2022/7/11 14:49
 */
@Service
@Slf4j
public class InitProjectSelfService extends InitSelfService{
    @Autowired
    private StandardsProjectInfoMapper standardsProjectInfoMapper;

    public void initData(String customerCode, Integer type){
        super.initData(customerCode, type, null);
    }

    @Override
    protected Boolean isNeedInit(String customerCode, Integer type, Long id) {
        //从暂存表中查询数据
        Wrapper<StandardsProjectInfoEntity> wrapper = new LambdaQueryWrapper<StandardsProjectInfoEntity>()
                .eq(StandardsProjectInfoEntity::getCustomerCode, customerCode)
                .eq(StandardsProjectInfoEntity::getStandardDataType, type);

        return CollectionUtils.isEmpty(standardsProjectInfoMapper.selectList(wrapper));
    }

    @Override
    protected void executeInit(String customerCode, Integer type, Long id) {
        List<StandardsProjectInfoEntity> list = standardsProjectInfoMapper.selectAllPublishData(customerCode, Constants.CategoryConstants.WHETHER_TRUE, type);
        log.info("项目信息主表数据大小：{}, customerCode：{}", list.size(), customerCode);
        if (CollectionUtils.isEmpty(list)){
            return;
        }


        for (StandardsProjectInfoEntity entity : list) {
            entity.setOriginId(entity.getId());
            entity.setId(SnowflakeIdUtils.getNextId());
        }
        Map<Long, Long> originalId2IdMap = list.stream()
                .collect(Collectors.toMap(StandardsProjectInfoEntity::getOriginId, StandardsProjectInfoEntity::getId));
        for (StandardsProjectInfoEntity entity : list) {
            if (entity.getPid() != null && !Constants.StandardsProjectOrContractInfoConstants.FIRST_LEVEL_PID.equals(entity.getPid())) {
                entity.setPid(originalId2IdMap.get(entity.getPid()));
            }
        }
        standardsProjectInfoMapper.insertBatchSelf(list);
    }
}
