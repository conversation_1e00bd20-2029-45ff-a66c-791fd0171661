package com.glodon.qydata.service.init;

import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description: 初始化数据
 * @date 2022/7/8 14:09
 */
@Slf4j
public abstract class InitService {


    /**
     * 初始化企业的内置数据
     */
    public final void initData(String customerCode, Integer type, Long id){
        if (!isNeedInit(customerCode, type, id)){
            log.info("初次检验是否需要初始化数据,customerCode:{},type:{},id:{}", customerCode, type, id);
            return;
        }

        String lockValue = lockValue();

        if (tryLockRetry(customerCode, lockValue)) {
            try {
                if (!isNeedInit(customerCode, type, id)){
                    log.info("再次检验是否需要初始化数据,customerCode:{},type:{},id:{}", customerCode, type, id);
                    return;
                }
                executeInit(customerCode, type, id);
            } finally {
                unlock(customerCode, lockValue);
            }
        }
    }

    /**
     * 是否需要初始化
     */
    protected abstract Boolean isNeedInit(String customerCode, Integer type, Long id);

    /**
     * 执行初始化
     */
    protected abstract void executeInit(String customerCode, Integer type, Long id);

    /**
     * 加锁
     */
    protected abstract Boolean tryLockRetry(String customerCode, String lockValue);

    /**
     * 解锁
     */
    protected abstract void unlock(String customerCode, String lockValue);

    /**
     * 生成锁的value值
     */
    protected String lockValue(){
        String id = SnowflakeIdUtils.getSystemUuid();
        long threadId = Thread.currentThread().getId();
        return id + ":" + threadId;
    }


}

