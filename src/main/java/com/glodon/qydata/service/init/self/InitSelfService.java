package com.glodon.qydata.service.init.self;

import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.service.init.InitService;
import com.glodon.qydata.util.RedisUtil;
import com.glodon.qydata.util.SpringUtil;

/**
 * <AUTHOR>
 * @description: 初始化暂存数据
 * @date 2022/7/8 14:09
 */
public abstract class InitSelfService extends InitService {

    @Override
    protected Boolean tryLockRetry(String customerCode, String lockValue) {
        RedisUtil redisUtil = SpringUtil.getBean("redisUtil");
        return redisUtil.tryLockRetry(lockValue, RedisKeyEnum.LOCK_INIT_SELF_DATA_INFO, customerCode);
    }

    @Override
    protected void unlock(String customerCode, String lockValue) {
        RedisUtil redisUtil = SpringUtil.getBean("redisUtil");
        redisUtil.unlock(lockValue, RedisKeyEnum.LOCK_INIT_SELF_DATA_INFO, customerCode);
    }

}

