package com.glodon.qydata.service.init.enterprise;

import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.constant.TableNameConstants;
import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.config.idsequence.IDGenerator;
import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.mapper.standard.category.CommonProjCategoryMapper;
import com.glodon.qydata.service.init.InitService;
import com.glodon.qydata.util.RedisUtil;
import com.glodon.qydata.util.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 工程分类初始化
 * @date 2022/7/8 14:34
 */
@Service
public class InitCategoryService extends InitService {
    @Autowired
    private CommonProjCategoryMapper commonProjCategoryMapper;

    @Autowired
    private RedisUtil redisUtil;

    public void initData(String customerCode, Integer type){
        super.initData(customerCode, type, null);
    }

    @Override
    protected Boolean isNeedInit(String customerCode, Integer type, Long id) {
        return commonProjCategoryMapper.selectInit(customerCode, type) == 0;
    }

    @Override
    protected void executeInit(String customerCode, Integer type, Long id) {
        InitCategoryService initCategoryService = SpringUtil.getBean("initCategoryService");
        initCategoryService.executeInitTransactional(customerCode, type, id);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void executeInitTransactional(String customerCode, Integer type, Long id) {
        // 给新企业创建系统内置的副本
        List<CommonProjCategory> sysCategories = commonProjCategoryMapper.selectAll(
                Constants.CategoryConstants.SYSTEM_CUSTOMER_CODE, type, Constants.CategoryConstants.WHETHER_TRUE);

        if (CollectionUtils.isNotEmpty(sysCategories)){
            sysCategories.forEach(item -> {
                item.setGlobalId(Constants.CategoryConstants.SYSTEM_CUSTOMER_CODE);
                item.setQyCode(customerCode);
                item.setCreateTime(null);
                item.setUpdateTime(null);
                item.setId(IDGenerator.getNextIntId(TableNameConstants.TB_COMMONPROJCATEGORY_STANDARDS));
            });
            commonProjCategoryMapper.saveBatchCommonProjCategory(sysCategories);
        }
    }

    @Override
    protected Boolean tryLockRetry(String customerCode, String lockValue) {
        return redisUtil.tryLockRetry(lockValue, RedisKeyEnum.LOCK_INIT_CATEGORY, customerCode);
    }

    @Override
    protected void unlock(String customerCode, String lockValue) {
        redisUtil.unlock(lockValue, RedisKeyEnum.LOCK_INIT_CATEGORY, customerCode);
    }
}
