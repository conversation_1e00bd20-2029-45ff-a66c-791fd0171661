package com.glodon.qydata.service.init;

import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.init.BuildStandardExcelVo;
import com.glodon.qydata.vo.init.CommonCategoryExcelVo;
import com.glodon.qydata.vo.init.ExpressionExcelVo;

import java.util.List;
import java.util.Set;

/**
 * @Description: 导入内置数据相关Service
 * @author: weijf
 * @date: 2022-10-10
 */
public interface ImportBuiltInDataService {

    /**
     * 导入内置建造标准
     * @param excelDataList
     * @param categoryCode 一级工程分类编码，参考工程分类
     * @param buildStandardName 待导入的建造标准名称，若没有，则新建，若有，则覆盖原有内置数据
     * @param defaultBuildStandardId 新建时，默认建造标准ID，若不传，则随机
     * <AUTHOR>
     * @return
     */
    ResponseVo importBuildStandardData(List<BuildStandardExcelVo> excelDataList, String categoryCode, String categoryName, String buildStandardName, Long defaultBuildStandardId);

    /**
     * 导入内置计算口径
     * @param excelDataList
     * @param standardTradeName 【产品设计指标】的数据，同步到某个工程特征下，若名称为空，则不同步
     * <AUTHOR>
     * @return
     */
    ResponseVo importExpressionData(List<ExpressionExcelVo> excelDataList,String standardTradeName);

    /**
     * 处理某个工程特征的数据，把某专业下的数据与内置计算口径的数据刷新成一致的
     * @param excelDataList
     * @param standardTradeName
     * @return
     */
    ResponseVo refreshUserFeatureData(List<ExpressionExcelVo> excelDataList,String standardTradeName,String defaultInitCustomerCode);

    /**
     * 删除分类视图中多余的计算口径数据
     * （包括暂存，只删除该分类下只有这几个计算口径的数据）
     * @return
     * <AUTHOR>
     */
    void deleteFeatureCategoryViewUnuseData(List<ExpressionExcelVo> excelDataList,String standardTradeName);

    /**
     * 根据Excel模板，只初始化分类视图数据
     * @param excelDataList
     * @param standardTradeName
     * <AUTHOR>
     */
    void initCategoryViewOnly(List<ExpressionExcelVo> excelDataList, String standardTradeName,String defaultInitCustomerCode);

    /**
     * 根据Excel模板，只初始化工程特征暂存数据，若已存在，则不处理
     * @param excelDataList
     * @param standardTradeName
     * @param defaultInitCustomerCode
     */
    void initSelfFeatureOnly(List<ExpressionExcelVo> excelDataList, String standardTradeName, String defaultInitCustomerCode);

    /**
     * 导入内置工程分类
     *  注意：导入完成后，需要初始化用户工程分类
     * @param parentCategoryName 上级分类名称，多级用|隔开
     * @param categoryType 需要导入到的分类，多个用英文逗号隔开，数据取自：CategoryTypeConstants
     * @param categoryTypeCode 如果导入到一级分类的话，此参数为导入到的模板Code，如果非一级，则此参数不生效，默认取父级模板Code
     * @param categoryTypeName 如果导入到一级分类的话，此参数为导入到的模板名称，如果非一级，则此参数不生效，默认取父级模板名称
     * @return
     * <AUTHOR>
     */
    ResponseVo importCommonprojCategoryData(List<CommonCategoryExcelVo> excelDataList, String parentCategoryName, String categoryType,String categoryTypeCode,String categoryTypeName);

    /**
     * 初始化用户工程分类（发布、暂存）
     * @param excelDataList
     * @param parentCategoryName 上级分类名称，多级用|隔开
     * @param categoryType 需要导入到的分类，多个用英文逗号隔开，数据取自：CategoryTypeConstants
     * @param defaultInitCustomerCode 需要导入到的分类，多个用英文逗号隔开，数据取自：CategoryTypeConstants
     * @return
     * <AUTHOR>
     */
    ResponseVo initUserCommonprojCategoryData(List<CommonCategoryExcelVo> excelDataList, String parentCategoryName, String categoryType,String defaultInitCustomerCode);

    Set<String> buildStandardCustomerCodeList();
    void refreshBuildStandard(Set<String> customerCodeList);
}
