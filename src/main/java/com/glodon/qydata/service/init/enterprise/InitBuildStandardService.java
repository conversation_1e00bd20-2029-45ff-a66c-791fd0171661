package com.glodon.qydata.service.init.enterprise;

import com.glodon.qydata.common.annotation.BusinessCache;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard;
import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandardDetail;
import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildStandardDetailDesc;
import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.entity.standard.trade.ZbStandardsTrade;
import com.glodon.qydata.entity.zbsq.Item;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.standard.buildStandard.ZbProjectStandardDetailMapper;
import com.glodon.qydata.mapper.standard.buildStandard.ZbProjectStandardMapper;
import com.glodon.qydata.mapper.standard.buildStandard.ZbStandardsBuildStandardDetailDescMapper;
import com.glodon.qydata.service.init.InitService;
import com.glodon.qydata.service.standard.category.CommonProjCategoryService;
import com.glodon.qydata.service.standard.category.impl.CommonProjectCategoryUsedService;
import com.glodon.qydata.service.standard.trade.IStandardsTradeService;
import com.glodon.qydata.service.subjectdivision.SubjectDivisionService;
import com.glodon.qydata.util.RedisUtil;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import com.glodon.qydata.vo.standard.buildStandard.RefreshBuildStandard;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.glodon.qydata.common.constant.CategoryTypeConstants.TYPE_DEFAULT;
import static com.glodon.qydata.common.constant.Constants.CategoryConstants.*;
import static com.glodon.qydata.common.constant.Constants.WHETHER_TRUE;

/**
 * <AUTHOR>
 * @description: 建造标准初始化
 * @date 2022/7/8 16:01
 */
@Slf4j
@Service
public class InitBuildStandardService extends InitService {
    @Autowired
    private ZbProjectStandardMapper zbProjectStandardMapper;
    @Autowired
    private ZbProjectStandardDetailMapper zbProjectStandardDetailMapper;
    @Autowired
    private ZbStandardsBuildStandardDetailDescMapper zbStandardsBuildStandardDetailDescMapper;
    @Autowired
    private CommonProjectCategoryUsedService commonProjectCategoryUsedService;
    @Autowired
    private CommonProjCategoryService commonProjCategoryService;
    @Autowired
    private IStandardsTradeService tradeService;
    @Autowired
    private SubjectDivisionService subjectDivisionService;
    @Autowired
    private RedisUtil redisUtil;

    private static final List<String> DEFAULT_STANDARD_LIST = Arrays.asList("推荐建造标准-居住建筑");

    public void initData(String customerCode){
        super.initData(customerCode, null, null);
    }

    @Override
    protected Boolean isNeedInit(String customerCode, Integer type, Long id) {
        // 工程分类类型
        Integer categoryType = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        if (!Objects.equals(categoryType, TYPE_DEFAULT)) {
            return false;
        }

        // 查询客户标准列表
        List<ZbProjectStandard> customerStandards = zbProjectStandardMapper.selectByCustomerCode(customerCode, Constants.CategoryConstants.WHETHER_TRUE);

        // 如果客户标准列表为空，需要初始化
        if (CollectionUtils.isEmpty(customerStandards)){
            return true;
        }

        // 获取客户标准名称列表
        Set<String> customerStandardNames = customerStandards.stream()
                .filter(x -> Objects.equals(x.getIsExample(), WHETHER_TRUE)).map(ZbProjectStandard::getName).collect(Collectors.toSet());

        // 判断客户标准名称列表是否包含所有默认标准
        return !customerStandardNames.containsAll(DEFAULT_STANDARD_LIST);
    }

    @Override
    protected void executeInit(String customerCode, Integer type, Long id) {
        // 需要导入的默认标准
        List<String> needInitStandardNames = DEFAULT_STANDARD_LIST;
        // 查询客户标准数据
        List<ZbProjectStandard> customerStandards = zbProjectStandardMapper.selectByCustomerCode(customerCode, Constants.CategoryConstants.WHETHER_TRUE);
        // 过滤出需要导入的默认标准
        if (CollectionUtils.isNotEmpty(customerStandards)){
            List<String> customerStandardNames = customerStandards.stream()
                    .filter(x -> Objects.equals(x.getIsExample(), WHETHER_TRUE)).map(ZbProjectStandard::getName).collect(Collectors.toList());
            needInitStandardNames = DEFAULT_STANDARD_LIST.stream().filter(item -> !customerStandardNames.contains(item)).collect(Collectors.toList());
        }
        // 工程分类类型
        Integer categoryType = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        if (!Objects.equals(categoryType, TYPE_DEFAULT)) {
            log.error("企业：{}的工程分类类型：{}，跳过", customerCode, categoryType);
            return;
        }
        // 查询该企业的工程分类
        List<CommonProjCategory> allCategoryList = commonProjCategoryService.getAllCategoryList(customerCode, categoryType, Constants.CategoryConstants.WHETHER_TRUE);
        // 工程分类名称和id的映射
        Map<String, String> topCategoryNameAndCodeMap = allCategoryList.stream().filter(x -> Objects.equals(x.getLevel(), LEVEL_1.longValue()))
                .collect(Collectors.toMap(CommonProjCategory::getCategoryname, CommonProjCategory::getCommonprojcategoryid, (k1, k2) -> k1));
        // 初始化默认数据
        List<RefreshBuildStandard> refreshBuildStandards = initDefaultData(customerCode, needInitStandardNames, topCategoryNameAndCodeMap);
        if (CollectionUtils.isEmpty(refreshBuildStandards)){
            log.error("企业：{}，没有需要初始化的建造标准，跳过", customerCode);
            return;
        }
        // 查询该企业的专业数据
        Map<String, Long> tradeNameAndIdMap = this.getTradeNameAndIdMap(customerCode);
        if (tradeNameAndIdMap == null || tradeNameAndIdMap.isEmpty()) {
            log.error("企业：{}未配置任何专业数据，跳过", customerCode);
            return;
        }

        Map<String, List<ZbProjectStandard>> categoryCodeGroupedStandards = customerStandards.stream().collect(Collectors.groupingBy(ZbProjectStandard::getCategoryCode));

        // 定义待入库的标准数据
        List<ZbProjectStandard> copyStandardList = Lists.newArrayList();
        List<ZbProjectStandardDetail> copyDetailList = Lists.newArrayList();
        List<ZbStandardsBuildStandardDetailDesc> copyDescList = Lists.newArrayList();
        List<ZbProjectStandard> copyStandardSelfList = Lists.newArrayList();

        for (RefreshBuildStandard defaultData : refreshBuildStandards) {
            try {
                String categoryCode = topCategoryNameAndCodeMap.get(defaultData.getCategoryName());
                // 获取项目划分科目
                List<Item> itemList = subjectDivisionService.getTemplateByCategoryWithInit(categoryCode);
                Map<String, String> itemNameAndIdMap = itemList.stream().collect(Collectors.toMap(Item::getFullName, Item::getId, (k1, k2) -> k1));
                // 处理启用状态
                boolean existUsing = false;
                if (categoryCodeGroupedStandards.containsKey(categoryCode)){
                    existUsing = categoryCodeGroupedStandards.get(categoryCode).stream().anyMatch(x -> Objects.equals(x.getIsUsing(), WHETHER_TRUE));
                }
                RefreshBuildStandard refreshBuildStandard = executeCopy(defaultData, customerCode, tradeNameAndIdMap, itemNameAndIdMap, existUsing, categoryCode);
                copyStandardList.add(refreshBuildStandard.getProjectStandard());
                copyDetailList.addAll(refreshBuildStandard.getDetailList());
                copyDescList.addAll(refreshBuildStandard.getDescList());
            } catch (Exception e) {
                log.error("企业：{}，建造标准：{}，初始化失败，原因：{}", customerCode, defaultData.getName(), e.getMessage(), e);
            }
        }

        // 复制暂存数据。只需要复制主表，其余表的数据会走增量的暂存初始化逻辑
        this.copeSelfData(customerCode, copyStandardList, copyStandardSelfList);

        ((InitBuildStandardService) AopContext.currentProxy()).saveToDB(customerCode, copyStandardList, copyDetailList, copyDescList, copyStandardSelfList);
    }

    @Override
    protected Boolean tryLockRetry(String customerCode, String lockValue) {
        return redisUtil.tryLockRetry(lockValue, RedisKeyEnum.LOCK_INIT_BUILD_STANDARD, customerCode);
    }

    @Override
    protected void unlock(String customerCode, String lockValue) {
        redisUtil.unlock(lockValue, RedisKeyEnum.LOCK_INIT_BUILD_STANDARD, customerCode);
    }

    private List<RefreshBuildStandard> initDefaultData(String customerCode,
                                                       List<String> needInitStandardNames,
                                                       Map<String, String> topCategoryNameAndCodeMap) {
        // 查询内置数据
        List<ZbProjectStandard> defaultStandardList = zbProjectStandardMapper.selectByCustomerCode("-100", WHETHER_FALSE);
        if (CollectionUtils.isEmpty(defaultStandardList)){
            throw new BusinessException("系统标准数据异常，请检查");
        }

        List<RefreshBuildStandard> defaultDataList = new ArrayList<>();
        for (ZbProjectStandard defaultStandard : defaultStandardList) {
            if (!needInitStandardNames.contains(defaultStandard.getName()) || !topCategoryNameAndCodeMap.containsKey(defaultStandard.getCategoryName())){
                log.warn("企业：{}，需要初始化的建造标准名称不匹配或工程分类名称不存在，跳过：{}-{}-{}", customerCode, needInitStandardNames, defaultStandard.getName(), defaultStandard.getCategoryName());
                continue;
            }
            RefreshBuildStandard defaultData = new RefreshBuildStandard();
            defaultData.setName(defaultStandard.getName());
            defaultData.setStandardId(defaultStandard.getId());
            defaultData.setProjectStandard(defaultStandard);
            defaultData.setCategoryCode(defaultStandard.getCategoryCode());
            defaultData.setCategoryName(defaultStandard.getCategoryName());
            defaultData.setDetailList(zbProjectStandardDetailMapper.selectByStandardId(defaultStandard.getId()));
            defaultData.setDescList(zbStandardsBuildStandardDetailDescMapper.selectByStandardId(defaultStandard.getId()));
            if(CollectionUtils.isEmpty(defaultData.getDetailList()) || CollectionUtils.isEmpty(defaultData.getDescList())){
                throw new BusinessException("系统标准数据异常standardName：[" + defaultStandard.getName() + "]，请检查");
            }
            defaultDataList.add(defaultData);
        }
        return defaultDataList;
    }

    private RefreshBuildStandard executeCopy(RefreshBuildStandard defaultData,
                                             String customerCode,
                                             Map<String, Long> tradeNameAndIdMap,
                                             Map<String, String> itemNameAndIdMap,
                                             boolean existUsing,
                                             String categoryCode) {
        ZbProjectStandard defaultStandard = defaultData.getProjectStandard();
        List<ZbProjectStandardDetail> defaultDetailList = defaultData.getDetailList();
        List<ZbStandardsBuildStandardDetailDesc> defaultDescList = defaultData.getDescList();
        // 复制建造标准主表
        ZbProjectStandard copyStandard = new ZbProjectStandard();
        BeanUtils.copyProperties(defaultStandard, copyStandard);
        // 重新生成ID
        Long newStandardId = SnowflakeIdUtils.getNextId();
        // 重设部分字段
        copyStandard.setId(newStandardId);
        copyStandard.setCustomerCode(customerCode);
        copyStandard.setUpdateTime(null);
        copyStandard.setIsUsing(existUsing ? WHETHER_FALSE : WHETHER_TRUE);
        copyStandard.setCategoryCode(categoryCode);

        // 复制建造标准详情
        List<ZbProjectStandardDetail> copyDetailList = Lists.newArrayList();
        Map<Long, Long> detailDefaultIdAndNewIdMap = new HashMap<>();
        List<Long> detailNewIdList = SnowflakeIdUtils.getNextId(defaultDetailList.size());
        for (int i = 0; i < defaultDetailList.size(); i++) {
            ZbProjectStandardDetail defaultDetail = defaultDetailList.get(i);
            ZbProjectStandardDetail copyDetail = new ZbProjectStandardDetail();
            BeanUtils.copyProperties(defaultDetail, copyDetail);
            copyDetail.setId(detailNewIdList.get(i));
            copyDetail.setStandardId(newStandardId);
            copyDetail.setUpdateTime(null);
            // 专业id的赋值
            if (StringUtils.isNotEmpty(defaultDetail.getTradeName())){
                copyDetail.setTradeId(tradeNameAndIdMap.get(defaultDetail.getTradeName()));
            }
            // 项目划分id的获取。根据项目划分科目全路径，获取项目划分科目id；如果无对应，不维护id并且清空路径
            if (Objects.equals(defaultData.getName(), "推荐建造标准-居住建筑") && StringUtils.isNotEmpty(defaultDetail.getItemDivisionSubjectName())){
                if (itemNameAndIdMap.containsKey(defaultDetail.getItemDivisionSubjectName())){
                    copyDetail.setItemDivisionSubjectId(itemNameAndIdMap.get(defaultDetail.getItemDivisionSubjectName()));
                }else {
                    copyDetail.setItemDivisionSubjectName(null);
                }
            }
            detailDefaultIdAndNewIdMap.put(defaultDetail.getId(), copyDetail.getId());
            copyDetailList.add(copyDetail);
        }

        // 复制建造标准标准说明
        List<ZbStandardsBuildStandardDetailDesc> copyDescList = Lists.newArrayList();
        List<Long> descNewIdList = SnowflakeIdUtils.getNextId(defaultDescList.size());
        for (int i = 0; i < defaultDescList.size(); i++) {
            ZbStandardsBuildStandardDetailDesc defaultDesc = defaultDescList.get(i);
            ZbStandardsBuildStandardDetailDesc copyDesc = new ZbStandardsBuildStandardDetailDesc();
            BeanUtils.copyProperties(defaultDesc, copyDesc);
            if (!detailDefaultIdAndNewIdMap.containsKey(defaultDesc.getStandardDetailId())){
                throw new BusinessException("系统标准数据异常detailId：[" + defaultDesc.getStandardDetailId() + "]，请检查");
            }
            copyDesc.setStandardId(newStandardId);
            copyDesc.setStandardDetailId(detailDefaultIdAndNewIdMap.get(defaultDesc.getStandardDetailId()));
            copyDesc.setId(descNewIdList.get(i));
            copyDescList.add(copyDesc);
        }

        RefreshBuildStandard copyData = new RefreshBuildStandard();
        copyData.setName(copyStandard.getName());
        copyData.setStandardId(copyStandard.getId());
        copyData.setProjectStandard(copyStandard);
        copyData.setDetailList(copyDetailList);
        copyData.setDescList(copyDescList);
        return copyData;
    }

    private Map<String, Long> getTradeNameAndIdMap(String customerCode) {
        List<ZbStandardsTrade> tradeList = tradeService.initAllListByCustomerCode(customerCode);
        if (CollectionUtils.isEmpty(tradeList)){
            return null;
        }
        return tradeList.stream()
                .filter(x -> StringUtils.isNotEmpty(x.getDescription()))
                .collect(Collectors.toMap(ZbStandardsTrade::getDescription, ZbStandardsTrade::getId, (k1, k2) -> k1));
    }

    private void copeSelfData(String customerCode, List<ZbProjectStandard> copyStandardList, List<ZbProjectStandard> copyStandardSelfList) {
        // 查询该企业的是否有暂存数据
        List<ZbProjectStandard> selfStandards = zbProjectStandardMapper.selectSelfByCustomerCode(customerCode, Constants.CategoryConstants.WHETHER_TRUE);

        if (CollectionUtils.isEmpty(selfStandards)) {
            return;
        }

        List<Long> selfNewIdList = SnowflakeIdUtils.getNextId(copyStandardList.size());
        for (int i = 0; i < copyStandardList.size(); i++) {
            ZbProjectStandard copyStandard = copyStandardList.get(i);
            ZbProjectStandard copyStandardSelf = new ZbProjectStandard();
            BeanUtils.copyProperties(copyStandard, copyStandardSelf);
            copyStandardSelf.setOriginId(copyStandard.getId());
            copyStandardSelf.setId(selfNewIdList.get(i));
            copyStandardSelfList.add(copyStandardSelf);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @BusinessCache(customerCode = "${customerCode}", isInvalidateCache = true)
    public void saveToDB(String customerCode,
                         List<ZbProjectStandard> copyStandardList,
                         List<ZbProjectStandardDetail> copyDetailList,
                         List<ZbStandardsBuildStandardDetailDesc> copyDescList,
                         List<ZbProjectStandard> copyStandardSelfList) {
        if (CollectionUtils.isNotEmpty(copyStandardList)){
            zbProjectStandardMapper.batchInsert(copyStandardList);
        }
        if (CollectionUtils.isNotEmpty(copyDetailList)){
            zbProjectStandardDetailMapper.saveBatch(copyDetailList);
        }
        if (CollectionUtils.isNotEmpty(copyDescList)){
            zbStandardsBuildStandardDetailDescMapper.batchSave(copyDescList);
        }
        if (CollectionUtils.isNotEmpty(copyStandardSelfList)) {
            zbProjectStandardMapper.selfBatchInsert(copyStandardSelfList);
        }
    }
}
