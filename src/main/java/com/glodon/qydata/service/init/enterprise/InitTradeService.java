package com.glodon.qydata.service.init.enterprise;

import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.entity.standard.trade.ZbStandardsTrade;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.standard.trade.ZbStandardsTradeMapper;
import com.glodon.qydata.service.init.InitService;
import com.glodon.qydata.service.standard.feature.IProjectFeatureService;
import com.glodon.qydata.service.standard.mainQuantity.IStandardsMainQuantityService;
import com.glodon.qydata.util.RedisUtil;
import com.glodon.qydata.util.SpringUtil;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 初始化工程专业、主要量指标、工程特征
 * @date 2022/7/8 16:09
 */
@Service
public class InitTradeService extends InitService {
    @Autowired
    private ZbStandardsTradeMapper tradeMapper;

    @Autowired
    private IProjectFeatureService featureService;

    @Autowired
    private IStandardsMainQuantityService mainQuantityService;

    @Autowired
    private RedisUtil redisUtil;

    public void initData(String customerCode){
        super.initData(customerCode, null, null);
    }

    @Override
    protected Boolean isNeedInit(String customerCode, Integer type, Long id) {
        return CollectionUtils.isEmpty(tradeMapper.selectAllListByCustomerCode(customerCode));
    }

    @Override
    protected void executeInit(String customerCode, Integer type, Long id) {
        InitTradeService initTradeService = SpringUtil.getBean("initTradeService");
        initTradeService.executeInitTransactional(customerCode);
    }

    @Transactional(rollbackFor = Exception.class)
    public void executeInitTransactional(String customerCode) {
        // 专业
        // 根据企业编码查询到的专业，如果列表为空，表示企业第一次做查询，并且企业下无专业数据，需要复制一份内置专业，归属至此企业
        List<ZbStandardsTrade> systemTradeList = tradeMapper.selectListByCustomerCodeAndType(
                Constants.ZbStandardsTradeConstants.SYSTEM_CUSTOMER_CODE, Constants.ZbStandardsTradeConstants.TradeType.SYSTEM_TRADE);

        if (CollectionUtils.isEmpty(systemTradeList)){
            return;
        }

        List<Long> idList = SnowflakeIdUtils.getNextId(systemTradeList.size());
        for (int i = 0; i < systemTradeList.size(); i++) {
            ZbStandardsTrade newTrade = systemTradeList.get(i);
            newTrade.setId(idList.get(i));
            newTrade.setCustomerCode(customerCode);
            newTrade.setCreatorId(null);
            newTrade.setUpdaterId(null);
        }

        //插入专业列表
        tradeMapper.batchInsert(systemTradeList);

        // 主要量指标
        mainQuantityService.executeInit(customerCode);

        // 工程特征
        featureService.initFeatureData(customerCode, null);
    }

    @Override
    protected Boolean tryLockRetry(String customerCode, String lockValue) {
        return redisUtil.tryLockRetry(lockValue, RedisKeyEnum.LOCK_INIT_TRADE_MAIN_QUANTITY_FEATURE, customerCode);
    }

    @Override
    protected void unlock(String customerCode, String lockValue) {
        redisUtil.unlock(lockValue, RedisKeyEnum.LOCK_INIT_TRADE_MAIN_QUANTITY_FEATURE, customerCode);
    }


    /**
     * 新增专业时引用，需要携带工程特征和主要量指标的数据
     */
    public void insertTradeData(ZbStandardsTrade zbStandardsTrade){
        String customerCode = zbStandardsTrade.getCustomerCode();
        String name = zbStandardsTrade.getDescription();
        if (Boolean.FALSE.equals(insertTradeIsNeedInit(customerCode, name))){
            return;
        }

        String lockValue = lockValue();

        if (tryLockRetry(customerCode, lockValue)) {
            try {
                if (Boolean.FALSE.equals(insertTradeIsNeedInit(customerCode, name))){
                    throw new BusinessException("数据已存在，请刷新重试...");
                }
                insertTradeExecuteInit(zbStandardsTrade);
            } finally {
                unlock(customerCode, lockValue);
            }
        }
    }

    private Boolean insertTradeIsNeedInit(String customerCode, String name) {
        return tradeMapper.searchRecordsByCusCodeAndName(name, customerCode) == null;
    }

    private void insertTradeExecuteInit(ZbStandardsTrade zbStandardsTrade) {
        InitTradeService initTradeService = SpringUtil.getBean("initTradeService");
        initTradeService.insertTradeExecuteInitTransactional(zbStandardsTrade);
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertTradeExecuteInitTransactional(ZbStandardsTrade zbStandardsTrade) {
        String customerCode = zbStandardsTrade.getCustomerCode();
        Long globalId = zbStandardsTrade.getCreatorId();
        Long tradeId = zbStandardsTrade.getId();
        String referTradeCode = zbStandardsTrade.getReferTrade();
        String tradeCode = zbStandardsTrade.getTradeCode();
        String tradeName = zbStandardsTrade.getDescription();

        // 查询出专业对应的专业id
        ZbStandardsTrade tradeRefer = tradeMapper.selectListByCusAndTradeCode(customerCode, referTradeCode);
        Long tradeIdSystem = tradeRefer.getId();
        // 保存入库
        tradeMapper.insert(zbStandardsTrade);
        // 同步复制保存工程特征、工程特征分类视图数据
        featureService.copyFeatureToNewTrade(customerCode, globalId, tradeIdSystem, tradeId, referTradeCode, tradeName);
        // 同步复制保存主要量指标数据
        mainQuantityService.initMainQuantityList(customerCode, globalId, tradeIdSystem, tradeId, tradeCode, tradeName);
    }
}
