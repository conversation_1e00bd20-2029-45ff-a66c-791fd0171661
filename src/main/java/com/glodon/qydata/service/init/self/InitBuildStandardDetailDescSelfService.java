package com.glodon.qydata.service.init.self;

import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard;
import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandardDetail;
import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildStandardDetailDesc;
import com.glodon.qydata.mapper.standard.buildStandard.ZbStandardsBuildStandardDetailDescMapper;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 初始化化暂存表标准说明数据
 * <AUTHOR>
 * @date 2022-8-1
 */
@Service
@Slf4j
public class InitBuildStandardDetailDescSelfService {
    @Autowired
    private ZbStandardsBuildStandardDetailDescMapper standardsBuildStandardDetailDescMapper;
    /**
     * 初始化暂存表标准说明
     * @param standard
     * @param details
     */
    public List<ZbStandardsBuildStandardDetailDesc> initData(ZbProjectStandard standard, List<ZbProjectStandardDetail> details) {
        List<ZbStandardsBuildStandardDetailDesc> returnList = Lists.newArrayList();
        if(standard == null || CollectionUtils.isEmpty(details)){
            return returnList;
        }
        List<ZbStandardsBuildStandardDetailDesc> detailDescList = standardsBuildStandardDetailDescMapper.selectByStandardId(standard.getOriginId());
        if(CollectionUtils.isEmpty(detailDescList)){
            return returnList;
        }
        Map<Long,ZbProjectStandardDetail> detailDescMap = details.stream().collect(Collectors.toMap(ZbProjectStandardDetail::getOriginId, x->x,(k1, k2)-> k1));
        for (ZbStandardsBuildStandardDetailDesc detailDesc : detailDescList){
            ZbStandardsBuildStandardDetailDesc standardsBuildStandardDetailDesc = new ZbStandardsBuildStandardDetailDesc();
            Long standardDetailId = detailDesc.getStandardDetailId();
            ZbProjectStandardDetail projectStandardDetail = detailDescMap.get(standardDetailId);
            if(projectStandardDetail == null){
                continue;
            }
            BeanUtils.copyProperties(detailDesc,standardsBuildStandardDetailDesc);
            standardsBuildStandardDetailDesc.setOriginId(detailDesc.getId());
            standardsBuildStandardDetailDesc.setStandardDetailId(projectStandardDetail.getId());
            standardsBuildStandardDetailDesc.setStandardId(standard.getId());
            standardsBuildStandardDetailDesc.setId(SnowflakeIdUtils.getNextId());
            returnList.add(standardsBuildStandardDetailDesc);
        }
        if(!CollectionUtils.isEmpty(returnList)){
            standardsBuildStandardDetailDescMapper.batchSaveSelf(returnList);
        }
        return returnList;
    }
}
