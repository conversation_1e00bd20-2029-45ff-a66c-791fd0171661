package com.glodon.qydata.service.init.enterprise;

import cn.hutool.core.collection.CollUtil;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.common.enums.TagTypeEnum;
import com.glodon.qydata.entity.standard.tag.TagIntegration;
import com.glodon.qydata.entity.standard.trade.TradeTagEnum;
import com.glodon.qydata.mapper.standard.tag.TagIntegrationMapper;
import com.glodon.qydata.mapper.standard.trade.TradeTagEnumMapper;
import com.glodon.qydata.service.init.InitService;
import com.glodon.qydata.util.RedisUtil;
import com.glodon.qydata.util.SpringUtil;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 初始化专业分类标签
 * @date 2024/11/12 16:09
 */
@Service
public class InitTradeTagService extends InitService {
    @Autowired
    private TradeTagEnumMapper tradeTagMapper;

    @Autowired
    private TagIntegrationMapper tagIntegrationMapper;

    @Autowired
    private RedisUtil redisUtil;

    public void initData(String customerCode){
        super.initData(customerCode, null, null);
    }

    @Override
    protected Boolean isNeedInit(String customerCode, Integer type, Long id) {
        // 没有标签记录,并且没有初始化标记
        return CollectionUtils.isEmpty(tradeTagMapper.selectAllListByCustomerCode(customerCode))
                && CollUtil.isEmpty(tagIntegrationMapper.selectByEnterpriseId(customerCode, TagTypeEnum.TRADE_TAG.getTagType()));
    }

    @Override
    protected void executeInit(String customerCode, Integer type, Long id) {
        InitTradeTagService initTradeService = SpringUtil.getBean("initTradeTagService");
        initTradeService.executeInitTransactional(customerCode);
    }

    @Transactional(rollbackFor = Exception.class)
    public void executeInitTransactional(String customerCode) {
        // 查询内置的专业分类
        List<TradeTagEnum> systemTradeTagList = tradeTagMapper.selectAllListByCustomerCode(
                Constants.ZbStandardsTradeConstants.SYSTEM_CUSTOMER_CODE);
        if (CollectionUtils.isEmpty(systemTradeTagList)){
            return;
        }

        // 保存为当前企业的专业分类
        for (TradeTagEnum entity : systemTradeTagList) {
            if (!Constants.CategoryTagConstants.SYSTEM_CUSTOMER_CODE.equals(entity.getEnterpriseId())) {
//                entity.setOriginalId(entity.getId());
            } else {
                entity.setEnterpriseId(customerCode);
            }

            Long nextId = SnowflakeIdUtils.getNextId();
            entity.setId(nextId);
            entity.setCreateTime(new Date());
        }

        // 插入数据
        tradeTagMapper.saveBatch(systemTradeTagList);

        // 记录初始化状态
        tagIntegrationMapper.save(TagIntegration.builder()
                .id(SnowflakeIdUtils.getNextId())
                .enterpriseId(customerCode)
                .createTime(new Date())
                .tagType(TagTypeEnum.TRADE_TAG.getTagType())
                .build());
    }

    @Override
    protected Boolean tryLockRetry(String customerCode, String lockValue) {
        return redisUtil.tryLockRetry(lockValue, RedisKeyEnum.LOCK_INIT_TRADE_MAIN_QUANTITY_FEATURE, customerCode);
    }

    @Override
    protected void unlock(String customerCode, String lockValue) {
        redisUtil.unlock(lockValue, RedisKeyEnum.LOCK_INIT_TRADE_MAIN_QUANTITY_FEATURE, customerCode);
    }
}
