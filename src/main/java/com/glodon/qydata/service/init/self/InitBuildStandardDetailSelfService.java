package com.glodon.qydata.service.init.self;

import cn.hutool.core.collection.CollUtil;
import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard;
import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandardDetail;
import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildStandardDetailDesc;
import com.glodon.qydata.mapper.standard.buildStandard.ZbProjectStandardDetailMapper;
import com.glodon.qydata.mapper.standard.buildStandard.ZbProjectStandardMapper;
import com.glodon.qydata.mapper.standard.buildStandard.ZbStandardsBuildCategoryMapper;
import com.glodon.qydata.util.SpringUtil;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 建造标准明细暂存数据初始化
 * @date 2022/7/11 9:54
 */
@Service
public class InitBuildStandardDetailSelfService extends InitSelfService {
    @Autowired
    private ZbProjectStandardMapper zbProjectStandardMapper;
    @Autowired
    private ZbProjectStandardDetailMapper zbProjectStandardDetailMapper;
    @Autowired
    private InitBuildStandardDetailDescSelfService initBuildStandardDetailDescSelfService;
    @Autowired
    private InitBuildStandardPositionService initBuildStandardPositionService;
    @Autowired
    private ZbStandardsBuildCategoryMapper zbStandardsBuildCategoryMapper;

    public void initData(Long standardId) {
        super.initData(null, null, standardId);
    }

    @Override
    protected Boolean isNeedInit(String customerCode, Integer type, Long standardId) {
        return CollUtil.isEmpty(zbProjectStandardDetailMapper.selectSelfByStandardId(standardId))
                && CollUtil.isEmpty(zbStandardsBuildCategoryMapper.selectSelfByStandardId(standardId));
    }

    @Override
    protected void executeInit(String customerCode, Integer type, Long standardId) {
        InitBuildStandardDetailSelfService initBuildStandardDetailSelfService = SpringUtil.getBean("initBuildStandardDetailSelfService");
        initBuildStandardDetailSelfService.executeInitTransactional(standardId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void executeInitTransactional(Long standardId) {
        ZbProjectStandard standard = zbProjectStandardMapper.selectSelfStandardById(standardId);

        if (standard == null) {
            return;
        }

        List<ZbProjectStandardDetail> details = zbProjectStandardDetailMapper.selectByStandardId(standard.getOriginId());

        if (CollUtil.isNotEmpty(details)) {
            for (ZbProjectStandardDetail entity : details) {
                entity.setOriginId(entity.getId());
                entity.setId(SnowflakeIdUtils.getNextId());
                entity.setStandardId(standardId);
                entity.setUpdateTime(new Date());
            }

            zbProjectStandardDetailMapper.saveSelfBatch(details);
        }
        // 初始化标准说明
        List<ZbStandardsBuildStandardDetailDesc> detailDescList = initBuildStandardDetailDescSelfService.initData(standard, details);
        // 初始化产品定位
        initBuildStandardPositionService.initData(standard, detailDescList);
    }
}
