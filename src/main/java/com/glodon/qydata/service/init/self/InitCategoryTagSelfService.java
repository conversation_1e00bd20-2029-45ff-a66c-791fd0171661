package com.glodon.qydata.service.init.self;

import cn.hutool.core.collection.CollUtil;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.enums.TagTypeEnum;
import com.glodon.qydata.entity.standard.category.CategoryTag;
import com.glodon.qydata.entity.standard.category.CategoryTagEnum;
import com.glodon.qydata.entity.standard.tag.TagIntegration;
import com.glodon.qydata.mapper.standard.category.*;
import com.glodon.qydata.mapper.standard.tag.TagIntegrationMapper;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;


@Service
@Slf4j
public class InitCategoryTagSelfService extends InitSelfService {
    @Autowired
    private CategoryTagSelfMapper categoryTagSelfMapper;

    @Autowired
    private CategoryTagMapper categoryTagMapper;

    @Autowired
    private CategoryTagEnumMapper categoryTagEnumMapper;

    @Autowired
    private CategoryTagEnumSelfMapper categoryTagEnumSelfMapper;

    @Autowired
    private TagIntegrationMapper tagIntegrationMapper;

    public void initData(String customerCode) {
        super.initData(customerCode, null, null);
    }

    @Override
    protected Boolean isNeedInit(String customerCode, Integer type, Long id) {
        //从暂存表和集成表中查询数据
        return CollUtil.isEmpty(categoryTagSelfMapper.selectByEnterpriseId(customerCode))
                && CollUtil.isEmpty(tagIntegrationMapper.selectByEnterpriseId(customerCode, TagTypeEnum.CATEGORY_TAG.getTagType()));
    }

    @Override
    protected void executeInit(String customerCode, Integer type, Long id) {
//        List<CategoryTag> tagList = categoryTagMapper.selectByEnterpriseId(customerCode);
//        List<CategoryTagEnum> enumList = new ArrayList<>();
//        if (CollUtil.isEmpty(tagList)) {
//            //主表没有数据就是第一次初始化，直接拿内置数据的值
//            tagList = categoryTagMapper.selectByEnterpriseId(Constants.CategoryTagConstants.SYSTEM_CUSTOMER_CODE);
//            enumList = categoryTagEnumMapper.selectByTagId(Constants.CategoryTagConstants.SYSTEM_CUSTOMER_CODE, id);
//        }

        List<CategoryTag> tagList = categoryTagMapper.selectByEnterpriseId(Constants.CategoryTagConstants.SYSTEM_CUSTOMER_CODE);
        List<CategoryTagEnum> enumList = categoryTagEnumMapper.selectByTagId(Constants.CategoryTagConstants.SYSTEM_CUSTOMER_CODE, id);

        Map<Long, Long> tagIdMap = new HashMap<>();
        if (CollUtil.isNotEmpty(tagList)) {
            for (CategoryTag entity : tagList) {
                if (!Constants.CategoryTagConstants.SYSTEM_CUSTOMER_CODE.equals(entity.getEnterpriseId())) {
                    entity.setOriginalId(entity.getId());
                } else {
                    entity.setEnterpriseId(customerCode);
                }
                Long nextId = SnowflakeIdUtils.getNextId();
                tagIdMap.put(entity.getId(), nextId);
                entity.setId(nextId);
                entity.setCreateTime(new Date());
            }

            categoryTagSelfMapper.saveBatch(tagList);
        }

        if (CollUtil.isNotEmpty(enumList)) {
            for (CategoryTagEnum entity : enumList) {
                entity.setEnterpriseId(customerCode);
                entity.setId(SnowflakeIdUtils.getNextId());
                entity.setCreateTime(new Date());
                entity.setTagId(tagIdMap.get(entity.getTagId()));
            }

            categoryTagEnumSelfMapper.saveBatch(enumList);
        }
        tagIntegrationMapper.save(TagIntegration.builder()
                .id(SnowflakeIdUtils.getNextId())
                .enterpriseId(customerCode)
                .createTime(new Date())
                .tagType(TagTypeEnum.CATEGORY_TAG.getTagType())
                .build());
    }
}
