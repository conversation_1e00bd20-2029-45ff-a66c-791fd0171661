package com.glodon.qydata.service.cloud;

import com.glodon.qydata.config.feign.FeignConfiguration;
import com.glodon.qydata.vo.cloud.UserInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * @className: CloudFeignService
 * @description:
 * @author: zhaoyj-g
 * @date: 2020/9/23
 **/
@FeignClient(name = "cloud-account",url = "${glodon.getUserinfoByIdentityHost}",configuration = FeignConfiguration.class)
public interface CloudAccountFeignService {

    /**
     * 获取用户信息
     * @param globalId
     * @return
     */
    @RequestMapping(value = "/api/userinfobyid?type=globalid&identity={globalId}",method = RequestMethod.GET,headers = "Authorization={accessToken}")
    UserInfo getUser(@PathVariable("globalId") String globalId, @PathVariable("accessToken") String accessToken);

}
