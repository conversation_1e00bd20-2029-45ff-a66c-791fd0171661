package com.glodon.qydata.service.cloud;

import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.config.CommonConfig;
import com.glodon.qydata.util.MD5;
import com.glodon.qydata.util.RedisUtil;
import com.glodon.qydata.vo.cloud.CloudR;
import com.glodon.qydata.vo.cloud.CloudTokenDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @className: CloudService
 * @description:
 * @author: zhaoyj-g
 * @date: 2020/9/24
 **/
@Service("cloudService")
@Slf4j
public class CloudService {
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private CloudFeignService cloudFeignService;
    @Autowired
    private CommonConfig commonConfig;

    private String getToken() {
        try {
            long ts = System.currentTimeMillis();
            CloudR<CloudTokenDto> cd = cloudFeignService.getToken(commonConfig.GCW_APPID, MD5.md5(commonConfig.GCW_SECRET + ts + commonConfig.GCW_SECRET_KEY).toUpperCase(),ts,1);
            return cd.getData().getToken();
        } catch (Exception e) {
            return null;
        }
    }

    public String getAndCacheToken() {
        String gwcToken = getToken();
        redisUtil.setObject(RedisKeyEnum.GCW_TOKEN_KEY,gwcToken);
        return gwcToken;
    }
    public String getCacheToken() {
        String gwcToken = redisUtil.getObject(RedisKeyEnum.GCW_TOKEN_KEY,String.class);
        if (StringUtils.isBlank(gwcToken)) {
            return getAndCacheToken();
        }
        return gwcToken;
    }
}
