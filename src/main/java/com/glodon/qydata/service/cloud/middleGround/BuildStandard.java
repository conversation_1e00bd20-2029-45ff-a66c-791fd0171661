package com.glodon.qydata.service.cloud.middleGround;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @description: 中台返回-建造标准
 * <AUTHOR>
 * @date 2022/8/22 17:07
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BuildStandard implements Serializable {

    /** 唯一id(中台id) */
    private Long id;
    /** 编码 */
    private String sequence;
    /** 标准名称 */
    private String name;
    /** 标准说明 */
    private String standardDescription;
    /** 带T_业态code */
    private String projectCategoryCode;
    /** 业态定位 */
    private String categoryPositioning;
    /** 建造标准值 */
    private String standardValue;
    /** 备注 */
    private String remark;
    /** 默认建造标准枚举值 */
    private String defaultStandardValue;
    /** 建造标准模板-levelCode值 */
    private String templateLevelCode;
    /** 所属业态 */
    private String categoryCode;
    /** 前端显示组件类型：checkBox、radio、select、input */
    private  String showType;
    /** 值类型text number percent */
    private  String valueType;
}
