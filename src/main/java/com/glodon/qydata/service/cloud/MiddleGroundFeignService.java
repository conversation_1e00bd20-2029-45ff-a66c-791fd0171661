package com.glodon.qydata.service.cloud;

import com.glodon.qydata.config.feign.FeignConfiguration;
import com.glodon.qydata.service.cloud.middleGround.HistoricalBuildStandard;
import com.glodon.qydata.vo.common.MiddleGroundPage;
import com.glodon.qydata.vo.standard.buildStandard.ImportListDetailVo;
import com.glodon.qydata.vo.standard.buildStandard.ImportListVo;
import com.glodon.qydata.vo.standard.buildStandard.MiddleGroundResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * @packageName: com.glodon.qydata.service.cloud
 * @className: MiddleGroundFeignService
 * @author: yanyuhui <EMAIL>
 * @date: 2022/8/22 17:34
 * @description: 中台服务调用Feign
 */
@FeignClient(name = "middle-ground", url = "${glodon.middlegroundhost}", configuration = FeignConfiguration.class)
public interface MiddleGroundFeignService {

    @PostMapping("/gcdp-mbcb/api/project/getProjectList/{enterpriseId}")
    MiddleGroundResp<MiddleGroundPage<ImportListDetailVo>> importList(@PathVariable(value = "enterpriseId") String enterpriseId, @RequestBody ImportListVo listVo);

    /**
     * @description: 建造标准详情获取
     * @param enterpriseId
     * @param id 工程id
     * @param categoryCode 可空， 业态编码Code null默认返回所有业态的建造标准数据
     * @param withConstructionInstallationIndex 可空， 是否返回建安指标数据 0 不返回 1 返回 null值 默认返回
     * @return com.glodon.qydata.vo.standard.buildStandard.MiddleGroundResp<com.glodon.qydata.controller.standard.buildStandard.gcdp.HistoricalBuildStandard>
     * <AUTHOR>
     * @date 2022/8/23 13:50
     */
    @GetMapping("/gcdp-mbcb/api/buildStandard/getBuildStandardDetail/{enterpriseId}")
    MiddleGroundResp<HistoricalBuildStandard> getBuildStandardDetail(@PathVariable(value = "enterpriseId") String enterpriseId,
                                                                     @RequestParam("id") Long id,
                                                                     @RequestParam("categoryCode") String categoryCode,
                                                                     @RequestParam("withConstructionInstallationIndex") Integer withConstructionInstallationIndex);
}
