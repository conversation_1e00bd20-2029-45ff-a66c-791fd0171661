package com.glodon.qydata.service.cloud;

import com.glodon.qydata.config.feign.FeignConfiguration;
import com.glodon.qydata.vo.cloud.CloudR;
import com.glodon.qydata.vo.cloud.CloudTokenDto;
import com.glodon.qydata.vo.system.EntInfoVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @className: CloudFeignService
 * @description:
 * @author: zhaoyj-g
 * @date: 2020/9/23
 **/
@FeignClient(name = "cloud",url = "${glodon.gcw_cloud_url}",configuration = FeignConfiguration.class)
public interface CloudFeignService {

    /**
     * 取token
     * @param appid
     * @param sign
     * @param t1
     * @return
     */
    @GetMapping("/auth/token/sign")
    CloudR<CloudTokenDto> getToken(@RequestParam(value = "appid") String appid, @RequestParam(value = "sign") String sign, @RequestParam(value = "t1") Long t1, @RequestParam(value = "isContinue") Integer isContinue);

    /**
     * 从广材网获取企业信息
     * @param appid
     * @param globalId
     * @return
     */
    @GetMapping(value = "/user/customer/get-ent-info")
    CloudR<EntInfoVo> getEntInfo(@RequestParam(value = "globalId") String globalId, @RequestHeader(name = "appid") String appid, @RequestHeader(name = "token") String token);

}
