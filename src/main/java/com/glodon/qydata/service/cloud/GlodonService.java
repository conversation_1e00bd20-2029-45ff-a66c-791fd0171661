package com.glodon.qydata.service.cloud;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.glodon.qydata.common.constant.DigitalCostConstants;
import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.config.CommonConfig;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.util.BasicAuthBuilder;
import com.glodon.qydata.util.MD5;
import com.glodon.qydata.util.RedisUtil;
import com.glodon.qydata.util.SignGeneratorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: ideaworkspace
 * @description: 依赖广联云相关接口
 * @author: zhaohy-c
 * @create: 2020-03-10 14:10
 */
@Service("glodonService")
@Slf4j
@SuppressWarnings("squid:S1192") // 禁止sonar魔法值检测
public class GlodonService {

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private RedisUtil redisUtil;

    private static final String APP_KEY = "0b9216dea2514131a46d6bdbcfc771df";

    private static final String APP_SECRET = "ebb14b29174f4a6c8177ebbd0076f36e";

    private static final String CODE = "code";
    private static final int CODE_VALUE_ZERO = 0;

    /**
     * 根据用户ID查询用户及企业信息
     * 参考接口：http://pm.glodon.com/wiki/pages/viewpage.action?pageId=********
     * @param userGlobalId
     * @return
    {
        "code": 0,
        "message": "OK",
        "data": {
            "enterprise": {
                "id": 6356680818883661899,//企业ID，即主账号userid
                "displayName": "我的企业",//企业展示名，昵称。********新增
                "status":0,//企业认证账号： -1 未认证 0 待审核 1 营业执照通过 2 营业执照认证不通过
                "accountName": "ewqe31",//企业登录名称
                "name": "ewqe31",//企业名称
                "orgCode": null,//'企业编码/企业别名/组织编码/企业后缀'
                "creditCode": null,//统一社会信用代码
                "updateTime": *************,//
                "createTime": *************
             },
        "isMainAccount": true // 当前账号是否是企业主账号
    }
    }
     * @throws BusinessException
     * @auther: weijf
     */
    public JSONObject getEntInfor(String userGlobalId, String accessToken) throws BusinessException {
        String url = new StringBuffer().append(commonConfig.getGlodonEntUrl()).append("/api/ent/getEntInfor")
                .append("?userId=").append(userGlobalId).toString();
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add(BasicAuthBuilder.headerKey(), "Bearer "+accessToken);
            headers.add(DigitalCostConstants.CONTENT_TYPE_HEADER, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
            // 明确指定Accept类型为JSON，避免application/octet-stream问题
            headers.setAccept(Arrays.asList(MediaType.APPLICATION_JSON, MediaType.TEXT_PLAIN));

            HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(null, headers);
            // 使用String接收响应，避免HttpMessageConverter问题
            ResponseEntity<String> exchange = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
            String responseBody = exchange.getBody();

            // 手动解析JSON字符串为JSONObject
            JSONObject resultBody = null;
            if (StringUtils.isNotBlank(responseBody)) {
                try {
                    resultBody = JSONObject.parseObject(responseBody);
                } catch (Exception e) {
                    log.error("解析JSON响应失败，响应内容: {}, 错误: {}", responseBody, e.getMessage());
                    throw new BusinessException("解析服务响应失败: " + e.getMessage());
                }
            }
            JSONObject data = null;

            if(resultBody!=null && CODE_VALUE_ZERO == resultBody.getIntValue(CODE) && resultBody.getJSONObject(DigitalCostConstants.RESULT_KEY_DATA)!=null) {
                data = resultBody.getJSONObject(DigitalCostConstants.RESULT_KEY_DATA);
            }
            return data;
        }catch (Exception e) {
            log.error("访问广联云接口 getEntInfor error,url={},userGlobalId={},accessToken={},e={}",url,userGlobalId,accessToken,e.getMessage());
            throw new BusinessException(ResponseCode.ERROR.getCode(), "调用广联云查询用户及企业信息接口异常,e:"+e.getMessage());
        }
    }

    /**
     * 获取应用级的accessToken
     * @return
     * <AUTHOR>
     */
    public String getClientCredentialsAccessToken() throws BusinessException{
        try{
            String token = redisUtil.getString(RedisKeyEnum.ACCESS_TOKEN_CLIENT_CREDENTIALS);
            if(StringUtils.isNotEmpty(token)){
                return token;
            }
            JSONObject tokenObj = getAccessToken(null,"client_credentials");
            if(tokenObj!=null && tokenObj.getJSONObject(DigitalCostConstants.RESULT_KEY_DATA) !=null){
                JSONObject data = tokenObj.getJSONObject(DigitalCostConstants.RESULT_KEY_DATA);
                String accessToken = data.getString(DigitalCostConstants.ACCESS_TOKEN_WITH_UNDERLINE);
                Integer expires = data.getInteger("expires_in");
                RedisKeyEnum redisKeyEnum = RedisKeyEnum.ACCESS_TOKEN_CLIENT_CREDENTIALS;
                redisKeyEnum.setSeconds(expires);
                redisUtil.setString(redisKeyEnum,accessToken);
                return accessToken;
            }
            log.error("调用广联云获取应用级的accessToken接口异常,返回={}", tokenObj == null ? null : tokenObj.toString());
            throw new BusinessException( "调用广联云获取应用级的accessToken接口异常");
        }catch (BusinessException de){
            throw de;
        }
    }


    /**
     * 通过userId免密获取用户级accessToken
     * 参考文档：http://pm.glodon.com/wiki/pages/viewpage.action?pageId=70018213
     * @param userGlobalId
     * @return
     * @throws BusinessException
     * @auther: weijf
     */
    public JSONObject getAccessToken(String userGlobalId, String grantType) throws BusinessException {
        String url = new StringBuffer().append(commonConfig.getGlodonUrl()).append("/v3/oauth2/token").toString();
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add(BasicAuthBuilder.headerKey(), BasicAuthBuilder.headerValue());
            headers.add(DigitalCostConstants.CONTENT_TYPE_HEADER, MediaType.APPLICATION_FORM_URLENCODED_VALUE);

            MultiValueMap<String, String> params= new LinkedMultiValueMap<>();
            params.add("grant_type", grantType);
            if(userGlobalId!=null) {
                params.add("uid", userGlobalId);
            }
            HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(params, headers);
            ResponseEntity<JSONObject> exchange = restTemplate.exchange(url, HttpMethod.POST, entity, JSONObject.class);
            return exchange.getBody();
        }catch (Exception e) {
            log.error("访问广联云接口 getAccessToken error,url={},userGlobalId={},grantType={},e={}",url,userGlobalId,grantType,e.getMessage());
            throw new BusinessException("调用广联云查询授权token接口异常,e:"+e.getMessage());
        }
    }


    /**
     * 后台系统直接查询成员列表
     * 参考接口：http://pm.glodon.com/wiki/pages/viewpage.action?pageId=72922333
     * @param accessToken (使用client_credentials方式获取accessToken)
     * @param entId 主账号ID（即企业ID）
     * @param identity 要查询的姓名或账号，传null查询所有账号
     * @param pageNum 查询页数（默认1）
     * @param pageSize 每页数据数（默认10，最大200）
     * @param deleted 是否查询已删除账号，0：查询正常账号，1：查询已删除账号
     * @return
     * @throws BusinessException
     */
    public JSONObject getEntMemberList(String accessToken, String entId, String identity, int pageNum, String pageSize, String deleted) throws BusinessException {
        String url = new StringBuffer().append(commonConfig.getGlodonEntUrl()).append("/api/member/direct/query").toString();
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add(BasicAuthBuilder.headerKey(), "Bearer "+accessToken);
            headers.add(DigitalCostConstants.CONTENT_TYPE_HEADER, MediaType.APPLICATION_FORM_URLENCODED_VALUE);

            MultiValueMap<String, String> params= new LinkedMultiValueMap<>();
            params.add("identity", identity);
            params.add("entId", entId);
            params.add("pageNum",pageNum+"");
            params.add("pageSize",pageSize);
            params.add("deleted", StringUtils.isEmpty(deleted)?"0":deleted);

            HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(params, headers);
            ResponseEntity<JSONObject> exchange = restTemplate.exchange(url, HttpMethod.POST, entity, JSONObject.class);
            return exchange.getBody();
        }catch (Exception e) {
            log.error("访问广联云接口 /api/member/direct/query error,url={},accessToken={},identity={},pageNum={},pageSize={},e={}",url,accessToken,identity,pageNum,pageSize,e.getMessage());
            throw new BusinessException("调用广联云查询企业账号下的成员接口异常");
        }
    }

    /**
     * 后台直接查询成员信息接口
     * http://pm.glodon.com/wiki/pages/viewpage.action?pageId=74665657
     * @param accessToken 应用级accessToken
     * @param userId 用户中心globalId
     * @return
     * @throws BusinessException
     */
    public JSONObject getEntMember(String accessToken, String userId) throws BusinessException {
        JSONObject data;
        String url = new StringBuffer().append(commonConfig.getGlodonEntUrl()).append("/api/member/direct/queryOne").append("?userId=").append(userId).toString();
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add(BasicAuthBuilder.headerKey(), "Bearer "+accessToken);
            headers.add(DigitalCostConstants.CONTENT_TYPE_HEADER, MediaType.APPLICATION_FORM_URLENCODED_VALUE);

            HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(null, headers);
            ResponseEntity<JSONObject> exchange = restTemplate.exchange(url, HttpMethod.GET, entity, JSONObject.class);
            JSONObject resultBody = exchange.getBody();

            int code = resultBody.getIntValue(CODE);
            if (resultBody!=null && CODE_VALUE_ZERO == code) {
                data = resultBody.getJSONObject(DigitalCostConstants.RESULT_KEY_DATA);
            }else{
                throw new BusinessException(resultBody.getString("cause"));
            }
            return data;
        }catch (BusinessException de){
            throw de;
        }catch (Exception e) {
            log.error("访问广联云接口 /api/member/direct/queryOne error,url={},userId={},e={}",url,accessToken,userId,e.getMessage());
            throw new BusinessException("调用广联云查询成员信息接口异常");
        }
    }

    /**
     * 查询企业主账号下产品权益.
     可以根据产品和企业子账号globalId查询.
     接口地址 ：https://pm.glodon.com/wiki/pages/viewpage.action?pageId=75046877
     * @param enterpriseGlobalId
     * @param gmsPids
     * @param memberGlobalId
     * @return
     * @throws BusinessException
     */
    public JSONObject getProductRights(String enterpriseGlobalId, String gmsPids,String memberGlobalId) throws BusinessException {
        JSONObject data;
        String url = new StringBuffer().append(commonConfig.getGlodonApiAuthUrl()).append("/api/public/v1/customer/").append(enterpriseGlobalId)
                .append("/product/rights?appKey=kqIUvqfcH7obwsZRx4U3L3yG5M3sLuRL&licenseType=cloud_customer&gmsPids=").append(gmsPids)
                .append("&memberGlobalId=").append(memberGlobalId)
                .append("&showUsedValueFlag=true").toString();
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add(DigitalCostConstants.CONTENT_TYPE_HEADER, MediaType.APPLICATION_FORM_URLENCODED_VALUE);

            HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(null, headers);
            url = SignGeneratorUtils.signTheRequest(url, StringUtils.EMPTY);
            ResponseEntity<JSONObject> exchange = restTemplate.exchange(url, HttpMethod.GET, entity, JSONObject.class);
            JSONObject resultBody = exchange.getBody();

            if (resultBody!=null && resultBody.getIntValue(CODE) == 0) {
                data = resultBody.getJSONObject(DigitalCostConstants.RESULT_KEY_DATA);
            }else{
                throw new BusinessException(resultBody==null?"resultBody is null":resultBody.toJSONString());
            }
            return data;
        }catch (BusinessException de){
            throw de;
        }catch (Exception e) {
            log.error("访问广联云授权接口 /api/public/v1/customer/{enterpriseGlobalId}/product/rights error,url={},userId={},gmsPids={},e={}",url,enterpriseGlobalId,gmsPids,e.getMessage());
            throw new BusinessException("调用广联云授权接口：查询企业主账号下产品权益 异常");
        }
    }

    public List<String> getUserIdsByGlobalIds(List<String> globalIds) {
        List<String> userIds = new ArrayList<>();
        String paramValue = null;
        try {
            paramValue = URLEncoder.encode(StringUtils.join(globalIds, ","), Charset.defaultCharset().displayName());
        } catch (UnsupportedEncodingException e) {
            paramValue = StringUtils.join(globalIds, ",");
            log.error("参数URL编码异常：{}", paramValue);
        }
        String url = commonConfig.getGlodonUrl() + "/v3/api/security/userid?identity=" + paramValue;

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add(BasicAuthBuilder.headerKey(), BasicAuthBuilder.headerValue());
            HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(null, headers);
            ResponseEntity<JSONObject> exchange = restTemplate.exchange(url, HttpMethod.GET, entity, JSONObject.class);
            JSONObject resultBody = exchange.getBody();

            if (resultBody!=null && resultBody.getIntValue(CODE) == 0) {
                JSONArray data = resultBody.getJSONArray(DigitalCostConstants.RESULT_KEY_DATA);
                for (int i = 0; i < data.size(); i++) {
                    JSONObject obj = data.getJSONObject(i);
                    userIds.addAll(obj.values().stream().map(String::valueOf).collect(Collectors.toList()));
                }
            } else {
                throw new BusinessException(resultBody==null?"resultBody is null":resultBody.toJSONString());
            }
        } catch (Exception e) {
            log.error("访问广联云授权接口 /v3/api/security/userid?identity= error,identity={},e={}", paramValue, e.getMessage());
            throw new BusinessException("调用广联云授权接口：根据globalId查询userId 异常");
        }
        return userIds;
    }

    public JSONArray getUserInfoByUserIds(List<String> userIds) {
        String url = commonConfig.getGlodonUrl() + "/v3/api/security/userinfo";
        HttpHeaders headers = new HttpHeaders();
        headers.add(BasicAuthBuilder.headerKey(), BasicAuthBuilder.headerValue());
        headers.add(DigitalCostConstants.CONTENT_TYPE_HEADER, MediaType.APPLICATION_JSON_VALUE);
        JSONObject body = new JSONObject();
        body.put("userId", StringUtils.join(userIds, ","));

        HttpEntity<String> entity = new HttpEntity<>(body.toJSONString(), headers);
        ResponseEntity<JSONObject> response = restTemplate.postForEntity(url, entity, JSONObject.class);
        JSONObject resultBody = response.getBody();

        try {
            if (resultBody != null && resultBody.getIntValue(CODE) == 0) {
                return resultBody.getJSONArray(DigitalCostConstants.RESULT_KEY_DATA);
            } else {
                throw new BusinessException(resultBody == null ? "resultBody is null" : resultBody.toJSONString());
            }
        } catch (Exception e) {
            log.error("访问广联云授权接口 /v3/api/security/userinfo error,userIds={},e={}", StringUtils.join(userIds, ","), e.getMessage());
            throw new BusinessException("调用广联云授权接口：根据userId查询userInfo 异常");
        }
    }

    /**
     * @description: 根据租户id查询企业id
     * @author: luoml-b
     * @date: 2023/7/4 15:24
     * @param: tenantId
     * @return: java.lang.String
     **/
    public String getEnterpriseIdByTenantId(String tenantId) {
        if (StringUtils.isBlank(tenantId)) {
            return null;
        }

        JSONObject entInfo = getEntInfoByTenantId(tenantId);
        if (entInfo != null) {
            return entInfo.getString("channelCustomerId");
        }

        return null;
    }


    /**
     * @description: 根据租户id查询企业信息
     * @author: luoml-b
     * @date: 2023/7/4 15:19
     * @param: tenantId
     * @return: com.alibaba.fastjson.JSONObject
     **/
    public JSONObject getEntInfoByTenantId(String tenantId) {
        String url = commonConfig.getGlodonApiAuthUrl() + "/api/public/v1/customer/queryByChannel" +
                "?appKey=" + APP_KEY + "&channelCode=PTC&channelCustomerId=" + tenantId;
        try {
            url = signPayRequest(url, null);
            HttpHeaders headers = new HttpHeaders();
            headers.add(DigitalCostConstants.CONTENT_TYPE_HEADER, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
            HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(null, headers);
            ResponseEntity<JSONObject> exchange = restTemplate.exchange(url, HttpMethod.GET, entity, JSONObject.class);
            JSONObject resultBody = exchange.getBody();
            if (resultBody != null && new Integer("0").equals(resultBody.getIntValue(CODE))) {
                return resultBody.getJSONObject(DigitalCostConstants.RESULT_KEY_DATA);
            }
        }  catch (Exception e) {
            log.error("根据租户id获取企业id接口 error, url={}", url, e);
            throw new BusinessException(ResponseCode.ERROR, "调用根据租户id获取企业id接口异常");
        }

        return null;
    }

    /**
     * @description: 接口签名方法：用于api-auth.glodon.com接口
     * @author: luoml-b
     * @date: 2023/7/4 15:20
     * @param: url
     * @param: body
     * @return: java.lang.String
     **/
    public static String signPayRequest(String url, String body) {
        Map<String, String> requestParams = new TreeMap<>(); // 参数需要按字典序排序
        String pre_url = url.substring(0, url.indexOf("?"));
        StringBuilder sb = new StringBuilder(pre_url); // 不带参数的URL
        String param = url.substring(url.indexOf("?") + 1);
        String[] paramArray = param.split("&");
        for (String s : paramArray) {
            String[] p = s.split("=");
            if (p.length == 2) {
                requestParams.put(p[0], p[1]);  // 获取url中的所有参数
            }
        }

        for (Map.Entry<String, String> entry : requestParams.entrySet()) {
            String key = entry.getKey();
            String value = null;
            if (entry.getValue() != null) {
                value = entry.getValue();
            }
            if (!key.equals("g-signature") && value != null) {
                //g-signature不参与签名运算
                sb.append("&").append(value);
            }
        }
        if (StringUtils.isNotBlank(body)) {
            sb.append("&").append(body); // body放在参数后面
        }
        sb.append("&").append(APP_SECRET); // 最后加上Secret
        String sign = MD5.md5(sb.toString()); //使用MD5签名
        if (StringUtils.isNotEmpty(sign)) {
            url += "&g-signature=" + sign.toUpperCase(); // g-signature为签名结果，添加到URL
        }

        return url;
    }

}
