package com.glodon.qydata.service.cloud;

import com.glodon.qydata.config.feign.FeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * @className: HuiGuoFeignService
 * @description:
 * @author: tianzf
 * @date: 2022/3/8
 **/
@FeignClient(name = "huiguo.data.migration",url = "https://hgdapi-pre.glodon.com",configuration = FeignConfiguration.class)
public interface HuiGuoFeignService {

    @PostMapping(value = "/gdap/api/basicinformation/tenantall")
    String tenantAll();

    @PostMapping(value = "/gdap/api/basicinformation/tenandata")
    String tenantData(@RequestBody Map<String, ?> entityBody);
}
