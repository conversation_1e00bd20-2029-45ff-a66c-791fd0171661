package com.glodon.qydata.service.depend;

import com.alibaba.fastjson.JSONObject;
import com.glodon.qydata.config.feign.ApiAuthFeignConfiguration;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * @className: ApiAuthFeignService
 * @description: 授权中心接口调用层
 * @author: lijj-h
 * @date: 2022/7/13
 **/
@FeignClient(name = "apiAuth",url = "${apiAuth.url}",configuration = {ApiAuthFeignConfiguration.class})
public interface ApiAuthFeignService {

    /**
    　　* @description: 根据企业主账号globalId查询客户信息
    　　* @param enterpriseId, appKey, gSignature
    　　* @return JSONObject
    　　* <AUTHOR>
    　　* @date 2022/7/13 17:28
    　　*/
    @GetMapping(value = "/api/public/v1/customer/enterpriseAccount/{enterpriseGlobalId}")
    JSONObject getQyDataByEnterpriseId(@PathVariable("enterpriseGlobalId") String enterpriseId, @RequestParam("appKey") String appKey, @RequestParam("g-signature") String gSignature);

    /**
     　　* @description: 查询企业主账号下产品权益.
     　　* @param enterpriseId, appKey, gSignature
     　　* @return JSONObject
     　　* <AUTHOR>
     　　* @date 2022/7/13 17:28
     　　*/
    @RequestMapping(value = "/api/public/v1/customer/{enterpriseGlobalId}/product/rights?appKey={appKey}&gmsPids={gmsPids}&memberGlobalId={memberGlobalId}&g-signature={gSignature}&showUsedValueFlag=false&licenseType=cloud_customer",
            method = RequestMethod.GET)
    JSONObject getRightsByEnterpriseId(@PathVariable("enterpriseGlobalId") String enterpriseGlobalId,
                                       @PathVariable("appKey") String appKey,
                                       @PathVariable("gSignature") String gSignature,
                                       @PathVariable("gmsPids") String gmsPids,
                                       @PathVariable("memberGlobalId") String memberGlobalId);
}
