package com.glodon.qydata.service.event.impl;

import com.alibaba.fastjson.JSONObject;
import com.glodon.qydata.common.constant.BusinessConstants;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.service.cloud.GlodonService;
import com.glodon.qydata.service.depend.ApiAuthService;
import com.glodon.qydata.service.event.AssetsRegisterService;
import com.glodon.qydata.service.event.InitDataService;
import com.glodon.qydata.service.system.IGlodonUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * @author: luoml-b
 * @date: 2023/7/4 14:09
 * @description: 企业资产注册service
 */
@Service
@Slf4j
public class AssetsRegisterServiceImpl implements AssetsRegisterService {

    @Autowired
    private GlodonService glodonService;

    @Autowired
    private ApiAuthService apiAuthService;

    @Autowired
    private InitDataService initDataService;

    @Autowired
    private IGlodonUserService glodonUserService;

    /**
     * @description: 注册之后基础信息库初始化操作
     * @author: luoml-b
     * @date: 2023/7/4 17:17
     * @param: req
     **/
    public void register(JSONObject req) {
        //解析消息体拿到tenantId
        String tenantId = getTenantId(req);

        //通过tenantId换customerCode
        String customerCode = getCustomerCode(tenantId);

        //进行初始化操作
        initDataService.initData(customerCode);
    }

    /**
     * @description: 解析消息体获取tenantId
     * @author: luoml-b
     * @date: 2023/7/4 17:18
     * @param: req
     * @return: java.lang.String
     **/
    private String getTenantId(JSONObject req) {
        JSONObject jsonObject = Optional.ofNullable(JSONObject.parseObject(req.getString("msgContent")))
                .orElseThrow(() -> new BusinessException("解析消息体返回值异常"));
        String tenantId = jsonObject.getString(Constants.TENANT_ID);
        String productCode = jsonObject.getString(BusinessConstants.PRODUCT_CODE);
        JSONObject licenseInfo = jsonObject.getJSONObject("licenseInfo");
        //消息提有两种结构，如果第一种结构拿不到productCode就使用第二种结构拿
        if (null != licenseInfo && (StringUtils.isBlank(tenantId) || StringUtils.isBlank(productCode))) {
            tenantId = StringUtils.isNotBlank(licenseInfo.getString(Constants.TENANT_ID))
                    ? licenseInfo.getString(Constants.TENANT_ID) : tenantId;
            productCode = StringUtils.isNotBlank(licenseInfo.getString(BusinessConstants.PRODUCT_CODE))
                    ? licenseInfo.getString(BusinessConstants.PRODUCT_CODE) : productCode;
        }
        if (!Constants.Assets.PRODUCT_CODE_DCOST.equals(productCode)) {
            throw new BusinessException("没有新成本权限，不予以初始化基础信息库数据");
        }
        log.info("企业资产注册or修改同步初始化基础信息库，入参：{}", jsonObject);
        return tenantId;
    }


    /**
     * @description: 根据tenantId获取customerCode
     * @author: luoml-b
     * @date: 2023/7/4 17:17
     * @param: tenantId
     * @return: java.lang.String
     **/
    private String getCustomerCode(String tenantId) {
        String enterpriseId = glodonService.getEnterpriseIdByTenantId(tenantId);
        String qyCodeByEnterpriseId = apiAuthService.getQyCodeByEnterpriseId(enterpriseId);
        log.info("企业资产注册or修改同步初始化基础信息库，tenantId is :{}, enterpriseId is :{}, qyCodeByEnterpriseId is :{}",
                tenantId, enterpriseId, qyCodeByEnterpriseId);
        return glodonUserService.setQyFlagCache(qyCodeByEnterpriseId, enterpriseId);
    }
}
