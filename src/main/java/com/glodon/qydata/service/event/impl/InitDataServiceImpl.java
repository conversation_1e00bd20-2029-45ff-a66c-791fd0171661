package com.glodon.qydata.service.event.impl;

import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.service.event.InitDataService;
import com.glodon.qydata.service.init.enterprise.InitCategoryService;
import com.glodon.qydata.service.init.enterprise.InitProjectService;
import com.glodon.qydata.service.init.enterprise.InitTradeService;
import com.glodon.qydata.service.standard.category.impl.CommonProjectCategoryUsedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: luoml-b
 * @date: 2023/7/4 17:13
 * @description:
 */
@Service
@Slf4j
public class InitDataServiceImpl implements InitDataService {

    @Autowired
    private InitCategoryService initCategoryService;

    @Autowired
    private InitProjectService initProjectService;

    @Autowired
    private InitTradeService initTradeService;

    @Autowired
    private CommonProjectCategoryUsedService commonProjectCategoryUsedService;


    /**
     * @description: 初始化基础信息库数据
     * @author: luoml-b
     * @date: 2023/7/4 17:16
     * @param: customerCode
     **/
    //@Async
    @Override
    public void initData(String customerCode) {
        if (StringUtils.isBlank(customerCode)) {
            log.error("InitDataService.initData()，初始化失败，customerCode为空");
            return;
        }
        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        //此时type指的是工程分类的类型（特征该企业是否之前从慧果导入过数据）
        initCategoryService.initData(customerCode, type);
        initProjectService.initData(customerCode, Constants.StandardsProjectOrContractInfoConstants.PROJECT_INFO);
        initProjectService.initData(customerCode, Constants.StandardsProjectOrContractInfoConstants.CONTRACT_INFO);
        initTradeService.initData(customerCode);
    }
}
