package com.glodon.qydata.vo.standard.feature;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;


/**
 * @description: 工程特征删除参数载体
 * <AUTHOR>
 * @date 2021/10/21 20:58
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ProjectFeatureDeleteVO implements Serializable {

    private static final long serialVersionUID = 2709650773793741684L;

    /**
     * 特征id
     */
    @NotNull(message = "特征id不能为空")
    private String id;

    /**
     * 视图类型
     */
    @NotNull(message = "视图类型不能为空")
    private Integer viewType;

    /**
     * 分类编码
     */
    private String categoryCode;
}
