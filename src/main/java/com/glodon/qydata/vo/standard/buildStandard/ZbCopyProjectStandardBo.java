package com.glodon.qydata.vo.standard.buildStandard;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
public class ZbCopyProjectStandardBo {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 标准名称
     */
    @NotEmpty(message = "标准名称不能为空")
    private String name;
    /**
     * 标准所选业态编码
     */
    @NotEmpty(message = "标准业态不能为空")
    private String categoryCode;
    /**
     * 标准所选业态名称
     */
    @NotNull(message = "标准业态名称不能为空")
    private String categoryName;
}
