package com.glodon.qydata.vo.standard.trade;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
　　* @description: 标准统一--内置专业引用列表返回前端数据载体
　　* <AUTHOR>
　　* @date 2021/10/21 17:13
　　*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "内置专业引用列表返回前端数据载体")
public class StandardTradeVO implements Serializable {

    private static final long serialVersionUID = -6259089312185272781L;

    @Schema(description = "专业id")
    private Long id;
    @Schema(description = "专业名称")
    private String description;
    @Schema(description = "专业编码")
    private String tradeCode;
    @Schema(description = "参考专业编码")
    private String referTradeCode;
    @Schema(description = "专业类型  0 内置专业  1 新增专业")
    private Byte type;
    @Schema(description = "排序字段")
    private Integer ord;
    @Schema(description = "是否启用")
    private Integer enabled;
    @Schema(description = "专业标签id")
    private Long tradeTagId;
    @Schema(description = "当前节点类型,0:标签 1:专业")
    private Integer nodeType = 1;
    @Schema(description = "标签下的专业")
    private List<StandardTradeVO> children;
}
