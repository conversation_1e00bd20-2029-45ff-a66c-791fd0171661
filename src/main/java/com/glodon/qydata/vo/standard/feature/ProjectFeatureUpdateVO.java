package com.glodon.qydata.vo.standard.feature;

import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 更新工程特征信息载体
 * <AUTHOR>
 * @date 2021/10/24 19:48
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ProjectFeatureUpdateVO implements Serializable {
    private static final long serialVersionUID = 4209733670989539625L;
    /**
     * 主键ID
     */
    @NotNull(message = "工程特征ID不能为空")
    private Long id;

    /**
     * 工程特征名称
     */
    private String name;

    /**
     * 数据类型:值为dictionary表中type_code字段等于data_type所对应的code字段的值
     */
    private String typeCode;

    /**
     * 工程特征对应的选项，与数据类型连用,数据格式为json字符串：{id:xxx，name：xxx,sort: xxx}
     */
    private String option;

    /**
     * 工程分类 工程分类:json字符串 {"projectType":"[["001","001001","001001002"],["001","001001","001001003"],["001","001001","001001004"],["001","001001","001001005"],["001","001002","001002001"],["001","001002","001002002"],["001","001002","001002003"],["001","001003","001003002"],["001","001003","001003003"],["001","001003","001003004"],["001","001004","001004001"],["001","001004","001004002"],["001","001004","001004003"],["001","001004","001004004"],["001","001005"],["001","001006","001006001"],["001","001006","001006002"],["001","001006","001006003"],["001","001006","001006004"],["001","001006","001006005"],["001","001006","001006006"],["001","001006","001006007"],["001","001007"],["002","002001","002001001"],["002","002001","002001002"],["002","002001","002001003"],["002","002002"],["002","002003"],["002","002004"],["002","002005"],["002","002006","002006001"],["002","002006","002006002"],["002","002006","002006003"],["002","002007"]]"}
     */
    private String projectType;

    /**
     * 是否启用：1启用；0未启用；（系统：默认启用；用户新建：新建工程特征【是否启用】状态，启用后的工程特征，才可在【新增工程特征】时搜索）
     */
    private Integer isUsing;

    /**
     * 是否默认：：开启【1】、关闭【0】
     */
    private Integer isDefault;

    /**
     * 是否必填：1 必填；0 不必填
     */
    private Integer isRequired;

    /**
     * 注释，最大300个字符
     */
    private String remark;

    /**
     * 是否计算口径：1是；0否
     */
    private Integer isExpression;

    /**
     * 工程专业id
     */
    @NotNull(message = "工程专业不能为空")
    private Long tradeId;

    /**
     * 企业编码
     */
    private String customerCode;

    /**
     * 更新人ID（广联达用户体系）
     */
    private Long updateGlobalId;

    /**
     * 视图类型
     */
    @NotNull(message = "视图类型不能为空")
    private Integer viewType;

    /**
     * 分类编码
     */
    private String categoryCode;

    /**
     * 数据类型
     */
    private Integer type;

    /**
     * 单位
     */
    private String unit;

    /**
     * 该用户的工程分类列表
     */
    private List<CommonProjCategory> categoryListTree;
}