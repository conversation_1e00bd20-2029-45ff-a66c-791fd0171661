package com.glodon.qydata.vo.standard.unit;

import lombok.Data;

/**
 * @description: 查询单位Vo
 * @author: wangq
 * @create: 2022-01-17 15:01
 **/
@Data
public class SearchUnitVo {
    public SearchUnitVo() {
    }

    public SearchUnitVo(String customerCode, String searchText) {
        this.customerCode = customerCode;
        this.searchText = searchText;
    }

    /** 企业编码 **/
    private String customerCode;

    /** 要搜索的单位名称（模糊匹配） **/
    private String searchText;

    /** 任务委托新增字段-目标企业enterpriseId**/
    private String destEnterpriseId;
    /** 任务委托新增字段-产品来源，ysbz(预算编制)、gsbz(概算编制)、不传则取所有产品来源委托的项目**/
    private String trustProductSource;
}
