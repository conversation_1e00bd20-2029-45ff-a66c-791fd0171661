package com.glodon.qydata.vo.standard.feature;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * @description: 查询工程特征
 * <AUTHOR>
 * @date 2021/10/25 20:04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectFeatureFilterVO {

    /**
     * 工程专业id
     */
    private Long tradeId;

    /**
     * 分类编码
     */
    private String categoryCode;

    /**
     * 视图类型
     */
    @NotNull(message = "视图类型不能为空")
    private Integer viewType;

    /**
     * 企业当前使用的分类类别
     */
    private Integer type;

    /**
     * 是否展示未启用  0 只展示启用的  1 展示未启用的，即全部
     */
    private Integer isShowNotUsing;

    /**
     * 是否跳过用户名称赋值（0：不跳过；1：跳过；默认为跳过）
     */
    private Integer isSkipUserName;
}
