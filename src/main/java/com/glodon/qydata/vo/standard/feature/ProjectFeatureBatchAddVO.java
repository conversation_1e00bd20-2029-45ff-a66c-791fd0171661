package com.glodon.qydata.vo.standard.feature;

import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;


/**
 * @description: 批量插入工程特征
 * <AUTHOR>
 * @date 2024/11/14 14:53
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ProjectFeatureBatchAddVO implements Serializable {

    private static final long serialVersionUID = 8607558105213154168L;

    /**
     * 工程专业id
     */
    @NotNull(message = "专业id不能为空")
    private List<Long> tradeIds;

    /**
     * 视图类型
     */
    @NotNull(message = "视图类型不能为空")
    private Integer viewType;

    /**
     * 分类编码
     */
    private String categoryCode;

    /**
     * 数据分类
     */
    private Integer type;

    /**
     * 该用户的工程分类列表
     */
    private List<CommonProjCategory> categoryListTree;
}
