package com.glodon.qydata.vo.standard.mainQuantity;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 标准打通--主要量指标前端数据载体
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StandardsMainQuantityBO implements Serializable {
    /**
     * 主键id  修改需要用到
     */
    private Long id;

    /**
     * 所属专业id
     */
    @NotNull(message = "专业id不能为空")
    private Long tradeId;

    /**
     * 名称 必填
     */
    @NotEmpty(message = "科目名称不能为空")
    @Size(min=1,max=30,message = "科目名称限制30位")
    private String description;

    /**
     * 单位  不必填
     */
    @Size(max=30,message = "科目单位限制30位")
    private String unit;

    /**
     * 备注  不必填
     */
    @Size(max=200,message = "科目备注限制200位")
    private String remark;

    /**
     * 基准科目id
     */
    private Long baseId;

    /**
     * 排序字段
     */
    private Integer ord;

    private static final long serialVersionUID = 1L;
}