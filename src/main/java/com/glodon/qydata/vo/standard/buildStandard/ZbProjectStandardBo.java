package com.glodon.qydata.vo.standard.buildStandard;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
public class ZbProjectStandardBo {
    /**
     * 主键id
     */
    private Long id;
    /**
     * 标准名称
     */
    @NotEmpty(message = "标准名称不能为空")
    private String name;
    /**
     * 标准所选业态编码
     */
    @NotEmpty(message = "标准业态不能为空")
    private String categoryCode;
    /**
     * 是否启用
     */
    @NotNull(message = "是否启用不能为空")
    private Integer isUsing;
    /**
     * 引用标准的id
     */
    private Long srcStandardId;
    /**
     * 更新的操作类型：rename、setUsing、setUsingWithCheck
     */
    private String operateType;
}
