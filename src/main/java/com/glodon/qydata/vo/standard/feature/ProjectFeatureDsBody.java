package com.glodon.qydata.vo.standard.feature;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 特征标准数据同步
 * <AUTHOR>
 * @date 2021/11/19 8:44
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ProjectFeatureDsBody implements Serializable {
    private static final long serialVersionUID = -5184232170927458655L;

    /**
     * 外部处理过的工程特征列表
     */
    private List<ProjectFeatureDsVO> featureDsVOList;

    /**
     * 企业编码
     */
    private String customerCode;


}