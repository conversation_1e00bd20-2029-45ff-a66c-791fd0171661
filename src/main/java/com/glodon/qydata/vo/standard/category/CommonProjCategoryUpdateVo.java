package com.glodon.qydata.vo.standard.category;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 工程分类修改vo
 * <AUTHOR>
 * @date 2021/10/20 11:11
 */
@Data
@ToString
public class CommonProjCategoryUpdateVo implements Serializable {

    private static final long serialVersionUID = 5604546961935227570L;

    /**
     * 操作的工程分类
     */
    @NotEmpty(message = "工程分类编码不能为空")
    private String commonprojcategoryid;
    @NotNull(message = "工程分类ID不能为空")
    private Integer id;

    /**
     * 操作类型(0:修改名称，1:启用禁用，2:修改备注)
     */
    @NotNull(message = "操作类型不能为空")
    private Integer operate;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 是否启用（1，启用；0，禁用）
     */
    private Integer isUsing;

    /**
     * 备注
     */
    private String remark;

    private Integer tagLevel;


}
