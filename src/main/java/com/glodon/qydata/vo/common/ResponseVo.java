package com.glodon.qydata.vo.common;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.glodon.qydata.common.enums.ResponseCode;
import lombok.Data;

import java.io.Serializable;

/**
 * http请求返回的最外层对象
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResponseVo<T> implements Serializable{

    private static final long serialVersionUID = 5768675978348010153L;
    /**
     * 错误码.
     */
    private Integer code;

    /**
     * 提示信息.
     */
    private String msg;
    /**
     * 具体的内容.
     */
    private T data;

    public ResponseVo() {}

    public ResponseVo(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public ResponseVo(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public ResponseVo(ResponseCode responseCode, T data) {
        this.code = responseCode.getCode();
        this.msg = responseCode.getMessage();
        this.data = data;
    }

    public ResponseVo(ResponseCode responseCod) {
        this.code = responseCod.getCode();
        this.msg = responseCod.getMessage();
    }


    public static <T> ResponseVo<T> success(int code, T object) {
        ResponseVo<T> responseVo = new ResponseVo<T>();
        responseVo.setCode(code);
        responseVo.setMsg(ResponseCode.SUCCESS.getMessage());
        responseVo.setData(object);
        return responseVo;
    }

    public static <T> ResponseVo<T> success(T object) {
        return success(ResponseCode.SUCCESS.getCode(), object);
    }

    public static <T> ResponseVo<T> success() {
        return success(ResponseCode.SUCCESS.getCode(), null);
    }

    public static <T> ResponseVo<T> error(Integer code, String message) {
        ResponseVo<T> responseVo = new ResponseVo<T>();
        responseVo.setCode(code);
        responseVo.setMsg(message);
        return responseVo;
    }

    public static <T> ResponseVo<T> error(Integer code, String message, T object) {
        ResponseVo<T> responseVo = new ResponseVo<T>();
        responseVo.setCode(code);
        responseVo.setMsg(message);
        responseVo.setData(object);
        return responseVo;
    }

    public static <T> ResponseVo<T> error(ResponseCode responseCode, T object) {
        ResponseVo<T> ResponseVo = new ResponseVo<T>();
        ResponseVo.setCode(responseCode.getCode());
        ResponseVo.setMsg(responseCode.getMessage());
        ResponseVo.setData(object);
        return ResponseVo;
    }

    public static <T> ResponseVo<T> error(String message) {
        return error(ResponseCode.ERROR.getCode(), message);
    }

    public static <T> ResponseVo<T> error(ResponseCode responseCode) {
        return error(responseCode.getCode(), responseCode.getMessage());
    }

    public static <T> ResponseVo<T> error() {
        return error(ResponseCode.ERROR);
    }
}
