package com.glodon.qydata.vo.amap;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * 高德地图 API 响应结果对象
 *
 * <AUTHOR>
 */
@Data
public class AMapResponse {
    /**
     * 返回结果状态值
     * 值为0或1，0表示失败；1表示成功
     */
    @JSONField(name = "status")
    private String status;

    /**
     * 返回状态说明
     * 返回状态说明，status 为0时，info 返回错误原因，否则返回“OK”。
     */
    @JSONField(name = "info")
    private String info;

    /**
     * 状态码
     * 返回状态说明，10000代表正确，详情参阅 info 状态表
     */
    @JSONField(name = "infocode")
    private String infocode;

    /**
     * 返回的行政区数量
     */
    @JSONField(name = "count")
    private String count;

    /**
     * 建议结果列表
     */
    @JSONField(name = "suggestion")
    private AMapSuggestion suggestion;

    /**
     * 行政区列表
     */
    @JSONField(name = "districts")
    private List<AMapDistrict> districts;

}
