package com.glodon.qydata.vo.amap;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class AMapDistrict {
    /**
     * 城市编码
     */
    @JSONField(name = "citycode")
    private List<String> citycode;

    /**
     * 区域编码
     * 街道没有独有的 adcode，均继承父类（区县）的 adcode
     */
    @JSONField(name = "adcode")
    private String adcode;

    /**
     * 行政区名称
     */
    @JSONField(name = "name")
    private String name;

    /**
     * 区域中心点
     * 乡镇级别返回的center是边界线上的形点，其他行政级别返回的center不一定是中心点，若政府机构位于面内，则返回政府坐标，政府不在面内，则返回繁华点坐标。
     */
    @JSONField(name = "center")
    private String center;

    /**
     * 行政区划级别
     * country:国家
     * province:省份（直辖市会在province显示）
     * city:市（直辖市会在province显示）
     * district:区县
     * street:街道
     */
    @JSONField(name = "level")
    private String level;

    /**
     * 下级行政区列表，包含 district 元素
     */
    @JSONField(name = "districts")
    private List<AMapDistrict> districts;

    /**
     * 行政区边界坐标点
     * 当一个行政区范围，由完全分隔两块或者多块的地块组成，每块地的 polyline 坐标串以 | 分隔 。
     * 如北京 的 朝阳区
     */
    @JSONField(name = "polyline")
    private String polyline;

    @JsonIgnore
    private String namePath;

    @JsonIgnore
    private int ord;
    @JsonIgnore
    private int sort;

    public AMapDistrict(String adcode, String name, String level) {
        this.adcode = adcode;
        this.name = name;
        this.level = level;
    }
}
