package com.glodon.qydata.vo.init;

import com.glodon.qydata.common.annotation.ExcelParam;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 导入内置建造标准的数据请求VO
 * @author: weijf
 * @date: 2022-10-10
 */
@Data
public class BuildStandardExcelVo implements Serializable {

    @ExcelParam(value = "科目编码", index = 0)
    private String levelCode;

    @ExcelParam(value = "科目名称", index = 1)
    private String name;

    @ExcelParam(value = "标准说明", index = 2)
    private String detailDesc;

    @ExcelParam(value = "枚举值", index = 3)
    private String selectList;

    @ExcelParam(value = "数据类型", index = 4)
    private String typeCode;

    @ExcelParam(value = "所属专业", index = 5)
    private String tradeName;

    @ExcelParam(value = "关联项目划分科目", index = 6)
    private String subjectName;

}
