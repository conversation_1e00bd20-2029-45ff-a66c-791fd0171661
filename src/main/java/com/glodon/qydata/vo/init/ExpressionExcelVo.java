package com.glodon.qydata.vo.init;

import com.glodon.qydata.common.annotation.ExcelParam;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 导入内置计算口径的数据请求VO
 * @author: weijf
 * @date: 2022-10-10
 */
@Data
public class ExpressionExcelVo implements Serializable {

    @ExcelParam(value = "序号", index = 0)
    private String indexNum;

    @ExcelParam(value = "指标名称", index = 1)
    private String name;

    @ExcelParam(value = "单位", index = 2)
    private String unit;

    @ExcelParam(value = "是否加入口径字典", index = 3) //未使用
    private String addDict;

    // 需要内置默认口径字典表，不允许删除，不允许修改单位，可以修改是否启用，统一在备注标注“只读”字样
    // 【产品设计指标】 需要 同时添加进专业工程【精装修工程】中，默认启用，默认是计算口径，是否必填不勾选，备注为空
    @ExcelParam(value = "分类", index = 4)
    private String dataType;

    @ExcelParam(value = "说明", index = 5)
    private String desc;

}
