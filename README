1、默认是dev环境，打包命令，可在右侧maven中直接创建一个命令，下次直接点击即可
    mvn clean package -Dmaven.test.skip -Ptest
    mvn clean package -Dmaven.test.skip -Ppro

2、从 https://gitee.com/lionsoul/ip2region/tree/master/data 下载 ip2region.db 文件 存储本地
   并更新至application-dev.yml文件的ip2region.path属性中

3、文档放到doc下，如数据库设计文件

4、数据库设计文件编辑工具下载地址：https://gitee.com/robergroup/chiner/releases

5、分页用法：
        1)、map参数中增加分页参数：page，如下
            Map params = new HashMap();
            PageParameter page = new PageParameter();
            page.setCurrentPage(xxx);
            page.setPageSize(Constants.DEFAULT_LIST_PAGE_SIZE);
            params.put("page", page);

        2)、  mapper的查询方法后缀带上【ByPage】即可，如 xxxMapper.xxxxxxByPage(params); 返回List<T>对象
        3)、 查询完成后，page对象自动已被赋值相关分页数据

-----2025-03-26更新-----
6、地区表（tb_area）升级说明
  产线环境：
    地区表数据会根据国家行政区划的调整进行更新。
  私有化环境：
      升级方式：建议全量替换地区表的表结构和表数据。
      数据同步方式：
      若已部署中台的地区表 gcdp_dim_tb_area，可以通过以下两种方式同步数据：
          使用大数据平台进行数据同步。
          使用全量 SQL 脚本进行数据导入。
