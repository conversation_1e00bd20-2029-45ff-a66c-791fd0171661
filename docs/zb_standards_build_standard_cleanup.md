# zb_standards_build_standard 表组数据清理功能

## 功能概述

本功能为 `zb_standards_build_standard`、`zb_standards_build_standard_detail` 和 `zb_standards_build_standard_detail_desc`（建设标准表组）实现了数据清理功能，参考现有的 `tb_commonprojcategory_standards` 表清理逻辑，支持按 `qy_code` 字段进行分组统计和个人账号数据清理。

## 表结构信息

### zb_standards_build_standard（主表）
- `id` (bigint): 主键ID
- `customer_code` (varchar): 企业编码，用于分组统计（映射为 qy_code）
- `name` (varchar): 建设标准名称
- `type` (int): 建设标准类型
- `status` (int): 状态
- `create_time` (datetime): 创建时间
- `update_time` (datetime): 更新时间
- `create_global_id` (bigint): 创建用户ID
- `update_global_id` (bigint): 更新用户ID

### zb_standards_build_standard_detail（详情表）
- `id` (bigint): 主键ID
- `standard_id` (bigint): 关联主表ID
- `detail_name` (varchar): 详情名称
- `detail_value` (varchar): 详情值
- `detail_type` (int): 详情类型
- `sort_order` (int): 排序
- `create_time` (datetime): 创建时间
- `update_time` (datetime): 更新时间

### zb_standards_build_standard_detail_desc（描述表）
- `id` (bigint): 主键ID
- `standard_id` (bigint): 关联主表ID
- `description` (text): 描述内容
- `desc_type` (int): 描述类型
- `create_time` (datetime): 创建时间
- `update_time` (datetime): 更新时间

## 表关联关系

```
zb_standards_build_standard (主表)
    ├── zb_standards_build_standard_detail (详情表)
    │   └── 关联字段: standard_id = zb_standards_build_standard.id
    └── zb_standards_build_standard_detail_desc (描述表)
        └── 关联字段: standard_id = zb_standards_build_standard.id
```

## 主要特性

1. **三表关联处理**：同时处理主表和两个子表的数据清理
2. **数据分析**：统计三张表的整体数据量和空间占用
3. **分组统计**：按 `qy_code` 进行 GROUP BY 分组统计（基于主表的 customer_code 字段）
4. **账号类型判断**：调用 `AccountTypeService.getAccountType()` 方法判断账号类型
5. **个人账号识别**：识别并收集个人账号（PERSONAL_TYPE）相关数据
6. **关联删除**：按照外键关系正确删除三张表中的相关记录
7. **性能优化**：使用缓存机制预先收集记录ID，避免重复查询
8. **详细日志**：完整的操作日志记录
9. **事务管理**：删除操作使用事务确保数据一致性

## API 接口

### 1. 执行数据清理操作

**接口地址**：`POST /basicInfo/system/dataCleanup/performBuildStandardCleanup`

**参数**：
- `executeCleanup` (Boolean): 是否执行清理操作
  - `true`: 执行删除操作
  - `false`: 仅分析展示，不执行删除

**请求示例**：
```bash
curl -X POST "http://localhost:8080/basicInfo/system/dataCleanup/performBuildStandardCleanup?executeCleanup=false"
```

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalRecords": 1400000,
    "qyCodeGroupStats": {
      "*********": 200,
      "ENTERPRISE001": 1800
    },
    "personalAccountDataList": [
      {
        "qyCode": "*********",
        "globalId": "*********",
        "recordCount": 200,
        "spaceInfo": {
          "qyCode": "*********",
          "recordCount": 200,
          "estimatedSpaceBytes": 6291456,
          "estimatedSpaceFormatted": "6.00 MB",
          "accountType": "PERSONAL_TYPE"
        }
      }
    ],
    "cleanupExecuted": false,
    "deletedRecords": 0,
    "message": "zb_standards_build_standard 表组数据分析完成，未执行清理操作"
  }
}
```

### 2. 获取数据统计信息

**接口地址**：`GET /basicInfo/system/dataCleanup/getBuildStandardStatistics`

**说明**：仅分析数据，不执行清理操作，等同于 `performBuildStandardCleanup?executeCleanup=false`

**请求示例**：
```bash
curl -X GET "http://localhost:8080/basicInfo/system/dataCleanup/getBuildStandardStatistics"
```

## 数据流程

### 1. 数据分析阶段
```
统计三表数据量 → 按qy_code分组（基于主表） → 预收集记录ID → 计算空间估算
```

### 2. 账号类型判断阶段
```
遍历qy_code → 数字格式预过滤 → 调用AccountTypeService → 更新空间统计
```

### 3. 个人账号数据收集阶段
```
筛选个人账号 → 收集主表记录ID → 关联空间信息 → 生成清理列表
```

### 4. 数据清理执行阶段（可选）
```
遍历个人账号 → 按顺序删除三表记录 → 统计删除结果 → 计算释放空间
```

## 删除顺序和关联性

为确保数据一致性和关联完整性，删除操作按以下步骤执行：

### 第一步：收集主表删除ID
- 从 `zb_standards_build_standard` 主表中收集所有需要删除的记录的 `id` 值
- 基于个人账号的 `customer_code`（映射为 qy_code）筛选出需要删除的主表记录ID列表
- 确保在删除过程中不会丢失关联关系

### 第二步：按ID关联删除子表
- 使用收集到的主表ID列表，删除 `zb_standards_build_standard_detail_desc` 表中 `standard_id` 字段匹配这些ID的记录
- 使用相同的主表ID列表，删除 `zb_standards_build_standard_detail` 表中 `standard_id` 字段匹配这些ID的记录

### 第三步：删除主表记录
- 最后删除 `zb_standards_build_standard` 主表中的记录
- 使用之前收集的ID列表进行批量删除

## 核心方法说明

### performBuildStandardDataCleanup(boolean executeCleanup)
主要的数据清理方法，执行完整的清理流程：
1. `statisticsBuildStandardData()` - 统计三表数据量和表空间
2. `analyzeBuildStandardQyCodeGroups()` - 分组分析和空间估算
3. `determineAccountTypesByQyCodeForBuildStandard()` - 判断账号类型
4. `collectPersonalAccountDataByQyCodeForBuildStandard()` - 收集个人账号数据
5. `generateSpaceSummaryForBuildStandard()` - 生成空间汇总信息
6. `executeBuildStandardDataCleanup()` - 执行数据清理（可选）

### 特殊处理方法
- `getBuildStandardQyCodeGroupStats()` - 特殊处理 customer_code 字段映射
- `collectAllBuildStandardRecordIds()` - 收集主表记录ID用于缓存
- `collectMainTableIdsByQyCode()` - 收集需要删除的主表ID列表
- `deleteBuildStandardDetailRecordsByIds()` - 根据主表ID列表删除子表记录
- `deleteBuildStandardMainRecordsByIds()` - 根据ID列表批量删除主表记录

### 新增的关联删除方法
- `collectBuildStandardMainTableIdsByQyCode()` - Mapper方法：收集主表删除ID
- `deleteBuildStandardDetailRecordsByIds()` - Mapper方法：批量删除子表记录
- `deleteBuildStandardMainRecordsByIds()` - Mapper方法：批量删除主表记录

## 性能特点

1. **预设空间估算**：基于三表的预设空间信息（3.3GB数据 + 1.7GB索引）进行空间占用估算
2. **记录ID缓存**：预先收集主表记录ID到内存缓存，避免后续重复查询
3. **关联查询优化**：通过主表ID关联删除子表记录，避免全表扫描
4. **批量操作支持**：支持批量删除操作，提高删除效率

## 安全考虑

1. **事务管理**：删除操作使用 `@Transactional` 注解确保数据一致性
2. **参数验证**：对输入参数进行验证和过滤
3. **权限控制**：继承 `BaseController` 的权限控制机制
4. **操作日志**：详细记录操作用户和操作结果
5. **异常处理**：完善的异常捕获和错误信息返回
6. **关联完整性**：确保三表删除操作在同一事务中完成
7. **ID预收集**：先收集主表ID，再执行删除，避免在删除过程中丢失关联关系
8. **批量删除优化**：使用IN子句进行批量删除，提高删除效率
9. **空ID列表处理**：当主表ID列表为空时，跳过子表删除操作，避免误删

## 使用示例

### 1. 仅分析数据（推荐先执行）
```bash
# 获取统计信息
curl -X GET "http://localhost:8080/basicInfo/system/dataCleanup/getBuildStandardStatistics"

# 或者使用POST方式
curl -X POST "http://localhost:8080/basicInfo/system/dataCleanup/performBuildStandardCleanup?executeCleanup=false"
```

### 2. 执行数据清理
```bash
curl -X POST "http://localhost:8080/basicInfo/system/dataCleanup/performBuildStandardCleanup?executeCleanup=true"
```

## 测试验证

项目包含了完整的单元测试，包括：
- `testPerformBuildStandardDataCleanup_AnalysisOnly()` - 测试仅分析模式
- `testPerformBuildStandardDataCleanup_WithExecution()` - 测试执行清理模式
- `testPerformBuildStandardDataCleanup_NoPersonalAccounts()` - 测试无个人账号场景

运行测试：
```bash
mvn test -Dtest=DataCleanupServiceTest#testPerformBuildStandardDataCleanup*
```

## 注意事项

1. **数据备份**：执行清理操作前请确保已备份重要数据
2. **测试环境验证**：建议先在测试环境验证清理效果
3. **业务影响评估**：清理操作可能影响相关业务功能，请在业务低峰期执行
4. **监控日志**：执行过程中请密切关注应用日志
5. **回滚准备**：准备好数据回滚方案以应对异常情况
6. **关联完整性**：确保删除操作不会破坏数据的关联完整性
7. **建设标准依赖**：注意建设标准可能被其他模块引用，删除前需确认影响范围

## 与其他表清理功能的对比

| 特性 | zb_standards_build_standard | tb_commonprojcategory_standards | 单表功能 |
|------|----------------------------|--------------------------------|----------|
| 分组字段 | qy_code (customer_code) | qy_code | qy_code/customer_code |
| 表数量 | 3张表（关联） | 2张表（分表） | 1张表 |
| 预设空间 | 5GB | 87GB | 150MB-1425MB |
| 缓存策略 | qy_code缓存 | qy_code缓存 | 独立缓存 |
| 删除方式 | 关联删除 | 物理删除 | 物理删除 |
| 业务复杂度 | 高（三表关联） | 高（项目分类关联多） | 中低 |

## 扩展性

本实现采用了模块化设计，可以轻松扩展支持其他多表关联的清理场景：
1. 复用现有的关联删除逻辑
2. 添加新的表特定的处理方法
3. 在控制器中添加新的API接口
4. 根据需要调整空间估算参数

## 业务影响分析

由于 `zb_standards_build_standard` 表组存储的是建设标准信息，删除个人账号相关数据可能影响：
1. 建设标准配置
2. 标准详情信息
3. 标准描述内容
4. 相关业务流程

建议在执行清理前：
1. 确认个人账号数据的业务价值
2. 评估删除对现有建设标准的影响
3. 通知相关业务人员
4. 准备数据恢复方案
5. 检查是否有其他表引用这些建设标准
