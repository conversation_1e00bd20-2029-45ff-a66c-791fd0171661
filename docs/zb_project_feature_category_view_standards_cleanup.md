# zb_project_feature_category_view_standards 双表数据清理功能

## 功能概述

本功能为 `zb_project_feature_category_view_standards` 和 `zb_project_feature_category_view_standards_0`（业态视图-工程特征表）实现了数据清理功能，参考现有的 `tb_commonprojcategory_standards` 双表清理逻辑，支持按 `customer_code` 字段进行分组统计和个人账号数据清理。

## 表结构信息

### zb_project_feature_category_view_standards（主表）
- `id` (bigint): 主键ID
- `trade_id` (bigint): 工程专业id
- `category_code` (varchar): 工程分类一级分类编码
- `type` (int): 工程分类type
- `feature_id` (bigint): zb_project_feature_standards特征id
- `ord_category` (int): 分类视图排序
- `customer_code` (varchar): 企业编码，用于分组统计
- `trade_name` (varchar): 专业名称
- `origin_id` (bigint): 原始ID
- `enterprise_id` (bigint): gccs企业id
- `modify_time_sjzt` (timestamp): 更新时间-造价中台用

### zb_project_feature_category_view_standards_0（分表）
结构与主表相同，用于分表存储。

## 分表策略

该表使用了 ShardingSphere 分表策略：
- **分表字段**：`customer_code`
- **分表算法**：`CustomShardingAlgorithm`
- **实际表**：`zb_project_feature_category_view_standards`, `zb_project_feature_category_view_standards_0`

## 主要特性

1. **双表支持**：同时处理主表和分表的数据清理
2. **数据分析**：统计两张表的整体数据量和空间占用
3. **分组统计**：按 `customer_code` 进行 GROUP BY 分组统计，合并双表结果
4. **账号类型判断**：调用 `AccountTypeService.getAccountType()` 方法判断账号类型
5. **个人账号识别**：识别并收集个人账号（PERSONAL_TYPE）相关数据
6. **数据清理**：根据参数决定是否执行实际的数据删除操作
7. **性能优化**：使用缓存机制预先收集记录ID，避免重复查询
8. **详细日志**：完整的操作日志记录
9. **事务管理**：删除操作使用事务确保数据一致性

## API 接口

### 1. 执行数据清理操作

**接口地址**：`POST /basicInfo/system/dataCleanup/performCategoryViewCleanup`

**参数**：
- `executeCleanup` (Boolean): 是否执行清理操作
  - `true`: 执行删除操作
  - `false`: 仅分析展示，不执行删除

**请求示例**：
```bash
curl -X POST "http://localhost:8080/basicInfo/system/dataCleanup/performCategoryViewCleanup?executeCleanup=false"
```

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalRecords": 380000,
    "customerCodeGroupStats": {
      "*********": 500,
      "ENTERPRISE001": 2000
    },
    "personalAccountDataList": [
      {
        "customerCode": "*********",
        "globalId": "*********",
        "recordCount": 500,
        "spaceInfo": {
          "customerCode": "*********",
          "recordCount": 500,
          "estimatedSpaceBytes": ********,
          "estimatedSpaceFormatted": "12.00 MB",
          "accountType": "PERSONAL_TYPE"
        }
      }
    ],
    "cleanupExecuted": false,
    "deletedRecords": 0,
    "message": "zb_project_feature_category_view_standards 表数据分析完成，未执行清理操作"
  }
}
```

### 2. 获取数据统计信息

**接口地址**：`GET /basicInfo/system/dataCleanup/getCategoryViewStatistics`

**说明**：仅分析数据，不执行清理操作，等同于 `performCategoryViewCleanup?executeCleanup=false`

**请求示例**：
```bash
curl -X GET "http://localhost:8080/basicInfo/system/dataCleanup/getCategoryViewStatistics"
```

## 数据流程

### 1. 数据分析阶段
```
统计双表数据量 → 按customer_code分组 → 合并双表统计 → 预收集记录ID → 计算空间估算
```

### 2. 账号类型判断阶段
```
遍历customer_code → 数字格式预过滤 → 调用AccountTypeService → 更新空间统计
```

### 3. 个人账号数据收集阶段
```
筛选个人账号 → 收集双表记录ID → 关联空间信息 → 生成清理列表
```

### 4. 数据清理执行阶段（可选）
```
遍历个人账号 → 执行双表删除操作 → 统计删除结果 → 计算释放空间
```

## 核心方法说明

### performCategoryViewDataCleanup(boolean executeCleanup)
主要的数据清理方法，执行完整的清理流程：
1. `statisticsCategoryViewData()` - 统计双表数据量和表空间
2. `analyzeCategoryViewCustomerCodeGroups()` - 分组分析和空间估算
3. `determineAccountTypesByCustomerCodeForCategoryView()` - 判断账号类型
4. `collectPersonalAccountDataByCustomerCodeForCategoryView()` - 收集个人账号数据
5. `generateSpaceSummaryForCategoryView()` - 生成空间汇总信息
6. `executeCategoryViewDataCleanup()` - 执行数据清理（可选）

### 双表处理特点
- **统计合并**：分别统计两张表，然后合并相同 customer_code 的记录数
- **缓存优化**：预先收集两张表的所有记录ID到统一缓存
- **删除操作**：同时删除两张表中的相关记录
- **日志记录**：详细记录每张表的删除情况

## 性能特点

1. **预设空间估算**：基于双表的预设空间信息（950MB数据 + 475MB索引）进行空间占用估算
2. **记录ID缓存**：预先收集所有记录ID到内存缓存，避免后续重复查询
3. **批量操作支持**：复用现有的批量删除方法，提高删除效率
4. **分页查询支持**：支持限制查询条数，避免大结果集
5. **双表并行处理**：同时处理两张表的数据，提高处理效率

## 安全考虑

1. **事务管理**：删除操作使用 `@Transactional` 注解确保数据一致性
2. **参数验证**：对输入参数进行验证和过滤
3. **权限控制**：继承 `BaseController` 的权限控制机制
4. **操作日志**：详细记录操作用户和操作结果
5. **异常处理**：完善的异常捕获和错误信息返回
6. **双表一致性**：确保两张表的删除操作在同一事务中完成

## 使用示例

### 1. 仅分析数据（推荐先执行）
```bash
# 获取统计信息
curl -X GET "http://localhost:8080/basicInfo/system/dataCleanup/getCategoryViewStatistics"

# 或者使用POST方式
curl -X POST "http://localhost:8080/basicInfo/system/dataCleanup/performCategoryViewCleanup?executeCleanup=false"
```

### 2. 执行数据清理
```bash
curl -X POST "http://localhost:8080/basicInfo/system/dataCleanup/performCategoryViewCleanup?executeCleanup=true"
```

## 测试验证

项目包含了完整的单元测试，包括：
- `testPerformCategoryViewDataCleanup_AnalysisOnly()` - 测试仅分析模式
- `testPerformCategoryViewDataCleanup_WithExecution()` - 测试执行清理模式
- `testPerformCategoryViewDataCleanup_EmptyTables()` - 测试空表场景

运行测试：
```bash
mvn test -Dtest=DataCleanupServiceTest#testPerformCategoryViewDataCleanup*
```

## 注意事项

1. **数据备份**：执行清理操作前请确保已备份重要数据
2. **测试环境验证**：建议先在测试环境验证清理效果
3. **业务影响评估**：清理操作可能影响相关业务功能，请在业务低峰期执行
4. **监控日志**：执行过程中请密切关注应用日志
5. **回滚准备**：准备好数据回滚方案以应对异常情况
6. **分表一致性**：确保分表策略正常工作，避免数据不一致
7. **业态视图依赖**：注意业态视图可能被其他模块引用，删除前需确认影响范围

## 与其他表清理功能的对比

| 特性 | zb_project_feature_category_view_standards | tb_commonprojcategory_standards | 单表功能 |
|------|-------------------------------------------|--------------------------------|----------|
| 分组字段 | customer_code | qy_code | customer_code |
| 表数量 | 2张表（分表） | 2张表（分表） | 1张表 |
| 预设空间 | 1425MB | 87GB | 150MB-450MB |
| 缓存策略 | 双表统一缓存 | 双表统一缓存 | 单表缓存 |
| 删除方式 | 物理删除 | 物理删除 | 物理删除 |
| 业务复杂度 | 高（业态视图关联多） | 高（项目分类关联多） | 中低 |

## 扩展性

本实现采用了模块化设计，可以轻松扩展支持其他使用分表策略的表：
1. 复用现有的双表处理逻辑
2. 添加新的表特定的处理方法
3. 在控制器中添加新的API接口
4. 根据需要调整空间估算参数

## 业务影响分析

由于 `zb_project_feature_category_view_standards` 双表存储的是业态视图-工程特征信息，删除个人账号相关数据可能影响：
1. 业态视图配置
2. 工程特征分类
3. 专业工程关联
4. 分类视图排序

建议在执行清理前：
1. 确认个人账号数据的业务价值
2. 评估删除对现有业态视图的影响
3. 通知相关业务人员
4. 准备数据恢复方案
