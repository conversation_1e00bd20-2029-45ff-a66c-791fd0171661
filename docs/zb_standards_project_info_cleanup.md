# zb_standards_project_info 表数据清理功能

## 功能概述

本功能为 `zb_standards_project_info`（项目信息表）实现了数据清理功能，参考现有的 `zb_standards_trade` 表清理逻辑，支持按 `customer_code` 字段进行分组统计和个人账号数据清理。

## 表结构信息

`zb_standards_project_info` 表包含以下关键字段：
- `id` (bigint): 主键ID
- `customer_code` (varchar): 企业编码，用于分组统计
- `project_name` (varchar): 项目名称
- `project_code` (varchar): 项目编码
- `project_type` (int): 项目类型
- `project_status` (int): 项目状态
- `project_description` (text): 项目描述
- `start_date` (date): 开始日期
- `end_date` (date): 结束日期
- `budget_amount` (decimal): 预算金额
- `actual_amount` (decimal): 实际金额
- `project_manager` (varchar): 项目经理
- `contact_phone` (varchar): 联系电话
- `contact_email` (varchar): 联系邮箱
- `address` (varchar): 项目地址
- `region_code` (varchar): 地区编码
- `is_deleted` (tinyint): 删除标志（0-未删除，1-已删除）
- `create_time` (datetime): 创建时间
- `update_time` (datetime): 更新时间
- `create_global_id` (bigint): 创建用户ID
- `update_global_id` (bigint): 更新用户ID

## 主要特性

1. **数据分析**：统计表的整体数据量和空间占用
2. **分组统计**：按 `customer_code` 进行 GROUP BY 分组统计
3. **账号类型判断**：调用 `AccountTypeService.getAccountType()` 方法判断账号类型
4. **个人账号识别**：识别并收集个人账号（PERSONAL_TYPE）相关数据
5. **数据清理**：根据参数决定是否执行实际的数据删除操作
6. **性能优化**：使用缓存机制预先收集记录ID，避免重复查询
7. **详细日志**：完整的操作日志记录
8. **事务管理**：删除操作使用事务确保数据一致性

## API 接口

### 1. 执行数据清理操作

**接口地址**：`POST /basicInfo/system/dataCleanup/performProjectInfoCleanup`

**参数**：
- `executeCleanup` (Boolean): 是否执行清理操作
  - `true`: 执行删除操作
  - `false`: 仅分析展示，不执行删除

**请求示例**：
```bash
curl -X POST "http://localhost:8080/basicInfo/system/dataCleanup/performProjectInfoCleanup?executeCleanup=false"
```

**响应示例**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalRecords": 200000,
    "customerCodeGroupStats": {
      "*********": 400,
      "ENTERPRISE001": 2000
    },
    "personalAccountDataList": [
      {
        "customerCode": "*********",
        "globalId": "*********",
        "recordCount": 400,
        "spaceInfo": {
          "customerCode": "*********",
          "recordCount": 400,
          "estimatedSpaceBytes": ********,
          "estimatedSpaceFormatted": "15.00 MB",
          "accountType": "PERSONAL_TYPE"
        }
      }
    ],
    "cleanupExecuted": false,
    "deletedRecords": 0,
    "message": "zb_standards_project_info 表数据分析完成，未执行清理操作"
  }
}
```

### 2. 获取数据统计信息

**接口地址**：`GET /basicInfo/system/dataCleanup/getProjectInfoStatistics`

**说明**：仅分析数据，不执行清理操作，等同于 `performProjectInfoCleanup?executeCleanup=false`

**请求示例**：
```bash
curl -X GET "http://localhost:8080/basicInfo/system/dataCleanup/getProjectInfoStatistics"
```

## 数据流程

### 1. 数据分析阶段
```
统计数据量 → 按customer_code分组 → 预收集记录ID → 计算空间估算
```

### 2. 账号类型判断阶段
```
遍历customer_code → 数字格式预过滤 → 调用AccountTypeService → 更新空间统计
```

### 3. 个人账号数据收集阶段
```
筛选个人账号 → 收集记录ID → 关联空间信息 → 生成清理列表
```

### 4. 数据清理执行阶段（可选）
```
遍历个人账号 → 执行删除操作 → 统计删除结果 → 计算释放空间
```

## 核心方法说明

### performProjectInfoDataCleanup(boolean executeCleanup)
主要的数据清理方法，执行完整的清理流程：
1. `statisticsProjectInfoData()` - 统计数据量和表空间
2. `analyzeProjectInfoCustomerCodeGroups()` - 分组分析和空间估算
3. `determineAccountTypesByCustomerCodeForProjectInfo()` - 判断账号类型
4. `collectPersonalAccountDataByCustomerCodeForProjectInfo()` - 收集个人账号数据
5. `generateSpaceSummaryForProjectInfo()` - 生成空间汇总信息
6. `executeProjectInfoDataCleanup()` - 执行数据清理（可选）

### 缓存优化方法
- `collectAndCacheAllRecordIdsByCustomerCodeForProjectInfo()` - 预收集记录ID到缓存
- `collectRecordIdsByCustomerCodeForProjectInfo()` - 从缓存获取记录ID
- `collectRecordIdsByCustomerCodeFromDatabaseForProjectInfo()` - 数据库查询兜底方案

## 性能特点

1. **预设空间估算**：基于表的预设空间信息（500MB数据 + 250MB索引）进行空间占用估算
2. **记录ID缓存**：预先收集所有记录ID到内存缓存，避免后续重复查询
3. **批量操作支持**：复用现有的批量删除方法，提高删除效率
4. **分页查询支持**：支持限制查询条数，避免大结果集

## 安全考虑

1. **事务管理**：删除操作使用 `@Transactional` 注解确保数据一致性
2. **参数验证**：对输入参数进行验证和过滤
3. **权限控制**：继承 `BaseController` 的权限控制机制
4. **操作日志**：详细记录操作用户和操作结果
5. **异常处理**：完善的异常捕获和错误信息返回

## 使用示例

### 1. 仅分析数据（推荐先执行）
```bash
# 获取统计信息
curl -X GET "http://localhost:8080/basicInfo/system/dataCleanup/getProjectInfoStatistics"

# 或者使用POST方式
curl -X POST "http://localhost:8080/basicInfo/system/dataCleanup/performProjectInfoCleanup?executeCleanup=false"
```

### 2. 执行数据清理
```bash
curl -X POST "http://localhost:8080/basicInfo/system/dataCleanup/performProjectInfoCleanup?executeCleanup=true"
```

## 测试验证

项目包含了完整的单元测试，包括：
- `testPerformProjectInfoDataCleanup_AnalysisOnly()` - 测试仅分析模式
- `testPerformProjectInfoDataCleanup_WithExecution()` - 测试执行清理模式
- `testPerformProjectInfoDataCleanup_MultiplePersonalAccounts()` - 测试多个个人账号场景

运行测试：
```bash
mvn test -Dtest=DataCleanupServiceTest#testPerformProjectInfoDataCleanup*
```

## 注意事项

1. **数据备份**：执行清理操作前请确保已备份重要数据
2. **测试环境验证**：建议先在测试环境验证清理效果
3. **业务影响评估**：清理操作可能影响相关业务功能，请在业务低峰期执行
4. **监控日志**：执行过程中请密切关注应用日志
5. **回滚准备**：准备好数据回滚方案以应对异常情况
6. **项目信息依赖**：注意项目信息可能被其他模块引用，删除前需确认影响范围
7. **财务数据关联**：该表包含预算和实际金额信息，删除可能影响财务统计

## 与其他表清理功能的对比

| 特性 | zb_standards_project_info | zb_standards_trade | zb_standards_main_quantity |
|------|--------------------------|-------------------|---------------------------|
| 分组字段 | customer_code | customer_code | customer_code |
| 表数量 | 1张表 | 1张表 | 1张表 |
| 预设空间 | 750MB | 150MB | 240MB |
| 缓存策略 | customer_code缓存 | customer_code缓存 | customer_code缓存 |
| 删除方式 | 物理删除 | 物理删除 | 物理删除 |
| 业务复杂度 | 高（项目信息关联多） | 低（专业信息） | 中（主要量指标） |

## 扩展性

本实现采用了模块化设计，可以轻松扩展支持其他使用 `customer_code` 字段的表：
1. 复用现有的 `customer_code` 相关 Mapper 方法
2. 添加新的表特定的处理方法
3. 在控制器中添加新的API接口
4. 根据需要调整空间估算参数

## 业务影响分析

由于 `zb_standards_project_info` 表存储的是项目信息，删除个人账号相关数据可能影响：
1. 项目管理功能
2. 项目统计报表
3. 财务预算管理
4. 项目进度跟踪
5. 联系人信息管理

建议在执行清理前：
1. 确认个人账号数据的业务价值
2. 评估删除对现有项目管理的影响
3. 通知相关业务人员
4. 准备数据恢复方案
5. 检查是否有其他表引用这些项目信息
