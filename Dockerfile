FROM swr.cn-north-4.myhuaweicloud.com/glodon-jijia/openjdk17-ebq:latest

<NAME_EMAIL>

COPY ./target/qydata-basic-info-server.jar myapp.jar
COPY ./ip2region.db /opt/project/data/ip2region.db

ENV TZ=Asia/Shanghai
ENV JAR_OPTS="--spring.profiles.active=common"
ENV JVM_OPTS=""
ENV ADD_OPENS="--add-opens java.base/java.lang=ALL-UNNAMED\
               --add-opens java.base/java.util=ALL-UNNAMED\
               --add-opens java.base/java.io=ALL-UNNAMED\
               --add-opens java.base/java.util.concurrent=ALL-UNNAMED\
               --add-opens java.base/java.net=ALL-UNNAMED\
               --add-opens java.base/java.math=ALL-UNNAMED\
               --add-opens java.base/java.text=ALL-UNNAMED"

RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

EXPOSE 8083

CMD ["sh", "-c", "java $JVM_OPTS $ADD_OPENS -jar myapp.jar $JAR_OPTS"]